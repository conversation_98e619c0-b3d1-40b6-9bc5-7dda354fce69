#!/bin/bash

clear_screen() {
    # Função para limpar a tela do terminal
    clear
}

print_opcoes() {
    # Função para exibir o menu de opções
    clear_screen
    echo "Gerar build para teste ou para loja:"
    echo "1. Q.A."
    echo "2. Loja"
    # Adicione mais opções de menu conforme necessário
}

print_menu_teste() {
    # Função para exibir o menu de opções
    # clear_screen
    echo "Selecione uma opção de build:"
    echo "1.  Android -> Staging"
    echo "2.  Android -> Staging (SEM EMPRESTIMOS)"
    echo "3.  Android -> Produção"
    echo "4.  iOS -> Staging"
    echo "5.  iOS -> Staging (SEM EMPRESTIMOS)"
    echo "6.  iOS -> Produção"
    echo "7.  WEB -> Staging"
    echo "8.  Android | IOS | WEB -> Staging"
    # Adicione mais opções de menu conforme necessário
}

execute_command_teste() {
    echo "Opcao selecionada:"
    # Função para executar um comando com base na opção selecionada
    if [[ $option == 1 ]]; then
    echo "::: STAGING :::"
        flutter build apk --debug --dart-define=FLAVOR=stag --flavor homolog 
    elif [[ $option == 2 ]]; then

    echo "::: STAGING :::"
        flutter build apk --release --dart-define=FLAVOR=stag --flavor homolog 
    elif [[ $option == 3 ]]; then

    echo ":::::::: PRODUCAO :::::::::"
        flutter build apk --release --dart-define=FLAVOR=prod --flavor prod
    elif [[ $option == 4 ]]; then

    echo "::: STAGING :::"
        flutter build ios --debug --dart-define=FLAVOR=stag 
    elif [[ $option == 5 ]]; then

    echo "::: STAGING :::"
        flutter build ios --release --dart-define=FLAVOR=stag
    elif [[ $option == 6 ]]; then

    echo ":::::::: PRODUCAO :::::::::"
        flutter build ios --release --dart-define=FLAVOR=prod
    elif [[ $option == 7 ]]; then

    echo "::: STAGING :::"
        flutter build web --web-renderer=html --dart-define=FLAVOR=stag  && firebase deploy --only hosting
    elif [[ $option == 8 ]]; then

    echo "::: STAGING :::"
        flutter build apk --debug --dart-define=FLAVOR=stag --flavor homolog 
        flutter build ios --debug --dart-define=FLAVOR=stag 
        flutter build web --web-renderer=html --dart-define=FLAVOR=stag  && firebase deploy --only hosting
    # Adicione mais condições 'elif' conforme necessário para cada opção
    fi

    # sleep 5
}

print_menu_loja() {
    # Função para exibir o menu de opções
    # clear_screen
    echo "Selecione uma opção de build:"
    echo "1. Android"
    echo "2. iOS"
    # Adicione mais opções de menu conforme necessário
}

execute_command_loja() {
    echo "Opcao selecionada:"
    echo ":::::::: PRODUCAO :::::::::"
    # Função para executar um comando com base na opção selecionada
    if [[ $option == 1 ]]; then
        flutter build appbundle --dart-define=FLAVOR=prod --flavor prod  --release
    elif [[ $option == 2 ]]; then
        flutter build ios --dart-define=FLAVOR=prod --release
    # Adicione mais condições 'elif' conforme necessário para cada opção
    fi
}

main() {
    while true; do
        print_opcoes
        read -p "
Digite o número da opção desejada (ou 'q' para sair): " option

        if [[ $option == "q" ]]; then
            break
        elif [[ $option == 1 ]]; then    
            print_menu_teste
            read -p "
Digite o número da opção desejada (ou 'q' para sair): " option
            if [[ $option == "q" ]]; then
                break
            fi    
            execute_command_teste
        elif [[ $option == 2 ]]; then    
            print_menu_loja
            read -p "
Digite o número da opção desejada (ou 'q' para sair): " option
            execute_command_loja
            if [[ $option == "q" ]]; then
                break
            fi
        fi

        read -n 1 -s -r -p "
        
Pressione qualquer tecla para continuar..."
    done
}

main
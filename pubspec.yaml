name: siclosbank
description: 'A new Flutter project. Run Flutter version 3.32.8'
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.6+65

environment:
  sdk: ^3.8.1 #'>=3.3.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_modular: ^6.4.1
  equatable: ^2.0.5
  firebase_core: ^4.0.0
  firebase_remote_config: ^6.0.0
  firebase_messaging: ^16.0.0
  firebase_analytics: ^12.0.0
  shared_preferences: ^2.2.3
  dio: ^5.7.0
  http_parser: ^4.0.2
  json_annotation: ^4.9.0
  encrypt: ^5.0.3
  random_string_generator: ^2.0.0
  flutter_bloc: ^9.1.1
  open_file: ^3.5.10
  path_provider: ^2.1.5
  shimmer: ^3.0.0
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.0
  flutter_screenutil: ^5.9.3
  intl: ^0.20.2
  flutter_udid: ^3.0.0
  device_info_plus: ^11.2.1
  cpf_cnpj_validator: ^2.0.0
  string_validator: ^1.1.0
  url_launcher: ^6.3.1
  percent_indicator: ^4.2.4
  timer_count_down: ^2.2.2
  webview_flutter: ^4.11.0
  syncfusion_flutter_pdfviewer: ^28.1.39
  permission_handler: ^11.3.1
  package_info_plus: ^8.1.3
  flutter_xlider: ^3.5.0
  pull_to_refresh: ^2.0.0
  share_plus: ^10.1.4
  calendar_date_picker2: ^1.1.9
  flutter_local_notifications: ^18.0.1
  local_auth: ^2.3.0
  flutter_native_splash: ^2.4.1
  image_cropper: ^9.0.0
  image_picker: ^1.1.2
  flutter_image_compress: ^2.4.0
  camera: ^0.11.1
  path: ^1.9.0
  syncfusion_flutter_charts: ^28.2.12
  # flutter_in_store_app_version_checker: ^1.5.2
  newrelic_mobile: ^1.1.10
  carousel_slider: ^5.1.1
  result_dart: ^2.1.1
  pretty_dio_logger: ^1.4.0
  extended_masked_text: ^3.0.1
  pretty_qr_code: ^3.5.0
  bloc: ^9.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: '^0.14.3'
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.9
  # retrofit_generator: ^8.2.1
  mocktail: ^1.0.4

flutter_native_splash:
  android: true
  ios: true
  color: '#00424B'
  image: 'assets/images/ic_launcher.png'

  android_12:
    color: '#00424B'
    image: 'assets/images/ic_launcher.png'

dependency_overrides:
  # intl: ^0.19.0
  webview_flutter_android: ^4.4.2
  webview_flutter_wkwebview: ^3.20.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
    - assets/jsons/
    - assets/html/clicksign_page.html
    - assets/fonts/
    - assets/env/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Thin.ttf
          weight: 100
        - asset: assets/fonts/Roboto-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Roboto-Black.ttf
          weight: 900
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

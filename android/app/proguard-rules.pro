# Preserva tudo da Play Core
-keep class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

# Preserva tarefas (Tasks) usadas pela Play Core
-keep class com.google.android.play.core.tasks.** { *; }
-dontwarn com.google.android.play.core.tasks.**

# Preserva classes do SplitCompat e SplitInstall
-keep class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-keep class com.google.android.play.core.splitinstall.** { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallRequest$Builder { *; }

# Preserva classes que usam reflection
-keepattributes *Annotation*, Signature, InnerClasses, EnclosingMethod

# Incluir regras sugeridas pelo build
#-include build/app/outputs/mapping/prodRelease/missing_rules.txt

# Firebase
-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.**

# Classes genéricas com reflection
-keepattributes Signature
-keepattributes *Annotation*
-keep class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# GSON ou Moshi (se estiver usando)
-keep class sun.misc.Unsafe { *; }

# Evita remoção de classes usadas por reflection
-keep class **.** { *; }
-keepnames class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Evita erros em libs internas com type parameters
-keepclassmembers class * {
    *;
}
-keepclassmembers class * {
    public <init>(...);
}

# Corrige problemas com Dagger/Hilt se estiver usando
-keep class dagger.** { *; }
-dontwarn dagger.**

# Corrige Firebase Remote Config ou Messaging
-keep class com.google.firebase.messaging.** { *; }
-keep class com.google.firebase.components.** { *; }
-keep class com.google.firebase.provider.** { *; }
-keep class com.google.firebase.** { *; }

# Corrige erros com Kotlin Metadata
-keepclassmembers class ** {
    @kotlin.Metadata *;
}

# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.squareup.okhttp.CacheControl
-dontwarn com.squareup.okhttp.Call
-dontwarn com.squareup.okhttp.Callback
-dontwarn com.squareup.okhttp.Handshake
-dontwarn com.squareup.okhttp.Headers
-dontwarn com.squareup.okhttp.MediaType
-dontwarn com.squareup.okhttp.OkHttpClient
-dontwarn com.squareup.okhttp.OkUrlFactory
-dontwarn com.squareup.okhttp.Protocol
-dontwarn com.squareup.okhttp.Request$Builder
-dontwarn com.squareup.okhttp.Request
-dontwarn com.squareup.okhttp.RequestBody
-dontwarn com.squareup.okhttp.Response$Builder
-dontwarn com.squareup.okhttp.Response
-dontwarn com.squareup.okhttp.ResponseBody
-dontwarn okhttp3.OkUrlFactory
-dontwarn retrofit.Endpoint
-dontwarn retrofit.ErrorHandler
-dontwarn retrofit.Profiler
-dontwarn retrofit.RequestInterceptor
-dontwarn retrofit.RestAdapter$Builder
-dontwarn retrofit.RestAdapter$Log
-dontwarn retrofit.RestAdapter$LogLevel
-dontwarn retrofit.RestAdapter
-dontwarn retrofit.client.Client$Provider
-dontwarn retrofit.client.Client
-dontwarn retrofit.client.Header
-dontwarn retrofit.client.Request
-dontwarn retrofit.client.Response
-dontwarn retrofit.converter.Converter
-dontwarn retrofit.mime.TypedInput
-dontwarn retrofit.mime.TypedOutput

-dontwarn com.newrelic.agent.android.ndk.AgentNDK$Builder
-dontwarn com.newrelic.agent.android.ndk.AgentNDK
-dontwarn com.newrelic.agent.android.ndk.AgentNDKListener
-dontwarn com.newrelic.agent.android.ndk.NativeCrash
-dontwarn com.newrelic.agent.android.ndk.NativeException
-dontwarn com.newrelic.agent.android.ndk.NativeStackTrace
-dontwarn com.newrelic.agent.android.ndk.NativeThreadInfo
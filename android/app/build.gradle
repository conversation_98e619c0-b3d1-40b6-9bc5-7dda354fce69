plugins {
    id "com.android.application"
    id "kotlin-android"
     id "com.newrelic.agent.android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'

}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.siclos.siclosbank"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion
    // ndkVersion = "27.0.********"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    dependencies {
        coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4' // Verifique se há uma versão mais recente
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.siclos.siclosbank"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24 // flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }
    
    signingConfigs {
         debug {
        }
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = file(keystoreProperties['storeFile'])
            storePassword = keystoreProperties["storePassword"] as String
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now,
            // so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("release")
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // useProguard true
        }
        debug {
            signingConfig = signingConfigs.debug
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
       

        }
    }

    flavorDimensions "app"
    productFlavors {
        prod {
            dimension "app"
            resValue "string", "app_name", "SiclosBank"
        }

        homolog {
            dimension "app"
            applicationIdSuffix ".homolog"
            versionNameSuffix "-shomolog"
            resValue "string", "app_name", "SB Homolog."
        }

        dev {
            dimension "app"
            applicationIdSuffix ".develop"
            versionNameSuffix "-develop"
            resValue "string", "app_name", "SB Develop."
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    // implementation("androidx.activity:activity-ktx:1.6.1")
    // implementation 'androidx.multidex:multidex:2.0.0'
    // implementation 'androidx.work:work-runtime-ktx:2.7.1'
    // implementation 'androidx.appcompat:appcompat:1.2.0'

    /* unico */
    // implementation "io.unico:capture:5.18.0"

    //FirebaseCrashlytics
    implementation platform('com.google.firebase:firebase-bom:33.0.0')

    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'

    // FilePicker nativo
//    implementation 'com.github.atwa:filepicker:1.0.7'
   }


<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
   <!-- <uses-feature android:name="android.hardware.camera" /> -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:node="remove"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" tools:node="remove"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" tools:node="remove"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" tools:node="remove"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO" tools:node="remove"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" tools:node="remove"/>
   <!-- <uses-permission android:name="android.permission.USE_BIOMETRIC" /> -->
<!--    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />-->
<!--    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />-->
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
<!--    <uses-permission android:name="android.permission.VIBRATE" />-->
<!--    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />-->
<!--    <uses-feature android:name="android.hardware.camera" />-->
<!--    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />-->
<!--    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />-->
<!--    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />-->

    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        tools:node="remove" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="com.google.android.gms.permission.AD_ID"
        tools:node="remove" />

    <queries>
        <!-- If your app checks for SMS support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="sms" />
        </intent>
        <!-- If your app checks for call support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="tel" />
        </intent>
        <intent>
            <action android:name="android.intent.action.ACTION_OPEN_DOCUMENT_TREE" />
        </intent>
    </queries>

    <application
        android:label="@string/app_name"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>

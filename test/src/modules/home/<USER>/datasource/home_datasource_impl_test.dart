import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_endpoints.dart';
import 'package:siclosbank/app/modules/home/<USER>/datasource/endpoints/home_endpoints.dart';
import 'package:siclosbank/app/modules/home/<USER>/datasource/home_datasource_impl.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';
import 'package:siclosbank/app/shared/data/models/wallet/balance_response.dart';

class MockClient extends Mock implements IClient {}

void main() {
  late HomeDatasourceImplements datasource;
  late MockClient mockClient;

  setUp(() {
    mockClient = MockClient();
    datasource = HomeDatasourceImplements(client: mockClient);
  });

  group('Home Datasource Implements', () {});
}

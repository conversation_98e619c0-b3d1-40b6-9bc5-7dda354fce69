import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/home/<USER>/datasource/home_datasource.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/home_repository.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/i_home_repository.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class MockHomeDatasource extends Mock implements IHomeDatasource {}

class MockServerErrorHandling extends Mock implements ServerErrorHandling {}

void main() {
  late IHomeRepository repository;
  late MockHomeDatasource mockDatasource;

  setUp(() {
    mockDatasource = MockHomeDatasource();
    repository = HomeRepository(mockDatasource);
  });

  group('Home Repository', () {});
}

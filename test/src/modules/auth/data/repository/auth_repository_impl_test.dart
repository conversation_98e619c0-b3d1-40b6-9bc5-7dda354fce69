import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/auth/data/database/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/models/login_request.dart';
import 'package:siclosbank/app/modules/auth/data/repository/auth_repository_impl.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/data/models/token_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class MockAuthDatasource extends Mock implements IAuthDatasource {}

class MockAuthDatabase extends Mock implements IAuthDatabase {}

class MockServerErrorHandling extends Mock implements ServerErrorHandling {}

void main() {
  late AuthRepositoryImpl repository;
  late MockAuthDatasource mockDatasource;
  late MockAuthDatabase mockDatabase;

  setUp(() {
    mockDatasource = MockAuthDatasource();
    mockDatabase = MockAuthDatabase();
    repository = AuthRepositoryImpl(mockDatasource, mockDatabase);
  });

  group('AuthRepositoryImpl', () {
    group('login', () {
      final loginRequest =
          LoginRequest(login: 'test', password: 'test', tokenFCM: '');
      final tokenResponse = TokenResponse(token: '123');
      final authorizeDeviceResponse = AuthorizeDeviceResponse(id: '456');

      test(
          'should return TokenResponse when the response status code is 200 or 206',
          () async {
        final apiResponse =
            ApiResponse(statusCode: 200, data: {'token': '123'});

        when(() => mockDatasource.login(loginRequest.toMap()))
            .thenAnswer((_) async => apiResponse);

        final result = await repository.login(loginRequest);

        expect(result, isA<TokenResponse>());
        expect((result as TokenResponse).token, tokenResponse.token);
        verify(() => mockDatasource.login(loginRequest.toMap())).called(1);
        verifyNoMoreInteractions(mockDatasource);
      });

      test(
          'should return AuthorizeDeviceResponse when the response status code is 202',
          () async {
        final apiResponse = ApiResponse(statusCode: 202, data: {'id': '456'});

        when(() => mockDatasource.login(loginRequest.toMap()))
            .thenAnswer((_) async => apiResponse);

        final result = await repository.login(loginRequest);

        expect(result, isA<AuthorizeDeviceResponse>());
        expect(
            (result as AuthorizeDeviceResponse).id, authorizeDeviceResponse.id);
        verify(() => mockDatasource.login(loginRequest.toMap())).called(1);
        verifyNoMoreInteractions(mockDatasource);
      });

      test(
          'should handle error correctly when the response status code is not 200, 206, or 202',
          () async {
        final apiResponse = ApiResponse(statusCode: 400);

        when(() => mockDatasource.login(loginRequest.toMap()))
            .thenAnswer((_) async => apiResponse);

        expect(await repository.login(loginRequest),
            throwsA(isA<ErrorResponse>()));
        verify(() => mockDatasource.login(loginRequest.toMap())).called(1);
        verifyNoMoreInteractions(mockDatasource);
      });

      test('should handle exception correctly when an error is thrown',
          () async {
        final error = Exception('Server error');

        when(() => mockDatasource.login(loginRequest.toMap())).thenThrow(error);

        final result = await repository.login(loginRequest);

        expect(result, 'Handled Error');
        verify(() => mockDatasource.login(loginRequest.toMap())).called(1);
        verifyNoMoreInteractions(mockDatasource);
      });
    });

    // group('logout', () {
    //   test('should call logout on the datasource', () async {
    //     when(() => mockDatasource.logout())
    //         .thenAnswer((_) async => Future.value());

    //     await repository.logout();

    //     verify(() => mockDatasource.logout()).called(1);
    //     verifyNoMoreInteractions(mockDatasource);
    //   });

    //   test('should handle exception correctly when an error is thrown',
    //       () async {
    //     final error = Exception('Server error');

    //     when(() => mockDatasource.logout()).thenThrow(error);

    //     await repository.logout();

    //     verify(() => mockDatasource.logout()).called(1);
    //     verifyNoMoreInteractions(mockDatasource);
    //   });
    // });

    // group('recoveryPassword', () {
    //   final cpf = '12345678900';
    //   final email = '<EMAIL>';
    //   final body = {"cpf": cpf, "email": email};

    // //   test(
    // //       'should call recoverPassword on the datasource with correct parameters',
    // //       () async {
    // //     when(() => mockDatasource.recoverPassword(body))
    // //         .thenAnswer((_) async => Future.value());

    // //     await repository.recoveryPassword(
    // //       cpf: cpf,
    // //     );

    // //     verify(() => mockDatasource.recoverPassword(body)).called(1);
    // //     verifyNoMoreInteractions(mockDatasource);
    // //   });

//     // //   test('should handle exception correctly when an error is thrown',
//     // //       () async {
//     // //     final error = Exception('Server error');

//     // //     when(() => mockDatasource.recoverPassword(body)).thenThrow(error);

//     // //     await repository.recoveryPassword(
//     // //       cpf: cpf,
//     // //     );

//     // //     verify(() => mockDatasource.recoverPassword(body)).called(1);
//     // //     verifyNoMoreInteractions(mockDatasource);
//     // //   });
//     // // });
  });
}

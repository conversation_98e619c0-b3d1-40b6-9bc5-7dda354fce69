import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_datasource_impl.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_endpoints.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

class MockClient extends Mock implements IClient {}

void main() {
  late AuthDatasourceImpl datasource;
  late MockClient mockClient;

  setUp(() {
    mockClient = MockClient();
    datasource = AuthDatasourceImpl(mockClient);
  });

  group('AuthDatasourceImpl', () {
    test('login should call post on the client with correct parameters',
        () async {
      final loginRequest = {'username': 'test', 'password': 'test'};
      final apiResponse = ApiResponse(data: {'token': '123'});

      when(() => mockClient.fetch(
          path: AuthEndPoints.login,
          method: 'post',
          data: loginRequest)).thenAnswer((_) async => apiResponse);

      final result = await datasource.login(loginRequest);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
          method: 'post',
          path: AuthEndPoints.login,
          data: loginRequest)).called(1);
      verifyNoMoreInteractions(mockClient);
    });

    test('logout should call fetch on the client with correct parameters',
        () async {
      when(() => mockClient.fetch(
          method: 'POST',
          path: AuthEndPoints.logout,
          data: {})).thenAnswer((_) async => Future.value(ApiResponse()));

      await datasource.logout();

      verify(() => mockClient.fetch(
          method: 'POST', path: AuthEndPoints.logout, data: {})).called(1);
      verifyNoMoreInteractions(mockClient);
    });

    // test(
    //     'recoverPassword should call fetch on the client with correct parameters',
    //     () async {
    //   final body = {'email': '<EMAIL>'};

    //   when(() => mockClient.fetch(
    //       method: 'POST',
    //       path: AuthEndPoints.recoverPassword,
    //       data: body)).thenAnswer((_) async => Future.value(ApiResponse()));

    //   await datasource.recoverPassword(body);

    //   verify(() => mockClient.fetch(
    //       method: 'POST',
    //       path: AuthEndPoints.recoverPassword,
    //       data: body)).called(1);
    //   verifyNoMoreInteractions(mockClient);
    // });
  });
}

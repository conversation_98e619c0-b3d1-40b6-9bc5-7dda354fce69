import 'package:flutter_test/flutter_test.dart';
import 'package:siclosbank/app/modules/auth/data/models/login_request.dart';

void main() {
  group('LoginRequest', () {
    test('fromJson should return a valid model', () {
      final json = {
        'login': 'testUser',
        'password': 'testPassword',
      };

      final model = LoginRequest.fromMap(json);

      expect(model.login, 'testUser');
      expect(model.password, 'testPassword');
    });

    test('toJson should return a valid map', () {
      final model = LoginRequest(
          login: 'testUser', password: 'testPassword', tokenFCM: '');

      final json = model.toMap();

      expect(json['login'], 'testUser');
      expect(json['password'], 'testPassword');
    });

    test('toString should return a valid JSON string', () {
      final model = LoginRequest(
          login: 'testUser', password: 'testPassword', tokenFCM: '');

      final jsonString = model.toString();

      expect(jsonString, '{"login":"testUser","password":"testPassword"}');
    });
  });
}

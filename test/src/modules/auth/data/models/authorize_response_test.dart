import 'package:flutter_test/flutter_test.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';

void main() {
  group('AuthorizeResponse', () {
    test('fromJson should return a valid model', () {
      final json = {
        'id': '123',
        'email': '<EMAIL>',
        'celular': '*********',
        'pin': true,
      };

      final model = AuthorizeDeviceResponse.fromJson(json);

      expect(model.id, '123');
      expect(model.email, '<EMAIL>');
      expect(model.phone, '*********');
      expect(model.pin, true);
    });

    test('to<PERSON><PERSON> should return a valid map', () {
      final model = AuthorizeDeviceResponse(
        id: '123',
        email: '<EMAIL>',
        phone: '*********',
        pin: true,
      );

      final jsonString = model.toString();

      expect(jsonString,
          '{"id":"123","email":"<EMAIL>","celular":"*********","pin":true}');
    });
  });
}

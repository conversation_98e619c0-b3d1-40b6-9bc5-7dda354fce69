import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/auth/data/models/login_request.dart';
import 'package:siclosbank/app/modules/auth/domain/repositories/i_auth_repository.dart';
import 'package:siclosbank/app/modules/auth/domain/usecases/login_usecase.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/data/models/token_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class MockAuthRepository extends Mock implements IAuthRepository {}

void main() {
  late LoginUsecase loginUsecase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    loginUsecase = LoginUsecase(mockAuthRepository);
  });

  group('LoginUsecase', () {
    final loginRequest =
        LoginRequest(login: 'testUser', password: 'testPassword', tokenFCM: '');
    final tokenResponse = TokenResponse(token: 'testToken');
    final authorizeDeviceResponse = AuthorizeDeviceResponse(id: 'testId');

    test('login should return TokenResponse and set token in AppSession',
        () async {
      when(() => mockAuthRepository.login(any()))
          .thenAnswer((_) async => tokenResponse);

      final result = await loginUsecase.login(loginRequest);

      expect(result, isA<TokenResponse>());
      verify(() => mockAuthRepository.login(loginRequest)).called(1);
    });

    test(
        'login should return AuthorizeDeviceResponse and set authorize device response in AppSession',
        () async {
      when(() => mockAuthRepository.login(any()))
          .thenAnswer((_) async => authorizeDeviceResponse);

      final result = await loginUsecase.login(loginRequest);

      expect(result, isA<AuthorizeDeviceResponse>());
      verify(() => mockAuthRepository.login(loginRequest)).called(1);
    });

    test('login should handle ErrorResponse and return ServerErrorHandling',
        () async {
      final errorResponse = ErrorResponse(message: 'error');
      when(() => mockAuthRepository.login(any())).thenThrow(errorResponse);

      final result = await loginUsecase.login(loginRequest);

      expect(result, isA<ServerErrorHandling>());
      verify(() => mockAuthRepository.login(loginRequest)).called(1);
    });

    // // test('logout should call repository logout method', () async {
    // //   when(() => mockAuthRepository.logout()).thenAnswer((_) async => {});

    // //   await loginUsecase.logout();

    // //   verify(() => mockAuthRepository.logout()).called(1);
    // // });

    // test('logout should handle errors and return ServerErrorHandling',
    //     () async {
    //   when(() => mockAuthRepository.logout()).thenThrow(Exception());

    //   await loginUsecase.logout();

    //   verify(() => mockAuthRepository.logout()).called(1);
    // });

    // test('recoveryPassword should call repository recoveryPassword method',
    //     () async {
    //   when(() => mockAuthRepository.recoveryPassword(
    //         cpf: any(named: 'cpf'),
    //       )).thenAnswer((_) async => '');

    //   await loginUsecase.recoveryPassword(
    //     cpf: '12345678901',
    //   );

    //   verify(() => mockAuthRepository.recoveryPassword(
    //         cpf: '12345678901',
    //       )).called(1);
    // });

    test('recoveryPassword should handle errors and return ServerErrorHandling',
        () async {
      when(() => mockAuthRepository.recoveryPassword(
            cpf: any(named: 'cpf'),
          )).thenThrow(Exception());

      await loginUsecase.recoveryPassword(
        cpf: '12345678901',
      );

      verify(() => mockAuthRepository.recoveryPassword(
            cpf: '12345678901',
          )).called(1);
    });
  });
}

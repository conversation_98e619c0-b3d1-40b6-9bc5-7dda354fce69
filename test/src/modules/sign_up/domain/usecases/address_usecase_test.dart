import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/address_usecase.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';

class MockSignUpRepository extends Mock implements ISignUpRepository {}

void main() {
  late AddressUsecaseImpl addressUsecase;
  late MockSignUpRepository mockSignUpRepository;

  setUp(() {
    mockSignUpRepository = MockSignUpRepository();
    addressUsecase = AddressUsecaseImpl(mockSignUpRepository);
  });

  group('AddressUsecaseImpl', () {
    const userId = 'test_user_id';

    test('should return AddressResponse when getAddress is called', () async {
      final addressResponse = AddressResponse(street: '123 Test St');
      when(() => mockSignUpRepository.getAddress(userId))
          .thenAnswer((_) async => addressResponse);

      final result = await addressUsecase.getAddress(userId);

      expect(result, addressResponse);
      verify(() => mockSignUpRepository.getAddress(userId)).called(1);
    });

    test('should throw an exception when getAddress fails', () async {
      when(() => mockSignUpRepository.getAddress(userId))
          .thenThrow(Exception('Failed to get address'));

      expect(() => addressUsecase.getAddress(userId), throwsException);
      verify(() => mockSignUpRepository.getAddress(userId)).called(1);
    });

    test('should return RegisterUserResponse when confirmAddress is called',
        () async {
      final registerUserResponse = RegisterUserResponse(id: userId);
      when(() => mockSignUpRepository.confirmAddress(userId))
          .thenAnswer((_) async => registerUserResponse);

      final result = await addressUsecase.confirmAddress(userId);

      expect(result, registerUserResponse);
      verify(() => mockSignUpRepository.confirmAddress(userId)).called(1);
    });

    test('should throw an exception when confirmAddress fails', () async {
      when(() => mockSignUpRepository.confirmAddress(userId))
          .thenThrow(Exception('Failed to confirm address'));

      expect(() => addressUsecase.confirmAddress(userId), throwsException);
      verify(() => mockSignUpRepository.confirmAddress(userId)).called(1);
    });
  });
}

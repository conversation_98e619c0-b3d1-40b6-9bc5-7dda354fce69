import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/check_is_collaborator_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class MockSignUpRepository extends Mock implements ISignUpRepository {}

void main() {
  late CheckIsCollaboratorUsecase usecase;
  late MockSignUpRepository mockRepository;

  setUp(() {
    mockRepository = MockSignUpRepository();
    usecase = CheckIsCollaboratorUsecase(mockRepository);
  });

  test(
      'should return RegisterUserResponse when the call to repository is successful',
      () async {
    final cpf = '***********';
    final response = RegisterUserResponse();

    when(() => mockRepository.checkIsCollaborator(cpf))
        .thenAnswer((_) async => response);

    final result = await usecase.call(cpf);

    expect(result, response);
    verify(() => mockRepository.checkIsCollaborator(cpf));
    verifyNoMoreInteractions(mockRepository);
  });

  test(
      'should handle error correctly when the call to repository throws an error',
      () async {
    final cpf = '***********';
    final error = ErrorResponse();

    when(() => mockRepository.checkIsCollaborator(cpf)).thenThrow(error);

    expect(() async => await usecase(cpf), throwsA(isA<ErrorResponse>()));
    verify(() => mockRepository.checkIsCollaborator(cpf)).called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/create_password_usecase.dart';

class MockSignUpRepository extends Mock implements ISignUpRepository {}

void main() {
  late CreatePasswordUsecaseImpl usecase;
  late MockSignUpRepository mockRepository;

  setUp(() {
    mockRepository = MockSignUpRepository();
    usecase = CreatePasswordUsecaseImpl(mockRepository);
  });

  test('should call createPassword on the repository with correct parameters',
      () async {
    final userId = 'user123';
    final password = 'password123';

    when(() =>
            mockRepository.createPassword(userId: userId, password: password))
        .thenAnswer((_) => Future.value());

    await usecase.call(userId: userId, password: password);
    verify(() =>
        mockRepository.createPassword(userId: userId, password: password));
    verifyNoMoreInteractions(mockRepository);
  });

  test('should rethrow the exception when repository throws an error',
      () async {
    final userId = 'user123';
    final password = 'password123';
    final error = Exception('Server error');

    when(() =>
            mockRepository.createPassword(userId: userId, password: password))
        .thenThrow(error);

    expect(
        () => usecase.call(userId: userId, password: password), throwsA(error));
    verify(() =>
        mockRepository.createPassword(userId: userId, password: password));
    verifyNoMoreInteractions(mockRepository);
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/send_sms_usecase.dart';

class MockSignUpRepository extends Mock implements ISignUpRepository {}

void main() {
  late SendSmsUsecaseImpl usecase;
  late MockSignUpRepository mockRepository;

  setUp(() {
    mockRepository = MockSignUpRepository();
    usecase = SendSmsUsecaseImpl(mockRepository);
  });

  test('should call sendSms on the repository with correct parameters',
      () async {
    const userId = 'user123';

    when(() => mockRepository.sendSms(userId))
        .thenAnswer((_) => Future.value());

    await usecase.call(userId);

    verify(() => mockRepository.sendSms(userId)).called(1);
    verifyNoMoreInteractions(mockRepository);
  });

  test('should rethrow the exception when sendSms throws an error', () async {
    const userId = 'user123';
    final error = Exception('Server error');

    when(() => mockRepository.sendSms(userId)).thenThrow(error);

    expect(() => usecase.call(userId), throwsA(error));
    verify(() => mockRepository.sendSms(userId)).called(1);
    verifyNoMoreInteractions(mockRepository);
  });

  test('should call checkSmsCode on the repository with correct parameters',
      () async {
    const userId = 'user123';
    const code = '123456';

    when(() => mockRepository.checkSmsCode(userId: userId, code: code))
        .thenAnswer((_) => Future.value());

    await usecase.checkSmsCode(userId: userId, code: code);

    verify(() => mockRepository.checkSmsCode(userId: userId, code: code))
        .called(1);
    verifyNoMoreInteractions(mockRepository);
  });

  test('should rethrow the exception when checkSmsCode throws an error',
      () async {
    const userId = 'user123';
    const code = '123456';
    final error = Exception('Server error');

    when(() => mockRepository.checkSmsCode(userId: userId, code: code))
        .thenThrow(error);

    expect(
        () => usecase.checkSmsCode(userId: userId, code: code), throwsA(error));
    verify(() => mockRepository.checkSmsCode(userId: userId, code: code))
        .called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}

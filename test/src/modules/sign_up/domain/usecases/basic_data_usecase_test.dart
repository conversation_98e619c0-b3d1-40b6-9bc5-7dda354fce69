import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/modules/sign_up/domain/models/occupation_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/models/politically_exposed_person_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/basic_data_usecase.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';

class MockSignUpRepository extends Mock implements ISignUpRepository {}

void main() {
  late BasicDataUsecaseImpl usecase;
  late MockSignUpRepository mockRepository;

  setUp(() {
    mockRepository = MockSignUpRepository();
    usecase = BasicDataUsecaseImpl(mockRepository);
  });

  group('BasicDataUsecaseImpl', () {
    // // test('createUser should return RegisterUserResponse on success', () async {
    // //   final basicDataRequest = BasicDataRequest(cpf: '***********');
    // //   final response =
    // //       RegisterUserResponse(id: '1', stage: 'stage1', cpf: '***********');

    // //   when(() => mockRepository.createBasicData(basicDataRequest))
    // //       .thenAnswer((_) async => response);

    // //   final result = await usecase.createUser(basicDataRequest);

    // //   expect(result, response);
    // //   verify(() => mockRepository.createBasicData(basicDataRequest)).called(1);
    // // });

    // test('createUser should handle error correctly', () async {
    //   final basicDataRequest = BasicDataRequest(cpf: '***********');
    //   final error = Exception('Error');

    //   when(() => mockRepository.createBasicData(basicDataRequest))
    //       .thenThrow(error);

    //   expect(
    //     () async => await usecase.createUser(basicDataRequest),
    //     throwsA(isA<Exception>()),
    //   );
    //   verify(() => mockRepository.createBasicData(basicDataRequest)).called(1);
    // });

    test('getOccupationsOptions should return a list of OccupationResponse',
        () async {
      final occupations = [
        {"id": '1', "description": 'Occupation1'}
      ];

      when(() => mockRepository.getOccupationsOptions())
          .thenAnswer((_) async => occupations);

      final result = await usecase.getOccupationsOptions();

      expect(
          result, equals([OccupationResponse(key: '1', value: 'Occupation1')]));
      verify(() => mockRepository.getOccupationsOptions()).called(1);
    });

    test(
        'getPepOptions should return a list of PoliticallyExposedPersonResponse',
        () async {
      final pepOptions = [
        PoliticallyExposedPersonResponse(key: '1', value: 'option1'),
        PoliticallyExposedPersonResponse(key: '2', value: 'option2'),
        PoliticallyExposedPersonResponse(key: '3', value: 'option3'),
      ];

      when(() => mockRepository.getPublicyExposedPersonOptions())
          .thenAnswer((_) async => {
                '1': 'option1',
                '2': 'option2',
                '3': 'option3',
              });

      final result = await usecase.getPepOptions();

      expect(result, equals(pepOptions));
      verify(() => mockRepository.getPublicyExposedPersonOptions()).called(1);
    });
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';

void main() {
  group('BasicDataRequest', () {
    test('props should contain all fields', () {
      final request = BasicDataRequest(
        cpf: '***********',
        mobilePhone: '***********',
        email: '<EMAIL>',
        fullName: 'Full Name',
        birthdate: '2000-01-01',
        maritalStatus: 'Single',
        motherName: 'Mother Name',
        socialName: 'Social Name',
        occupation: 'Occupation',
        politicallyExposedPerson: 'No',
        documentType: 'RG',
        documentNUmber: '*********',
        expeditionDate: '2000-01-01',
        issuingInstitution: 'SSP',
        nationality: 'Brazileiro',
      );

      expect(
        request.props,
        [
          '***********',
          '***********',
          '<EMAIL>',
          'nickname',
          'Full Name',
          '2000-01-01',
          'Single',
          'Mother Name',
          'Social Name',
          'Occupation',
          'No',
        ],
      );
    });

    // test('should create an instance with default values', () {
    //   final request = BasicDataRequest();

    //   expect(request.cpf, '');
    //   expect(request.mobilePhone, '');
    //   expect(request.email, '');
    //   expect(request.fullName, '');
    //   expect(request.birthdate, '');
    //   expect(request.maritalStatus, '');
    //   expect(request.motherName, '');
    //   expect(request.socialName, '');
    //   expect(request.occupation, '');
    //   expect(request.politicallyExposedPerson, '');
    // });
  });
}

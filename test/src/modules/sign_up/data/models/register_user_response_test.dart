import 'package:flutter_test/flutter_test.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';

void main() {
  group('RegisterUserResponse', () {
    test('fromMap should return a valid model', () {
      final map = {
        'id': '1',
        'stage': 'stage1',
        'cpf': '***********',
      };

      final response = RegisterUserResponse.fromMap(map);

      expect(response.id, '1');
      expect(response.stage, 'stage1');
      expect(response.cpf, '***********');
    });

    test('toMap should return a valid map', () {
      final response = RegisterUserResponse(
        id: '1',
        stage: 'stage1',
        cpf: '***********',
      );

      final map = response.toMap();

      expect(map['id'], '1');
      expect(map['stage'], 'stage1');
      expect(map['cpf'], '***********');
    });

    test('from<PERSON><PERSON> should return a valid model', () {
      final jsonStr = '{"id":"1","stage":"stage1","cpf":"***********"}';

      final response = RegisterUserResponse.fromJson(jsonStr);

      expect(response.id, '1');
      expect(response.stage, 'stage1');
      expect(response.cpf, '***********');
    });

    test('toJson should return a valid json string', () {
      final response = RegisterUserResponse(
        id: '1',
        stage: 'stage1',
        cpf: '***********',
      );

      final jsonStr = response.toJson();

      expect(jsonStr,
          '{"id":"1","isCollaborator":null,"isRegistered":null,"stage":"stage1","cpf":"***********"}');
    });

    test('props should contain all fields', () {
      final response = RegisterUserResponse(
        id: '1',
        stage: 'stage1',
        cpf: '***********',
      );

      expect(
        response.props,
        [
          '1',
          'stage1',
          '***********',
        ],
      );
    });
  });
}

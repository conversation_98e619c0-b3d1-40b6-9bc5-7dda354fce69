import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/sign_up/data/datasource/sign_up_datasource_impl.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/modules/sign_up/data/datasource/endpoints/sign_up_endpoints.dart';

class MockClient extends Mock implements IClient {}

void main() {
  late MockClient mockClient;
  late SignUpDatasourceImpl datasource;

  setUp(() {
    mockClient = MockClient();
    datasource = SignUpDatasourceImpl(mockClient);
  });

  group('SignUpDatasourceImpl', () {
    test('checkIsCollaborator', () async {
      const cpf = '***********';
      final path = SignUpEndpoints.checkIsCollaborator(cpf);
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch<dynamic>(method: 'GET', path: path))
          .thenAnswer((_) async => apiResponse);

      final result = await datasource.checkIsCollaborator(cpf);

      expect(result, apiResponse);
      verify(() => mockClient.fetch<dynamic>(method: 'GET', path: path))
          .called(1);
    });

    test('getPublicyExposedPersonOptions', () async {
      final path = SignUpEndpoints.publiclyExposedPersonOptions;
      final apiResponse = ApiResponse(data: []);

      when(() => mockClient.fetch(method: 'GET', path: path))
          .thenAnswer((_) async => apiResponse);

      final result = await datasource.getPublicyExposedPersonOptions();

      expect(result, apiResponse);
      verify(() => mockClient.fetch(method: 'GET', path: path)).called(1);
    });

    test('getOccupationsOptions', () async {
      final path = SignUpEndpoints.occupationsOptions;
      final apiResponse = ApiResponse(data: []);

      when(() => mockClient.fetch(method: 'GET', path: path))
          .thenAnswer((_) async => apiResponse);

      final result = await datasource.getOccupationsOptions();

      expect(result, apiResponse);
      verify(() => mockClient.fetch(method: 'GET', path: path)).called(1);
    });

    test('createBasicDataUser', () async {
      final basicDataRequest = BasicDataRequest(
        fullName: 'John Doe',
        cpf: '***********',
        birthdate: '2000-01-01',
        email: '<EMAIL>',
        mobilePhone: '1234567890',
        socialName: 'John',
        politicallyExposedPerson: 'No',
        maritalStatus: 'Single',
        motherName: 'Jane Doe',
        occupation: 'Engineer',
        documentType: 'RG',
        documentNUmber: '123456789',
        expeditionDate: '2000-01-01',
        issuingInstitution: 'SSP',
        nationality: 'Brazileiro',
      );
      final path = SignUpEndpoints.createUser;
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch(
            method: 'POST',
            path: path,
            data: basicDataRequest.toMap(),
          )).thenAnswer((_) async => apiResponse);

      final result = await datasource.createBasicDataUser(basicDataRequest);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
            method: 'POST',
            path: path,
            data: basicDataRequest.toMap(),
          )).called(1);
    });

    test('sendSms', () async {
      const userId = 'user123';
      final path = SignUpEndpoints.sendSms(userId);
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch(
            method: 'POST',
            path: path,
            queryParameters: {'id': userId},
          )).thenAnswer((_) async => apiResponse);

      final result = await datasource.sendSms(userId);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
            method: 'POST',
            path: path,
            queryParameters: {'id': userId},
          )).called(1);
    });

    test('checkSmsCode', () async {
      const userId = 'user123';
      const code = '123456';
      final path = SignUpEndpoints.checkSmsCode(userId);
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch(
            method: 'POST',
            path: path,
            queryParameters: {'id': userId},
            data: {'code': code},
          )).thenAnswer((_) async => apiResponse);

      final result = await datasource.checkSmsCode(userId: userId, code: code);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
            method: 'POST',
            path: path,
            queryParameters: {'id': userId},
            data: {'code': code},
          )).called(1);
    });

    test('createPassword', () async {
      const userId = 'user123';
      const password = 'password123';
      final path = SignUpEndpoints.createPassword(userId);
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch(
            method: 'PATCH',
            path: path,
            data: {'password': password},
          )).thenAnswer((_) async => apiResponse);

      final result =
          await datasource.createPassword(userId: userId, password: password);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
            method: 'PATCH',
            path: path,
            data: {'password': password},
          )).called(1);
    });

    test('confirmAddress', () async {
      const userId = 'user123';
      final path = SignUpEndpoints.addressConfirm(userId);
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch(
            method: 'PATCH',
            path: path,
          )).thenAnswer((_) async => apiResponse);

      final result = await datasource.confirmAddress(userId);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
            method: 'PATCH',
            path: path,
          )).called(1);
    });

    test('getAddress', () async {
      const userId = 'user123';
      final path = SignUpEndpoints.address(userId);
      final apiResponse = ApiResponse(data: true);

      when(() => mockClient.fetch(
            method: 'GET',
            path: path,
          )).thenAnswer((_) async => apiResponse);

      final result = await datasource.getAddress(userId);

      expect(result, apiResponse);
      verify(() => mockClient.fetch(
            method: 'GET',
            path: path,
          )).called(1);
    });
  });
}

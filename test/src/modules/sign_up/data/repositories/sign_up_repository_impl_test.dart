import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:siclosbank/app/modules/sign_up/data/datasource/i_sign_up_datasource.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/modules/sign_up/data/repositories/sign_up_repository_impl.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class MockSignUpDatasource extends Mock implements ISignUpDatasource {}

class MockServerErrorHandling extends Mock implements ServerErrorHandling {
  Future<void> handleError(Exception error) async {
    // Mock implementation
  }
}

void main() {
  late SignUpRepositoryImpl repository;
  late MockSignUpDatasource mockDatasource;
  late MockServerErrorHandling mockErrorHandling;

  setUp(() {
    mockDatasource = MockSignUpDatasource();
    mockErrorHandling = MockServerErrorHandling();
    repository = SignUpRepositoryImpl(mockDatasource);
  });

  group('SignUpRepositoryImpl', () {
    // test('createBasicData should return RegisterUserResponse on success',
    //     () async {
    //   // final basicDataRequest = BasicDataRequest(cpf: '***********');
    //   final responseMap = {'id': '1', 'stage': 'stage1', 'cpf': '***********'};
    //   final registerUserResponse = RegisterUserResponse.fromMap(responseMap);

    //   when(() => mockDatasource.createBasicDataUser(basicDataRequest))
    //       .thenAnswer((_) async => ApiResponse<dynamic>(data: responseMap));

    //   final result = await repository.createBasicData(basicDataRequest);

    //   expect(result, registerUserResponse);
    //   verify(() => mockDatasource.createBasicDataUser(basicDataRequest))
    //       .called(1);
    // });

    // test('createBasicData should handle error correctly', () async {
    //   final basicDataRequest = BasicDataRequest(cpf: '***********');
    //   final error = Exception('Error');

    //   when(() => mockDatasource.createBasicDataUser(basicDataRequest))
    //       .thenThrow(error);

    //   expect(
    //     () async => await repository.createBasicData(basicDataRequest),
    //     throwsA(isA<Exception>()),
    //   );
    //   verify(() => mockDatasource.createBasicDataUser(basicDataRequest))
    //       .called(1);
    // });

    test('confirmAddress should return RegisterUserResponse on success',
        () async {
      const userId = '1';
      final responseMap = {'id': '1', 'stage': 'stage1', 'cpf': '***********'};
      final registerUserResponse = RegisterUserResponse.fromMap(responseMap);

      when(() => mockDatasource.confirmAddress(userId))
          .thenAnswer((_) async => ApiResponse<dynamic>(data: responseMap));

      final result = await repository.confirmAddress(userId);

      expect(result, registerUserResponse);
      verify(() => mockDatasource.confirmAddress(userId)).called(1);
    });

    test('confirmAddress should handle error correctly', () async {
      const userId = '1';
      final error = Exception('Error');

      when(() => mockDatasource.confirmAddress(userId)).thenThrow(error);

      expect(
        () async => await repository.confirmAddress(userId),
        throwsA(isA<Exception>()),
      );
      verify(() => mockDatasource.confirmAddress(userId)).called(1);
    });
  });
}

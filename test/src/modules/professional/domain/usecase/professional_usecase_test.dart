import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/professional/data/models/hour_base_response.dart';
import 'package:siclosbank/app/modules/professional/data/models/time_sheet_response.dart';
import 'package:siclosbank/app/modules/professional/data/models/vacation_response.dart';
import 'package:siclosbank/app/modules/professional/domain/repository/i_professional_repository.dart';
import 'package:siclosbank/app/modules/professional/domain/usecase/professional_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../data/repository/repository_professional_mock.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late IProfessionalRepository repo;
  late IProfessionalUsecase useCase;

  setUp(() {
    repo = RepositoryProfessionalMock();
    useCase = ProfessionalUsecase(repo);
  });

  group('getHourBase:', () {
    test('should return with success a HourBase model', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenAnswer((_) => Future.value(HourBaseResponse.fromMap({})));

      var result = await useCase.getHourBase(relatedCode: '', plate: '');
      expect(result, isA<HourBaseResponse>());
    });

    test('should throw an error and return Error Response', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenThrow((_) => Future.error(ErrorResponse()));

      expect(() async => await useCase.getHourBase(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });

  group('getTimeSheet:', () {
    test('should return with success a List<TimeSheetResponse> model',
        () async {
      when(() => repo.getTimeSheet(
              relatedCode: any(named: 'idEmpresa'),
              plate: any(named: 'plate'),
              initDate: any(named: 'dateInitial'),
              finallyDate: any(named: 'dateFinally')))
          .thenAnswer((_) => Future.value(<TimeSheetResponse>[]));

      var result = await useCase.getTimeSheet(relatedCode: '', plate: '');
      expect(result, isA<List<TimeSheetResponse>>());
    });

    test('should throw an error and return Error Response', () async {
      when(() => repo.getTimeSheet(
              relatedCode: any(named: 'idEmpresa'),
              plate: any(named: 'plate'),
              initDate: any(named: 'dateInitial'),
              finallyDate: any(named: 'dateFinally')))
          .thenThrow((_) => Future.error(ErrorResponse()));

      expect(() async => await useCase.getTimeSheet(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });
  group('getVacation:', () {
    test('should return with success a List<VacationResponse>', () async {
      when(() => repo.getVacation(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenAnswer((_) => Future.value(<VacationResponse>[]));

      var result = await useCase.getVacationList(relatedCode: '', plate: '');
      expect(result, isA<List<VacationResponse>>());
    });

    test('should throw an error and return Error Response', () async {
      when(() => repo.getVacation(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenThrow((_) => Future.error(ErrorResponse()));

      expect(
          () async => await useCase.getVacationList(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });
  group('getHourBase:', () {
    test('should return with success', () async {
      when(() => repo.downloadIRProfessional(any()))
          .thenAnswer((_) => Future.value());

      await useCase.downloadIR();

      verify(() async => await useCase.downloadIR()).called(1);
    });

    test('should throw an error and return Error Response', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenThrow((_) => Future.error(ErrorResponse()));

      expect(() async => await useCase.getHourBase(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });
  group('getHourBase:', () {
    test('should return with success a HourBase model', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenAnswer((_) => Future.value(HourBaseResponse.fromMap({})));

      var result = await useCase.getHourBase(relatedCode: '', plate: '');
      expect(result, isA<HourBaseResponse>());
    });

    test('should throw an error and return Error Response', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenThrow((_) => Future.error(ErrorResponse()));

      expect(() async => await useCase.getHourBase(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });
  group('getHourBase:', () {
    test('should return with success a HourBase model', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenAnswer((_) => Future.value(HourBaseResponse.fromMap({})));

      var result = await useCase.getHourBase(relatedCode: '', plate: '');
      expect(result, isA<HourBaseResponse>());
    });

    test('should throw an error and return Error Response', () async {
      when(() => repo.getHourBase(
              relatedCode: any(named: 'idEmpresa'), plate: any(named: 'plate')))
          .thenThrow((_) => Future.error(ErrorResponse()));

      expect(() async => await useCase.getHourBase(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });
}

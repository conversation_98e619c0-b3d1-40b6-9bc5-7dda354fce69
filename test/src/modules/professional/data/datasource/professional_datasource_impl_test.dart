import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/professional/data/datasource/professional_datasouce_impl.dart';
import 'package:siclosbank/app/modules/professional/data/datasource/professional_datasource.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';
import 'package:siclosbank/app/shared/errors/client_exception.dart';

import '../../../../shared/data/client/dio_client_mock.dart';

void main() {
  late IClient client;
  late IProfessionalDatasource datasource;

  var formData = DataTypes.formData;

  setUp(() async {
    client = DioClientMock();
    datasource = ProfessionalDatasourceImpl(client);

    registerFallbackValue(formData);
  });

  successMockFetch() {
    when(
      () => client.fetch(
        method: any(named: 'method'),
        path: any(named: 'path'),
      ),
    ).thenAnswer((_) async => (ApiResponse(statusCode: 200)));
  }

  errorMockFetch() {
    when(
      () => client.fetch(
        method: any(named: 'method'),
        path: any(named: 'path'),
      ),
    ).thenThrow(const ClientException(data: 'Bad request', statusCode: 400));
  }

  successMockFetchList() {
    when(
      () => client.fetch<List<dynamic>>(
        method: any(named: 'method'),
        path: any(named: 'path'),
        data: any(named: 'data'),
        dataType: any(named: 'dataType'),
      ),
    ).thenAnswer(
      (invocation) async =>
          (ApiResponse<List<dynamic>>(data: <dynamic>[], statusCode: 200)),
    );
  }

  errorMockFetchList() {
    when(
      () => client.fetch<List<dynamic>>(
        method: any(named: 'method'),
        path: any(named: 'path'),
        data: any(named: 'data'),
        dataType: any(named: 'dataType'),
      ),
    ).thenThrow(const ClientException(data: 'Bad request', statusCode: 400));
  }

  group('Get Hour Base: ', () {
    test('should call the api correctly', () async {
      successMockFetch();
      await datasource.getHourBase('idEmpresa', 'plate');
      verify(
        () => client.fetch(
          method: any(named: 'method'),
          path: any(named: 'path'),
        ),
      ).called(1);
    });

    test('must call api and return ApiResponse', () async {
      successMockFetch();
      var result = await datasource.getHourBase('idEmpresa', 'plate');
      expect(result, isA<ApiResponse>());
    });

    test(
      'when there is an ERROR when calling the api, it should return a ErrorResponse',
      () async {
        errorMockFetch();
        expect(
          () async => await datasource.getHourBase('idEmpresa', 'plate'),
          throwsA(isInstanceOf<ClientException>()),
        );
      },
    );
  });

  group('Get Timesheet: ', () {
    Future<ApiResponse> callGetTimeSheetFunction() => datasource.getTimeSheet(
      relatedCode: 'idEmpresa',
      plate: 'plate',
      finallyDate: '',
      initDate: '',
    );
    test('should call the api correctly', () async {
      successMockFetch();
      await callGetTimeSheetFunction();

      verify(
        () => client.fetch(
          method: any(named: 'method'),
          path: any(named: 'path'),
        ),
      ).called(1);
    });

    test('must call api and return ApiResponse', () async {
      successMockFetch();
      var result = await callGetTimeSheetFunction();
      expect(result, isA<ApiResponse>());
    });

    test(
      'when there is an ERROR when calling the api, it should return a ClientResponse',
      () async {
        errorMockFetch();
        expect(
          () async => await callGetTimeSheetFunction(),
          throwsA(isInstanceOf<ClientException>()),
        );
      },
    );
  });

  group('Get Vacation:', () {
    Future<ApiResponse> callGetVacationFunction() =>
        datasource.getVacation('', '');
    test('should call the api correctly', () async {
      successMockFetch();
      await callGetVacationFunction();

      verify(
        () => client.fetch(
          method: any(named: 'method'),
          path: any(named: 'path'),
        ),
      ).called(1);
    });

    test('must call api and return ApiResponse', () async {
      successMockFetch();
      var result = await callGetVacationFunction();
      expect(result, isA<ApiResponse>());
    });

    test(
      'when there is an ERROR when calling the api, it should return a ClientResponse',
      () async {
        errorMockFetch();
        expect(
          () async => await callGetVacationFunction(),
          throwsA(isInstanceOf<ClientException>()),
        );
      },
    );
  });

  group('Download IR: ', () {
    test('should call the api correctly', () async {
      successMockFetchList();
      await datasource.downloadIRProfessional('cpf');
      verify(
        () => client.fetch<List<dynamic>>(
          method: any(named: 'method'),
          path: any(named: 'path'),
        ),
      ).called(1);
    });

    test('must call api and cast the return to List<int> object', () async {
      successMockFetchList();
      var result = await datasource.downloadIRProfessional('cpf');
      expect(result, isA<List<int>>());
    });

    test(
      'when there is an ERROR when calling the api, it should return a ErrorResponse object',
      () async {
        errorMockFetchList();
        expect(
          () async => await datasource.downloadIRProfessional('cpf'),
          throwsA(isInstanceOf<ClientException>()),
        );
      },
    );
  });

  group('Download vacation receipt: ', () {
    test('should call the api correctly', () async {
      successMockFetchList();
      await datasource.downloadVacationReceipt(
        cpf: 'cpf',
        relatedCode: 'relatedCode',
        plate: 'plate',
        datePerAquis: 'dataPerAquis',
        datePayment: 'dataPagamento',
      );
      verify(
        () => client.fetch<List<dynamic>>(
          method: any(named: 'method'),
          path: any(named: 'path'),
        ),
      ).called(1);
    });

    test('must call api and cast the return to List<int> object', () async {
      successMockFetchList();
      var result = await datasource.downloadVacationReceipt(
        cpf: 'cpf',
        relatedCode: 'relatedCode',
        plate: 'plate',
        datePerAquis: 'dataPerAquis',
        datePayment: 'dataPagamento',
      );
      expect(result, isA<List<int>>());
    });

    test(
      'when there is an ERROR when calling the api, it should return a ErrorResponse object',
      () async {
        errorMockFetchList();
        expect(
          () async => await datasource.downloadVacationReceipt(
            cpf: 'cpf',
            relatedCode: 'relatedCode',
            plate: 'plate',
            datePerAquis: 'dataPerAquis',
            datePayment: 'dataPagamento',
          ),
          throwsA(isInstanceOf<ClientException>()),
        );
      },
    );
  });

  group('Download payslip: ', () {
    Future callFunction() =>
        datasource.downloadPayslip(cpf: 'cpf', mes: '', ano: '');
    test('should call the api correctly', () async {
      registerFallbackValue(formData);
      successMockFetchList();

      await callFunction();

      verify(
        () => client.fetch<List<dynamic>>(
          method: any(named: 'method'),
          path: any(named: 'path'),
          data: any(named: 'data'),
          dataType: any(named: 'dataType'),
        ),
      ).called(1);
    });

    test('must call api and cast the return to List<int> object', () async {
      successMockFetchList();
      var result = await callFunction();
      expect(result, isA<List<int>>());
    });

    test(
      'when there is an ERROR when calling the api, it should return a ErrorResponse object',
      () async {
        errorMockFetchList();
        expect(
          () async => await callFunction(),
          throwsA(isInstanceOf<ClientException>()),
        );
      },
    );
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:siclosbank/app/modules/professional/data/datasource/professional_datasource.dart';
import 'package:siclosbank/app/modules/professional/data/models/hour_base_response.dart';
import 'package:siclosbank/app/modules/professional/data/models/time_sheet_response.dart';
import 'package:siclosbank/app/modules/professional/data/models/vacation_response.dart';
import 'package:siclosbank/app/modules/professional/data/repository/professional_repository_impl.dart';
import 'package:siclosbank/app/modules/professional/domain/repository/i_professional_repository.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../datasource/professional_datacource_mock.dart';

void main() {
  late IProfessionalDatasource datasource;
  late IProfessionalRepository repository;

  setUp(() async {
    datasource = ProfessionalDatasourceMock();
    repository = ProfessionalRepositoryImpl(datasource);
  });

  group('getHourBase:', () {
    test('should return with success a HourBase model', () async {
      when(
        () => datasource.getHourBase(any(), any()),
      ).thenAnswer((_) => Future.value(ApiResponse(data: [
            {'teste': 'teste'}
          ])));
      var result = await repository.getHourBase(relatedCode: '', plate: '');

      expect(result, isA<HourBaseResponse>());
    });

    test('should throw an error and return Error Response', () async {
      when(
        () => datasource.getHourBase(any(), any()),
      ).thenThrow((_) => Future.error(ErrorResponse()));

      expect(
          () async => await repository.getHourBase(relatedCode: '', plate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });

  group('getTimeSheet:', () {
    test('should return with success a List of TimeSheet Response model',
        () async {
      when(
        () => datasource.getTimeSheet(
            relatedCode: any(named: 'idEmpresa'),
            plate: any(named: 'plate'),
            finallyDate: any(named: 'dateFinally'),
            initDate: any(named: 'dateInitial')),
      ).thenAnswer((_) => Future.value(ApiResponse(data: [])));

      var result = await repository.getTimeSheet(
          relatedCode: '', plate: '', finallyDate: '', initDate: '');
      expect(result, isA<List<TimeSheetResponse>>());
    });

    test('should throw an error and return Error Response', () async {
      when(
        () => datasource.getTimeSheet(
            relatedCode: any(named: 'idEmpresa'),
            plate: any(named: 'plate'),
            finallyDate: any(named: 'dateFinally'),
            initDate: any(named: 'dateInitial')),
      ).thenThrow((_) => Future.error(ErrorResponse()));
      expect(
          () async => await repository.getTimeSheet(
              relatedCode: '', plate: '', finallyDate: '', initDate: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });

  group('getVacation:', () {
    test('should return with success a List<VacationResponse>', () async {
      when(
        () => datasource.getVacation(any(), any()),
      ).thenAnswer((_) => Future.value(ApiResponse(data: [])));

      var result = await repository.getVacation(relatedCode: '', plate: '');
      expect(result, isA<List<VacationResponse>>());
    });

    test('should throw an error and return Error Response', () async {
      when(
        () => datasource.getVacation(any(), any()),
      ).thenThrow((_) => Future.error(ErrorResponse()));
      expect(
          () async => await repository.getVacation(
                relatedCode: '',
                plate: '',
              ),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });

  group('downloadIRProfessional:', () {
    test('should return with success a List<VacationResponse>', () async {
      when(
        () => datasource.downloadIRProfessional(any()),
      ).thenAnswer((_) => Future.value(ApiResponse()));

      var result = await repository.downloadIRProfessional('');
      expect(result, isA<List<int>>());
    });

    test('should throw an error and return Error Response', () async {
      when(
        () => datasource.downloadIRProfessional(any()),
      ).thenThrow((_) => Future.error(ErrorResponse()));
      expect(() async => await repository.downloadIRProfessional(''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });

  group('downloadPayslip:', () {
    test('should return with success a List<VacationResponse>', () async {
      when(
        () => datasource.downloadPayslip(
            cpf: any(named: 'cpf'),
            ano: any(named: 'ano'),
            mes: any(named: 'mes')),
      ).thenAnswer((_) => Future.value(ApiResponse()));

      var result =
          await repository.downloadPayslip(cpf: '', month: '', year: '');
      expect(result, isA<List<int>>());
    });

    test('should throw an error and return Error Response', () async {
      when(
        () => datasource.downloadPayslip(
            cpf: any(named: 'cpf'),
            ano: any(named: 'ano'),
            mes: any(named: 'mes')),
      ).thenThrow((_) => Future.error(ErrorResponse()));
      expect(
          () async =>
              await repository.downloadPayslip(cpf: '', month: '', year: ''),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });

  group('downloadVacationReceipt:', () {
    test('should return with success a List<VacationResponse>', () async {
      when(
        () => datasource.downloadVacationReceipt(
          cpf: any(named: 'cpf'),
          relatedCode: any(named: 'relatedCode'),
          plate: any(named: 'plate'),
          datePayment: any(named: 'datePayment'),
          datePerAquis: any(named: 'datePerAquis'),
        ),
      ).thenAnswer((_) => Future.value(ApiResponse()));

      var result = await repository.downloadVacationReceipt(
          cpf: '',
          relatedCode: '',
          plate: '',
          datePayment: '',
          datePerAquis: '',
          installment: 0);
      expect(result, isA<List<int>>());
    });

    test('should throw an error and return Error Response', () async {
      when(
        () => datasource.downloadVacationReceipt(
          cpf: any(named: 'cpf'),
          relatedCode: any(named: 'relatedCode'),
          plate: any(named: 'plate'),
          datePayment: any(named: 'datePayment'),
          datePerAquis: any(named: 'datePerAquis'),
        ),
      ).thenThrow((_) => Future.error(ErrorResponse()));
      expect(
          () async => await repository.downloadVacationReceipt(
              cpf: '',
              relatedCode: '',
              plate: '',
              datePayment: '',
              datePerAquis: '',
              installment: 0),
          throwsA(isInstanceOf<ErrorResponse>()));
    });
  });
}

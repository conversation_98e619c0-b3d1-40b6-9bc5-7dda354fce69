import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/auth/auth_module.dart';
import 'package:siclosbank/app/modules/help/help_module.dart';
import 'package:siclosbank/app/modules/home/<USER>';
import 'package:siclosbank/app/modules/investment/investment_module.dart';
import 'package:siclosbank/app/modules/layout/layout_module.dart';
import 'package:siclosbank/app/modules/loan/loan_module.dart';
import 'package:siclosbank/app/modules/notifications/notifications_module.dart';
import 'package:siclosbank/app/modules/pin/pin_module.dart';
import 'package:siclosbank/app/modules/pix/pix_module.dart';
import 'package:siclosbank/app/modules/professional/professional_module.dart';
import 'package:siclosbank/app/modules/profile/profile_module.dart';
import 'package:siclosbank/app/modules/sign_up/sign_up_module.dart';
import 'package:siclosbank/app/modules/statement/statement_module.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';
import 'package:siclosbank/app/shared/data/client/dio/dio_client.dart';
import 'package:siclosbank/app/shared/data/database/app_database.dart';
import 'package:siclosbank/app/shared/data/database/app_database_implments.dart';
import 'package:siclosbank/app/shared/data/database/storage_adapter.dart';
import 'package:siclosbank/app/shared/data/datasource/app_datasource.dart';
import 'package:siclosbank/app/shared/data/datasource/app_datasource_impl.dart';
import 'package:siclosbank/app/shared/data/repository/app_repository_impl.dart';
import 'package:siclosbank/app/shared/domain/repository/app_repository.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/presenter/bloc/balance/balance_bloc.dart';
import 'package:siclosbank/app/shared/presenter/bloc/banks/banks_bloc.dart';
import 'package:siclosbank/app/shared/presenter/bloc/code_sent/code_sent_bloc.dart';
import 'package:siclosbank/app/shared/presenter/bloc/fast_credit/fast_credit_bloc.dart';
import 'package:siclosbank/app/shared/presenter/bloc/terms/terms_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/terms/terms_view.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/intro/initial_view.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/intro/intro_view.dart';

import 'modules/deposit/presenter/deposit_module.dart';
import 'modules/transaction/transaction_module.dart';
import 'shared/data/database/shared_preference_store.dart';
import 'shared/domain/usecase/bank_usecase.dart';
import 'shared/navigation/named_routes.dart';

class AppModule extends Module {
  @override
  void exportedBinds(Injector i) {
    i.addSingleton<IStorageAdapter>(SharedPreferenceStore.new);
    i.addSingleton<IClient>(DioClient.new);

    i.addSingleton<IAppDatasource>(AppDatasourceImpl.new);
    i.addSingleton<IAppDatabase>(AppDatabase.new);
    i.addSingleton<IAppRepository>(AppRepository.new);
    i.addSingleton<IAppUseCase>(AppUsecase.new);
    i.addLazySingleton<IBankUsecase>(BankUsecase.new);

    i.add(FastCreditBloc.new);
    i.add(BalanceBloc.new);
    i.add(TermsBloc.new);
    i.add(BanksBloc.new);
    i.add(CodeSentBloc.new);
  }

  @override
  List<Module> get imports => [];

  @override
  void binds(i) {}

  @override
  void routes(r) {
    TransitionType? transitionType = r.args.data?['transitionType'];

    r.child(Routes.codeSendEmail,
        child: (_) => const CodeSentPage(codeSentType: CodeSentType.EMAIL));
    r.child(Routes.codeSendSms,
        child: (_) => const CodeSentPage(codeSentType: CodeSentType.SMS));

    r.child(Routes.initial, child: (_) => const InitialView());
    r.child(Routes.intro, child: (_) => const IntroView());
    r.child('/terms', child: (_) {
      return TermsProvider(
        typerTerms: r.args.data['args'],
      );
    });

    r.module(Routes.login, module: AuthModule(), transition: transitionType);
    r.module(Routes.signUp, module: SignUpModule(), transition: transitionType);
    r.module(Routes.professional,
        module: ProfessionalModule(), transition: transitionType);
    r.module(Routes.signUp, module: SignUpModule(), transition: transitionType);
    r.module(Routes.help, module: HelpModule(), transition: transitionType);
    r.module(Routes.layout, module: LayoutModule(), transition: transitionType);
    r.module(Routes.pin, module: PinModule(), transition: transitionType);
    r.module(Routes.loan, module: LoanModule(), transition: transitionType);
    r.module(Routes.home, module: HomeModule(), transition: transitionType);
    r.module(Routes.investments,
        module: InvestmentModule(), transition: transitionType);
    r.module(Routes.profile,
        module: ProfileModule(), transition: transitionType);
    r.module(Routes.notifications,
        module: NotificationsModule(), transition: transitionType);
    r.module(Routes.statement,
        module: StatementModule(), transition: transitionType);
    r.module(Routes.pix, module: PixModule(), transition: transitionType);

    r.module(Routes.transaction,
        module: TransactionModule(), transition: transitionType);
    r.module(Routes.deposit,
        module: DepositModule(), transition: transitionType);
  }
}

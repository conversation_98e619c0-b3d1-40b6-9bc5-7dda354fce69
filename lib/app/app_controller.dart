// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';

import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/data/models/collaborator_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/margin_limit_credit_response.dart';
import 'package:siclosbank/app/shared/data/models/wallet/bank_model.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';

import 'shared/constants/remote_config_tags.dart';
import 'shared/data/models/app/version_app.dart';
import 'shared/data/models/loan/simulation_response.dart';
import 'shared/data/models/token_response.dart';
import 'shared/data/models/user_response.dart';
import 'shared/data/models/wallet/bank_account_user_response.dart';
import 'shared/data/models/wallet/political_credit_response.dart';
import 'shared/constants/constants.dart';

class AppSession {
  static AppSession? _instance;

  TokenResponse? _token;
  String? _tokenNotLogin;
  String? _tokenFirebaseMessaging;
  bool? dismissModalUpdateVersion;
  bool? _userPF;
  AuthorizeDeviceResponse? _authorizeDeviceResponse;
  String? _uniqueDeviceID;
  String? _modeloDevice;
  String? _versaoDevice;
  String? _versaoApp;
  String? _numBuildApp;
  bool? _bottomIOS = false;
  final _versionAppController = StreamController<VersionApp>();
  User? _user;
  CollaboratorResponse? _collaborator;
  double? _balance;
  PoliticalCreditResponse? _politicalFastCredit;
  PoliticalCreditResponse? _conventionalCreditPolitical;
  SimulationResponse? _simulationFastCredit;
  MarginLimitCreditResponse? marginLimitResponse;
  ValueNotifier<BankAccountResponse?> bankAccountNotifier = ValueNotifier(null);
  ValueNotifier<List<ClaimRequestedModel>> listClaimsPixState = ValueNotifier(
    [],
  );

  int? _timeSession;
  bool? elegibleFastCredit;
  bool? elegibleConvencionalCredit;

  String? _lastLogin;
  String? _lastSenha;
  final bool _useFaceID = false;

  User? get user => _user;

  set user(User? user) {
    _user = user;
    StorageUtils.saveUser(user: user);
  }

  CollaboratorResponse? get collaborator => _collaborator;
  set collaborator(CollaboratorResponse? collaborator) {
    _collaborator = collaborator;
    StorageUtils.saveColaborator(collaborator: collaborator);
  }

  double get balance => _balance ?? 0.0;
  set balance(double? balance) {
    _balance = balance;
  }

  PoliticalCreditResponse? get politicalFastCredit => _politicalFastCredit;

  set politicalFastCredit(PoliticalCreditResponse? politicalFastCredit) {
    _politicalFastCredit = politicalFastCredit;
  }

  SimulationResponse? get simulationFastCredit => _simulationFastCredit;
  set saveSimulationFastCredit(SimulationResponse simulationFastCredit) {
    _simulationFastCredit = simulationFastCredit;
  }

  PoliticalCreditResponse? get conventionalCreditPolitical =>
      _conventionalCreditPolitical;

  set conventionalCreditPolitical(
    PoliticalCreditResponse? conventionalCreditPolitical,
  ) {
    log(
      conventionalCreditPolitical?.toJson() ?? '',
      name: 'conventionalCreditPolitical',
    );
    _conventionalCreditPolitical = conventionalCreditPolitical!;
  }

  BankAccountResponse? get bankAccount => bankAccountNotifier.value;
  set bankAccount(BankAccountResponse? bankAccount) {
    bankAccountNotifier.value = bankAccount;
    StorageUtils.saveBankAccountUser(accountBank: bankAccount!);
  }

  List<ClaimRequestedModel> get listClaimsPix => listClaimsPixState.value;
  set listClaimsPix(List<ClaimRequestedModel> listClaims) {
    listClaimsPixState.value = listClaims;
  }

  String? get tokenFirebaseMessaging => _tokenFirebaseMessaging;

  set tokenFirebaseMessaging(String? token) {
    _tokenFirebaseMessaging = token;
    StorageUtils.saveTokenFirebaseMessaging(token: token!);
    print('[FCM TOKEN] $token');
  }

  String? _whatsAppChat;
  String? get numberWhatsApp => _whatsAppChat;
  String? _numberPhone;
  String? get numberPhone => _numberPhone;

  String? _emailHomeSupport;
  String? get emailHomeSupport => _emailHomeSupport;

  String? _emailCcSuporte;

  List<BanksModel>? _banks;
  Future<List<BanksModel>> get getBanks async {
    if (_banks == null) {
      _banks = await StorageUtils.getBanksList();
      _banks ??= [];
    }
    return Future.value(_banks!);
  }

  setBanks(List<BanksModel> banks) async {
    _banks = banks;
    await StorageUtils.setBanksList(banks);
  }

  BanksModel? getBankByISBPCode(String ispb) {
    return _banks?.firstWhere((element) => element.ispb == ispb);
  }

  String? get emailCcSuporte => _emailCcSuporte;

  String get userAgent => "$_modeloDevice - $_versaoDevice";

  String get versaoApp => "v. $_versaoApp";

  String get numBuildApp => "($_numBuildApp)";

  String get versaoAppHeader => "$_versaoApp";

  int get timeSession => _timeSession ?? 60;
  set timeSession(int timeSession) {
    _timeSession = timeSession;
  }

  String? get lastLogin => _lastLogin;
  String? get lastSenha => _lastSenha;
  set lastLogin(String? login) => _lastLogin = login;
  set lastSenha(String? senha) => _lastSenha = senha;

  bool get useFaceID => _useFaceID;

  AppSession() {
    _loadInfo();
    if (!kIsWeb) _initRemoteConfig();
  }

  static AppSession getInstance() {
    _instance ??= AppSession();
    return _instance!;
  }

  static clear() {
    StorageUtils.saveInitQuestionnaire(show: true);
    _instance = null;
  }

  Stream<VersionApp> get versionAppStream => _versionAppController.stream;

  bool? get userPF => _userPF;
  set userPF(bool? isPF) => _userPF = isPF!;

  setAuthToken(TokenResponse token) {
    print("[AUTH TOKEN] ${token.token}");
    _token = token;
    StorageUtils.saveTokenResponse(token: token);
  }

  setNotLoggedToken(String token) {
    _tokenNotLogin = token;
  }

  AuthorizeDeviceResponse? get authorizeDeviceResponse =>
      _authorizeDeviceResponse;
  setAuthorizeDeviceResponse(AuthorizeDeviceResponse authorizeDeviceResponse) {
    _authorizeDeviceResponse = authorizeDeviceResponse;
  }

  String? get uniqueDeviceID => _uniqueDeviceID;
  bool? get bottomIOS => _bottomIOS;

  String getAuth({bool isLogged = true}) {
    final token = isLogged ? _token?.token : _tokenNotLogin;

    if (token != null) {
      return "Bearer $token";
    } else {
      return "";
    }
  }

  _loadInfo() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      _versaoApp = packageInfo.version;
      _numBuildApp = packageInfo.buildNumber;

      _uniqueDeviceID = await FlutterUdid.consistentUdid;
    } on Exception {
      _versaoApp = "0.0.0";
      _uniqueDeviceID = "Unknown";
      _numBuildApp = "0";
    }

    if (kIsWeb) {
      _bottomIOS = false;
      _versaoDevice = 'WebApp';
      _modeloDevice = 'Navegador';
      return;
    }

    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      _modeloDevice = await StorageUtils.getModelDevice();
      _versaoDevice = await StorageUtils.getVersionDevice();
      if (_modeloDevice == null) {
        if (Platform.isAndroid) {
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
          _modeloDevice = androidInfo.model;
        } else {
          IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
          _modeloDevice = iosInfo.modelName;
        }
      } else if (_modeloDevice != null) {
        StorageUtils.setModeloDevice(_modeloDevice!);
      }

      if (_versaoDevice == null) {
        if (Platform.isAndroid) {
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
          _versaoDevice = "Android ${androidInfo.version.release}";
        } else {
          IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
          _versaoDevice = "iOS ${iosInfo.systemVersion}";
          _bottomIOS = ConstantDevice.IOS_MACHINES_WITH_BOTTOM_NOTCH.contains(
            iosInfo.utsname.machine,
          );
        }

        if (_versaoDevice != null) {
          StorageUtils.setVersaoDevice(_versaoDevice!);
        }
      }
    } catch (error) {
      _bottomIOS = false;
    }
  }

  _initRemoteConfig() async {
    final remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: kDebugMode
            ? const Duration(minutes: 3)
            : const Duration(hours: 6),
        minimumFetchInterval: const Duration(hours: 1),
      ),
    );

    try {
      await remoteConfig.fetchAndActivate();
      _whatsAppChat = remoteConfig.getString(RemoteConfigTags.WHATSAPP_NUMBER);
    } catch (e) {
      // log(e.toString(), name: 'RemoteConfig WhatsApp');
      _whatsAppChat = null;
    }

    try {
      _numberPhone = remoteConfig.getString(RemoteConfigTags.PHONE_SUPORTE);
    } catch (e) {
      // log(e.toString(), name: 'RemoteConfig Phone Support');
      _numberPhone = null;
    }

    try {
      _emailHomeSupport = remoteConfig.getString(
        RemoteConfigTags.EMAIL_PRINCIPAL_SUPORTE,
      );
    } catch (e) {
      // log(e.toString(), name: 'RemoteConfig Email Home Support');
      _emailHomeSupport = null;
    }

    try {
      _emailCcSuporte = remoteConfig.getString(
        RemoteConfigTags.EMAIL_CC_SUPORTE,
      );
    } catch (e) {
      // log(e.toString(), name: 'RemoteConfig Email CC Support');
      _emailCcSuporte = null;
    }
  }
}

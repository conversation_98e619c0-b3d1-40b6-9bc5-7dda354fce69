import 'dart:io';

import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/collaborator_response.dart';
import 'package:siclosbank/app/shared/data/models/terms/termo_response.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/presenter/bloc/terms/enums/type_terms.dart';

import '../../constants/credit_type_enum.dart';
import '../../data/models/loan/simulation_response.dart';
import '../../data/models/wallet/balance_response.dart';
import '../../data/models/wallet/bank_model.dart';
import '../../data/models/wallet/political_credit_response.dart';

abstract class IAppRepository {
  Future<TermResponse> getTerm(TyperTerms type);
  Future<CollaboratorResponse> getCollaborator(String cpf);

  Future<BalanceResponse> getUserBalance(String cpf);

  Future<PoliticalCreditResponse> getPoliticalCredit(CreditType creditType);
  Future<bool> getElegibleFastCreditUser(String cpf);

  Future<SimulationResponse> simulateFastCreditUser({required String userId});
  Future<User> getUserByCpf(String cpf);
  Future<int> getTimeSession();

  Future<List<BanksModel>> getBankCodeList();
  Future<bool> checkCodeDeleteDevice(String deviceId, String deleteCode);
  Future<bool> getEnableIdDigitalLogin();
  Future<bool> setEnableIdDigitalLogin(bool newValue);

  Future<bool> cleanLoginDigital();
  Future<bool> checkCodeRegisterDevice(
      {required String userId, required String code, required String tokenFCM});

  Future<bool> sendCheckDeviceCode({
    required String userId,
    required bool isEmail,
  });

  Future<File> getImageProfile();

  Future<AddressResponse> getAddress(String userId);
}

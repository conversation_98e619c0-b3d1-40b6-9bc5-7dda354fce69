import 'package:siclosbank/app/shared/data/models/wallet/bank_model.dart';

import 'package:siclosbank/app/shared/domain/repository/app_repository.dart';

abstract class IBankUsecase {
  Future<List<BanksModel>> getbankList();
}

class BankUsecase implements IBankUsecase {
  final IAppRepository repository;
  BankUsecase({required this.repository});
  @override
  Future<List<BanksModel>> getbankList() async {
    return repository.getBankCodeList();
  }
}

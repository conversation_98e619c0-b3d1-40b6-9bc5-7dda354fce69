import 'dart:io';

import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/collaborator_response.dart';
import 'package:siclosbank/app/shared/data/models/terms/termo_response.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/domain/repository/app_repository.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';
import 'package:siclosbank/app/shared/presenter/bloc/terms/enums/type_terms.dart';

import '../../constants/credit_type_enum.dart';
import '../../data/models/loan/loan_status_response.dart';
import '../../data/models/loan/simulation_response.dart';
import '../../data/models/wallet/balance_response.dart';
import '../../data/models/wallet/political_credit_response.dart';

abstract class IAppUseCase {
  Future<TermResponse> getTermsApp(TyperTerms type);
  Future<CollaboratorResponse> getCollaborator(String cpf);
  Future<BalanceResponse> getUserbalance();
  Future<PoliticalCreditResponse> getPoliticalCredit(CreditType creditType);
  Future<bool> getElegibleFastCreditUser();
  Future<SimulationResponse> simulationFastCredit();
  Future<User> getUserByCpf();
  Future<int> getTimeSession();
  Future<bool> checkCodeDeleteDevice(String deviceId, String deleteCode);
  Future<bool> getEnableIdDigitalLogin();
  Future<bool> setEnableIdDigitalLogin(bool newvalue);
  Future<bool> cleanEnableIdDigitalLogin();

  Future<bool> checkCodeRegisterDevice({
    required String userId,
    required String code,
  });

  Future<bool> sendCheckDeviceCode({
    required String userId,
    required bool isEmail,
  });

  Future<File> getImageProfile();

  Future<AddressResponse> getAddress(String userId);
}

class AppUsecase implements IAppUseCase {
  final IAppRepository _repository;

  AppUsecase(this._repository);

  @override
  Future<TermResponse> getTermsApp(TyperTerms type) async {
    try {
      var result = await _repository.getTerm(type);

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CollaboratorResponse> getCollaborator(String cpf) async {
    try {
      var result = await _repository.getCollaborator(cpf);
      AppSession.getInstance().collaborator = result;
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BalanceResponse> getUserbalance() async {
    try {
      final cpf = AppSession.getInstance().user?.cpf ?? "";
      var result = await _repository.getUserBalance(cpf);
      AppSession.getInstance().balance = result.balance;
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<PoliticalCreditResponse> getPoliticalCredit(
      CreditType creditType) async {
    try {
      final result = await _repository.getPoliticalCredit(creditType);
      if (creditType == CreditType.fast) {
        AppSession.getInstance().politicalFastCredit = result;
      } else {
        AppSession.getInstance().conventionalCreditPolitical = result;
      }
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> getElegibleFastCreditUser() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final cpf = user!.cpf!;

    try {
      final result = await _repository.getElegibleFastCreditUser(cpf);

      AppSession.getInstance().elegibleFastCredit = result;
      // final loan = AppSession.getInstance().user!.loanStatusResponse;
      // if (loan == null) {
      //   AppSession.getInstance().user!.loanStatusResponse =
      //       LoanStatusResponse(elegibleFastCredit: result);
      // } else {
      //   AppSession.getInstance().user!.loanStatusResponse =
      //       loan.copyWith(elegibleFastCredit: result);
      // }

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<SimulationResponse> simulationFastCredit() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final userId = user!.id!;

    try {
      final result = await _repository.simulateFastCreditUser(
        userId: userId,
      );
      AppSession.getInstance().saveSimulationFastCredit = result;
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<User> getUserByCpf() async {
    final cpf = AppSession.getInstance().user?.cpf ?? "";
    try {
      final result = await _repository.getUserByCpf(cpf);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<int> getTimeSession() async {
    try {
      final result = await _repository.getTimeSession();

      AppSession.getInstance().timeSession = result;

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> checkCodeDeleteDevice(String deviceId, String deleteCode) async {
    try {
      var result =
          await _repository.checkCodeDeleteDevice(deviceId, deleteCode);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> checkCodeRegisterDevice(
      {required String userId, required String code}) async {
    final token = AppSession.getInstance().tokenFirebaseMessaging ?? 'none';
    try {
      return await _repository.checkCodeRegisterDevice(
        userId: userId,
        code: code,
        tokenFCM: token,
      );
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> sendCheckDeviceCode({
    required String userId,
    required bool isEmail,
  }) async {
    try {
      return await _repository.sendCheckDeviceCode(
          userId: userId, isEmail: isEmail);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> getEnableIdDigitalLogin() async {
    try {
      final result = await _repository.getEnableIdDigitalLogin();

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> cleanEnableIdDigitalLogin() async {
    try {
      final result = await _repository.cleanLoginDigital();

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> setEnableIdDigitalLogin(bool newValue) async {
    try {
      final result = await _repository.setEnableIdDigitalLogin(newValue);

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<File> getImageProfile() async {
    try {
      final result = await _repository.getImageProfile();
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AddressResponse> getAddress(String userId) async {
    try {
      final result = await _repository.getAddress(userId);
      return result;
    } catch (e) {
      rethrow;
    }
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/app_controller.dart';

import '../../../data/models/wallet/bank_model.dart';
import '../../../domain/usecase/bank_usecase.dart';
import '../../../errors/error_response.dart';

part 'banks_event.dart';
part 'banks_state.dart';

class BanksBloc extends Bloc<BanksEvent, BanksState> {
  final IBankUsecase usecase;
  BanksBloc(this.usecase) : super(BanksInitial()) {
    on<BanksEvent>((event, emit) async {
      if (event is GetBankList) {
        try {
          emit(BanksLoading());
          final list = await AppSession.getInstance().getBanks;
          if (list.isNotEmpty) {
            emit(BanksSuccess(list));
            return;
          } else {
            final result = await usecase.getbankList();
            AppSession.getInstance().setBanks(result);
            emit(BanksSuccess(result));
          }
        } on Exception catch (e) {
          emit(BanksError(e as ErrorResponse));
        }
      }
    });
  }
}

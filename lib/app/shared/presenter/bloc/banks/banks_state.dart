part of 'banks_bloc.dart';

sealed class BanksState extends Equatable {
  const BanksState();

  @override
  List<Object> get props => [];
}

final class BanksInitial extends BanksState {}

final class BanksLoading extends BanksState {}

final class BanksSuccess extends BanksState {
  final List<BanksModel> banks;
  const BanksSuccess(this.banks);

  @override
  List<Object> get props => [banks];
}

final class BanksError extends BanksState {
  final ErrorResponse error;
  BanksError(this.error);

  @override
  List<Object> get props => [error];
}

import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/loan/domain/usecases/loan_usecase.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/errors.dart';

import '../../../config/flavor.dart';
import '../../../constants/constants.dart';
import '../../../constants/credit_type_enum.dart';
import '../../../data/models/loan/simulation_response.dart';

part 'fast_credit_event.dart';
part 'fast_credit_state.dart';

class FastCreditBloc extends Bloc<FastCreditEvent, FastCreditState> {
  final IAppUseCase _appUsecase;

  FastCreditBloc(
    this._appUsecase,
  ) : super(FastCreditInitial()) {
    on<FastCreditEvent>((event, emit) async {
      if (event is InitFetchEvent) {
        if (isHomologOrDev) {
          // Em homologação ou desenvolvimento, não faz a verificação de elegibilidade
          // e da política de crédito rápido
          add(const GetFastCreditPoliticalEvent());
          add(const CheckUserElegibleFastCredit());
        }
      }

      if (event is GetFastCreditPoliticalEvent) {
        try {
          emit(FastCreditLoading());
          final political = AppSession.getInstance().politicalFastCredit;
          if (political == null) {
            final result =
                await _appUsecase.getPoliticalCredit(CreditType.fast);
            emit(GetValueFastCreditSuccess(result.valueCredit));
          } else {
            emit(GetValueFastCreditSuccess(political.valueCredit));
          }
        } on ErrorResponse catch (e) {
          emit(FastCreditError(e));
        }
      }

      if (event is CheckUserElegibleFastCredit) {
        try {
          emit(FastCreditLoading());
          final collaborator = AppSession.getInstance().collaborator;

          if (collaborator != null) {
            var result = await _appUsecase.getElegibleFastCreditUser();
            // verifica se user tem situation code compativel com o empréstimo
            // result = result && collaborator.isAvailableToLoan();
            emit(GetElegibleFastCreditSuccess(result));
          } else {
            emit(const GetElegibleFastCreditSuccess(false));
          }
        } on ErrorResponse catch (e) {
          if (e is NotFound) {
            emit(const GetElegibleFastCreditSuccess(false));
          }
          emit(FastCreditError(e));
        }
      }

      if (event is SimulateFastCreditEvent) {
        try {
          emit(FastCreditLoading());
          final simulation = AppSession.getInstance().simulationFastCredit;
          if (simulation != null) {
            emit(GetSimulationFastCreditSuccess(simulation));
            return;
          } else {
            final result = await _appUsecase.simulationFastCredit();
            emit(GetSimulationFastCreditSuccess(result));
          }
        } on ErrorResponse catch (e) {
          emit(FastCreditError(e));
        }
      }
    });
  }
}

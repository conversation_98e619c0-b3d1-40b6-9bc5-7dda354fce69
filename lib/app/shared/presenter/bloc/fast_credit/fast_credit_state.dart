part of 'fast_credit_bloc.dart';

sealed class FastCreditState extends Equatable {
  const FastCreditState();

  @override
  List<Object> get props => [];
}

final class FastCreditInitial extends FastCreditState {}

final class FastCreditLoading extends FastCreditState {}

final class FastCreditError extends FastCreditState {
  final ErrorResponse error;

  const FastCreditError(this.error);
}

final class GetSimulationFastCreditSuccess extends FastCreditState {
  final SimulationResponse simulationResponse;

  const GetSimulationFastCreditSuccess(this.simulationResponse);
}

final class GetValueFastCreditSuccess extends FastCreditState {
  final double value;

  const GetValueFastCreditSuccess(this.value);
}

final class GetElegibleFastCreditSuccess extends FastCreditState {
  final bool isElegible;
  const GetElegibleFastCreditSuccess(this.isElegible);
}

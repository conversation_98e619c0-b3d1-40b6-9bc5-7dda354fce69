part of 'fast_credit_bloc.dart';

sealed class FastCreditEvent {
  const FastCreditEvent();
}

final class InitFetchEvent implements FastCreditEvent {}

final class GetFastCreditPoliticalEvent implements FastCreditEvent {
  const GetFastCreditPoliticalEvent();
}

final class CheckUserElegibleFastCredit implements FastCreditEvent {
  const CheckUserElegibleFastCredit();
}

final class SimulateFastCreditEvent implements FastCreditEvent {}

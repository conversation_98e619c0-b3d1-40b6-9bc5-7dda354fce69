part of 'code_sent_bloc.dart';

abstract class CodeSentState {}

final class CodeSentInitial extends CodeSentState {
  CodeSentInitial();
}

final class CodeSentLoading extends CodeSentState {
  CodeSentLoading();
}

final class CodeSentResendLoading extends CodeSentState {
  CodeSentResendLoading();
}

final class CodeSentResendSuccess extends CodeSentState {
  CodeSentResendSuccess();
}

final class CodeSentSuccess extends CodeSentState {
  final bool result;
  CodeSentSuccess({required this.result});
}

final class CodeSentError extends CodeSentState {
  final ErrorResponse error;
  CodeSentError(this.error);
}

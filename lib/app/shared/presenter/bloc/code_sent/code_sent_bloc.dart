import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';

part 'code_sent_event.dart';
part 'code_sent_state.dart';

class CodeSentBloc extends Bloc<CodeSentEvent, CodeSentState> {
  final IAppUseCase _usecase;

  CodeSentBloc(this._usecase) : super(CodeSentInitial()) {
    on((event, emit) async {
      if (event is CheckCodeRegistrationDeviceEvent) {
        try {
          emit(CodeSentLoading());

          final authorizeResponse =
              AppSession.getInstance().authorizeDeviceResponse!;

          final result = await _usecase.checkCodeRegisterDevice(
              userId: authorizeResponse.id!, code: event.code);
          if (result) {
            emit(CodeSentSuccess(result: result));
          } else {
            emit(CodeSentError(
                ErrorResponse(statusCode: 400))); // codigo invalido
          }
        } on ErrorResponse catch (error) {
          emit(CodeSentError(error));
        }
      }
      if (event is CheckCodeRemoveDeviceEvent) {
        try {
          emit(CodeSentLoading());

          final result =
              await _usecase.checkCodeDeleteDevice(event.deviceId!, event.code);

          emit(CodeSentSuccess(result: result));
        } on ErrorResponse catch (error) {
          emit(CodeSentError(error));
        }
      }
      if (event is SendNewTokenEvent) {
        final authorizeResponse =
            AppSession.getInstance().authorizeDeviceResponse!;

        try {
          emit(CodeSentResendLoading());

          await _usecase.sendCheckDeviceCode(
              userId: authorizeResponse.id ?? '',
              isEmail: event.type == CodeSentType.EMAIL);

          emit(CodeSentResendSuccess());
        } on ErrorResponse catch (error) {
          emit(CodeSentError(error));
        }
      }
    });
  }
}

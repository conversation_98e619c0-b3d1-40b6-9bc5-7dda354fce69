part of 'code_sent_bloc.dart';

abstract class CodeSentEvent implements Equatable {}

class CheckCodeRegistrationDeviceEvent extends CodeSentEvent {
  final String code;
  final String? deviceId;
  CheckCodeRegistrationDeviceEvent(this.code, this.deviceId);

  @override
  List<dynamic> get props => [code];

  @override
  bool? get stringify => throw UnimplementedError();
}

class CheckCodeRemoveDeviceEvent extends CodeSentEvent {
  final String code;
  final String? deviceId;
  CheckCodeRemoveDeviceEvent(this.code, this.deviceId);

  @override
  List<dynamic> get props => [code];

  @override
  bool? get stringify => throw UnimplementedError();
}

class SendNewTokenEvent extends CodeSentEvent {
  final CodeSentType type;

  SendNewTokenEvent(this.type);
  @override
  List<dynamic> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'terms_bloc.dart';

sealed class TermsEvent extends Equatable {
  const TermsEvent();

  @override
  List<Object> get props => [];
}

class GetTermosEvent implements TermsEvent {
  final TyperTerms type;

  GetTermosEvent({required this.type});
  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

class AcceptTermsEvent implements TermsEvent {
  AcceptTermsEvent();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

class DeniedTermsEvent implements TermsEvent {
  DeniedTermsEvent();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

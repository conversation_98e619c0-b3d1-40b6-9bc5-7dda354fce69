enum TipoEmprestimoEnum {
  CONSIGNADO,
  PESSOAL,
}

String? getTipoEmprestimoString(TipoEmprestimoEnum? tipoEmprestimo) {
  if (tipoEmprestimo == null) {
    return null;
  }
  return tipoEmprestimo == TipoEmprestimoEnum.PESSOAL
      ? "pessoal"
      : "consignado";
}

TipoEmprestimoEnum? getTipoEmprestimoEnum(String? tipoEmprestimo) {
  if (tipoEmprestimo == null || tipoEmprestimo.isEmpty) {
    return null;
  }
  return tipoEmprestimo == "pessoal"
      ? TipoEmprestimoEnum.PESSOAL
      : TipoEmprestimoEnum.CONSIGNADO;
}

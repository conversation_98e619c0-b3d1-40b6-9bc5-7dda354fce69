enum TyperTerms {
  PF('PF'),
  P<PERSON>('PJ'),
  <PERSON>MPRESTIMO('EMPRESTIMO'),
  EMPRESTIMO_PESSOAL('EMPRESTIMO_PESSOAL'),
  POLITICA_PRIVACIDADE('POLITICAS_DE_PRIVACIDADE'),
  INVESTIMENTO_RECEBIVEL('INVESTIMENTO_RECEBIVEL'),
  INVESTIMENTO('INVESTIMENTO'),
  TERMOS_DE_USO('TERMOS_DE_USO');

  // Campo que armazena a string associada a cada valor
  final String label;

  // Construtor para inicializar o campo
  const TyperTerms(this.label);
}

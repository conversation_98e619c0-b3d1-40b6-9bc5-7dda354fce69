import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/shared/data/models/wallet/balance_response.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'balance_event.dart';
part 'balance_state.dart';

class BalanceBloc extends Bloc<BalanceEvent, BalanceState> {
  final IAppUseCase _usecase;
  BalanceBloc(this._usecase) : super(BalanceInitial()) {
    on<BalanceEvent>((event, emit) async {
      if (event is GetBalanceEvent) {
        try {
          emit(BalanceLoading());
          final result = await _usecase.getUserbalance();
          emit(BalanceSuccess(balance: result));
        } on ErrorResponse catch (e) {
          emit(BalanceError(e));
        }
      }
    });
  }
}

part of 'balance_bloc.dart';

sealed class BalanceState {
  const BalanceState();
}

final class BalanceInitial implements BalanceState {}

final class BalanceLoading implements BalanceState {}

final class BalanceSuccess implements BalanceState {
  final BalanceResponse balance;

  BalanceSuccess({required this.balance});
}

final class BalanceError implements BalanceState {
  final ErrorResponse error;

  BalanceError(this.error);
}

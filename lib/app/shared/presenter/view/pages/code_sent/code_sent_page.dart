import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import 'package:siclosbank/app/modules/profile/presenter/bloc/manage_devices/manage_devices_state.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import 'package:siclosbank/app/shared/presenter/bloc/code_sent/code_sent_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_reenviar_codigo.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/edt_codigo_verificacao.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../../../../app_controller.dart';

enum CodeSentType { SMS, EMAIL }

enum CodeSentAction { REMOVE_DEVICE, REGISTER_DEVICE }

class CodeSentPage extends StatelessWidget {
  final CodeSentType codeSentType;
  final String? deviceIdToRemove;
  const CodeSentPage(
      {super.key, required this.codeSentType, this.deviceIdToRemove});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CodeSentBloc>(
      create: (context) => Modular.get<CodeSentBloc>(),
      child:
          _CodeSentView(type: codeSentType, deviceIdToRemove: deviceIdToRemove),
    );
  }
}

class _CodeSentView extends StatefulWidget {
  const _CodeSentView({
    super.key,
    required this.type,
    this.deviceIdToRemove,
  });

  final CodeSentType type;
  final String? deviceIdToRemove;

  @override
  State<_CodeSentView> createState() => __CodeSentView();
}

class __CodeSentView extends State<_CodeSentView> {
  late CodeSentType _type;
  dynamic authorizeDeviceResponse;

  late String deviceId;
  bool enableResendButton = false;

  late CodeSentAction action;
  final CountdownController controller = CountdownController(
    autoStart: true,
  );

  @override
  void initState() {
    super.initState();

    action = CodeSentAction.REGISTER_DEVICE;
    _type = widget.type;

    if (widget.deviceIdToRemove != null) {
      deviceId = widget.deviceIdToRemove!;
    } else if (Modular.args.data?['args'] is ManageDevicesState) {
      final state = Modular.args.data?['args'] as ManageDevicesState;
      deviceId = state.deviceIdToRemove!;
      action = state.action;
    } else if (Modular.args.data?['args'] is String) {
      deviceId = Modular.args.data?['args'] as String;
    } else {
      deviceId = '';
    }

    authorizeDeviceResponse =
        AppSession.getInstance().authorizeDeviceResponse?.id != null
            ? AppSession.getInstance().authorizeDeviceResponse
            : AppSession.getInstance().user;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorsApp.cinza[100],
      appBar: AppBarApp(
        title: _type == CodeSentType.EMAIL
            ? I18n.of(context)!.email_enviado
            : I18n.of(context)!.sms_enviado,
      ),
      body: BlocConsumer<CodeSentBloc, CodeSentState>(
        listener: (context, state) {
          if (state is CodeSentError) {
            if (state.error.statusCode == 400) {
              DialogUtils.showSnackError(
                  context,
                  state.error
                      .copyWith(message: I18n.of(context)!.codigo_invalido));
            } else {
              DialogUtils.showSnackError(context, state.error);
            }
          }

          if (state is CodeSentResendSuccess) {
            SnackBarApp.showSnack(
                context: context,
                message: I18n.of(context)!.codigo_enviado_novamente,
                success: true);
          }

          if (state is CodeSentSuccess) {
            pop(true);
          }
        },
        builder: (context, state) {
          if (state is CodeSentLoading) {
            return _buildProgress();
          } else {
            return _buildBody(state);
          }
        },
      ),
    );
  }

  _buildBody(CodeSentState state) {
    var textTheme = Theme.of(context).textTheme;
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                const SizedBox(height: 24),
                Text(
                  I18n.of(context)!.digiteCodigoRecebido(
                      _type == CodeSentType.EMAIL
                          ? authorizeDeviceResponse?.email ?? '-'
                          : authorizeDeviceResponse?.phone ?? '-'),
                  style: textTheme.bodyMedium,
                ),
                SizedBoxResponsive(height: 70),
                ContainerResponsive(
                  child: EdtCodigoVerificacao(
                    sendCodVerificacao: (code) {
                      BlocProvider.of<CodeSentBloc>(context)
                          .add(action == CodeSentAction.REGISTER_DEVICE
                              ? CheckCodeRegistrationDeviceEvent(
                                  code,
                                  deviceId,
                                )
                              : CheckCodeRemoveDeviceEvent(
                                  code,
                                  deviceId,
                                ));
                    },
                  ),
                ),
                SizedBoxResponsive(height: 50),
                state is CodeSentResendLoading
                    ? Container(
                        alignment: Alignment.center,
                        // width: 20,
                        // height: 20,
                        child: Utils.circularProgressButton(size: 20),
                      )
                    : !enableResendButton
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                const I18n().reenviar_codigo_sms_timer,
                                style: Theme.of(context).textTheme.labelLarge,
                              ),
                              const SizedBox(width: 4),
                              Countdown(
                                controller: controller,
                                seconds: 60,
                                build: (_, double time) {
                                  return Text(
                                    time == 60
                                        ? '1:00'
                                        : '0:${time.round().toString().padLeft(2, '0')}',
                                    style:
                                        Theme.of(context).textTheme.labelLarge,
                                  );
                                },
                                interval: const Duration(seconds: 1),
                                onFinished: () {
                                  setState(() {
                                    enableResendButton = true;
                                  });
                                },
                              ),
                            ],
                          )
                        : ButtonResendCode(
                            onTap: () {
                              BlocProvider.of<CodeSentBloc>(context)
                                  .add(SendNewTokenEvent(_type));
                              setState(() {
                                enableResendButton = false;
                              });
                            },
                          ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _buildProgress() {
    return Center(
      child: Utils.circularProgressButton(),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:shimmer/shimmer.dart';

import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/images_app.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../themes/styles/colors_app.dart';
import '../../components/others/button_app.dart';

class IntroView extends StatefulWidget {
  const IntroView({super.key});

  @override
  State<IntroView> createState() => _IntroViewState();
}

class _IntroViewState extends State<IntroView> {
  ValueNotifier<bool> showError = ValueNotifier(false);
  @override
  void initState() {
    super.initState();
    setStatusBar();

    _verifyUserRegister();
    showError.addListener(() {
      if (showError.value) {
        DialogUtils.showSnackError(
            context, Exception('Verifique conexão com a internet.'));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: Stack(
        children: <Widget>[
          Positioned.fill(
            child: CachedNetworkImage(
              imageUrl: 'https://cdn.siclos.net/tela_inicial.png',
              fit: BoxFit.cover,
              progressIndicatorBuilder: (context, url, progress) {
                return Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(5)),
                    ),
                  ),
                );
              },
              errorWidget: (context, url, error) {
                return ImagesApp.bgIntroView();
              },
              errorListener: (value) {
                if (!showError.value) {
                  showError.value = true;
                }
              },
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              height: size.height * 0.85,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: <Widget>[
                  const SizedBox(height: 16),
                  ButtonApp(
                    width: size.width,
                    text: const I18n().ja_tenho_uma_conta,
                    textColor: ColorsApp.cinza[900]!,
                    onPress: () async {
                      push(Routes.login);
                    },
                  ),
                  const SizedBox(height: 16),
                  ButtonApp(
                    width: size.width,
                    buttonColor: Colors.black26,
                    textColor: Colors.white,
                    border: 1,
                    text: const I18n().abrir_conta,
                    onPress: () async {
                      push(Routes.signUp);
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  void setStatusBar() {
    setState(() {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
    });
  }

  _verifyUserRegister() async {
    await StorageUtils.getUserRegister().then((userId) {
      if (userId != null && userId.isNotEmpty) {
        // pushOnePage(
        //     context,
        //     AnaliseCadastroProvider(
        //       userId: userId,
        //     ));
      }
    });
  }
}

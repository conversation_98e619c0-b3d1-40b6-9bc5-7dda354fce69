// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../../app_controller.dart';
import '../../../../config/flavor.dart';
import '../../../../data/models/app/version_app.dart';
import '../../../../navigation/named_routes.dart';
import '../../../../utils/storage_utils.dart';
import '../../components/others/sheet_alert_confirm_dark.dart';
import 'intro_view.dart';

class InitialView extends StatefulWidget {
  const InitialView({super.key});

  @override
  State<InitialView> createState() => _InitialViewState();
}

class _InitialViewState extends State<InitialView> {
  Widget? firstWidget;
  final _dialogKey = GlobalKey();

  @override
  void initState() {
    checkUser();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<VersionApp>(
        stream: AppSession.getInstance().versionAppStream,
        builder: (context, snapshot) {
          var versionApp = snapshot.data;

          if (versionApp?.canUpdate == true) {
            // show dialog if it is not already displayed
            var dismiss =
                AppSession.getInstance().dismissModalUpdateVersion ?? false;

            if (_dialogKey.currentContext == null && !dismiss) {
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                await SheetAlertConfirmDark.showSheet(
                  context,
                  key: _dialogKey,
                  message:
                      'Existe uma nova versão do app Siclos disponível. Para uma melhor experiência, atualize seu app.',
                  textPositive: 'Atualizar aplicativo',
                  textAlignMessage: TextAlign.center,
                  showBtnNegative: false,
                  clickPositive: () {
                    // BlocProvider.of<HomeBloc>(context)
                    //     .add(CheckLatestVersion());
                  },
                ).then((value) {
                  AppSession.getInstance().dismissModalUpdateVersion = true;
                });
              });
            }
          }
          return firstWidget ?? buildContainerProgress();
        });
  }

  buildContainerProgress() {
    return Container(
      color: Colors.white,
      child: Container(),
    );
  }

  checkUser() async {
    // setState(() {
    //   if (AppSession.getInstance().hasNewVersionRelease() ?? false) {
    //     widgetPrimeiro = const UpdateAppPage();
    //   } else {
    //     widgetPrimeiro = IntroView();
    //   }
    // }

    // Mantem usuario logado:
    if (Flavor.isDevelopment) {
      var token = await StorageUtils.getAuthTokenResponse();
      var funcionario = await StorageUtils.getColaborator();
      if (token != null) {
        setState(() {
          AppSession.getInstance().setAuthToken(token);
          AppSession.getInstance().user = token.user;
          AppSession.getInstance().collaborator = funcionario;
          firstWidget = const IntroView();
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            navigate(Routes.layoutHome);
          });
        });
      } else {
        setState(() {
          firstWidget = const IntroView();
        });
      }
    } else {
      // sempre que usuario sai do app é deslogado:
      StorageUtils.clearDataLogin();
      firstWidget = const IntroView();
    }
  }
}

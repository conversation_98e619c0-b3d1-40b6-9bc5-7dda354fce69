import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/bloc/terms/enums/type_terms.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../bloc/terms/terms_bloc.dart';
import '../../components/others/app_bar_app.dart';
import '../../components/others/button_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TermsProvider extends StatelessWidget {
  TyperTerms typerTerms;
  // bool showBtnControl;
  // bool showToolbar;
  // String? userId;
  // String? idInvestimento;
  // TipoEmprestimoEnum? tipoEmprestimo;

  TermsProvider({
    super.key,
    required this.typerTerms,
    // this.showBtnControl = true,
    // this.showToolbar = true,
    // this.userId,
    // this.idInvestimento,
    // required this.tipoEmprestimo,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TermsBloc>(
      create: (context) {
        return Modular.get<TermsBloc>();

        //  TermsBloc(
        //   // tipo: tipo,
        //   // appUsecase: usecase,
        //   // showBtnControls: showBtnControl,
        //   // showToolbar: showToolbar,
        //   // userId: userId,
        //   // tipoEmprestimo: tipoEmprestimo,
        // );
      },
      child: TermosView(
        type: typerTerms,
        // state: state,
        // userId: userId,
        // idInvestimento: idInvestimento,
      ),
    );
  }
}

class TermosView extends StatefulWidget {
  // TermosState state;
  // String? userId;
  // String? idInvestimento;
  TyperTerms type;
  TermosView({
    super.key,
    required this.type,
    // required this.state,
    // this.userId,
    // this.idInvestimento,
  });

  @override
  _TermosViewState createState() => _TermosViewState();
}

class _TermosViewState extends State<TermosView> {
  bool isCheckTermos = false;
  var loadingView = true;
  late String urlTerms;
  bool showButtonControlls = false;

  @override
  void initState() {
    urlTerms = '';
    super.initState();
    BlocProvider.of<TermsBloc>(context).add(GetTermosEvent(
      type: widget.type,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (showButtonControlls) {
          _clickNegar();
          return false;
        }
        return true;
      },
      child: BlocConsumer<TermsBloc, TermsState>(
        listener: (context, state) {
          if (state is GetTermsHtmlSuccessState) {
            setState(() {
              urlTerms = state.url;
            });
          }
          if (state is AcceptTermsSuccessState) {
            // BlocProvider.of<RegisterBloc>(context).add(ChangeStageEvent(
            //   stage: RegisterStage.DOCUMENTS,
            // ));
          }

          if (state is DeniedTermsSuccessState) {
            pop();
          }

          if (state is ErrorTermsState) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: AppBarApp(
              title: widget.type == TyperTerms.POLITICA_PRIVACIDADE
                  ? I18n.of(context)!.politica_privacidade.toUpperCase()
                  : I18n.of(context)!.termos_de_uso.toUpperCase(),
              clickBack: () {
                // if (showBtnControls) {
                // if (!widget.state.progressCenter) _clickNegar();
                // return;
                // }
                pop(context);
              },
            ),
            body: Builder(
              builder: (context) {
                if (state is LoadingCenterTerms) {
                  return _buildProgress();
                }
                if (urlTerms.isNotEmpty) {
                  return _buildTerm2(urlTerms);
                }
                return Container();
              },
            ),
            bottomNavigationBar:
                showButtonControlls && state is! LoadingCenterTerms
                    ? _buildButtonAceite(state)
                    : null,
          );
        },
      ),
    );
  }

  Container _buildProgress() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  Widget _buildTerm2(String url) {
    return SfPdfViewer.network(
      url,
      enableTextSelection: false,
    );
  }

  _textTituloNegar() {
    return I18n.of(context)!.recusar_termo_de_compromisso;
  }

  _textMensagemNegar() {
    return I18n.of(context)!.msg_recusar_termo_de_comprimisso;
  }

  _textBtnPositivoNegar() {
    return I18n.of(context)!.recusar;
  }

  _textBtnNegativoNegar() {
    return I18n.of(context)!.cancelar;
  }

  _buildButtonAceite(TermsState state) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, -0.9),
            blurRadius: 3,
            color: Colors.black12,
            // blurStyle: BlurStyle.outer,
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Checkbox(
                onChanged: (bool? value) {
                  setState(() {
                    isCheckTermos = !isCheckTermos;
                  });
                },
                value: isCheckTermos,
              ),
              Text(
                I18n.of(context)!.confirmo_que_li,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(height: 1.0),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ButtonApp(
            text: I18n.of(context)!.aceitar,
            onPress: _callBlocAceitar,
            enabled: isCheckTermos && state is! LoadDeniedTerms,
            progress: state is LoadAcceptTerms
                ? Utils.circularProgressButton(size: 20)
                : null,
          ),
          const SizedBox(height: 8),
          ButtonApp(
            text: I18n.of(context)!.recusar,
            buttonColor: Colors.white,
            enabled: state is! LoadAcceptTerms,
            onPress: _clickNegar,
            progress: state is LoadDeniedTerms
                ? Utils.circularProgressButton(
                    size: 20, color: ColorsApp.verde[500])
                : null,
          ),
        ],
      ),
    );
  }

  _clickNegar() {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return SheetAlertConfirm(
          title: _textTituloNegar(),
          message: _textMensagemNegar(),
          textPositiveButton: _textBtnPositivoNegar(),
          textNegativeButton: _textBtnNegativoNegar(),
          onClickPositive: () {
            _callProviderNegar();
          },
        );
      },
    );
  }

  _callBlocAceitar({String? pin}) {
    BlocProvider.of<TermsBloc>(context).add(AcceptTermsEvent());
  }

  _callProviderNegar() {
    BlocProvider.of<TermsBloc>(context).add(DeniedTermsEvent());
  }
}

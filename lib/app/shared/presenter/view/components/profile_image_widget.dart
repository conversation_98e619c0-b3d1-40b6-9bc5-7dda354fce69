import 'dart:typed_data';
import 'package:flutter/material.dart';

class ProfileImageWidget extends StatelessWidget {
  final Uint8List? imageBytes;
  final Function()? onTap;
  final double size;
  final bool isLoading;
  final String? nomeUsuario;

  const ProfileImageWidget({
    super.key,
    this.imageBytes,
    this.onTap,
    this.size = 100,
    this.isLoading = false,
    this.nomeUsuario,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
        child: ClipOval(
          child: isLoading
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : imageBytes != null
                  ? Image.memory(
                      imageBytes!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        print('Erro ao carregar imagem: $error');
                        return _buildFallbackImage();
                      },
                    )
                  : _buildFallbackImage(),
        ),
      ),
    );
  }

  Widget _buildFallbackImage() {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person,
              size: 50,
              color: Colors.grey,
            ),
            if (nomeUsuario != null)
              Text(
                nomeUsuario!,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
      ),
    );
  }
}

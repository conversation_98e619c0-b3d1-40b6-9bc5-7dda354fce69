import 'package:flutter/widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../themes/styles/colors_app.dart';
import '../../../../utils/utils.dart';

class ValidatorPasswordWidget extends StatefulWidget {
  final TextEditingController? passwordController;
  final Function? onPasswordOk;

  const ValidatorPasswordWidget({
    super.key,
    this.passwordController,
    this.onPasswordOk,
  });

  @override
  State<ValidatorPasswordWidget> createState() =>
      _ValidatorPasswordWidgetState();
}

class _ValidatorPasswordWidgetState extends State<ValidatorPasswordWidget> {
  TextEditingController? get _passwordController => widget.passwordController;
  bool? sizePasswordOK;
  bool? numberPasswordOK;
  bool? uppercasePasswordOK;
  bool? specialCharacterPasswordOK;

  @override
  void initState() {
    super.initState();
    _passwordController?.addListener(() {
      checkSizePassword();
      checkSpecialCharacter();
      checkUppercaseLetter();
      checkNumber();

      if (sizePasswordOK != null &&
          numberPasswordOK != null &&
          uppercasePasswordOK != null &&
          specialCharacterPasswordOK != null) {
        if (sizePasswordOK! &&
            numberPasswordOK! &&
            uppercasePasswordOK! &&
            specialCharacterPasswordOK!) {
          if (widget.onPasswordOk != null) widget.onPasswordOk!(true);
        } else {
          if (widget.onPasswordOk != null) widget.onPasswordOk!(false);
        }
      } else {
        if (widget.onPasswordOk != null) widget.onPasswordOk!(false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildCriteriosSenha();
  }

  Column buildCriteriosSenha() {
    return Column(
      children: <Widget>[
        buildCriteriaSize(),
        const SizedBox(
          height: 8,
        ),
        buildCriteriaNumbers(),
        const SizedBox(
          height: 8,
        ),
        buildCriteriaCapitalLetter(),
        const SizedBox(
          height: 8,
        ),
        buildSpecialCharacterCriteria()
      ],
    );
  }

  Row buildCriteriaSize() {
    return Row(
      children: <Widget>[
        sizePasswordOK == null
            ? IconsApp.icUnCheck()
            : sizePasswordOK!
                ? IconsApp.icCheck()
                : IconsApp.icInfo(color: ColorsApp.error[300]!),
        const SizedBox(
          width: 4,
        ),
        Text(
          const I18n().tamanho_senha,
          style: TextStyle(
            color: sizePasswordOK == null
                ? ColorsApp.cinza[500]
                : sizePasswordOK!
                    ? ColorsApp.verde[600]
                    : ColorsApp.error[300],
          ),
        )
      ],
    );
  }

  Row buildCriteriaNumbers() {
    return Row(
      children: <Widget>[
        numberPasswordOK == null
            ? IconsApp.icUnCheck()
            : numberPasswordOK!
                ? IconsApp.icCheck()
                : IconsApp.icInfo(color: ColorsApp.error[300]!),
        const SizedBox(
          width: 4,
        ),
        Text(
          const I18n().numero,
          style: TextStyle(
            color: numberPasswordOK == null
                ? ColorsApp.cinza[500]
                : numberPasswordOK!
                    ? ColorsApp.verde[600]
                    : ColorsApp.error[300],
          ),
        )
      ],
    );
  }

  Row buildCriteriaCapitalLetter() {
    return Row(
      children: <Widget>[
        uppercasePasswordOK == null
            ? IconsApp.icUnCheck()
            : uppercasePasswordOK!
                ? IconsApp.icCheck()
                : IconsApp.icInfo(color: ColorsApp.error[300]!),
        const SizedBox(
          width: 4,
        ),
        Text(
          const I18n().letra_maiuscula,
          style: TextStyle(
            color: uppercasePasswordOK == null
                ? ColorsApp.cinza[500]
                : uppercasePasswordOK!
                    ? ColorsApp.verde[600]
                    : ColorsApp.error[300],
          ),
        )
      ],
    );
  }

  Row buildSpecialCharacterCriteria() {
    return Row(
      children: <Widget>[
        specialCharacterPasswordOK == null
            ? IconsApp.icUnCheck()
            : specialCharacterPasswordOK!
                ? IconsApp.icCheck()
                : IconsApp.icInfo(color: ColorsApp.error[300]!),
        const SizedBox(
          width: 4,
        ),
        Text(
          const I18n().caractere_especial,
          style: TextStyle(
            color: specialCharacterPasswordOK == null
                ? ColorsApp.cinza[500]
                : specialCharacterPasswordOK!
                    ? ColorsApp.verde[600]
                    : ColorsApp.error[300],
          ),
        )
      ],
    );
  }

  void checkSizePassword() {
    setState(() {
      sizePasswordOK = (_passwordController!.text.length >= 6);
    });
  }

  void checkNumber() {
    setState(() {
      numberPasswordOK =
          (Utils.hasNumber(texto: _passwordController?.text ?? ''));
    });
  }

  void checkUppercaseLetter() {
    setState(() {
      uppercasePasswordOK =
          (Utils.hasUppercase(text: _passwordController?.text ?? ''));
    });
  }

  void checkSpecialCharacter() {
    setState(() {
      specialCharacterPasswordOK =
          (Utils.hasSpecialCharacter(text: _passwordController?.text ?? ''));
    });
  }
}

import 'package:flutter/material.dart';

import '../../../../themes/styles/colors_app.dart';

class SnackBarApp extends StatelessWidget {
  final String message;
  final bool success;
  final bool erroConnection;
  final bool warning;
  final bool info;

  static showSnack({
    required BuildContext context,
    required String message,
    required bool success,
    bool erroConnection = false,
    bool warning = false,
    bool info = false,
    bool isLong = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(getSnack(
      message: message,
      success: success,
      erroConnection: erroConnection,
      warning: warning,
      info: info,
      isLong: isLong,
    ));
  }

  static SnackBar getSnack({
    required String message,
    required bool success,
    bool erroConnection = false,
    bool warning = false,
    bool info = false,
    bool isLong = false,
  }) {
    return SnackBar(
      duration: isLong
          ? const Duration(seconds: 15)
          : const Duration(milliseconds: 4000),
      content: SnackBarApp(
        message: message,
        success: success,
        erroConnection: erroConnection,
        warning: warning,
        info: info,
      ),
      behavior: SnackBarBehavior.floating,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(6))),
      elevation: 0,
      backgroundColor: info
          ? ColorsApp.info[100]
          : warning
              ? ColorsApp.info[100]
              : success
                  ? ColorsApp.correto[100]
                  : ColorsApp.error[100],
    );
  }

  const SnackBarApp({
    super.key,
    required this.message,
    required this.success,
    this.erroConnection = false,
    this.warning = false,
    this.info = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
        top: 4,
        bottom: 4,
        left: 0,
        right: 0,
      ),
      child: Row(
        children: <Widget>[
          warning || info
              ? Icon(Icons.info_outline, color: ColorsApp.error[300]!)
              // ? ImageUtils.icInfo(
              //     color: info ? ColorsApp.info[300]! : ColorsApp.info[300]!)
              : erroConnection
                  ? Icon(Icons.info_outline,
                      color: ColorsApp.error[
                          300]!) //ImageUtils.icInfo(color: ColorsApp.error[300]!)
                  : success
                      ? Icon(Icons.check,
                          color: ColorsApp.correto[
                              300]!) // ImageUtils.icCheck(color: ColorsApp.correto[300]!)
                      : Icon(Icons.info_outline,
                          color: ColorsApp.error[
                              300]!), // ImageUtils.icInfo(color: ColorsApp.error[300]!),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.left,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: success
                        ? ColorsApp.correto[300]
                        : info
                            ? ColorsApp.info[300]
                            : warning
                                ? ColorsApp.info[300]
                                : ColorsApp.error[300],
                  ),
            ),
          ),
        ],
      ),
    );
  }
}

import '../../../../../../localization/generated/i18n.dart';
import '../../../../navigation/navigator_app.dart';
import '../../../../themes/styles/colors_app.dart';
import '../../../../utils/utils.dart';
import '../../responsive_widgets/responsive_widgets.dart';
import 'button_app.dart';
import 'package:flutter/material.dart';

class SheetAlertConfirm extends StatelessWidget {
  final String? title;
  final String? message;
  final String? textPositiveButton;
  final String? textNegativeButton;
  final Function onClickPositive;
  final Function? onClickNegative;
  final bool showBtnNegative;
  final TextStyle? negativeButtonStyle;
  final TextStyle? messageStyle;
  final TextStyle? titleStyle;
  final TextAlign? textAlignMessage;
  final Widget? child;

  static Future showSheet(
    BuildContext context, {
    String? title,
    String? message,
    String? textPositive,
    String? textNegative,
    required Function clickPositive,
    Function? clickNegative,
    bool showBtnNegative = true,
    TextStyle? styleNegativeButton,
    TextStyle? messageStyle,
    TextAlign? textAlignMessage,
    TextStyle? titleStyle,
    Widget? child,
    bool dismissible = false,
  }) {
    return showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      isDismissible: dismissible,
      builder: (context) {
        return Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: SheetAlertConfirm(
            title: title,
            message: message,
            textPositiveButton: textPositive,
            textNegativeButton: textNegative,
            onClickPositive: clickPositive,
            onClickNegative: clickNegative,
            showBtnNegative: showBtnNegative,
            negativeButtonStyle: styleNegativeButton,
            messageStyle: messageStyle,
            textAlignMessage: textAlignMessage,
            titleStyle: titleStyle,
            child: child,
          ),
        );
      },
    );
  }

  const SheetAlertConfirm({
    super.key,
    required this.title,
    required this.message,
    required this.textPositiveButton,
    this.textNegativeButton,
    required this.onClickPositive,
    this.onClickNegative,
    this.showBtnNegative = true,
    this.negativeButtonStyle,
    this.messageStyle,
    this.textAlignMessage,
    this.titleStyle,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    // Utils.setScreeenResponsive(context: context);
    var textTheme = Theme.of(context).textTheme;
    return ContainerResponsive(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            blurRadius: 64,
            color: ColorsApp.drop1,
            offset: Offset(0, -4),
          )
        ],
      ),
      padding: EdgeInsetsResponsive.only(
        top: 24,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      child: SafeArea(
        child: Column(
          // direction: Axis.vertical,
          // verticalDirection: VerticalDirection.down,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              title ?? '',
              style: titleStyle ?? textTheme.titleMedium!,
              textAlign: TextAlign.center,
            ),
            SizedBoxResponsive(height: 16),
            message != null
                ? ContainerResponsive(
                    margin: EdgeInsetsResponsive.only(bottom: 24),
                    child: Text(
                      message ?? '',
                      style: messageStyle ?? textTheme.bodyMedium,
                      textAlign: textAlignMessage ?? TextAlign.start,
                    ),
                  )
                : child ?? ContainerResponsive(),
            ButtonApp(
              text: textPositiveButton,
              onPress: () {
                Navigator.maybePop(context);
                onClickPositive();
              },
            ),
            SizedBoxResponsive(height: 8),
            Visibility(
              visible: showBtnNegative,
              child: ButtonApp(
                buttonColor: Colors.white,
                text: textNegativeButton ?? I18n.of(context)!.nao,
                textStyle: negativeButtonStyle,
                border: 1,
                borderColor: ColorsApp.cinza[800]!,
                onPress: () {
                  Navigator.maybePop(context);
                  if (onClickNegative != null) onClickNegative!();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

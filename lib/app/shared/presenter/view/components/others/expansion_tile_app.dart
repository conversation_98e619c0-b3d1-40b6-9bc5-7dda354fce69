import 'package:flutter/material.dart';

import '../../../../themes/styles/colors_app.dart';

class ExpansionTileApp extends StatefulWidget {
  const ExpansionTileApp({
    super.key,
    required this.title,
    required this.description,
  });
  final String title;
  final String description;

  @override
  State<ExpansionTileApp> createState() => _ExpansionTileAppState();
}

class _ExpansionTileAppState extends State<ExpansionTileApp> {
  late final ExpansionTileController controller;
  bool isExpanded = false;
  @override
  void initState() {
    controller = ExpansionTileController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.circular(10),
      ),
      child: ExpansionTile(
        controller: controller,
        title: Text(
          widget.title,
          style: textTheme.displayMedium?.copyWith(
            color: ColorsApp.cinza[700],
            fontWeight: FontWeight.w500,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        backgroundColor: Colors.white,
        collapsedBackgroundColor: Colors.white,
        collapsedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        showTrailingIcon: false,
        tilePadding: const EdgeInsets.only(
          top: 0,
          bottom: 0,
          left: 16,
          right: 16,
        ),
        subtitle: isExpanded
            ? null
            : Padding(
                padding: const EdgeInsets.only(top: 6),
                child: Text(
                  widget.description,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodyMedium?.copyWith(
                    color: ColorsApp.cinza[700],
                  ),
                ),
              ),
        onExpansionChanged: (value) {
          setState(() {
            isExpanded = value;
          });
        },
        children: [
          ListTile(
            contentPadding:
                const EdgeInsets.only(bottom: 16, right: 16, left: 16),
            minTileHeight: 30,
            subtitle: Text(
              widget.description,
              style: textTheme.bodyMedium?.copyWith(
                color: ColorsApp.cinza[700],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

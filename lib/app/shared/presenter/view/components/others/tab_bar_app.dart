import 'package:flutter/material.dart';

import '../../../../themes/styles/colors_app.dart';

class TabBarApp extends StatefulWidget {
  const TabBarApp({
    required this.firstTabTitle,
    this.secondTabTitle,
    this.onTabChange,
    this.showUnderline = false,
    super.key,
    this.pageInit = 0,
  });
  final Function(int pageIndex)? onTabChange;
  final String firstTabTitle;
  final String? secondTabTitle;
  final bool showUnderline;
  final int pageInit;

  @override
  State<TabBarApp> createState() => _TabBarAppState();
}

class _TabBarAppState extends State<TabBarApp> {
  int pageIndex = 0;

  @override
  void initState() {
    pageIndex = widget.pageInit;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final hasSecondTitle = widget.secondTabTitle != null;

    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: <Widget>[
        Expanded(
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            highlightColor: ColorsApp.cinza[400],
            onTap: hasSecondTitle
                ? () {
                    setState(() {
                      pageIndex = 0;
                    });
                    if (widget.onTabChange != null) {
                      widget.onTabChange!(pageIndex);
                    }
                  }
                : null,
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                border: Border(
                  bottom: widget.showUnderline && pageIndex == 0
                      ? const BorderSide(color: ColorsApp.verde, width: 2)
                      : BorderSide.none,
                ),
              ),
              child: Text(
                widget.firstTabTitle,
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      color: pageIndex == 0
                          ? ColorsApp.cinza[900]
                          : ColorsApp.cinza[600],
                    ),
              ),
            ),
          ),
        ),
        hasSecondTitle
            ? Expanded(
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  highlightColor: ColorsApp.cinza[400],
                  onTap: () {
                    setState(() {
                      pageIndex = 1;
                    });
                    if (widget.onTabChange != null) {
                      widget.onTabChange!(pageIndex);
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: widget.showUnderline && pageIndex == 1
                            ? const BorderSide(color: ColorsApp.verde, width: 2)
                            : BorderSide.none,
                      ),
                    ),
                    child: Text(
                      widget.secondTabTitle!,
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                            color: pageIndex == 0
                                ? ColorsApp.cinza[600]
                                : ColorsApp.cinza[900],
                          ),
                    ),
                  ),
                ),
              )
            : const SizedBox(),
      ],
    );
  }
}

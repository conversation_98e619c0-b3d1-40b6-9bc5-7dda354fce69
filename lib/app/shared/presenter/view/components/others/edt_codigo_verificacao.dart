import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class EdtCodigoVerificacao extends StatelessWidget {
  final _num1Controller = TextEditingController();
  final _num2Controller = TextEditingController();
  final _num3Controller = TextEditingController();
  final _num4Controller = TextEditingController();
  final _num5Controller = TextEditingController();
  final _num6Controller = TextEditingController();

  final _num1FocusNode = FocusNode();
  final _num2FocusNode = FocusNode();
  final _num3FocusNode = FocusNode();
  final _num4FocusNode = FocusNode();
  final _num5FocusNode = FocusNode();
  final _num6FocusNode = FocusNode();

  Function sendCodVerificacao;

  EdtCodigoVerificacao({
    super.key,
    required this.sendCodVerificacao,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          child: EdtCod(
            isOneDigit: true,
            borderPrimaryColor: true,
            controller: _num1Controller,
            autofocus: true,
            textInputAction: TextInputAction.next,
            focusNode: _num1FocusNode,
            clickAction: (String text) {
              if (text.isNotEmpty) {
                if (text.length == 6) {
                  _num1Controller.text = text.substring(0, 1);
                  _num2Controller.text = text.substring(1, 2);
                  _num3Controller.text = text.substring(2, 3);
                  _num4Controller.text = text.substring(3, 4);
                  _num5Controller.text = text.substring(4, 5);
                  _num6Controller.text = text.substring(5, 6);
                  Utils.unFocus(context);
                  sendCodVerificacao(getStringCodVerificacao());
                } else {
                  Utils.fieldFocusChange(
                      context, _num1FocusNode, _num2FocusNode);
                }
              }
            },
            validator: (text) {
              if (text != null && text.isEmpty) return "";
              return null;
            },
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: EdtCod(
            isOneDigit: true,
            borderPrimaryColor: true,
            controller: _num2Controller,
            textInputAction: TextInputAction.next,
            focusNode: _num2FocusNode,
            clickAction: (text) {
              if (text.isNotEmpty) {
                if (text.length == 6) {
                  _num1Controller.text = text.substring(0, 1);
                  _num2Controller.text = text.substring(1, 2);
                  _num3Controller.text = text.substring(2, 3);
                  _num4Controller.text = text.substring(3, 4);
                  _num5Controller.text = text.substring(4, 5);
                  _num6Controller.text = text.substring(5, 6);
                  Utils.unFocus(context);
                  sendCodVerificacao(getStringCodVerificacao());
                } else {
                  Utils.fieldFocusChange(
                      context, _num2FocusNode, _num3FocusNode);
                }
              } else
                Utils.fieldFocusChange(context, _num2FocusNode, _num1FocusNode);
            },
            validator: (text) {
              if (text != null && text.isEmpty) return "";
              return null;
            },
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: EdtCod(
            isOneDigit: true,
            borderPrimaryColor: true,
            controller: _num3Controller,
            textInputAction: TextInputAction.next,
            focusNode: _num3FocusNode,
            clickAction: (text) {
              if (text.isNotEmpty) {
                if (text.length == 6) {
                  _num1Controller.text = text.substring(0, 1);
                  _num2Controller.text = text.substring(1, 2);
                  _num3Controller.text = text.substring(2, 3);
                  _num4Controller.text = text.substring(3, 4);
                  _num5Controller.text = text.substring(4, 5);
                  _num6Controller.text = text.substring(5, 6);
                  Utils.unFocus(context);
                  sendCodVerificacao(getStringCodVerificacao());
                } else {
                  Utils.fieldFocusChange(
                      context, _num3FocusNode, _num4FocusNode);
                }
              } else
                Utils.fieldFocusChange(context, _num3FocusNode, _num2FocusNode);
            },
            validator: (text) {
              if (text != null && text.isEmpty) return "";
              return null;
            },
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: EdtCod(
            isOneDigit: true,
            borderPrimaryColor: true,
            controller: _num4Controller,
            textInputAction: TextInputAction.next,
            focusNode: _num4FocusNode,
            clickAction: (text) {
              if (text.isNotEmpty) {
                if (text.length == 6) {
                  _num1Controller.text = text.substring(0, 1);
                  _num2Controller.text = text.substring(1, 2);
                  _num3Controller.text = text.substring(2, 3);
                  _num4Controller.text = text.substring(3, 4);
                  _num5Controller.text = text.substring(4, 5);
                  _num6Controller.text = text.substring(5, 6);
                  Utils.unFocus(context);
                  sendCodVerificacao(getStringCodVerificacao());
                } else {
                  Utils.fieldFocusChange(
                      context, _num4FocusNode, _num5FocusNode);
                }
              } else
                Utils.fieldFocusChange(context, _num4FocusNode, _num3FocusNode);
            },
            validator: (text) {
              if (text != null && text.isEmpty) return "";
              return null;
            },
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: EdtCod(
            isOneDigit: true,
            borderPrimaryColor: true,
            controller: _num5Controller,
            textInputAction: TextInputAction.next,
            focusNode: _num5FocusNode,
            clickAction: (text) {
              if (text.isNotEmpty) {
                if (text.length == 6) {
                  _num1Controller.text = text.substring(0, 1);
                  _num2Controller.text = text.substring(1, 2);
                  _num3Controller.text = text.substring(2, 3);
                  _num4Controller.text = text.substring(3, 4);
                  _num5Controller.text = text.substring(4, 5);
                  _num6Controller.text = text.substring(5, 6);
                  Utils.unFocus(context);
                  sendCodVerificacao(getStringCodVerificacao());
                } else {
                  Utils.fieldFocusChange(
                      context, _num5FocusNode, _num6FocusNode);
                }
              } else
                Utils.fieldFocusChange(context, _num5FocusNode, _num4FocusNode);
            },
            validator: (text) {
              if (text != null && text.isEmpty) return "";
              return null;
            },
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: EdtCod(
            isOneDigit: true,
            borderPrimaryColor: true,
            controller: _num6Controller,
            textInputAction: TextInputAction.done,
            focusNode: _num6FocusNode,
            clickAction: (text) {
              if (text.isNotEmpty) {
                if (text.length == 6) {
                  _num1Controller.text = text.substring(0, 1);
                  _num2Controller.text = text.substring(1, 2);
                  _num3Controller.text = text.substring(2, 3);
                  _num4Controller.text = text.substring(3, 4);
                  _num5Controller.text = text.substring(4, 5);
                  _num6Controller.text = text.substring(5, 6);
                  Utils.unFocus(context);
                  sendCodVerificacao(getStringCodVerificacao());
                } else {
                  _num6FocusNode.unfocus();
                  sendCodVerificacao(getStringCodVerificacao());
                }
              } else
                Utils.fieldFocusChange(context, _num6FocusNode, _num5FocusNode);
            },
            validator: (text) {
              if (text != null && text.isEmpty) return "";
              return null;
            },
          ),
        ),
      ],
    );
  }

  getStringCodVerificacao() {
    return _num1Controller.text +
        _num2Controller.text +
        _num3Controller.text +
        _num4Controller.text +
        _num5Controller.text +
        _num6Controller.text;
  }

  getCodVerificacao() {
    if (_num1Controller.text.isEmpty ||
        _num2Controller.text.isEmpty ||
        _num3Controller.text.isEmpty ||
        _num4Controller.text.isEmpty ||
        _num5Controller.text.isEmpty ||
        _num6Controller.text.isEmpty)
      return;
    else {
      String cod = getStringCodVerificacao();
      sendCodVerificacao(cod);
    }
  }

  limpaCampos() {
    _num1Controller.text = "";
    _num2Controller.text = "";
    _num3Controller.text = "";
    _num4Controller.text = "";
    _num5Controller.text = "";
    _num6Controller.text = "";
    _num1FocusNode.requestFocus();
  }

  String get codigo =>
      _num1Controller.text +
      _num2Controller.text +
      _num3Controller.text +
      _num4Controller.text +
      _num5Controller.text +
      _num6Controller.text;
}

class EdtCod extends StatelessWidget {
  FocusNode? focusNode;
  TextEditingController? controller;
  TextInputAction? textInputAction;
  void Function(String)? clickAction;
  String? Function(String?)? validator;
  bool autofocus;
  bool borderPrimaryColor;
  bool isPassword;
  bool isOneDigit;
  bool isNumber;

  EdtCod({
    super.key,
    this.controller,
    this.textInputAction,
    this.focusNode,
    this.clickAction,
    this.validator,
    this.autofocus = false,
    this.borderPrimaryColor = false,
    this.isPassword = false,
    this.isOneDigit = false,
    this.isNumber = true,
  });

  @override
  Widget build(BuildContext context) {
    List<TextInputFormatter> listFormater = [
      // LengthLimitingTextInputFormatter(1),
    ];

    if (isNumber) {
      listFormater.add(FilteringTextInputFormatter.digitsOnly);
    }

    if (isOneDigit) {
      listFormater.add(LengthLimitingTextInputFormatter(1));
    }

    return TextFormField(
      enableInteractiveSelection: false,
      focusNode: focusNode,
      autofocus: autofocus,
      controller: controller,
      textInputAction: textInputAction ?? TextInputAction.done,
      keyboardType: isNumber ? TextInputType.number : null,
      textCapitalization: TextCapitalization.words,
      cursorColor: ColorsApp.verde[500],
      textAlign: TextAlign.center,
      obscureText: isPassword,
      inputFormatters: listFormater,
      onFieldSubmitted: clickAction,
      onChanged: clickAction,
      validator: validator,
      decoration: InputDecoration(
        filled: true,
        counterText: "",
        fillColor: Colors.white,
        border: UnderlineInputBorder(
            borderSide: BorderSide(
          color: ColorsApp.cinza[500]!,
        )),
        disabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(
          color: ColorsApp.cinza[500]!,
        )),
        enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(
          color: ColorsApp.cinza[500]!,
        )),
        focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(
          color: ColorsApp.verde[500]!,
        )),
        // errorBorder: Utils.outlineBoderEditValueError(),
        // focusedErrorBorder: Utils.outlineBoderEditValueError(),
      ),
    );
  }
}

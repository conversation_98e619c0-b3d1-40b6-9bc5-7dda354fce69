import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../themes/styles/colors_app.dart';

class TextFormFieldApp extends StatefulWidget {
  final String? label;
  final String? hint;

  final TextEditingController? controller;
  final FormFieldValidator<String>? validator;
  final bool typePassword;
  final Widget? suffixIcon;
  final Icon? prefixIcon;
  final TextInputType textInputType;
  final TextInputAction? textInputAction;
  final TextAlign textAlign;
  final Function(String)? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;
  final bool enable;
  final int? maxLength;
  final void Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;
  final Widget? suffix;
  final TextInputFormatter? formatter;
  final bool mostrarBgDesativado;
  final String? initialValue;

  /// Se valor for true exibirá o nome do campo como prefix e nao como label
  final bool isPrefixLabel;

  /// 'highLight' define se o campo quando focado, terá um pouco mais de destaque.
  /// A borda muda de 0.5 para 1.5 quando em foco, melhorando a visualizacao em fundos mais escuros.
  final bool highLight;

  const TextFormFieldApp({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.validator,
    this.typePassword = false,
    this.suffixIcon,
    this.prefixIcon,
    this.textInputType = TextInputType.text,
    this.textInputAction,
    this.textAlign = TextAlign.start,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.enable = true,
    this.maxLength,
    this.onFieldSubmitted,
    this.focusNode,
    this.suffix,
    this.formatter,
    this.mostrarBgDesativado = true,
    this.isPrefixLabel = false,
    this.highLight = false,
    this.initialValue,
  });

  @override
  State<TextFormFieldApp> createState() => _TextFormFieldAppState();
}

class _TextFormFieldAppState extends State<TextFormFieldApp> {
  String? get label => widget.label;
  String? get hint => widget.hint;
  TextEditingController? get controller => widget.controller;
  FormFieldValidator<String>? get validator => widget.validator;
  bool get typePassword => widget.typePassword;
  Widget? get suffixIcon => widget.suffixIcon;
  Icon? get prefixIcon => widget.prefixIcon;
  TextInputType get textInputType => widget.textInputType;
  TextAlign get textAlign => widget.textAlign;
  Function(String)? get onChanged => widget.onChanged;
  VoidCallback? get onTap => widget.onTap;
  bool get readOnly => widget.readOnly;
  bool get enable => widget.enable;
  int? get maxLength => widget.maxLength;
  TextInputAction? get textInputAction => widget.textInputAction;
  void Function(String)? get onFieldSubmitted => widget.onFieldSubmitted;
  FocusNode? get focusNode => widget.focusNode;
  Widget? get suffix => widget.suffix;
  TextInputFormatter? get formatter => widget.formatter;
  bool get mostrarBgDesativado => widget.mostrarBgDesativado;
  String? get initialValue => widget.initialValue;

  bool _ocultarSenha = false;
  @override
  void initState() {
    super.initState();
    if (typePassword) {
      _ocultarSenha = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    List<TextInputFormatter> listFormater = [];

    if (formatter != null) {
      if (textInputType == TextInputType.number) {
        listFormater.add(FilteringTextInputFormatter.digitsOnly);
      }

      listFormater.add(formatter!);
    }

    return TextFormField(
      initialValue: initialValue,
      obscureText: _ocultarSenha,
      controller: controller,
      cursorColor: ColorsApp.cinza[900],
      validator: validator,
      keyboardType: textInputType,
      textAlign: textAlign,
      onChanged: onChanged,
      onTap: onTap,
      readOnly: readOnly,
      enabled: enable,
      maxLength: maxLength,
      focusNode: focusNode,
      textInputAction: textInputAction,
      style: Theme.of(context).textTheme.bodyMedium,
      onFieldSubmitted: onFieldSubmitted,
      inputFormatters: listFormater,
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              color: ColorsApp.cinza[700],
            ),
        counterText: '',
        filled: true,
        fillColor: !enable && mostrarBgDesativado
            ? ColorsApp.bgDesativado
            : Colors.white,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            width: 0.5,
            color: Colors.black54,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            width: widget.highLight ? 1.5 : 0.5,
            color: ColorsApp.azul[100]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            width: widget.highLight ? 1.5 : 0.5,
            color: ColorsApp.azul[100]!,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            width: 0,
            color: Colors.transparent,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(11),
          borderSide: BorderSide(color: ColorsApp.error[300]!, width: 2.0),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(11),
          borderSide: BorderSide(color: ColorsApp.error[300]!, width: 2.0),
        ),
        focusColor: ColorsApp.azul[100],
        // hintText: label,
        contentPadding: const EdgeInsets.all(16),
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              color: ColorsApp.cinza[600],
            ),
        errorStyle: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: ColorsApp.error[300]),
        labelText: widget.isPrefixLabel ? null : label,
        prefixIcon: prefixIcon ??
            (widget.isPrefixLabel
                ? Padding(
                    padding: const EdgeInsets.only(
                        top: 15, bottom: 15, left: 15, right: 8),
                    child: Text(
                      label ?? '',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: ColorsApp.cinza[600],
                          ),
                    ))
                : null),
        suffix: suffix,
        suffixIcon: typePassword
            ? _ocultarSenha
                ? IconButton(
                    icon: IconsApp.icVisibilityOff(),
                    onPressed: () {
                      setState(() {
                        _ocultarSenha = !_ocultarSenha;
                      });
                    },
                  )
                : IconButton(
                    icon: IconsApp.icVisibility(),
                    onPressed: () {
                      setState(() {
                        _ocultarSenha = !_ocultarSenha;
                      });
                    },
                  )
            : suffixIcon,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../themes/styles/colors_app.dart';
import '../../../../themes/styles/icons_app.dart';

class AppBarApp extends StatefulWidget implements PreferredSizeWidget {
  final String? title;
  final Function()? clickBack;
  final List<Widget>? actions;
  final bool showBack;
  final bool showBackLogin;
  final Color? colorBackground;
  final Widget? titleWidget;
  final bool showElevation;
  final bool imageClose;
  final bool showLine;
  final Color? widgetBackColor;

  /// Define o brilho do fundo da tela. Se definir como claro (`light`), o conteudo e statusbar do app bar
  /// serao escuros. Se definir como escuro (`dark`), o conteudo será claro.
  final Brightness brightnessBackground;

  const AppBarApp({
    super.key,
    this.title,
    this.clickBack,
    this.actions,
    this.showBack = true,
    this.colorBackground,
    this.titleWidget,
    this.showElevation = false,
    this.imageClose = false,
    this.showLine = true,
    this.widgetBackColor,
    this.showBackLogin = false,
    this.brightnessBackground = Brightness.light,
  });

  @override
  State<AppBarApp> createState() => _AppBarAppState();

  @override
  Size get preferredSize => showLine
      ? const Size.fromHeight(kToolbarHeight + 1)
      : const Size.fromHeight(kToolbarHeight);
}

class _AppBarAppState extends State<AppBarApp> {
  final systemUiOverlay = const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
  );
  final systemUiOverlayLight = const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
  );

  @override
  Widget build(BuildContext context) {
    // Utils.setScreeenResponsive(context: context);
    var isBrignessLight = widget.brightnessBackground == Brightness.light;
    return Column(
      children: <Widget>[
        AppBar(
          systemOverlayStyle: (isBrignessLight
              ? SystemUiOverlayStyle.dark
              : SystemUiOverlayStyle.light),
          actions: widget.actions,
          centerTitle: false,
          leading: widget.showBackLogin ||
                  (widget.showBack && Navigator.canPop(context))
              ? IconButton(
                  icon: widget.imageClose
                      ? IconsApp.icClose(color: widget.widgetBackColor)
                      : IconsApp.icVoltarView(color: widget.widgetBackColor),
                  onPressed: () {
                    if (widget.clickBack != null) {
                      widget.clickBack!();
                    } else {
                      pop();
                    }
                  },
                )
              : null,
          automaticallyImplyLeading: false,
          backgroundColor: widget.colorBackground ?? ColorsApp.cinza[100],
          // titleSpacing: widget.titleWidget != null ? 0 : 16.0,
          title: widget.titleWidget ??
              Text(
                widget.title ?? "",
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(color: isBrignessLight ? null : Colors.white),
              ),
          elevation: widget.showElevation ? 2 : 0,
        ),
        Visibility(
          visible: widget.showLine,
          child: const Divider(
            height: 1,
            color: Color(0xFFDBDDE0),
          ),
        ),
      ],
    );
  }
}

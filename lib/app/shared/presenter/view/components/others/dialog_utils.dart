import 'dart:math';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../errors/error_response.dart';
import '../../../../errors/errors.dart';
import '../../../../navigation/navigator_app.dart';
import '../../../../themes/styles/colors_app.dart';
import 'dialog_title_alert.dart';
import 'snack_bar_app.dart';

abstract class DialogUtils {
  static showSnackError(BuildContext context, Exception error) {
    switch (error) {
      case TokenInvalid _:
        StorageUtils.clearDataLogin();
        navigate(Routes.intro);
        return;
      // break;

      case NoInternet _:
        SnackBarApp.showSnack(
          context: context,
          message: I18n.of(context)!.error_no_internet,
          success: false,
          erroConnection: true,
        );
        break;

      case ErrorFile _:
        SnackBarApp.showSnack(
            context: context,
            message:
                error.message ?? '', // I18n.of(context)!.erro_arquivo_upload,
            success: false);
        break;

      case SistemaManutencao _:
        break;

      case ErrorResponse _:
        SnackBarApp.showSnack(
          context: context,
          message: error.message ?? '',
          success: false,
          warning: false,
          info: false,
          isLong: false,
        );
        break;

      default:
        SnackBarApp.showSnack(
            context: context,
            message: error.toString() ?? I18n.of(context)!.error_time_out,
            success: false);
    }

    // if (error is ErrorResponse) {
    //   SnackBarApp.showSnack(
    //     context: context,
    //     message: error.message ?? '',
    //     success: false,
    //     warning: false,
    //     info: false,
    //     isLong: false,
    //   );
    // } else {
    //   SnackBarApp.showSnack(
    //       context: context,
    //       message: I18n.of(context)!.error_time_out,
    //       success: false);
    // }
  }

  static Future showLoadingWithText(BuildContext context, {GlobalKey? key}) {
    var theme = Theme.of(context);
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            child: AlertDialog(
              key: key,
              content: ListTile(
                contentPadding: const EdgeInsets.all(0),
                title: Text(
                  I18n.of(context)!.loading,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(color: ColorsApp.cinza[800]),
                ),
                leading: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
                ),
              ),
            ),
          );
        });
  }

  static dialogErrorText(BuildContext context, String message) {
    SnackBarApp.showSnack(context: context, message: message, success: false);
  }

  static stopLoading(GlobalKey key) {
    if (key.currentContext != null) {
      pop();
    }
  }

  static Future showDialogTitleMensagem({
    required BuildContext context,
    required String title,
    required String mensagem,
    Function? clickContinuar,
  }) {
    return DialogTitleAlert.show(
      context: context,
      title: title,
      mensagem: mensagem,
      clickContinuar: clickContinuar,
    );
  }
}

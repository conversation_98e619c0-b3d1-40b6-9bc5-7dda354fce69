import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class ButtonApp extends StatelessWidget {
  double marginTop = 0;
  double width;
  Widget? progress;
  String? text;
  bool enabled;
  void Function()? onPress;
  bool isHorizontal;
  double height;
  bool isBgPrimary;
  double circular;
  Color borderColor;
  Color? buttonColor;
  double border;
  Color? textColor;
  TextStyle? textStyle;
  Alignment? contentAlign;

  ButtonApp({
    super.key,
    this.marginTop = 0,
    this.width = 0,
    this.progress,
    this.text,
    this.enabled = true,
    this.onPress,
    this.isHorizontal = false,
    this.height = 48,
    this.isBgPrimary = false,
    this.circular = 11,
    this.borderColor = Colors.white,
    this.buttonColor,
    this.border = 0,
    this.textColor = ColorsApp.cinza,
    this.textStyle,
    this.contentAlign,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveWidgets.init(
      context,
      height: isHorizontal ? 360 : 720, // Optional
      width: isHorizontal ? 720 : 360, // Optional
      allowFontScaling: true, // Optional
    );
    var textTheme = Theme.of(context).textTheme;
    return ContainerResponsive(
      margin: EdgeInsetsResponsive.only(top: marginTop),
      width: width > 0 ? width : null,
      decoration: BoxDecoration(
        border: Border.all(width: border, color: borderColor),
        borderRadius: BorderRadius.all(
          Radius.circular(circular),
        ),
      ),
      height: height,
      child: ElevatedButton(
        // color: buttonColor ?? ColorsApp.verde[500],
        // elevation: 0,

        style: ButtonStyle(
          elevation: WidgetStateProperty.all(0),
          backgroundColor: WidgetStateProperty.all<Color>(!enabled
              ? ColorsApp.bgDesativado
              : buttonColor ?? ColorsApp.verde[500]!),
          textStyle: WidgetStatePropertyAll(
            textTheme.bodyMedium!.copyWith(
              color: !enabled ? ColorsApp.disableTextColor : textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              height: 1.0,
            ),
          ),
          alignment: contentAlign,
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(circular)),
          ),
        ),
        onPressed: (enabled && progress == null) ? (onPress ?? (() {})) : () {},

        child: progress ??
            TextResponsive(
              text ?? "",
              style: textStyle ??
                  textTheme.bodyMedium!.copyWith(
                    color: !enabled ? ColorsApp.disableTextColor : textColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
            ),
        // disabledColor: ColorsApp.cinza[300],
      ),
    );
  }
}

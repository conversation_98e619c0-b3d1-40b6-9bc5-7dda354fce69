// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../themes/styles/colors_app.dart';

class ButtonResendCode extends StatelessWidget {
  const ButtonResendCode({
    Key? key,
    required this.onTap,
  }) : super(key: key);

  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      onPressed: onTap,
      label: Text(
        const I18n().reenviar_codigo_sms,
      ),
      icon: const Icon(Icons.refresh),
      style: const ButtonStyle(
        foregroundColor: WidgetStatePropertyAll<Color>(ColorsApp.verde),
      ),
    );
  }
}

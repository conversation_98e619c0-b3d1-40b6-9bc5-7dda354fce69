import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../navigation/named_routes.dart';

class IconButtonHelp extends StatelessWidget {
  const IconButtonHelp({super.key, this.colorIcon});
  final Color? colorIcon;

  @override
  Widget build(BuildContext context) {
    return IconButton(
        icon: IconsApp.icHelp(colorIcon),
        onPressed: () {
          push(Routes.help);
        });
  }
}

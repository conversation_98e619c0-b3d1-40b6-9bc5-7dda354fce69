import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../errors/error_response.dart';
import '../../../../errors/errors.dart';
import '../../../../themes/styles/colors_app.dart';
import '../../../../utils/utils.dart';
import '../../responsive_widgets/responsive_widgets.dart';

enum TypeAlertBanner { success, error, alert, info }

class AlertBanner extends StatelessWidget {
  final bool isShow;
  final TypeAlertBanner typeAlertBanner;
  final Exception? error;
  final String? message;
  final double pTop;
  final double pBottom;

  const AlertBanner({
    super.key,
    required this.isShow,
    required this.typeAlertBanner,
    this.error,
    this.message,
    this.pTop = 16,
    this.pBottom = 16,
  });

  @override
  Widget build(BuildContext context) {
    return isShow ? _buildBanner(context) : ContainerResponsive();
  }

  _buildBanner(BuildContext context) {
    Widget? _icon;
    String? _msg;

    Utils.setScreeenResponsive(context: context);
    var textTheme = Theme.of(context).textTheme;
    if (typeAlertBanner == TypeAlertBanner.success) {
      _icon = IconsApp.icCheck(color: ColorsApp.verde[700]!);
      _msg = message;
    } else if (error is ErrorResponse) {
      ErrorResponse response = (error) as ErrorResponse;
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = response.message;
    } else if (error is NoInternet) {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = I18n.of(context)!.error_no_internet;
    } else if (error is SistemaManutencao) {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = I18n.of(context)!.sistema_manutencao;
    } else if (error is Exception) {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = 'Ocorreu um erro interno. Por favor, tente novamente mais tarde.';
    } else if (typeAlertBanner == TypeAlertBanner.alert) {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = message;
    } else if (typeAlertBanner == TypeAlertBanner.info) {
      _icon = IconsApp.icInfo(color: ColorsApp.info[300]!);
      _msg = message;
    } else if (typeAlertBanner == TypeAlertBanner.error &&
        message != null &&
        message!.isNotEmpty) {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = message;
    } else if (typeAlertBanner == TypeAlertBanner.error) {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = message;
    } else {
      _icon = IconsApp.icInfo(color: ColorsApp.error[300]!);
      _msg = I18n.of(context)!.error_time_out;
    }

    return ContainerResponsive(
      margin: EdgeInsetsResponsive.only(top: 16),
      padding: EdgeInsetsResponsive.only(
          left: 16, right: 16, top: pTop, bottom: pBottom),
      decoration: BoxDecoration(
        color: typeAlertBanner == TypeAlertBanner.success
            ? ColorsApp.verde[100]
            : typeAlertBanner == TypeAlertBanner.error
                ? ColorsApp.error[100]
                : typeAlertBanner == TypeAlertBanner.alert ||
                        typeAlertBanner == TypeAlertBanner.info
                    ? ColorsApp.info[100]
                    : ColorsApp.info[100],
        borderRadius: const BorderRadius.all(
          Radius.circular(6),
        ),
        border: Border.all(
          color: typeAlertBanner == TypeAlertBanner.success
              ? ColorsApp.verde[500]!
              : typeAlertBanner == TypeAlertBanner.error
                  ? ColorsApp.error[200]!
                  : typeAlertBanner == TypeAlertBanner.alert ||
                          typeAlertBanner == TypeAlertBanner.info
                      ? ColorsApp.info[300]!
                      : ColorsApp.info[300]!,
          width: 0.1,
        ),
      ),
      child: Row(
        children: <Widget>[
          Center(child: _icon),
          Expanded(
            child: ContainerResponsive(
              margin: EdgeInsetsResponsive.only(left: 8),
              child: Text(
                _msg ?? "Error",
                style: textTheme.displaySmall!.copyWith(
                    fontSize: 14,
                    color: typeAlertBanner == TypeAlertBanner.success
                        ? ColorsApp.verde[700]
                        : typeAlertBanner == TypeAlertBanner.error
                            ? ColorsApp.error[300]
                            : typeAlertBanner == TypeAlertBanner.alert ||
                                    typeAlertBanner == TypeAlertBanner.info
                                ? ColorsApp.info[300]
                                : ColorsApp.info[300]),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

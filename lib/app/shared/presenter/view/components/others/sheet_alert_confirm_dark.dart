import 'package:flutter/material.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../navigation/navigator_app.dart';
import '../../../../themes/styles/colors_app.dart';
import 'button_app.dart';

class SheetAlertConfirmDark extends StatelessWidget {
  final String? title;
  final String? message;
  final String? textPositiveButton;
  final String? textNegativeButton;
  final Function onClickPositive;
  final Function? onClickNegative;
  final bool showBtnNegative;
  final TextStyle? negativeButtonStyle;
  final TextStyle? messageStyle;
  final TextStyle? titleStyle;
  final TextAlign? textAlignMessage;

  static Future<Object?> showSheet(
    BuildContext context, {
    String? title,
    String? message,
    String? textPositive,
    String? textNegative,
    required Function clickPositive,
    Function? clickNegative,
    bool showBtnNegative = true,
    TextStyle? styleNegativeButton,
    TextStyle? messageStyle,
    TextAlign? textAlignMessage,
    TextStyle? titleStyle,
    Key? key,
  }) {
    return showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: Duration(milliseconds: 500),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, animation1, animation2) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SheetAlertConfirmDark(
              title: title,
              message: message,
              textPositiveButton: textPositive,
              textNegativeButton: textNegative,
              onClickPositive: clickPositive,
              onClickNegative: clickNegative,
              showBtnNegative: showBtnNegative,
              negativeButtonStyle: styleNegativeButton,
              messageStyle: messageStyle,
              textAlignMessage: textAlignMessage,
              titleStyle: titleStyle,
              key: key,
            )
          ],
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOut,
          ).drive(Tween<Offset>(
            begin: Offset(0, -1.0),
            end: Offset.zero,
          )),
          child: child,
        );
      },
    );
  }

  SheetAlertConfirmDark({
    Key? key,
    this.title,
    required this.message,
    required this.textPositiveButton,
    this.textNegativeButton,
    required this.onClickPositive,
    this.onClickNegative,
    this.showBtnNegative = true,
    this.negativeButtonStyle,
    this.messageStyle,
    this.textAlignMessage,
    this.titleStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Utils.setScreeenResponsive(context: context);
    var textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: const BoxDecoration(
        color: ColorsApp.cinza,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            blurRadius: 64,
            color: ColorsApp.drop1,
            offset: Offset(0, -4),
          )
        ],
      ),
      padding: const EdgeInsets.only(
        top: 28,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      child: Column(
        // direction: Axis.vertical,
        // verticalDirection: VerticalDirection.down,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          if (title != null)
            Text(
              title ?? '',
              style: titleStyle ??
                  textTheme.titleMedium!
                      .copyWith(height: 1.0, color: ColorsApp.snow),
              textAlign: TextAlign.center,
            ),
          const SizedBox(height: 16),
          message != null
              ? Container(
                  margin: const EdgeInsets.only(bottom: 24),
                  child: Text(
                    message ?? '',
                    style: messageStyle ??
                        textTheme.bodyMedium!
                            .copyWith(color: ColorsApp.snow, fontSize: 15),
                    textAlign: textAlignMessage ?? TextAlign.start,
                  ),
                )
              : Container(),
          ButtonApp(
            text: textPositiveButton,
            onPress: () {
              pop();
              onClickPositive();
            },
          ),
          const SizedBox(height: 8),
          Visibility(
            visible: showBtnNegative,
            child: ButtonApp(
              buttonColor: Colors.white,
              text: textNegativeButton ?? I18n.of(context)!.nao,
              textStyle: negativeButtonStyle,
              onPress: () {
                pop(context);
                if (onClickNegative != null) onClickNegative!();
              },
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../navigation/navigator_app.dart';
import 'button_app.dart';
import 'sheet_positive_actions.dart';

abstract class ModalSheetsApp {
  static showDicasSelfie(BuildContext context, {Function? onPress}) {
    SheetPositiveActions.showSuggestionsSheet(
      context,
      titulo: const I18n().hora_da_foto,
      conteudo: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(const I18n().dica1_auto_retrato),
          const SizedBox(height: 8),
          Text(const I18n().dica2_auto_retrato),
          const SizedBox(height: 8),
          Text(const I18n().dica3_auto_retrato),
          const SizedBox(height: 8),
          Text(const I18n().dica4_auto_retrato),
          const SizedBox(height: 16),
          ButtonApp(
            width: MediaQuery.of(context).size.width,
            height: 50,
            border: 0,
            text: const I18n().continuar,
            onPress: () async {
              pop();
              if (onPress != null) onPress();
            },
          ),
        ],
      ),
      altura: 240,
    );
  }
}

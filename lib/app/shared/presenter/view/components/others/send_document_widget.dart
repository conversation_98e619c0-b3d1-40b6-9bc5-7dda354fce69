import 'package:flutter/material.dart';

import '../../../../themes/styles/colors_app.dart';
import '../../../../utils/utils.dart';
import '../../responsive_widgets/responsive_widgets.dart';
import 'alert_banner.dart';

class SendDocumentWidget extends StatelessWidget {
  const SendDocumentWidget(
      {this.titulo,
      this.enviado,
      this.enviando,
      this.enable = true,
      this.onTap,
      this.mensagemAlert,
      super.key});
  final String? titulo;
  // final Widget icone;
  final bool enable;
  final bool? enviado;
  final VoidCallback? onTap;
  final bool? enviando;
  final String? mensagemAlert;

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AlertBanner(
          isShow: mensagemAlert != null && mensagemAlert!.isNotEmpty,
          typeAlertBanner: TypeAlertBanner.info,
          error: null,
          message: mensagemAlert ?? '',
        ),
        GestureDetector(
          onTap: onTap,
          child: Card(
            margin: const EdgeInsets.only(top: 4, bottom: 4),
            elevation: 5,
            child: ContainerResponsive(
              height: 72,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    TextResponsive(
                      titulo ?? '',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: enable ? null : ColorsApp.disableTextColor,
                          ),
                    ),
                    (enviando != null && enviando!)
                        ? Utils.circularProgressButton(size: 20)
                        : Utils.buildIcDocumento(enviado ?? false, enable),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/services.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'pin_code_custom.dart';
import 'package:flutter/material.dart';

class EditPinApp extends StatefulWidget {
  final Function? onText;
  final bool isEditRegistration;
  final bool resetPin;

  const EditPinApp({
    super.key,
    this.onText,
    this.isEditRegistration = false,
    this.resetPin = false,
  });

  @override
  createState() => _EditPinAppState();
}

class _EditPinAppState extends State<EditPinApp> {
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _focusCode = FocusNode();

  Function? get onText => widget.onText;
  bool get isEdtConfirm => widget.isEditRegistration;
  bool get resetPin => widget.resetPin;
  set resetPin(bool reset) => widget.resetPin;

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);

    if (resetPin) {
      resetPin = false;

      _codeController.text = "";
    }
    return PinCodeAppCustom(
      autoFocus: true,
      controller: _codeController,
      onChanged: (value) async {
        await Future.delayed(const Duration(milliseconds: 350));
        if (value.isNotEmpty && value.length == 4) {
          if (onText != null) onText!(_codeController.text);
          if (isEdtConfirm) {
            _clearPin();
          }
        } else {
          // Utils.fieldFocusChange(context, _num4Focus, _num3Focus);
        }
      },
      focusNode: _focusCode,
      length: 4,
      obsecureText: true,
      animationType: AnimationType.fade,
      textStyle: Theme.of(context).textTheme.bodyLarge!.copyWith(fontSize: 24),
      animationDuration: const Duration(milliseconds: 200),
      pinTheme: PinTheme(
        borderColor: resetPin && _codeController.text != ""
            ? ColorsApp.error[300]
            : ColorsApp.cinza[500]!,
        activeColor: ColorsApp.cinza[500]!,
        shape: PinCodeFieldShape.underline,
        inactiveColor: ColorsApp.cinza[500]!,
        selectedColor: ColorsApp.cinza[500]!,
        selectedFillColor: ColorsApp.verde[500]!,
        activeFillColor: ColorsApp.verde[500]!,
        fieldHeight: 72,
        fieldWidth: 72,
      ),
      backgroundColor: Colors.transparent,
      textInputType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
    );
  }

  _clearPin() {
    _codeController.clear();
    _focusCode.requestFocus();
    setState(() {});
  }
}

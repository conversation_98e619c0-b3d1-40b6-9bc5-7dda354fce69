import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class ItemChannels extends StatelessWidget {
  const ItemChannels({
    this.title,
    this.communicationChannel,
    this.onTap,
    this.isFirst = false,
    this.isLast = false,
    super.key,
  });

  final String? title;
  final String? communicationChannel;
  final VoidCallback? onTap;
  final bool isFirst;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return GestureDetector(
      onTap: onTap,
      child: ContainerResponsive(
        margin: const EdgeInsets.only(bottom: 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft:
                isFirst ? const Radius.circular(10) : const Radius.circular(0),
            topRight:
                isFirst ? const Radius.circular(10) : const Radius.circular(0),
            bottomLeft:
                isLast ? const Radius.circular(10) : const Radius.circular(0),
            bottomRight:
                isLast ? const Radius.circular(10) : const Radius.circular(0),
          ),
        ),
        height: 72,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(title ?? '', style: Theme.of(context).textTheme.bodyMedium),
              Expanded(
                child: Text(
                  communicationChannel ?? '',
                  textAlign: TextAlign.end,
                  style: Theme.of(context).textTheme.bodyMedium,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../modules/help/bloc/help_bloc.dart';
import 'item_card_text.dart';

class CardButtonsSupport extends StatelessWidget {
  const CardButtonsSupport({
    super.key,
    required this.clickEmail,
  });

  final VoidCallback clickEmail;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ItemCardText(
          text: I18n.of(context)!.email,
          isPrimeiro: true,
          click: clickEmail,
        ),
        // Visibility(
        //   visible: !kReleaseMode && !Flavor.isProduction,
        //   child: ItemCardText(
        //     text: I18n.of(context)!.segunda_a_terca,
        //     click: () {
        //       BlocProvider.of<HelpBloc>(context).add(OpenCallPhoneSuport());
        //     },
        //   ),
        // ),
        // Visibility(
        //   visible: !kReleaseMode && !Flavor.isProduction,
        //   child: ItemCardText(
        //     isUltimo: true,
        //     text: I18n.of(context)!.whatsapp_horario,
        //     click: () {
        //       BlocProvider.of<HelpBloc>(context).add(OpenWhatsAppSuport());
        //     },
        //   ),
        // ),
      ],
    );
  }
}

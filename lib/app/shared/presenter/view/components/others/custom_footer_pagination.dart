import 'package:flutter/widgets.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../utils/utils.dart';

class CustomFooterPagination extends StatelessWidget {
  const CustomFooterPagination({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomFooter(builder: (context, mode) {
      Widget body;
      if (mode == LoadStatus.idle) {
        body = Container();
      } else if (mode == LoadStatus.loading) {
        body = Utils.circularProgressButton(size: 25);
      } else if (mode == LoadStatus.failed) {
        body = Container();
      } else if (mode == LoadStatus.canLoading) {
        body = Container();
      } else {
        body = Container();
      }
      return Container(
        height: 55.0,
        child: Center(child: body),
      );
    });
  }
}

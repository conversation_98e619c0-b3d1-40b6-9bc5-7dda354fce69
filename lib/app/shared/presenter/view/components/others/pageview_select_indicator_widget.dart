import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class PageviewSelectIndicatorWidget extends StatelessWidget {
  const PageviewSelectIndicatorWidget(
      {super.key, required this.length, required this.currentPage});
  final int length;
  final int currentPage;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(length, (int index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4.0),
            width: 8.0,
            height: 8.0,
            decoration: BoxDecoration(
              color: currentPage == index ? ColorsApp.verde : ColorsApp.cinza,
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }
}

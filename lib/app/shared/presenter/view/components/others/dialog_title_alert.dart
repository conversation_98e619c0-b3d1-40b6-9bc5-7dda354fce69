import '../../../../../../localization/generated/i18n.dart';
import '../../../../navigation/navigator_app.dart';
import '../../../../themes/styles/colors_app.dart';
import 'button_app.dart';

import 'package:flutter/material.dart';

class DialogTitleAlert extends StatelessWidget {
  final String title;
  final String mensagem;
  final Function? clickContinuar;
  final bool tituloCentralizado;

  static Future show({
    required BuildContext context,
    required String title,
    required String mensagem,
    Function? clickContinuar,
    bool tituloCentralizado = false,
  }) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return PopScope(
            canPop: false,
            child: DialogTitleAlert(
              title: title,
              tituloCentralizado: tituloCentralizado,
              mensagem: mensagem,
              clickContinuar: clickContinuar,
            ),
          );
        });
  }

  const DialogTitleAlert({
    super.key,
    this.title = '',
    this.mensagem = '',
    this.clickContinuar,
    this.tituloCentralizado = false,
  });

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Align(
      alignment: Alignment.center,
      child: Container(
        padding: const EdgeInsets.only(
          top: 24,
          bottom: 16,
          left: 16,
          right: 16,
        ),
        margin: const EdgeInsets.only(left: 16, right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: ColorsApp.drop1,
              blurRadius: 36,
              offset: Offset(0.0, 16.0),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: tituloCentralizado
              ? CrossAxisAlignment.center
              : CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              title,
              style: textTheme.titleMedium!.copyWith(
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 18),
            Text(
              mensagem,
              style: textTheme.bodyMedium,
            ),
            const SizedBox(height: 18),
            ButtonApp(
              text: I18n.of(context)!.ok,
              width: MediaQuery.of(context).size.width,
              onPress: () {
                pop();
                if (clickContinuar != null) clickContinuar!();
              },
            ),
          ],
        ),
      ),
    );
  }
}

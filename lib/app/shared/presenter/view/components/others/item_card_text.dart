import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

class ItemCardText extends StatelessWidget {
  final String? text;
  final Function? click;
  final bool isPrimeiro;
  final bool isUltimo;

  const ItemCardText({
    super.key,
    this.text,
    this.click,
    this.isPrimeiro = false,
    this.isUltimo = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (click != null) click!();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: isPrimeiro
                ? const Radius.circular(10)
                : const Radius.circular(0),
            topRight: isPrimeiro
                ? const Radius.circular(10)
                : const Radius.circular(0),
            bottomLeft:
                isUltimo ? const Radius.circular(10) : const Radius.circular(0),
            bottomRight:
                isUltimo ? const Radius.circular(10) : const Radius.circular(0),
          ),
        ),
        padding: const EdgeInsets.only(
          left: 16,
          top: 28,
          bottom: 28,
          right: 16,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: Text(
                text ?? '',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(width: 20),
            IconsApp.icArrowRight(),
          ],
        ),
      ),
    );
  }
}

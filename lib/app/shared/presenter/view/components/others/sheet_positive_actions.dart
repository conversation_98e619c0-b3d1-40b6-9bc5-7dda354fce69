import 'package:flutter/material.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../navigation/navigator_app.dart';
import '../../../../themes/styles/colors_app.dart';
import '../../../../utils/utils.dart';
import '../../responsive_widgets/responsive_widgets.dart';
import 'button_app.dart';

class SheetPositiveActions extends StatelessWidget {
  final String titulo;
  final String mensagem;
  final String? textPositiveButton;
  final String? textNegativeButton;
  final Function onClickPositive;
  final Function onClickNegative;
  final bool buttonNegative;

  static Future showSuggestionsSheet(
    BuildContext context, {
    required String titulo,
    required Widget conteudo,
    double? altura,
  }) {
    return showModalBottomSheet(
      context: context,
      builder: (context) {
        Utils.setScreeenResponsive(
          context: context,
          // height: MediaQuery.of(context).size.height,
          // width: MediaQuery.of(context).size.width,
        );

        return ContainerResponsive(
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          height: altura,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                titulo,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              SizedBoxResponsive(height: 16),
              conteudo,
            ],
          ),
        );
      },
    );
  }

  static showSheet(
    BuildContext context, {
    required String titulo,
    required String mensagem,
    required String textPositive,
    String? textNegative,
    required Function clickPositive,
    required Function clickNegative,
    bool buttonNegative = false,
  }) {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return SheetPositiveActions(
          titulo: titulo,
          mensagem: mensagem,
          textPositiveButton: textPositive,
          textNegativeButton: textNegative,
          onClickPositive: clickPositive,
          onClickNegative: clickNegative,
          buttonNegative: buttonNegative,
        );
      },
    );
  }

  const SheetPositiveActions({
    super.key,
    required this.titulo,
    required this.mensagem,
    required this.textPositiveButton,
    this.textNegativeButton,
    required this.onClickPositive,
    required this.onClickNegative,
    this.buttonNegative = false,
  });

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            blurRadius: 64,
            color: ColorsApp.drop1,
            offset: Offset(0, -4),
          )
        ],
      ),
      padding: const EdgeInsets.only(
        top: 30,
        left: 24,
        right: 24,
        bottom: 24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            titulo,
            style: textTheme.titleMedium!.copyWith(height: 1.0),
            textAlign: TextAlign.center,
          ),
          const SizedBox(
            height: 16,
          ),
          Text(
            mensagem,
            style: textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ButtonApp(
            text: textPositiveButton ?? I18n.of(context)!.sim,
            onPress: () {
              pop();
              onClickPositive();
            },
          ),
          const SizedBox(height: 8),
          ButtonApp(
            text: textNegativeButton ?? I18n.of(context)!.nao,
            buttonColor: buttonNegative ? Colors.white : null,
            onPress: () {
              pop(context);
              onClickNegative();
            },
          ),
        ],
      ),
    );
  }
}

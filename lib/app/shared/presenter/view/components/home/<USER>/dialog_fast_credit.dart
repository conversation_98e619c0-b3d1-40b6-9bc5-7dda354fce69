import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/bloc/fast_credit/fast_credit_bloc.dart';

import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>/dialog_fast_credit_unavailable.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../navigation/named_routes.dart';
import '../../../../../themes/styles/colors_app.dart';
import '../../../../../utils/utils.dart';
import '../../others/button_app.dart';
import '../../others/snack_bar_app.dart';

class DialogInfoFastCredit extends StatelessWidget {
  static show({
    required BuildContext context,
    // bool available = false,
  }) {
    bool? available = AppSession.getInstance().elegibleFastCredit;
    if (available != null) {
      showDialog(
        context: context,
        builder: (context) {
          return available
              ? const DialogInfoFastCredit()
              : const DialogInfoFastCreditUnavailable();
        },
        // barrierDismissible: true,
      );
    }
  }

  const DialogInfoFastCredit({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    BlocProvider.of<FastCreditBloc>(context).add(SimulateFastCreditEvent());
    return dialogContent(context);
  }

  dialogContent(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
      child: Stack(
        children: <Widget>[
          Align(
            alignment: Alignment.center,
            child: Container(
                padding: const EdgeInsets.only(
                  top: 24,
                  bottom: 24,
                  left: 24,
                  right: 24,
                ),
                margin: const EdgeInsets.only(top: 40, left: 16, right: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: ColorsApp.drop1,
                      blurRadius: 36,
                      offset: Offset(0.0, 16.0),
                    ),
                  ],
                ),
                child: Stack(
                  children: <Widget>[
                    _containerData(context),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  _containerData(BuildContext context) {
    return BlocConsumer<FastCreditBloc, FastCreditState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        if (state is FastCreditLoading) {
          return Container(
            height: 200,
            alignment: Alignment.center,
            child: CircularProgressIndicator(),
          );
        }
        if (state is FastCreditError) {
          return Container(
            height: 200,
            alignment: Alignment.center,
            child: Text('Erro ao carregar dados'),
          );
        }
        if (state is GetSimulationFastCreditSuccess) {
          final simulation = state.simulationResponse;
          return Column(
            mainAxisSize: MainAxisSize.min, // To make the card compact
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                const I18n().credito_rapido,
                textAlign: TextAlign.left,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge!
                    .copyWith(color: ColorsApp.cinza[900]),
              ),
              Text(
                const I18n().consignado,
                textAlign: TextAlign.left,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(fontSize: 11),
              ),
              const SizedBox(height: 32),
              Text(
                const I18n().msg_info_credito_rapido,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _fieldInfo(context,
                      title: const I18n().valor_credito,
                      value:
                          Utils.formatValueInstatement(simulation.valueCredit)),
                  _fieldInfo(context,
                      title: const I18n().taxas_de_juros,
                      value: I18n.of(context)!.taxaJuros(Utils.formatDecimal(
                          (simulation.interestRates) * 100)),
                      valueStrong: false),
                ],
              ),
              // const SizedBox(height: 32),
              const Divider(
                height: 32,
                thickness: 0.4,
              ),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: _fieldInfo(context,
                        title: const I18n().primeiro_vencimento,
                        value: Utils.formatDateFirstPayment(
                            dataApi: simulation.firstPaymentDate)),
                  ),
                  const VerticalDivider(
                    thickness: 1,
                    color: ColorsApp.cinza,
                  ),
                  _fieldInfo(
                    context,
                    title: const I18n().parcelas,
                    value: '${simulation.installmentsMany}x',
                  ),
                  const VerticalDivider(
                    thickness: 1,
                    color: ColorsApp.cinza,
                  ),
                  _fieldInfo(context,
                      title: const I18n().valor_mensal,
                      value:
                          Utils.formatValueInstatement(simulation.valueTotal)),
                ],
              ),
              // const SizedBox(height: 8),
              const Divider(
                height: 32,
                thickness: 0.4,
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: ButtonApp(
                      text: I18n.of(context)!.cancelar,
                      buttonColor: ColorsApp.bgDesativado,
                      onPress: () {
                        Navigator.pop(context);
                        // if (clickOk != null) clickOk!();
                      },
                    ),
                  ),
                  const SizedBox(width: 18),
                  Expanded(
                    child: ButtonApp(
                      text: I18n.of(context)!.continuar,
                      onPress: () async {
                        final political =
                            AppSession.getInstance().politicalFastCredit;

                        if (political?.installmentMany == null ||
                            political?.installmentMany == 0) {
                          SnackBarApp.showSnack(
                              context: context,
                              message: I18n.of(context)!.msg_error_limite,
                              success: false);
                        } else {
                          await push(
                            Routes.loanFastCredit,
                          );
                          pop();

                          // if (result != null && result) {
                          //   // refreshLoanHomePage();
                          //   pop();
                          // }
                        }
                        // await pushSlideResult(
                        //     context,
                        //     CreditoRapidoSimulacaoProvider(
                        //       emprestimoResponse: state.emprestimoResponse,
                        //       currentPage: state.currentPage ?? 0,
                        //       listDocumentoStatus:
                        //           state.listDocumentosStatus ?? [],
                        //     )).then((result) {
                        //   if (result != null && result) {
                        //     BlocProvider.of<HomeBloc>(context)
                        //         .add(InitHomeEvent());
                        //     pop(context);
                        //   }
                        // });

                        // if (clickOk != null) clickOk!();
                      },
                    ),
                  ),
                ],
              ),
            ],
          );
        }
        return const Center(
          child: Text('Erro ao carregar dados'),
        );
      },
    );
  }

  SizedBox _fieldInfo(BuildContext context,
      {String title = '', String value = '', bool valueStrong = true}) {
    return SizedBox(
      height: 56,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            title,
            // style: bodyText2(context),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 4),
            child: Text(
              value,
              // '${Utils.formataValorParcela(valor)}',
              style: valueStrong
                  ? Theme.of(context).textTheme.bodyLarge
                  : Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}

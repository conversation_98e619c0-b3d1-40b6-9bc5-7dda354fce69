import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/bloc/balance/balance_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../navigation/named_routes.dart';

class CardBalanceNoCredit extends StatelessWidget {
  const CardBalanceNoCredit({
    super.key,
    required this.showStatementButton,
  });

  final bool showStatementButton;

  @override
  Widget build(BuildContext context) {
    return _CardBalanceUser(
      key: key,
      showStatementButton: showStatementButton,
    );
  }
}

class _CardBalanceUser extends StatefulWidget {
  const _CardBalanceUser({
    super.key,
    required this.showStatementButton,
  });

  final bool showStatementButton;

  @override
  State<_CardBalanceUser> createState() => _CardBalanceUserState();
}

class _CardBalanceUserState extends State<_CardBalanceUser> {
  bool isBalanceVisible = false;

  double valueBalance = 0.0;

  bool get showStatementButton => widget.showStatementButton;

  @override
  initState() {
    super.initState();
    setSaldo();
    getUserBalance();
  }

  getUserBalance() {
    BlocProvider.of<BalanceBloc>(context).add(GetBalanceEvent());
  }

  @override
  Widget build(BuildContext context) {
    final themedata = Theme.of(context);

    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      // padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 00),
      child: Column(
        children: [
          _balanceWidget(context, themedata),
          Visibility(
              visible: showStatementButton,
              child: _statementButton(context, themedata)),
        ],
      ),
    );
  }

  setSaldo() async {
    isBalanceVisible = await StorageUtils.showBalance();
    setState(() {});
  }

  Widget balanceWidget(ThemeData themedata, double balance) {
    final value = Utils.formatBalance(balance);
    if (!isBalanceVisible) {
      return Container(
        width: 150,
        height: 32,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      );
    }
    return Text(
      value,
      style: themedata.textTheme.headlineSmall!.copyWith(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        // color: ColorsApp.verde[500],
      ),
    );
  }

  Widget _balanceWidget(BuildContext context, ThemeData themedata) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: 4,
            children: [
              Text(
                "${I18n.of(context)!.saldo_disponivel}: ",
                style: themedata.textTheme.bodySmall,
              ),
              BlocConsumer<BalanceBloc, BalanceState>(
                listener: (context, state) {
                  if (state is BalanceError) {
                    DialogUtils.showSnackError(context, state.error);
                  }
                  if (state is BalanceSuccess) {
                    setState(() {
                      valueBalance = state.balance.balance!;
                    });
                  }
                },
                builder: (context, state) {
                  if (state is BalanceSuccess) {
                    return balanceWidget(
                      themedata,
                      state.balance.balance!,
                    );
                  }
                  if (state is BalanceLoading) {
                    return loadingWidget();
                  }
                  return balanceWidget(
                    themedata,
                    0.0,
                  );
                },
              ),
            ],
          ),
          InkWell(
            onTap: () {
              setState(() {
                isBalanceVisible = !isBalanceVisible;
                StorageUtils.setShowBalance(show: isBalanceVisible);
              });
            },
            child: isBalanceVisible
                ? IconsApp.icVisualizarSaldoOn()
                : IconsApp.icVisualizarSaldoOff(),
          )
        ],
      ),
    );
  }

  Shimmer loadingWidget() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: 150,
        height: 32,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );
  }

  Widget _statementButton(BuildContext context, ThemeData themedata) {
    return SizedBox(
      width: double.maxFinite,
      height: 36,
      child: FilledButton(
        style: FilledButton.styleFrom(
          backgroundColor: ColorsApp.verde[500],
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
          ),
        ),
        onPressed: () {
          push(Routes.statement);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              I18n.of(context)!.ver_extrato,
              style: themedata.textTheme.labelLarge,
            ),
            IconsApp.icArrowRight()
          ],
        ),
      ),
    );
  }
}

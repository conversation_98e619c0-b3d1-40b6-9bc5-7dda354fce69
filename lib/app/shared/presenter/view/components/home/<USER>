import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/config/flavor.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/bloc/balance/balance_bloc.dart';
import 'package:siclosbank/app/shared/presenter/bloc/fast_credit/fast_credit_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>/dialog_fast_credit.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../navigation/named_routes.dart';
import '../../../../utils/image_utils.dart';

class CardBalanceWithCredit extends StatelessWidget {
  const CardBalanceWithCredit(
      {super.key,
      required this.showStatementButton,
      required this.compactWidget});

  final bool showStatementButton;
  final bool compactWidget;

  @override
  Widget build(BuildContext context) {
    return isHomologOrDev
        ? _CardBalanceUser(
            key: key,
            showStatementButton: showStatementButton,
            compactWidget: compactWidget,
          )
        : CardBalanceNoCredit(showStatementButton: showStatementButton);
  }
}

class _CardBalanceUser extends StatefulWidget {
  const _CardBalanceUser(
      {super.key,
      required this.showStatementButton,
      required this.compactWidget});

  final bool showStatementButton;
  final bool compactWidget;

  @override
  State<_CardBalanceUser> createState() => _CardBalanceUserState();
}

class _CardBalanceUserState extends State<_CardBalanceUser> {
  bool isBalanceVisible = false;
  double valueFastCredit = 0.0;
  double valueBalance = 0.0;
  bool isElegible = false;

  bool get showStatementButton => widget.showStatementButton;
  bool get compactWidget => widget.compactWidget;

  @override
  initState() {
    super.initState();
    setSaldo();
    getUserBalance();
  }

  getUserBalance() {
    BlocProvider.of<BalanceBloc>(context).add(GetBalanceEvent());
    BlocProvider.of<FastCreditBloc>(context).add(InitFetchEvent());
  }

  @override
  Widget build(BuildContext context) {
    final themedata = Theme.of(context);

    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      // padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 00),
      child: Column(
        children: [
          _balanceWidget(context, themedata),
          Divider(
            color: Colors.grey[300],
            height: 1,
          ),
          _fastCreditWidget(themedata),
          Visibility(
              visible: showStatementButton,
              child: _statementButton(context, themedata)),
        ],
      ),
    );
  }

  setSaldo() async {
    isBalanceVisible = await StorageUtils.showBalance();
    setState(() {});
  }

  Widget getBalanceWidget(ThemeData themedata, double balance) {
    final value = Utils.formatBalance(balance);
    if (!isBalanceVisible) {
      return Container(
        width: 49,
        height: 15,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      );
    }
    return Text(
      value,
      style: themedata.textTheme.displaySmall,
    );
  }

  Widget getCreditWidget(ThemeData themedata) {
    if (!isBalanceVisible) {
      return Container(
        width: 49,
        height: 15,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      );
    }
    return Text(
      Utils.formatBalance(isElegible ? valueFastCredit : 0),
      style: themedata.textTheme.displaySmall,
    );
  }

  Widget getTotalBalanceCreditWidget(ThemeData themedata) {
    final total = valueBalance + (isElegible ? valueFastCredit : 0);
    if (!isBalanceVisible) {
      return Container(
        width: 49,
        height: 15,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      );
    }
    final styleText = compactWidget
        ? themedata.textTheme.titleSmall
        : themedata.textTheme.displaySmall;

    return Text(
      Utils.formatBalance(total),
      style: styleText,
    );
  }

  Padding _fastCreditWidget(ThemeData themedata) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: BlocConsumer<FastCreditBloc, FastCreditState>(
        listener: (context, state) {
          if (state is FastCreditError) {
            DialogUtils.showSnackError(context, state.error);
          }
          if (state is GetValueFastCreditSuccess) {
            setState(() {
              valueFastCredit = state.value;
            });
          }
          if (state is GetElegibleFastCreditSuccess) {
            setState(() {
              isElegible = state.isElegible;
              AppSession.getInstance().elegibleFastCredit = isElegible;
            });
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Visibility(
                    visible: !compactWidget,
                    child: Container(
                      color: ColorsApp.verde[500],
                      padding: const EdgeInsets.all(2),
                      margin: const EdgeInsets.only(right: 6),
                      child: Text(
                        'NOVO',
                        style: themedata.textTheme.labelSmall!.apply(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  Text(
                    "${I18n.of(context)!.credito_rapido}: ",
                    style: themedata.textTheme.displaySmall,
                  ),
                  state is FastCreditLoading
                      ? loadingWidget()
                      : getCreditWidget(themedata),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: InkWell(
                        onTap: state is! FastCreditLoading
                            ? () {
                                DialogInfoFastCredit.show(context: context);
                              }
                            : null,
                        child: compactWidget
                            ? ImageUtils.icInfo(
                                color: Colors.black,
                              )
                            : Text(
                                I18n.of(context)!.saiba_mais,
                                style: themedata.textTheme.labelSmall!.apply(
                                  color: ColorsApp.colorInfoAlert,
                                ),
                              ),
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 8,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "${I18n.of(context)!.saldo_mais_credito_rapido}: ",
                    style: compactWidget
                        ? themedata.textTheme.titleSmall
                        : themedata.textTheme.displaySmall,
                  ),
                  state is FastCreditLoading
                      ? loadingWidget()
                      : getTotalBalanceCreditWidget(themedata)
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _balanceWidget(BuildContext context, ThemeData themedata) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "${I18n.of(context)!.saldo_em_conta}: ",
                style: themedata.textTheme.displaySmall,
              ),
              BlocConsumer<BalanceBloc, BalanceState>(
                listener: (context, state) {
                  if (state is BalanceError) {
                    DialogUtils.showSnackError(context, state.error);
                  }
                  if (state is BalanceSuccess) {
                    setState(() {
                      valueBalance = state.balance.balance!;
                    });
                  }
                },
                builder: (context, state) {
                  if (state is BalanceSuccess) {
                    return getBalanceWidget(
                      themedata,
                      state.balance.balance!,
                    );
                  }
                  if (state is BalanceLoading) {
                    return loadingWidget();
                  }
                  return getBalanceWidget(
                    themedata,
                    0.0,
                  );
                },
              ),
            ],
          ),
          InkWell(
            onTap: () {
              setState(() {
                isBalanceVisible = !isBalanceVisible;
                StorageUtils.setShowBalance(show: isBalanceVisible);
              });
            },
            child: isBalanceVisible
                ? IconsApp.icVisualizarSaldoOn()
                : IconsApp.icVisualizarSaldoOff(),
          )
        ],
      ),
    );
  }

  Shimmer loadingWidget() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: 49,
        height: 15,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );
  }

  Widget _statementButton(BuildContext context, ThemeData themedata) {
    return SizedBox(
      width: double.maxFinite,
      height: 36,
      child: FilledButton(
        style: FilledButton.styleFrom(
          backgroundColor: ColorsApp.verde[500],
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
          ),
        ),
        onPressed: () {
          push(Routes.statement);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              I18n.of(context)!.ver_extrato,
              style: themedata.textTheme.labelLarge,
            ),
            IconsApp.icArrowRight()
          ],
        ),
      ),
    );
  }
}

import 'dart:ui';
import 'package:flutter/material.dart';
import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../themes/styles/colors_app.dart';
import '../../others/button_app.dart';

class DialogInfoFastCreditUnavailable extends StatelessWidget {
  static showDialogSucesso({
    required BuildContext context,
  }) {
    showDialog(
      context: context,
      builder: (context) {
        return const DialogInfoFastCreditUnavailable();
      },
      barrierDismissible: false,
    );
  }

  const DialogInfoFastCreditUnavailable({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return dialogContent(context);
  }

  dialogContent(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
      child: Stack(
        children: <Widget>[
          Align(
            alignment: Alignment.center,
            child: Container(
                padding: const EdgeInsets.only(
                  top: 24,
                  bottom: 24,
                  left: 24,
                  right: 24,
                ),
                margin: const EdgeInsets.only(top: 40, left: 16, right: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: ColorsApp.drop1,
                      blurRadius: 36,
                      offset: Offset(0.0, 16.0),
                    ),
                  ],
                ),
                child: Stack(
                  children: <Widget>[
                    _containerData(context),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  _containerData(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min, // To make the card compact
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Text(
          I18n.of(context)!.credito_rapido,
          textAlign: TextAlign.left,
          style: Theme.of(context)
              .textTheme
              .titleLarge!
              .copyWith(color: ColorsApp.cinza[900]),
        ),
        Text(
          I18n.of(context)!.consignado,
          textAlign: TextAlign.left,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11),
        ),
        const SizedBox(height: 32),
        Text(
          I18n.of(context)!.msg_info_credito_rapido_indisponivel,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 32),
        ButtonApp(
          text: I18n.of(context)!.fechar,
          onPress: () {
            Navigator.pop(context);
            // if (clickOk != null) clickOk!();
          },
        ),
      ],
    );
  }
}

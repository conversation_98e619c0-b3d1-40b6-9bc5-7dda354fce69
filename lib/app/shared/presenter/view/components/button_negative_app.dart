import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class ButtonNegativeApp extends StatelessWidget {
  double width;
  String text;
  bool enabled;
  VoidCallback? onPress;
  Widget? progress;

  ButtonNegativeApp(
      {super.key,
      this.width = 0,
      this.text = '',
      this.enabled = true,
      this.onPress,
      this.progress});

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return SizedBox(
      width: width > 0 ? width : null,
      height: 48,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(
              enabled ? Colors.white : ColorsApp.cinza[100]),
          elevation: const WidgetStatePropertyAll(0),
          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(
                color: ColorsApp.cinza[900]!,
                width: 1,
              ))),
        ),
        onPressed: enabled ? (onPress ?? () {}) : () {},
        // color: Colors.white,
        // elevation: 0,
        child: progress ??
            Text(
              text,
              textAlign: TextAlign.center,
              style: textTheme.bodyLarge!.copyWith(
                color: ColorsApp.cinza[900],
                height: 1.0,
              ),
            ),
        // disabledColor: ColorsApp.cinza[100],
      ),
    );
  }
}

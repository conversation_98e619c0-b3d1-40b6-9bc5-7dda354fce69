import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/button_negative_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class SheetCameraGallery extends StatelessWidget {
  final Function? onClickGallery;
  final Function? onClickCamera;

  const SheetCameraGallery({
    super.key,
    this.onClickGallery,
    this.onClickCamera,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                )
              ],
            ),
            padding: const EdgeInsets.only(
              top: 24,
              left: 24,
              right: 24,
              bottom: 24,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      child: ButtonNegativeApp(
                        text: I18n.of(context)!.galeria_fotos,
                        onPress: () {
                          Navigator.pop(context);
                          if (onClickGallery != null) onClickGallery!();
                        },
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: ButtonNegativeApp(
                        text: I18n.of(context)!.camera,
                        onPress: () {
                          Navigator.pop(context);
                          if (onClickCamera != null) onClickCamera!();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:io';
import 'dart:typed_data';

import 'package:path_provider/path_provider.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';
import 'package:siclosbank/app/shared/data/datasource/app_datasource.dart';
import 'package:siclosbank/app/shared/data/datasource/endpoints/endpoints.dart';

import '../../constants/credit_type_enum.dart';

class AppDatasourceImpl implements IAppDatasource {
  final IClient _client;
  AppDatasourceImpl(this._client);

  @override
  Future<ApiResponse> getTermsApp(String type) async {
    var result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.getTerms(type),
    );

    return result;
  }

  @override
  Future<ApiResponse> getCollaborator(String cpf) async {
    var result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.collaborators(cpf),
      // queryParameters: {'cpf': cpf},
    );
    return result;
  }

  @override
  Future<ApiResponse> getBalance(String cpf) async {
    var result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.balance(cpf),
    );

    return result;
  }

  @override
  Future<ApiResponse> getPoliticalCredit(CreditType creditType) async {
    var result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.politicalCredit(creditType.description),
    );

    return result;
  }

  @override
  Future<ApiResponse> getElegibleFastCreditUser(String cpf) async {
    final result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.elegibleFastCredit(cpf),
    );

    return result;
  }

  @override
  Future<ApiResponse> getUserByCpf(String cpf) async {
    final result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.getUserByCpf(cpf),
    );
    return result;
  }

  @override
  Future<ApiResponse> getTimeSession() async {
    final result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.getTimeSession(),
    );
    return result;
  }

  @override
  Future<ApiResponse> getBankCode() async {
    var result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.getBankCode(),
    );
    return result;
  }

  @override
  Future<ApiResponse> checkCodeDeleteDevice(
    String deviceId,
    String deleteCode,
  ) async {
    var result = _client.fetch(
      method: 'POST',
      path: AppEndpoints.checkCodeDeleteDevice,
      data: {"device_id": deviceId, "delete_code": deleteCode},
    );

    return result;
  }

  @override
  Future<ApiResponse> checkCodeValidateDevice(
      {required String userId,
      required String code,
      required String token}) async {
    final Map<String, dynamic> body = {
      'id': userId,
      'validate_device_code': code,
      'tokenFCM': token,
    };

    final result = await _client.fetch(
      method: 'POST',
      path: AppEndpoints.checkCodeValidateDevice,
      data: body,
    );
    return result;
  }

  @override
  Future<ApiResponse> sendCheckDeviceCode(
      {required bool isEmail, required String userId}) async {
    final Map<String, dynamic> body =
        isEmail ? {'email': true, 'sms': false} : {'email': false, 'sms': true};
    body['id'] = userId;

    return await _client.fetch(
      method: 'POST',
      path: AppEndpoints.sendCheckDeviceCode,
      data: body,
    );
  }

  @override
  Future<ApiResponse> simulateFastCredit(String userId) async {
    final result = await _client.fetch(
      method: 'POST',
      path: AppEndpoints.simulateFastCredit(userId),
    );

    return result;
  }

  @override
  Future<ApiResponse> getImageProfile() async {
    final result = await _client.fetch(
      method: 'GET',
      path: AppEndpoints.getImageProfile,
      responseType: 'bytes',
      headers: {
        'content-type': 'application/octet-stream',
        'Accept': 'application/json',
      },
      dataType: DataTypes.none,
    );

    return result;
  }

  @override
  Future<ApiResponse> getAddress(String userId) async {
    final path = AppEndpoints.address(userId);

    final result = await _client.fetch(
      method: 'GET',
      path: path,
    );

    return result;
  }
}

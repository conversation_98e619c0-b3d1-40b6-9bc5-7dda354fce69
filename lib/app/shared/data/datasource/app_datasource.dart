import 'package:siclosbank/app/shared/data/client/api_response.dart';
import '../../constants/credit_type_enum.dart';

abstract class IAppDatasource {
  Future<ApiResponse> getTermsApp(String type);
  Future<ApiResponse> getCollaborator(String cpf);
  Future<ApiResponse> getBalance(String cpf);
  Future<ApiResponse> getPoliticalCredit(CreditType creditType);
  Future<ApiResponse> getElegibleFastCreditUser(String cpf);
  Future<ApiResponse> simulateFastCredit(String userId);
  Future<ApiResponse> getUserByCpf(String cpf);
  Future<ApiResponse> getTimeSession();

  Future<ApiResponse> getBankCode();
  Future<ApiResponse> checkCodeDeleteDevice(String deviceId, String deleteCode);
  Future<ApiResponse> checkCodeValidateDevice(
      {required String userId, required String code, required String token});

  Future<ApiResponse> sendCheckDeviceCode(
      {required bool isEmail, required String userId});

  Future<ApiResponse> getImageProfile();

  Future<ApiResponse> getAddress(String userId);
}

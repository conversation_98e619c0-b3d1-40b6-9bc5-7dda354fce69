abstract class AppEndpoints {
  static String getTerms(String type) => '/user/term/$type';
  static String collaborators(String cpf) => '/colaborator/find-by-cpf/$cpf';
  static String balance(String cpf) => "/user/get-account-balance/$cpf";
  static String politicalCredit(String creditType) =>
      '/credit/political/$creditType';
  static String elegibleFastCredit(String cpf) =>
      '/credit/elegible_credit_fast/$cpf';
  static String simulateFastCredit(String userId) =>
      '/credit/simulation_credit_speed/$userId';
  static String getUserByCpf(String cpf) => "/user/find-by-cpf/$cpf";

  static String getTimeSession() => "/config-app/time-away";

  static String getBankCode() => "/transfer_bank/get-isbp-bank";
  static String checkCodeDeleteDevice = "/user_device/delete-device";
  static const String checkCodeValidateDevice =
      "/user_device/validate-code-device";

  static const String sendCheckDeviceCode =
      "/user_device/send-code-to-validate-device";

  static const String getImageProfile = "/user/image-profile";

  static address(String userId) => '/user/address/$userId';
}

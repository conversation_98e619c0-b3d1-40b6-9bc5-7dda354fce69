import 'package:flutter/foundation.dart' show FlutterError;
import 'package:newrelic_mobile/config.dart';
import 'package:newrelic_mobile/newrelic_mobile.dart';
import 'package:newrelic_mobile/newrelic_navigation_observer.dart';

import '../../../config/environment.dart';
import 'new_relic.dart';

class NewRelicService implements NewRelic {
  @override
  Future<void> start() async {
    FlutterError.onError = NewrelicMobile.onError;
    Config config = Config(accessToken: Environment.newRelicToken);
    await NewrelicMobile.instance.startAgent(config);
  }

  @override
  void recordError(Object error, StackTrace stackTrace) {
    NewrelicMobile.instance.recordError(error, stackTrace);
  }

  @override
  observer() {
    return NewRelicNavigationObserver();
  }
}

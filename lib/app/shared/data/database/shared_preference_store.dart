import 'dart:async';
import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'storage_adapter.dart';

/// Instancia se inicia quando algum dos metodos sao chamados
class SharedPreferenceStore implements IStorageAdapter {
  SharedPreferences? _instance;

  FutureOr<SharedPreferences> _get() async {
    if (_instance != null) {
      return _instance!;
    } else {
      _instance = await SharedPreferences.getInstance();
      return _instance!;
    }
  }

  @override
  Future delete(String key) async {
    var shared = await _get();

    shared.remove(key);
  }

  @override
  Future deleteAll() async {
    var shared = await _get();

    shared.clear();
  }

  @override
  Future get(String key) async {
    var shared = await _get();
    var result = shared.get(key);
    return result;
  }

  @override
  Future put(String key, dynamic value) async {
    var shared = await _get();

    if (value is bool) {
      return await shared.setBool(key, value);
    } else if (value is int) {
      return await shared.setInt(key, value);
    } else if (value is double) {
      return await shared.setDouble(key, value);
    } else if (value is String) {
      return await shared.setString(key, value);
    } else if ((value is List) || (value is Map)) {
      return await shared.setString(key, json.encode(value));
    } else {
      throw ('Invalid put $value to $key');
    }
  }

  @override
  Future<void> setThemeKey(String brightness) async {
    var shared = await _get();

    // final String themValue = brightness == Brightness.light ? 'light' : 'dark';

    shared.setString("THEME_SICLOS", brightness);
  }

  @override
  Future<String> getThemeKey() async {
    var shared = await _get();

    return shared.get("THEME_SICLOS") != null
        ? shared.get("THEME_SICLOS").toString()
        : '';
  }

  @override
  Future insert(String key, dynamic value) async {
    var shared = await _get();

    if (value is bool) {
      return shared.setBool(key, value);
    } else if (value is int) {
      return shared.setInt(key, value);
    } else if (value is double) {
      return shared.setDouble(key, value);
    } else if (value is String) {
      return shared.setString(key, value);
    } else if ((value is List) || (value is Map)) {
      String newValues = jsonEncode(value);
      return shared.setString(key, newValues);
    } else {
      throw Exception();
    }
    // shared.setString(tableName, json.encode(listValues));
  }

  @override
  Future getAll({String table = ''}) async {
    var shared = await _get();

    return shared.get(table);
  }

  @override
  Future insertOrUpdate(String key, dynamic value) async {}
}

import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/data/database/app_database.dart';
import 'package:siclosbank/app/shared/data/database/storage_adapter.dart';

class AppDatabase implements IAppDatabase {
  final IStorageAdapter storage;

  AppDatabase({required this.storage});

  @override
  Future getEnableIdDigitalLogin() async {
    return await storage.get(LoginDigital.IS_ENABLED);
  }

  @override
  Future<void> cleanLoginDigital() async {
    await storage.delete(LoginDigital.KEY);
    await storage.delete(LoginDigital.LAST_LOGIN);
    await storage.delete(LoginDigital.LAST_SENHA);
    await storage.delete(LoginDigital.SHOW_SHEET);
    await storage.put(LoginDigital.IS_ENABLED, false);
  }

  @override
  Future<void> setEnableIdDigitalLogin(bool newValue) async {
    await storage.put(LoginDigital.IS_ENABLED, newValue);
  }
}

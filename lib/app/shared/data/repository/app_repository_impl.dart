import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:path_provider/path_provider.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/database/app_database.dart';
import 'package:siclosbank/app/shared/data/datasource/app_datasource.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/collaborator_response.dart';
import 'package:siclosbank/app/shared/data/models/terms/termo_response.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/domain/repository/app_repository.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';
import 'package:siclosbank/app/shared/presenter/bloc/terms/enums/type_terms.dart';

import '../../constants/credit_type_enum.dart';
import '../models/loan/simulation_response.dart';
import '../models/wallet/balance_response.dart';
import '../models/wallet/bank_model.dart';
import '../models/wallet/political_credit_response.dart';

class AppRepository implements IAppRepository {
  final IAppDatasource _datasource;
  final IAppDatabase _database;

  AppRepository(this._datasource, this._database);

  @override
  Future<TermResponse> getTerm(TyperTerms type) async {
    try {
      ApiResponse response = await _datasource.getTermsApp(type.label);

      if (response.statusCode == 200) {
        TermResponse termResponse = TermResponse.fromMap(response.data);
        return termResponse;
      } else {
        throw await ServerErrorHandling.handleError(response.data);
      }
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<CollaboratorResponse> getCollaborator(String cpf) async {
    try {
      ApiResponse response = await _datasource.getCollaborator(cpf);
      final result = CollaboratorResponse.fromMap(response.data);
      return result;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<BalanceResponse> getUserBalance(String cpf) async {
    try {
      final result = await _datasource.getBalance(cpf);
      if (result.statusCode == 204) {
        return const BalanceResponse(balance: 0);
      } else {
        return BalanceResponse(balance: (result.data as num).toDouble());
      }
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<PoliticalCreditResponse> getPoliticalCredit(
    CreditType creditType,
  ) async {
    try {
      final result = await _datasource.getPoliticalCredit(creditType);
      return PoliticalCreditResponse.fromMap(result.data);
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<bool> getElegibleFastCreditUser(String cpf) async {
    try {
      final result = await _datasource.getElegibleFastCreditUser(cpf);

      return result.data['success'] as bool;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<SimulationResponse> simulateFastCreditUser({
    required String userId,
  }) async {
    try {
      final result = await _datasource.simulateFastCredit(userId);
      log(result.data.toString());
      return SimulationResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<User> getUserByCpf(String cpf) async {
    try {
      final result = await _datasource.getUserByCpf(cpf);
      return User.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<int> getTimeSession() async {
    try {
      final result = await _datasource.getTimeSession();

      return result.data['timeAway'] as int;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<BanksModel>> getBankCodeList() async {
    try {
      final result = await _datasource.getBankCode();
      final banks = result.data as List;

      return banks.map((bank) => BanksModel.fromMap(bank)).toList();
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> checkCodeDeleteDevice(String deviceId, String deleteCode) async {
    try {
      ApiResponse response = await _datasource.checkCodeDeleteDevice(
        deviceId,
        deleteCode,
      );

      // return response.data;
      return response.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> checkCodeRegisterDevice({
    required String userId,
    required String code,
    required String tokenFCM,
  }) async {
    try {
      final result = await _datasource.checkCodeValidateDevice(
        userId: userId,
        code: code,
        token: tokenFCM,
      );
      return result.data;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<bool> sendCheckDeviceCode({
    required String userId,
    required bool isEmail,
  }) async {
    try {
      final result = await _datasource.sendCheckDeviceCode(
        userId: userId,
        isEmail: isEmail,
      );
      return result.data;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<bool> getEnableIdDigitalLogin() async {
    final status = await _database.getEnableIdDigitalLogin();

    if (status == null) {
      return false;
    } else if (status is bool) {
      final newStatus = status;
      return newStatus;
    } else {
      return false;
    }
  }

  @override
  Future<bool> cleanLoginDigital() async {
    try {
      final status = await _database.cleanLoginDigital();
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setEnableIdDigitalLogin(bool newValue) async {
    try {
      await _database.setEnableIdDigitalLogin(newValue);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<File> getImageProfile() async {
    try {
      final result = await _datasource.getImageProfile();
      return uint8ListToFile(result.data, "profile");
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<AddressResponse> getAddress(String userId) async {
    final result = await _datasource.getAddress(userId);
    return AddressResponse.fromMap(result.data);
  }
}

Future<File> uint8ListToFile(Uint8List data, String filename) async {
  final Directory tempDir = await getTemporaryDirectory();
  final File file = File('${tempDir.path}/$filename');
  await file.writeAsBytes(data);
  return file;
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:siclosbank/app/shared/data/models/loan/loan_status_response.dart';

import '../../constants/stage_register_user.dart';

class User {
  String? id;
  String? name;
  String? cpf;
  DateTime? dateOfBirth;
  String? civilStatus;
  String? motherName;
  String? politicalExposure;
  int? monthlyIncome;
  String? email;
  String? phone;
  String? socialName;
  String? occupation;
  bool? activeUser;
  bool? linkedTel;
  bool? blocked;
  bool? investor;
  String? pin;
  String? profileImage;
  int? numberWrongPinAttempt;
  StageEnum? stage;
  bool? termsOfUseAccepted;
  DateTime? dateAcceptedTerms;
  DateTime? createdAt;
  DateTime? updatedAt;
  LoanStatusResponse? loanStatusResponse;
  bool? viewOnboardingHomePix;
  bool? viewOnboardingKeysPix;

  User({
    this.id,
    this.name,
    this.cpf,
    this.dateOfBirth,
    this.civilStatus,
    this.motherName,
    this.politicalExposure,
    this.monthlyIncome,
    this.email,
    this.phone,
    this.socialName,
    this.occupation,
    this.activeUser,
    this.linkedTel,
    this.blocked,
    this.investor,
    this.pin,
    this.profileImage,
    this.numberWrongPinAttempt,
    this.stage,
    this.termsOfUseAccepted,
    this.dateAcceptedTerms,
    this.createdAt,
    this.updatedAt,
    this.viewOnboardingHomePix,
    this.viewOnboardingKeysPix,
  });

  factory User.fromMap(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String?,
      name: json['name'] as String?,
      cpf: json['cpf'] as String?,
      dateOfBirth: json['date_of_birth'] == null
          ? null
          : DateTime.parse(json['date_of_birth'] as String),
      civilStatus: json['civil_status'] as String?,
      motherName: json['mother_name'] as String?,
      politicalExposure: json['political_exposure'] as String?,
      monthlyIncome: json['monthly_income'] as int?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      socialName: json['social_name'] as String?,
      occupation: json['occupation'] as String?,
      activeUser: json['active_user'] as bool?,
      linkedTel: json['linked_tel'] as bool?,
      blocked: json['blocked'] as bool?,
      investor: json['investor'] as bool?,
      pin: json['pin'] as String?,
      profileImage: json['profile_image'] as String?,
      numberWrongPinAttempt: json['number_wrong_pin_attempt'] as int?,
      stage: StageEnum.getByName(json['stage'] as String?),
      termsOfUseAccepted: json['terms_of_use_accepted'] as bool?,
      dateAcceptedTerms: json['date_accepted_terms'] == null
          ? null
          : DateTime.parse(json['date_accepted_terms'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      viewOnboardingHomePix: json['view_screen_pix'] as bool?,
      viewOnboardingKeysPix: json['view_screen_keys_pix'] as bool?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'cpf': cpf,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'civil_status': civilStatus,
      'mother_name': motherName,
      'political_exposure': politicalExposure,
      'monthly_income': monthlyIncome,
      'email': email,
      'phone': phone,
      'social_name': socialName,
      'occupation': occupation,
      'active_user': activeUser,
      'linked_tel': linkedTel,
      'blocked': blocked,
      'investor': investor,
      'pin': pin,
      'profile_image': profileImage,
      'number_wrong_pin_attempt': numberWrongPinAttempt,
      'stage': stage?.label,
      'terms_of_use_accepted': termsOfUseAccepted,
      'date_accepted_terms': dateAcceptedTerms?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'view_screen_pix': viewOnboardingHomePix,
      'view_screen_keys_pix': viewOnboardingKeysPix,
    };
  }

  String toJson() => json.encode(toMap());

  factory User.fromJson(String source) =>
      User.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'User(id: $id, name: $name, cpf: $cpf, dateOfBirth: $dateOfBirth, civilStatus: $civilStatus, motherName: $motherName, politicalExposure: $politicalExposure, monthlyIncome: $monthlyIncome, email: $email, phone: $phone, socialName: $socialName, occupation: $occupation, activeUser: $activeUser, linkedTel: $linkedTel, blocked: $blocked, investor: $investor, pin: $pin profileImage: $profileImage, numberWrongPinAttempt: $numberWrongPinAttempt, stage: $stage, termsOfUseAccepted: $termsOfUseAccepted, dateAcceptedTerms: $dateAcceptedTerms, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'authorize_device_response.g.dart';

@JsonSerializable()
class AuthorizeDeviceResponse {
  String? id;
  String? email;
  String? phone;
  bool? pin;

  AuthorizeDeviceResponse({this.id, this.email, this.phone, this.pin});

  factory AuthorizeDeviceResponse.fromJson(Map<String, dynamic> json) =>
      _$AutorizarResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AutorizarResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

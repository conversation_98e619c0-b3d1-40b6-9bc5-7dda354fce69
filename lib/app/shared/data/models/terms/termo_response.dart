// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class TermResponse extends Equatable {
  final String id;
  final String content;

  TermResponse({
    required this.id,
    required this.content,
  });

  factory TermResponse.fromMap(Map<String, dynamic> json) => TermResponse(
        id: json['id'] ?? "",
        content: json['content'] ?? "",
      );

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'content': content,
    };
  }

  @override
  String toString() {
    return json.encode(toJson());
  }

  @override
  List<Object> get props => [id, content];

  TermResponse copyWith({
    String? id,
    String? content,
  }) {
    return TermResponse(
      id: id ?? this.id,
      content: content ?? this.content,
    );
  }

  String toJson() => json.encode(toMap());

  factory TermResponse.fromJson(String source) =>
      TermResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;
}

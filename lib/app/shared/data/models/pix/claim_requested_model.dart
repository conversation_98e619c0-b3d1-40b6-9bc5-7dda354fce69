import 'dart:convert';

import 'package:siclosbank/app/modules/pix/models/enums/key_types_enum.dart';

import '../../../../modules/pix/models/enums/claim_type_enum.dart';
import '../../../../modules/pix/models/enums/status_claim_enum.dart';

class ClaimRequestedModel {
  final String id;
  final ClaimType claimType;
  final String key;
  final KeyType keyType;
  final String donorParticipant;
  final StatusClaim status;
  final DateTime createdAt;
  final DateTime completionPeriodEnd;
  final DateTime resolutionPeriodEnd;
  final DateTime updatedAt;
  final String? donorDocument;
  final String? claimerDocument;
  final String? claimerUserId;
  final String? donorUserId;

  ClaimRequestedModel({
    required this.id,
    required this.claimType,
    required this.key,
    required this.keyType,
    required this.donorParticipant,
    required this.status,
    required this.createdAt,
    required this.completionPeriodEnd,
    required this.resolutionPeriodEnd,
    required this.updatedAt,
    required this.donorDocument,
    required this.claimerDocument,
    required this.claimerUserId,
    required this.donorUserId,
  });

  factory ClaimRequestedModel.fromMap(Map<String, dynamic> json) {
    return ClaimRequestedModel(
      id: json['id'] as String,
      claimerUserId: json['claimer_user_id'] as String?,
      donorUserId: json['donor_user_id'] as String?,
      claimType:
          ClaimType.fromString((json['claim_type'] ?? json['claimType'])),
      key: json['key'] as String,
      keyType: KeyType.fromString((json['type_key'] ?? json['keyType'])),
      donorParticipant: (json['donor_participant'] ?? json['donorParticipant']),
      status: StatusClaim.fromString(json['status']),
      createdAt:
          DateTime.parse((json['created_at'] ?? json['createTimestamp'])),
      completionPeriodEnd: DateTime.parse(
          json['completion_period_end'] ?? json['completionPeriodEnd']),
      resolutionPeriodEnd: DateTime.parse(
          json['resolution_period_end'] ?? json['resolutionPeriodEnd']),
      updatedAt: DateTime.parse(json['updated_at'] ?? json['lastModified']),
      donorDocument:
          (json['donor_account'] ?? json['donorAccount'])?['taxId'] as String?,
      claimerDocument: json['claimer']?['taxId'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'claimType': claimType.name,
      'key': key,
      'keyType': keyType.name,
      'donorParticipant': donorParticipant,
      'status': status.name,
      'createTimestamp': createdAt.toIso8601String(),
      'completionPeriodEnd': completionPeriodEnd.toIso8601String(),
      'resolutionPeriodEnd': resolutionPeriodEnd.toIso8601String(),
      'lastModified': updatedAt.toIso8601String(),
      'donorDocument': donorDocument,
      'claimerDocument': claimerDocument,
    };
  }

  String toJson() => json.encode(toMap());

  factory ClaimRequestedModel.fromJson(String source) =>
      ClaimRequestedModel.fromMap(json.decode(source) as Map<String, dynamic>);

  get isOpen => status == StatusClaim.OPEN;

  bool isDonor(String document) {
    return donorDocument == document;
  }

  bool isClaimer(String document) {
    return claimerDocument == document;
  }

  bool isClaimerById(String id) {
    return claimerUserId == id;
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import '../../../../modules/pix/models/enums/cancel_reason_enum.dart';
import '../../../../modules/pix/models/enums/claim_type_enum.dart';
import '../../../../modules/pix/models/enums/key_types_enum.dart';

class CancelClaimModel {
  final String id;
  final ClaimType claimType;
  final String key;
  final KeyType keyType;
  final CancelReason cancelReason;

  CancelClaimModel({
    required this.id,
    required this.claimType,
    required this.key,
    required this.keyType,
    required this.cancelReason,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'claimType': claimType.name,
      'key': key,
      'keyType': keyType.name,
      'cancelReason': cancelReason.name,
    };
  }

  factory CancelClaimModel.fromMap(Map<String, dynamic> map) {
    return CancelClaimModel(
      id: map['id'] as String,
      claimType: ClaimType.fromString(map['claimType'] as String),
      key: map['key'] as String,
      keyType: KeyType.fromString(map['keyType'] as String),
      cancelReason: CancelReason.fromString(map['cancelReason'] as String),
    );
  }

  String toJson() => json.encode(toMap());

  factory CancelClaimModel.fromJson(String source) =>
      CancelClaimModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

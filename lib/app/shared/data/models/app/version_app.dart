import 'dart:convert';

class VersionApp {
  String? latestVersionRelease;
  String? currentVersionApp;
  String? minVersion;
  bool? canUpdate;

  VersionApp({
    this.latestVersionRelease,
    this.currentVersionApp,
    this.minVersion,
    this.canUpdate,
  });

  @override
  String toString() {
    return 'VersionApp(latestVersionRelease: $latestVersionRelease, currentVersionApp: $currentVersionApp, minVersion: $minVersion, canUpdate: $canUpdate)';
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'latestVersionRelease': latestVersionRelease,
      'currentVersionApp': currentVersionApp,
      'minVersion': minVersion,
      'canUpdate': canUpdate,
    };
  }

  factory VersionApp.fromMap(Map<String, dynamic> map) {
    return VersionApp(
      latestVersionRelease: map['latestVersionRelease'] != null
          ? map['latestVersionRelease'] as String
          : null,
      currentVersionApp: map['currentVersionApp'] != null
          ? map['currentVersionApp'] as String
          : null,
      minVersion:
          map['minVersion'] != null ? map['minVersion'] as String : null,
      canUpdate: map['canUpdate'] != null ? map['canUpdate'] as bool : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory VersionApp.fromJson(String source) =>
      VersionApp.fromMap(json.decode(source) as Map<String, dynamic>);
}

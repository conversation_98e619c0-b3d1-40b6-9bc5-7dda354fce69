// ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'dart:convert';

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:siclosbank/app/shared/data/models/user_response.dart';

@JsonSerializable()
class TokenResponse {
  @JsonKey(name: "token")
  String? token;
  @Json<PERSON>ey(name: "user")
  User? user;
  TokenResponse({
    this.token,
    this.user,
  });

  factory TokenResponse.fromMap(Map<String, dynamic> json) {
    return TokenResponse(
      token: json['token'] as String?,
      user: json['user'] == null ? null : User.fromMap(json['user']),
    );
  }

  Map<String, dynamic> toMap() => {
        'token': token,
        'user': user?.toMap(),
      };

  String toJson() => json.encode(toMap());

  factory TokenResponse.fromJson(String source) =>
      TokenResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => 'TokenResponse(token: $token, user: $user)';
}

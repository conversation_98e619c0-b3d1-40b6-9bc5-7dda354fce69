// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:string_validator/string_validator.dart';

@JsonSerializable()
class CollaboratorResponse {
  String? id;
  String? relatedCode;
  String? codeSession;
  String? coligadaName;
  String? pisPasep;
  String? admissionDate;
  String? plate;
  String? name;
  @JsonKey(name: "display_name")
  String? displayName;
  String? salary;
  int? dependentsNumber;
  String? cpf;
  String? birthDay;
  String? rg;
  String? agencyEmissionRg;
  String? dateEmissionRg;
  String? ufRg;
  String? workCard;
  String? seriesWorkCard;
  String? emissionDtWorkCard;
  String? ufWorkCard;
  String? functionName;
  String? situationCode;
  String? companyId;
  String? createdAt;
  String? updatedAt;
  double? limitEmprestimo;
  double? marginEmprestimo;
  bool? experiencePeriod;
  bool? under6Months;

  CollaboratorResponse({
    this.id,
    this.relatedCode,
    this.codeSession,
    this.coligadaName,
    this.pisPasep,
    this.admissionDate,
    this.plate,
    this.name,
    this.displayName,
    this.salary,
    this.dependentsNumber,
    this.cpf,
    this.birthDay,
    this.rg,
    this.agencyEmissionRg,
    this.dateEmissionRg,
    this.ufRg,
    this.workCard,
    this.seriesWorkCard,
    this.emissionDtWorkCard,
    this.ufWorkCard,
    this.functionName,
    this.situationCode,
    this.companyId,
    this.createdAt,
    this.updatedAt,
    this.limitEmprestimo,
    this.marginEmprestimo,
    this.experiencePeriod,
    this.under6Months,
  });

  factory CollaboratorResponse.fromMap(Map<String, dynamic> json) {
    return CollaboratorResponse(
      id: json['id'] as String?,
      relatedCode: json['relatedCode'] as String?,
      codeSession: json['codeSession'] as String?,
      coligadaName: json['coligadaName'] as String?,
      pisPasep: json['pisPasep'] as String?,
      admissionDate: json['admissionDate'] as String?,
      plate: json['plate'] as String?,
      name: json['name'] as String?,
      displayName: json['display_name'] as String?,
      salary: json['salary'] as String?,
      dependentsNumber: json['dependentsNumber'] as int?,
      cpf: json['cpf'] as String?,
      birthDay: json['birthDay'] as String?,
      rg: json['rg'] as String?,
      agencyEmissionRg: json['agencyEmissionRg'] as String?,
      dateEmissionRg: json['dateEmissionRg'] as String?,
      ufRg: json['ufRg'] as String?,
      workCard: json['workCard'] as String?,
      seriesWorkCard: json['seriesWorkCard'] as String?,
      emissionDtWorkCard: json['emissionDtWorkCard'] as String?,
      ufWorkCard: json['ufWorkCard'] as String?,
      functionName: json['functionName'] as String?,
      situationCode: json['situationCode'] as String?,
      companyId: json['companyId'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      limitEmprestimo: (json['limit'] as String?)?.toDouble(),
      marginEmprestimo: (json['margin'] as String?)?.toDouble(),
      experiencePeriod: (json['experiencePeriod'] as bool?),
      under6Months: (json['under6Months'] as bool?),
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'relatedCode': relatedCode,
      'codeSession': codeSession,
      'coligadaName': coligadaName,
      'pisPasep': pisPasep,
      'admissionDate': admissionDate,
      'plate': plate,
      'name': name,
      'display_name': displayName,
      'salary': salary,
      'dependentsNumber': dependentsNumber,
      'cpf': cpf,
      'birthDay': birthDay,
      'rg': rg,
      'agencyEmissionRg': agencyEmissionRg,
      'dateEmissionRg': dateEmissionRg,
      'ufRg': ufRg,
      'workCard': workCard,
      'seriesWorkCard': seriesWorkCard,
      'emissionDtWorkCard': emissionDtWorkCard,
      'ufWorkCard': ufWorkCard,
      'functionName': functionName,
      'situationCode': situationCode,
      'companyId': companyId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'limit': limitEmprestimo.toString(),
      'margin': marginEmprestimo.toString(),
      'experiencePeriod': experiencePeriod,
      'under6Months': under6Months,
    };
  }

  /// Somente colaboradores com o campo situationCode como A e F podem solicitar empréstimo
  bool isAvailableToLoan() {
    if (situationCode == 'A' || situationCode == 'F') {
      return true;
    } else {
      return false;
    }
  }

  String toJson() => json.encode(toMap());

  factory CollaboratorResponse.fromJson(String source) =>
      CollaboratorResponse.fromMap(json.decode(source) as Map<String, dynamic>);
}

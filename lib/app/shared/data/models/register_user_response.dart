import 'dart:convert';

import 'package:equatable/equatable.dart';

enum Stage {
  ENDERECO,
  VERIFICACAO_CELULAR,
  SENHA,
  DOCUMENTOS,
  ANALISE_DOCUMENTACAO,
  DOCUMENTACAO_NEGADA,
  DOCUMENTACAO_PENDENTE,
  FINALIZADO,
  TERMOS,
}

class RegisterUserResponse extends Equatable {
  String? id;
  bool? isCollaborator;
  bool? isRegistered;
  String? stage;
  String? cpf;
  bool? olderAge;
  String? content;

  RegisterUserResponse({
    this.id,
    this.isCollaborator,
    this.isRegistered,
    this.stage,
    this.cpf,
    this.olderAge,
    this.content,
  });

  RegisterUserResponse copyWith({
    String? id,
    bool? isCollaborator,
    bool? isRegistered,
    String? stage,
    String? cpf,
    bool? olderAge,
    String? content,
  }) {
    return RegisterUserResponse(
      id: id ?? this.id,
      isCollaborator: isCollaborator ?? this.isCollaborator,
      isRegistered: isRegistered ?? this.isRegistered,
      stage: stage ?? this.stage,
      cpf: cpf ?? this.cpf,
      olderAge: olderAge ?? this.olderAge,
      content: content ?? this.content,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'user_id': id,
      'isCollaborator': isCollaborator,
      'isRegistered': isRegistered,
      'stage': stage,
      'cpf': cpf,
      'older_age': olderAge,
      'content': content,
    };
  }

  factory RegisterUserResponse.fromMap(Map<String, dynamic> map) {
    return RegisterUserResponse(
      id: (map['user_id'] as String?) ?? map['id'] as String?,
      isCollaborator: map['isColaborator'] as bool?,
      isRegistered: map['isRegistered'] as bool?, //
      stage: map['stage'] as String?,
      cpf: map['cpf'] as String?,
      olderAge: map['older_age'] as bool?,
      content: map['content'] as String?,
    );
  }

  String toJson() => json.encode(toMap());

  factory RegisterUserResponse.fromJson(String source) =>
      RegisterUserResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  List<Object?> get props => [
        id,
        stage,
        cpf,
        content,
      ];

  @override
  String toString() {
    return 'RegisterUserResponse{id=$id, isCollaborator=$isCollaborator, isRegistered=$isRegistered, stage=$stage, cpf=$cpf, older_age: $olderAge, content: $content}';
  }

  bool get checkPhoneStage => stage?.compareTo("VERIFICACAO_CELULAR") == 0;

  bool get passwordStage => stage?.compareTo("SENHA") == 0;

  bool get addressStage => stage?.compareTo("ENDERECO") == 0;

  bool get documentsStage => stage?.compareTo("DOCUMENTOS") == 0;

  bool get termoCompromissoEstagio => stage?.compareTo("TERMOS") == 0;

  bool get analysisDocumentsStage =>
      stage?.compareTo("ANALISE_DOCUMENTACAO") == 0;

  bool get documentsDeniedStage => stage?.compareTo("DOCUMENTACAO_NEGADA") == 0;

  bool get pendingDocumentsStage =>
      stage?.compareTo("DOCUMENTACAO_PENDENTE") == 0;

  bool get finishedStage => stage?.compareTo("FINALIZADO") == 0;

  // bool get estagioTipo => estagio?.compareTo("tipo") == 0;

  // bool get estagioAuth => estagio?.compareTo("auth") == 0;
}

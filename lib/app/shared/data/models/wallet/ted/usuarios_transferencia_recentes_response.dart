import 'dart:convert';

import 'tec_usuario.dart';
import 'package:json_annotation/json_annotation.dart';

part 'usuarios_transferencia_recentes_response.g.dart';

@JsonSerializable()
class UsuariosTransferenciasRecentesResponse {
  List<TecUsuario>? contatos;

  UsuariosTransferenciasRecentesResponse({
    this.contatos,
  });

  factory UsuariosTransferenciasRecentesResponse.fromJson(
          Map<String, dynamic> json) =>
      _$UsuariosTransferenciasRecentesResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$UsuariosTransferenciasRecentesResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

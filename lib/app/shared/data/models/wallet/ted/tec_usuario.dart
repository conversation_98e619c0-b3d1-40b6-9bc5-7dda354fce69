import 'package:json_annotation/json_annotation.dart';

import 'conta_ted_response.dart';

part 'tec_usuario.g.dart';

@JsonSerializable()
class TecUsuario {
  String? id;
  String? nome;
  @<PERSON><PERSON><PERSON>ey(name: "imagem_perfil")
  String? imagemPerfil;
  String? tag;

  TecUsuario({
    this.id,
    this.nome,
    this.imagemPerfil,
    this.tag,
  });

  factory TecUsuario.fromJson(Map<String, dynamic> json) =>
      _$TecUsuarioFromJson(json);

  Map<String, dynamic> toJson() => _$TecUsuarioToJson(this);

  ContaTedResponse getContaDigital() {
    return ContaTedResponse(
      tipo: 'digital',
      nome: nome ?? '',
      idUsuario: id ?? '',
    );
  }
}

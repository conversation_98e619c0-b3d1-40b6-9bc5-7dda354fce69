import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'ted_response.g.dart';

@JsonSerializable()
class TedResponse {
  String? tid;
  @Json<PERSON>ey(name: "ted_id")
  String? tedId;
  @<PERSON><PERSON><PERSON>ey(name: "notificacao_id")
  String? notificacaoId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "data_cancelada")
  String? dataCancelada;

  TedResponse({
    this.tedId,
    this.tid,
    this.notificacaoId,
    this.dataCancelada,
  });

  factory TedResponse.fromJson(Map<String, dynamic> json) =>
      _$TedResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TedResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'tec_response.g.dart';

@JsonSerializable()
class TecResponse {
  String? tid;
  @JsonKey(name: "data_hora")
  String? dataHora;

  TecResponse({this.tid, this.dataHora});

  factory TecResponse.fromJson(Map<String, dynamic> json) =>
      _$TecResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TecResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

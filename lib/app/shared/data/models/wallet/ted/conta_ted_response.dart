import 'dart:convert';

import 'conta_ted_banco_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'conta_ted_response.g.dart';

@JsonSerializable()
class ContaTedResponse {
  int? id;
  String? status;
  String? tipo;
  String? agencia;
  String? conta;
  String? nome;
  String? cpf;
  String? cnpj;
  ContaTedBancoResponse? banco;

  String? idUsuario;

  bool get isDigital => tipo == 'digital';
  String get contaCorrenteFormatada => _contaCorrenteFormatada();
  bool get isContaBancaria =>
      tipo == "corrente" || tipo == "contato" || tipo == "poupanca";
  String get documento => (cnpj != null && cnpj!.isNotEmpty) ? cnpj! : cpf!;

  ContaTedResponse({
    this.id,
    this.nome,
    this.agencia,
    this.banco,
    this.cnpj,
    this.conta,
    this.cpf,
    this.status,
    this.tipo,
    this.idUsuario,
  });

  factory ContaTedResponse.fromJson(Map<String, dynamic> json) =>
      _$ContaTedResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ContaTedResponseToJson(this);

  ContaTedResponse copy() => ContaTedResponse(
        id: this.id,
        nome: this.nome,
        agencia: this.agencia,
        banco: this.banco,
        cnpj: this.cnpj,
        cpf: this.cpf,
        conta: this.conta,
        status: this.status,
        tipo: this.tipo,
        idUsuario: this.idUsuario,
      );

  @override
  String toString() {
    return json.encode(toJson());
  }

  _contaCorrenteFormatada() {
    try {
      var isContaCorrente = tipo != "poupanca";
      return "${isContaCorrente ? 'CC' : 'CP'} ${conta!.substring(0, conta!.length - 1)}-${conta!.substring(conta!.length - 1, conta!.length)}";
    } catch (error) {
      return "";
    }
  }
}

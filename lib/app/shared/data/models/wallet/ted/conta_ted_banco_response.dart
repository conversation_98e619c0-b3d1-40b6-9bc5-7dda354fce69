import 'package:json_annotation/json_annotation.dart';

part 'conta_ted_banco_response.g.dart';

@JsonSerializable()
class ContaTedBancoResponse {
  int? id;
  String? nome;
  String? codigo;

  ContaTedBancoResponse({
    this.id,
    this.nome,
    this.codigo,
  });

  factory ContaTedBancoResponse.fromJson(Map<String, dynamic> json) =>
      _$ContaTedBancoResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ContaTedBancoResponseToJson(this);
}

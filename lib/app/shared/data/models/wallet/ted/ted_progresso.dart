import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'ted_progresso.g.dart';

@JsonSerializable()
class TedProgresso {
  String? id;
  String? status;
  double? valor;
  bool? comprovante;

  TedProgresso({
    this.id,
    this.status,
    this.valor,
    this.comprovante,
  });

  factory TedProgresso.fromJson(Map<String, dynamic> json) =>
      _$TedProgressoFromJson(json);

  Map<String, dynamic> toJson() => _$TedProgressoToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

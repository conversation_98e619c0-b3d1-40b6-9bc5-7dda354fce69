import 'dart:convert';

import 'tec_usuario.dart';
import 'package:json_annotation/json_annotation.dart';

part 'busca_usuarios_tec_response.g.dart';

@JsonSerializable()
class BuscaUsuariosTecResponse {
  int? total;
  int? quantidade;
  int? pagina;
  List<TecUsuario>? contatos;

  BuscaUsuariosTecResponse({
    this.total,
    this.quantidade,
    this.pagina,
    this.contatos,
  });

  factory BuscaUsuariosTecResponse.fromJson(Map<String, dynamic> json) =>
      _$BuscaUsuariosTecResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BuscaUsuariosTecResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

import 'dart:convert';

import 'conta_ted_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'contato_contas_ted_response.g.dart';

@JsonSerializable()
class ContatoContasTedResponse {
  @JsonKey(name: "id_usuario")
  String? idUsuario;
  String? nome;
  String? tipo;
  String? cpf;
  String? cnpj;
  List<ContaTedResponse>? contas;

  bool get isContaDigital => tipo == "carteira";
  bool get isContaSiclos => tipo == "conta";

  String get documento => (cnpj != null && cnpj!.isNotEmpty) ? cnpj! : cpf!;

  ContatoContasTedResponse({
    required this.nome,
    this.cpf,
    this.cnpj,
    this.contas,
    this.tipo,
  });

  factory ContatoContasTedResponse.fromJson(Map<String, dynamic> json) =>
      _$ContatoContasTedResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ContatoContasTedResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

import 'dart:convert';

import 'tec_usuario.dart';
import 'busca_usuarios_tec_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'busca_usuarios_response.g.dart';

@JsonSerializable()
class BuscaUsuariosResponse {
  @JsonKey(name: "tecs_recentes")
  List<TecUsuario>? tecsRecentes;
  BuscaUsuariosTecResponse? todos;

  BuscaUsuariosResponse({
    this.tecsRecentes,
    this.todos,
  });

  factory BuscaUsuariosResponse.fromJson(Map<String, dynamic> json) =>
      _$BuscaUsuariosResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BuscaUsuariosResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

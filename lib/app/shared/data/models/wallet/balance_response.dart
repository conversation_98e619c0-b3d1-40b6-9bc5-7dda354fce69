// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

class BalanceResponse extends Equatable {
  final double? balance;
  @Json<PERSON>ey(name: "last_update")
  final String? lastUpdate;

  const BalanceResponse({
    this.balance,
    this.lastUpdate,
  });

  @override
  List<Object?> get props => [balance, lastUpdate];

  BalanceResponse copyWith({
    double? balance,
    String? lastUpdate,
  }) {
    return BalanceResponse(
      balance: balance ?? this.balance,
      lastUpdate: lastUpdate ?? this.lastUpdate,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'balance': balance,
      'last_update': lastUpdate,
    };
  }

  factory BalanceResponse.fromMap(Map<String, dynamic> map) {
    return BalanceResponse(
      balance: map['balance'] != null ? map['balance'] as double : null,
      lastUpdate:
          map['last_update'] != null ? map['last_update'] as String : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory BalanceResponse.fromJson(String source) =>
      BalanceResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;
}

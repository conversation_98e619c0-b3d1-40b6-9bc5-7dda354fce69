// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:string_validator/string_validator.dart';

class PoliticalCreditResponse implements Equatable {
  final String id;
  final String creditType;
  final double incomeMin;
  final int timeCompany;
  // valor minimo de parcela
  final double installmentValueMin;
  final int? installmentMany;
  // qtd minima de parcelas
  final int? timeMin;
  // qtd max de parcelas
  final int? timeMax;
  // se permite usuario ter mais de um emprestimo (com base em sua margem)
  final bool creditsMany;
  // juros
  final double interestRates;
  final double valueCredit;
  final double? valueMin;

  const PoliticalCreditResponse({
    required this.id,
    required this.creditType,
    required this.incomeMin,
    required this.timeCompany,
    required this.installmentValueMin,
    required this.installmentMany,
    required this.timeMin,
    required this.timeMax,
    required this.creditsMany,
    required this.interestRates,
    required this.valueCredit,
    required this.valueMin,
  });

  @override
  List<Object?> get props => [
        id,
        creditType,
        incomeMin,
        timeCompany,
        installmentValueMin,
        installmentMany,
        timeMin,
        timeMax,
        creditsMany,
        interestRates,
        valueCredit,
        valueMin,
      ];

  @override
  bool? get stringify => true;

  PoliticalCreditResponse copyWith({
    String? id,
    String? creditType,
    double? incomeMin,
    int? timeCompany,
    double? installmentValue,
    int? installmentMany,
    int? timeMin,
    int? timeMax,
    bool? creditsMany,
    double? interestRates,
    double? valueCredit,
    double? valueMin,
  }) {
    return PoliticalCreditResponse(
      id: id ?? this.id,
      creditType: creditType ?? this.creditType,
      incomeMin: incomeMin ?? this.incomeMin,
      timeCompany: timeCompany ?? this.timeCompany,
      installmentValueMin: installmentValue ?? this.installmentValueMin,
      installmentMany: installmentMany ?? this.installmentMany,
      timeMin: timeMin ?? this.timeMin,
      timeMax: timeMax ?? this.timeMax,
      creditsMany: creditsMany ?? this.creditsMany,
      interestRates: interestRates ?? this.interestRates,
      valueCredit: valueCredit ?? this.valueCredit,
      valueMin: valueMin ?? this.valueMin,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'credit_type': creditType,
      'income_min': incomeMin,
      'time_company': timeCompany,
      'installment_value': installmentValueMin,
      'installment_many': installmentMany,
      'time_min': timeMin,
      'time_max': timeMax,
      'credits_many': creditsMany,
      'interest_rates': interestRates,
      'value_credit': valueCredit,
      'value_min': valueMin,
    };
  }

  factory PoliticalCreditResponse.fromMap(Map<String, dynamic> map) {
    return PoliticalCreditResponse(
      id: map['id'] as String,
      creditType: map['credit_type'] as String,
      incomeMin: map['income_min'] == null
          ? 0.0
          : double.tryParse(map['income_min'] as String) ?? 0.0,
      timeCompany: map['time_company'] as int,
      installmentValueMin:
          double.parse((map['installment_value'] as String?) ?? '0.0'),
      installmentMany: map['installment_many'] as int?,
      timeMin: map['time_min'] as int?,
      timeMax: map['time_max'] as int?,
      creditsMany: map['credits_many'] as bool,
      interestRates: (map['interest_rates'] as String).toDouble(),
      valueCredit: (map['value_credit'] as String?)?.toDouble() ?? 0.0,
      valueMin: (map['value_min'] as String?)?.toDouble(),
    );
  }

  String toJson() => json.encode(toMap());

  factory PoliticalCreditResponse.fromJson(String source) =>
      PoliticalCreditResponse.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'PoliticalCreditResponse(id: $id, creditType: $creditType, incomeMin: $incomeMin, timeCompany: $timeCompany, installmentValue: $installmentValueMin, installmentMany: $installmentMany, timeMin: $timeMin, timeMax: $timeMax, creditsMany: $creditsMany, interestRates: $interestRates, valueCredit: $valueCredit)';
  }

  @override
  bool operator ==(covariant PoliticalCreditResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.creditType == creditType &&
        other.incomeMin == incomeMin &&
        other.timeCompany == timeCompany &&
        other.installmentValueMin == installmentValueMin &&
        other.installmentMany == installmentMany &&
        other.timeMin == timeMin &&
        other.timeMax == timeMax &&
        other.creditsMany == creditsMany &&
        other.interestRates == interestRates &&
        other.valueCredit == valueCredit;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        creditType.hashCode ^
        incomeMin.hashCode ^
        timeCompany.hashCode ^
        installmentValueMin.hashCode ^
        installmentMany.hashCode ^
        timeMin.hashCode ^
        timeMax.hashCode ^
        creditsMany.hashCode ^
        interestRates.hashCode ^
        valueCredit.hashCode;
  }
}

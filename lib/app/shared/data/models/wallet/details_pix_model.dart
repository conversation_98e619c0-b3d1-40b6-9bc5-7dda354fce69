// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:siclosbank/app/modules/statement/data/models/party_statement_model.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../modules/statement/data/models/details_statement.dart';

enum PixTransactionType {
  paymentIn,
  paymentOut,
  reversalIn,
  reversalOut,
}

class DetailsPixModel extends DetailsStatement {
  final double amount;
  final String? transactionId;
  final String endToEndId;
  final String? clientCode;
  final String? originalTransactionId;
  final DateTime? createdAt;

  // final String initiationType;
  // final String transactionType;

  DetailsPixModel({
    required this.amount,
    required this.transactionId,
    required this.endToEndId,
    required super.debitParty,
    required super.creditParty,
    this.clientCode,
    this.originalTransactionId,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'amount': amount,
      'transactionId': transactionId,
      'endToEndId': endToEndId,
      'debitParty': debitParty.toMap(),
      'creditParty': creditParty.toMap(),
      'clientCode': clientCode,
      'originalTransactionId': originalTransactionId,
      'createdAt': createdAt?.toString(),
    };
  }

  factory DetailsPixModel.fromMap(Map<String, dynamic> map) {
    var transactionId = map['transactionId'];
    if (transactionId is num) {
      transactionId = transactionId.toString();
    } else if (transactionId is String) {
      transactionId = transactionId;
    } else {
      transactionId = null;
    }
    DateTime? date;
    if (map['createTimestamp'] != null) {
      date = Utils.formatDateToDateTime(map['createTimestamp']
          as String); // DateTime.tryParse(map['createTimestamp'] as String);
    } else {
      if (map['date'] != null) {
        date = DateTime.tryParse(map['date'] as String)!
            .subtract(const Duration(hours: 3));
      } else {
        date = null;
      }
    }

    return DetailsPixModel(
      amount: (map['amount'] as num).toDouble(),
      transactionId: transactionId,
      endToEndId: map['endToEndId'] as String,
      debitParty: PartyStatementModel.fromMap(
          map['debitParty'] as Map<String, dynamic>),
      creditParty: PartyStatementModel.fromMap(
          map['creditParty'] as Map<String, dynamic>),
      clientCode: map['clientCode'] as String?,
      originalTransactionId: map['original_payment_id'] as String?,
      createdAt: date,
    );
  }

  String toJson() => json.encode(toMap());

  factory DetailsPixModel.fromJson(String source) =>
      DetailsPixModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'DetailsPixModel(amount: $amount, \ntransactionId: $transactionId, \nendToEndId: $endToEndId, \nclientCode: $clientCode, \noriginalTransactionId: $originalTransactionId, \ncreatedAt: ${createdAt.toString()}, \ndebitParty: $debitParty, \ncreditParty: $creditParty)';
  }
}

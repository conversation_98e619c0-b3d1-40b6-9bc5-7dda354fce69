// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class BankAccountResponse {
  BankAccountResponse(
      {this.id,
      this.bankName,
      this.accountNumber,
      this.accountBankCode,
      this.accountBranchCode});

  final String? id;
  final String? bankName;
  final String? accountNumber;
  final String? accountBankCode;
  final String? accountBranchCode;

  BankAccountResponse copyWith({
    String? id,
    String? bankName,
    String? accountNumber,
    String? accountBankCode,
    String? accountBranchCode,
  }) {
    return BankAccountResponse(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      accountBankCode: accountBankCode ?? this.accountBankCode,
      accountBranchCode: accountBranchCode ?? this.accountBranchCode,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'accountBankCode': accountBankCode,
      'accountBranchCode': accountBranchCode,
    };
  }

  factory BankAccountResponse.fromMap(Map<String, dynamic> map) {
    return BankAccountResponse(
      id: map['id'] as String?,
      bankName: map['bank'] as String?,
      accountNumber: map['account'] as String?,
      accountBankCode: map['bankCode'] as String?,
      accountBranchCode: map['branch'] as String?,
    );
  }

  String toJson() => json.encode(toMap());

  factory BankAccountResponse.fromJson(String source) =>
      BankAccountResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'BankAccountResponse(id: $id, bankName: $bankName, accountNumber: $accountNumber, accountBankCode: $accountBankCode, accountBranchCode: $accountBranchCode)';
  }

  @override
  bool operator ==(covariant BankAccountResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.bankName == bankName &&
        other.accountNumber == accountNumber &&
        other.accountBankCode == accountBankCode &&
        other.accountBranchCode == accountBranchCode;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        bankName.hashCode ^
        accountNumber.hashCode ^
        accountBankCode.hashCode ^
        accountBranchCode.hashCode;
  }
}

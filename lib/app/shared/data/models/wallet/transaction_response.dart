// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_type.dart';

class TransactionResponse {
  final String id;
  // final String? clientCode;
  final String? description;
  final DateTime? createDate;
  // final DateTime? lastUpdateDate;
  final double amount;
  final String? status;
  final String? balanceType;
  final MovementType movementType;

  TransactionResponse({
    required this.id,
    required this.description,
    required this.createDate,
    required this.amount,
    this.status,
    this.balanceType,
    required this.movementType,
  });

  factory TransactionResponse.fromMap(Map<String, dynamic> json) {
    return TransactionResponse(
      id: json['id'],
      description: json['description'],
      createDate: DateTime.parse(json['createDate']),
      amount: json['amount'].toDouble(),
      status: json['status'],
      balanceType: json['balanceType'],
      movementType: MovementTypeExtension.fromString(json['movementType']),
    );
  }

  TransactionResponse copyWith({
    String? id,
    String? description,
    DateTime? createDate,
    double? amount,
    String? status,
    String? balanceType,
    MovementType? movementType,
  }) {
    return TransactionResponse(
      id: id ?? this.id,
      description: description ?? this.description,
      createDate: createDate ?? this.createDate,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      balanceType: balanceType ?? this.balanceType,
      movementType: movementType ?? this.movementType,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description,
      'createDate': createDate.toString(),
      'amount': amount,
      'status': status,
      'balanceType': balanceType,
      'movementType': movementType,
    };
  }

  String toJson() => json.encode(toMap());

  factory TransactionResponse.fromJson(String source) =>
      TransactionResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'TransactionResponse(id: $id, description: $description, createDate: $createDate, amount: $amount, status: $status, balanceType: $balanceType, movementType: $movementType)';
  }

  @override
  bool operator ==(covariant TransactionResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.description == description &&
        other.createDate == createDate &&
        other.amount == amount &&
        other.status == status &&
        other.balanceType == balanceType &&
        other.movementType == movementType;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        description.hashCode ^
        createDate.hashCode ^
        amount.hashCode ^
        status.hashCode ^
        balanceType.hashCode ^
        movementType.hashCode;
  }

  isCredit() {
    return balanceType == "CREDIT";
  }

  isPendent() {
    return status == "Saldo Bloqueado";
  }
}

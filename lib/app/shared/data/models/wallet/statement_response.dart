// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:flutter/foundation.dart';

import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';

class StatementResponse {
  int? totalItems;
  int? limitPerPage;
  int? currentPage;
  int? totalPages;
  // Map<String, dynamic>? categorias;
  List<TransactionResponse>? transactions;

  StatementResponse({
    this.totalItems,
    this.limitPerPage,
    this.currentPage,
    this.totalPages,
    this.transactions,
  });

  StatementResponse copyWith({
    int? totalItems,
    int? limitPerPage,
    int? currentPage,
    int? totalPages,
    List<TransactionResponse>? transactions,
  }) {
    return StatementResponse(
      totalItems: totalItems ?? this.totalItems,
      limitPerPage: limitPerPage ?? this.limitPerPage,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      transactions: transactions ?? this.transactions,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'totalItems': totalItems,
      'limitPerPage': limitPerPage,
      'currentPage': currentPage,
      'totalPages': totalPages,
      'transactions': transactions?.map((x) => x.toMap()).toList(),
    };
  }

  factory StatementResponse.fromMap(Map<String, dynamic> map) {
    return StatementResponse(
      totalItems: map['totalItems'] as int?,
      limitPerPage: map['limitPerPage'] as int?,
      currentPage: map['currentPage'] as int?,
      totalPages: map['totalPages'] as int?,
      transactions: List<TransactionResponse>.from(
        (map['body']?['movements'] ?? []).map<TransactionResponse?>(
          (x) => TransactionResponse.fromMap(x),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory StatementResponse.fromJson(String source) =>
      StatementResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'StatementResponse(totalItems: $totalItems, limitPerPage: $limitPerPage, currentPage: $currentPage, totalPages: $totalPages, transactions: $transactions)';
  }

  @override
  bool operator ==(covariant StatementResponse other) {
    if (identical(this, other)) return true;

    return other.totalItems == totalItems &&
        other.limitPerPage == limitPerPage &&
        other.currentPage == currentPage &&
        other.totalPages == totalPages &&
        listEquals(other.transactions, transactions);
  }

  @override
  int get hashCode {
    return totalItems.hashCode ^
        limitPerPage.hashCode ^
        currentPage.hashCode ^
        totalPages.hashCode ^
        transactions.hashCode;
  }
}

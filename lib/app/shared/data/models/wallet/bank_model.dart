// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';

class BanksModel extends Equatable {
  final String ispb;
  final String name;
  final int code;
  final String fullName;
  BanksModel({
    required this.ispb,
    required this.name,
    required this.code,
    required this.fullName,
  });

  @override
  List<Object> get props => [ispb, name, code, fullName];

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'ispb': ispb,
      'name': name,
      'code': code,
      'fullName': fullName,
    };
  }

  factory BanksModel.fromMap(Map<String, dynamic> map) {
    return BanksModel(
      ispb: map['ispb'] ?? '',
      name: map['name'] ?? '',
      code: map['code'] ?? 0,
      fullName: map['fullName'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory BanksModel.fromJson(String source) =>
      BanksModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

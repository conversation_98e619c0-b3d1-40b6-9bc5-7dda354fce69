// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class AddressResponse {
  final String? idAddress;
  final String? cep;
  final String? city;
  final String? district;
  final String? number;
  final String? state;
  final String? street;
  final String? complement;

  final String? userId;
  AddressResponse({
    this.idAddress,
    this.cep,
    this.city,
    this.district,
    this.number,
    this.state,
    this.street,
    this.userId,
    this.complement,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': idAddress,
      'cep': cep,
      'city': city,
      'district': district,
      'number': number,
      'state': state,
      'street': street,
      'user_id': userId,
      'complement': complement,
    };
  }

  factory AddressResponse.fromMap(Map<String, dynamic> map) {
    return AddressResponse(
      idAddress: map['id'] != null ? map['id'] as String : null,
      cep: map['cep'] != null ? map['cep'] as String : null,
      city: map['city'] != null ? map['city'] as String : null,
      district: map['district'] != null ? map['district'] as String : null,
      number: map['number'] != null ? map['number'] as String : null,
      state: map['state'] != null ? map['state'] as String : null,
      street: map['street'] != null ? map['street'] as String : null,
      userId: map['user_id'] != null ? map['user_id'] as String : null,
      complement: map['complement'] as String?,
    );
  }

  String toJson() => json.encode(toMap());

  factory AddressResponse.fromJson(String source) =>
      AddressResponse.fromMap(json.decode(source) as Map<String, dynamic>);
}

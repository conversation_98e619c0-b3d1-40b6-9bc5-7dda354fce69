// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class SimulationResponse {
  final double valueCredit;
  final double valueTotal;
  final double interestRates;
  final int installmentsMany;
  final double installmentValue;
  final String firstPaymentDate;
  SimulationResponse({
    required this.valueCredit,
    required this.valueTotal,
    required this.interestRates,
    required this.installmentsMany,
    required this.installmentValue,
    required this.firstPaymentDate,
  });

  SimulationResponse copyWith({
    double? valueCredit,
    double? valueTotal,
    double? interestRates,
    int? installmentsMany,
    double? installmentValue,
    String? firstPaymentDate,
  }) {
    return SimulationResponse(
      valueCredit: valueCredit ?? this.valueCredit,
      valueTotal: valueTotal ?? this.valueTotal,
      interestRates: interestRates ?? this.interestRates,
      installmentsMany: installmentsMany ?? this.installmentsMany,
      installmentValue: installmentValue ?? this.installmentValue,
      firstPaymentDate: firstPaymentDate ?? this.firstPaymentDate,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'requested_amount': valueCredit,
      'total_amount_owed': valueTotal,
      'interest_rate': interestRates,
      'num_periods': installmentsMany,
      'payment_amount': installmentValue,
      'first_payment_date': firstPaymentDate,
    };
  }

  factory SimulationResponse.fromMap(Map<String, dynamic> map) {
    return SimulationResponse(
      valueCredit: (map['requested_amount'] as num).toDouble(),
      valueTotal: map['total_amount_owed'] as double,
      interestRates: map['interest_rate'] as double,
      installmentsMany: map['num_periods'] as int,
      installmentValue: map['payment_amount'] as double,
      firstPaymentDate: map['first_payment_date'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory SimulationResponse.fromJson(String source) =>
      SimulationResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'LoanSimulationResponse(valueCredit: $valueCredit, valueTotal: $valueTotal, interestRates: $interestRates, installmentsMany: $installmentsMany, installmentValue: $installmentValue)';
  }

  @override
  bool operator ==(covariant SimulationResponse other) {
    if (identical(this, other)) return true;

    return other.valueCredit == valueCredit &&
        other.valueTotal == valueTotal &&
        other.interestRates == interestRates &&
        other.installmentsMany == installmentsMany &&
        other.installmentValue == installmentValue;
  }

  @override
  int get hashCode {
    return valueCredit.hashCode ^
        valueTotal.hashCode ^
        interestRates.hashCode ^
        installmentsMany.hashCode ^
        installmentValue.hashCode;
  }
}

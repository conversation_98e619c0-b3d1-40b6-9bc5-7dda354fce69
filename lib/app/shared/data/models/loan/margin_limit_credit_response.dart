// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class MarginLimitCreditResponse {
  final String id;
  final double limit;
  final double margin;
  final int installmentsMax;
  MarginLimitCreditResponse({
    required this.id,
    required this.limit,
    required this.margin,
    required this.installmentsMax,
  });

  MarginLimitCreditResponse copyWith({
    String? id,
    double? limit,
    double? margin,
    int? installmentsMax,
  }) {
    return MarginLimitCreditResponse(
      id: id ?? this.id,
      limit: limit ?? this.limit,
      margin: margin ?? this.margin,
      installmentsMax: installmentsMax ?? this.installmentsMax,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'limit': limit,
      'margin': margin,
    };
  }

  factory MarginLimitCreditResponse.fromMap(Map<String, dynamic> map) {
    return MarginLimitCreditResponse(
      id: map['id'] as String,
      limit: (map['limit'] as num).toDouble(),
      margin: (map['margin'] as num).toDouble(),
      installmentsMax: map['installment_max'] as int,
    );
  }

  String toJson() => json.encode(toMap());

  factory MarginLimitCreditResponse.fromJson(String source) =>
      MarginLimitCreditResponse.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() =>
      'MarginCreditResponse(id: $id, limit: $limit, margin: $margin)';

  @override
  bool operator ==(covariant MarginLimitCreditResponse other) {
    if (identical(this, other)) return true;

    return other.id == id && other.limit == limit && other.margin == margin;
  }

  @override
  int get hashCode => id.hashCode ^ limit.hashCode ^ margin.hashCode;
}

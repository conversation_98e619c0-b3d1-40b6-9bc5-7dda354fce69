// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class RequestLoanResponse {
  final String idRequestLoan;
  final double? disbursementAmount;
  final double? totalAmountOwed;
  final double? interestRate;
  final int? installmentsMany;
  final double? installmentValue;
  final String? firstPaymentDate;
  final String? status;
  final String? disbursementDate;
  final String? issueDate;
  RequestLoanResponse({
    required this.idRequestLoan,
    this.disbursementAmount,
    this.totalAmountOwed,
    this.interestRate,
    this.installmentsMany,
    this.installmentValue,
    this.firstPaymentDate,
    this.status,
    this.disbursementDate,
    this.issueDate,
  });

  RequestLoanResponse copyWith({
    double? disbursementAmount,
    double? totalAmountOwed,
    double? interestRate,
    int? installmentsMany,
    double? installmentValue,
    String? firstPaymentDate,
    String? idRequestLoan,
    String? status,
    String? disbursementDate,
    String? issueDate,
  }) {
    return RequestLoanResponse(
      idRequestLoan: idRequestLoan ?? this.idRequestLoan,
      disbursementAmount: disbursementAmount ?? this.disbursementAmount,
      totalAmountOwed: totalAmountOwed ?? this.totalAmountOwed,
      interestRate: interestRate ?? this.interestRate,
      installmentsMany: installmentsMany ?? this.installmentsMany,
      installmentValue: installmentValue ?? this.installmentValue,
      firstPaymentDate: firstPaymentDate ?? this.firstPaymentDate,
      status: status ?? this.status,
      disbursementDate: disbursementDate ?? this.disbursementDate,
      issueDate: issueDate ?? this.issueDate,
    );
  }

  // Map<String, dynamic> toMap() {
  //   return <String, dynamic>{
  //     'id': idRequestLoan,
  //     'disbursementAmount': disbursementAmount,
  //     'totalAmountOwed': totalAmountOwed,
  //     'interest_rate': interestRate,
  //     'num_payments': installmentsMany,
  //     'payment_amount': installmentValue,
  //     'first_payment_date': firstPaymentDate,
  //   };
  // }

  factory RequestLoanResponse.fromMap(Map<String, dynamic> map) {
    return RequestLoanResponse(
      idRequestLoan: map['id'] as String,
      disbursementAmount:
          (map['loan_details']['disbursement_amount'] as num?)?.toDouble(),
      totalAmountOwed:
          (map['loan_details']['total_amount_owed'] as num?)?.toDouble(),
      interestRate: map['interest_rate'] as double?,
      installmentsMany: map['num_payments'] as int?,
      installmentValue:
          (map['loan_details']['payment_amount'] as num?)?.toDouble(),
      firstPaymentDate: map['first_payment_date'] as String?,
      status: map['status'] as String?,
      disbursementDate: map['disbursement_date'] as String?,
      issueDate: map['issue_date'] as String?,
    );
  }

  // String toJson() => json.encode(toMap());

  factory RequestLoanResponse.fromJson(String source) =>
      RequestLoanResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'RequestLoanResponse(disbursementAmount: $disbursementAmount, totalAmountOwed: $totalAmountOwed, interestRate: $interestRate, installmentsMany: $installmentsMany, installmentValue: $installmentValue, firstPaymentDate: $firstPaymentDate)';
  }

  @override
  bool operator ==(covariant RequestLoanResponse other) {
    if (identical(this, other)) return true;

    return other.idRequestLoan == idRequestLoan &&
        other.disbursementAmount == disbursementAmount &&
        other.totalAmountOwed == totalAmountOwed &&
        other.interestRate == interestRate &&
        other.installmentsMany == installmentsMany &&
        other.installmentValue == installmentValue &&
        other.firstPaymentDate == firstPaymentDate;
  }

  @override
  int get hashCode {
    return idRequestLoan.hashCode ^
        disbursementAmount.hashCode ^
        totalAmountOwed.hashCode ^
        interestRate.hashCode ^
        installmentsMany.hashCode ^
        installmentValue.hashCode ^
        firstPaymentDate.hashCode;
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class LoanStatusResponse {
  final bool? tomador;
  final String? status;
  // final bool? elegibleFastCredit;
  // final bool? elegibleConventionalCredit;

  LoanStatusResponse({
    this.tomador,
    this.status,
    // this.elegibleFastCredit,
    // this.elegibleConventionalCredit,
  });

  LoanStatusResponse copyWith({
    bool? tomador,
    String? status,
    // bool? elegibleFastCredit,
    // bool? elegibleConventionalCredit,
  }) {
    return LoanStatusResponse(
      tomador: tomador ?? this.tomador,
      status: status ?? this.status,
      // elegibleFastCredit: elegibleFastCredit ?? this.elegibleFastCredit,
      // elegibleConventionalCredit:
      //     elegibleConventionalCredit ?? this.elegibleConventionalCredit,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'tomador': tomador,
      'status': status,
      // 'elegibleFastCredit': elegibleFastCredit,
      // 'elegibleConventionalCredit': elegibleConventionalCredit,
    };
  }

  factory LoanStatusResponse.fromMap(Map<String, dynamic> map) {
    return LoanStatusResponse(
      tomador: map['tomador'] as bool?,
      status: map['status'] as String?,
      // elegibleFastCredit: map['elegibleFastCredit'] as bool?,
      // elegibleConventionalCredit: map['elegibleConventionalCredit'] as bool?,
    );
  }

  String toJson() => json.encode(toMap());

  factory LoanStatusResponse.fromJson(String source) =>
      LoanStatusResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'LoanStatusResponse(tomador: $tomador, status: $status)';
  }

  @override
  bool operator ==(covariant LoanStatusResponse other) {
    if (identical(this, other)) return true;

    return other.tomador == tomador && other.status == status;
    // other.elegibleFastCredit == elegibleFastCredit &&
    // other.elegibleConventionalCredit == elegibleConventionalCredit;
  }

  @override
  int get hashCode {
    return tomador.hashCode ^ status.hashCode;
    // elegibleFastCredit.hashCode ^
    // elegibleConventionalCredit.hashCode;
  }
}

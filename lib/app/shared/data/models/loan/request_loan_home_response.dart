// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:string_validator/string_validator.dart';

enum Status {
  AGREEMENT_RENDERING,
  PENDING_DISBURSEMENT,
  PENDING_SIGNATURE,
  PENDING_QUALIFICATION,
  PENDING_PAYMENT,
  ISSUED,
  CANCELED,
}

class RequestLoanHomeResponse {
  final String id;
  final String? numContract;
  final String creditStatus;
  final String creditType;
  final int installmentMany;
  final double installmentValue;
  final double interestRates;
  final double valueCredit;
  final String userId;
  final String creditIdCelcoin;
  final String createdAt;
  final String? situacao;
  final String? firstVenc;

  RequestLoanHomeResponse({
    required this.id,
    this.numContract,
    required this.creditStatus,
    required this.installmentMany,
    required this.installmentValue,
    required this.valueCredit,
    required this.userId,
    required this.creditIdCelcoin,
    required this.createdAt,
    this.interestRates = 0.0,
    this.situacao,
    required this.creditType,
    this.firstVenc,
  });

  RequestLoanHomeResponse copyWith({
    String? id,
    String? creditType,
    String? creditStatus,
    double? installmentValue,
    int? installmentMany,
    double? valueCredit,
    String? userId,
    String? creditIdCelcoin,
    String? createdAt,
    String? situacao,
    double? interestRates,
    String? numContract,
    String? firstVenc,
  }) {
    return RequestLoanHomeResponse(
      id: id ?? this.id,
      numContract: creditType ?? this.numContract,
      creditStatus: creditStatus ?? this.creditStatus,
      valueCredit: valueCredit ?? this.valueCredit,
      userId: userId ?? this.userId,
      creditIdCelcoin: creditIdCelcoin ?? this.creditIdCelcoin,
      createdAt: createdAt ?? this.createdAt,
      situacao: situacao ?? this.situacao,
      installmentMany: installmentMany ?? this.installmentMany,
      installmentValue: installmentValue ?? this.installmentValue,
      creditType: creditType ?? this.creditType,
    );
  }

  bool isCanceled() {
    return creditStatus == Status.CANCELED.name;
  }

  bool isFinalized() {
    //TODO: IMPLEMENTAR O STATUS DE LIQUIDADO
    return situacao == 'LIQUIDADO';
    // situacao == 'LIQUIDADO PARCIAL' ||
    // situacao == 'LIQUIDADO TOTAL';
    // return creditStatus == Status.CANCELED.name;
  }

  bool isIssued() {
    return creditStatus == Status.ISSUED.name;
  }

  isPendingSignature() {
    return creditStatus == Status.PENDING_SIGNATURE.name;
  }

  DateTime getDateTime() {
    return DateTime.parse(createdAt).subtract(const Duration(hours: 3));
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id_local': id,
      'contrato': numContract,
      'status': creditStatus,
      'valor': valueCredit,
      'user_id': userId,
      'id_credit_celcoin': creditIdCelcoin,
      'data_criacao': createdAt,
      'situacao': situacao,
    };
  }

  factory RequestLoanHomeResponse.fromMap(Map<String, dynamic> map) {
    return RequestLoanHomeResponse(
      id: map['id_local'] as String,
      numContract: map['contrato'] ?? '',
      creditStatus: map['status'] as String,
      installmentValue: map['valor_parcela'].toString().toDouble(),
      installmentMany: map['parcelas'] as int,
      interestRates: (map['interestRate'] ?? 0).toString().toDouble(),
      valueCredit: map['valor'].toString().toDouble(),
      userId: map['user_id'] as String,
      creditIdCelcoin: map['id_credit_celcoin'] as String,
      createdAt: map['data_criacao'] as String,
      situacao: map['situacao'] ?? '',
      creditType: map['credit'] ?? '',
      firstVenc: map['dataVencimento'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory RequestLoanHomeResponse.fromJson(String source) =>
      RequestLoanHomeResponse.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'RequestLoanHistoricalResponse(id: $id, creditType: $numContract, creditStatus: $creditStatus,installmentMany: $installmentMany, valueCredit: $valueCredit, userId: $userId, creditIdCelcoin: $creditIdCelcoin,  createdAt: $createdAt, updatedAt: $situacao)';
  }

  @override
  bool operator ==(covariant RequestLoanHomeResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.numContract == numContract &&
        other.creditStatus == creditStatus &&
        other.valueCredit == valueCredit &&
        other.userId == userId &&
        other.creditIdCelcoin == creditIdCelcoin &&
        other.createdAt == createdAt &&
        other.situacao == situacao;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        numContract.hashCode ^
        creditStatus.hashCode ^
        valueCredit.hashCode ^
        userId.hashCode ^
        creditIdCelcoin.hashCode ^
        createdAt.hashCode ^
        situacao.hashCode;
  }
}

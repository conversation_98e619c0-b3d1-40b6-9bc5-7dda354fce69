import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/errors/client_exception.dart';

import '../../database/storage_adapter.dart';
import '../client.dart';
import 'dio_interceptor.dart';

class DioClient extends IClient {
  late Dio _dio;
  late IStorageAdapter storage;

  DioClient({required this.storage}) {
    _dio = Dio();
    _dio.interceptors.add(DioInterceptor());
    _dio.interceptors.add(
      PrettyDioLogger(requestBody: false, responseBody: false),
    );
  }

  DioClient.withoutToken() {
    _dio = Dio();
  }

  @override
  Future<ApiResponse<T>> fetch<T>({
    required String method,
    required String path,
    dynamic data,
    String? responseType,
    DataTypes dataType = DataTypes.json,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    Map<String, dynamic>? extra,
    Map<String, dynamic>? headers,
    String? baseUrl,
    void Function(int count, int total)? onSendProgress,
    void Function(int count, int total)? onReceiveProgress,
  }) async {
    var contentType = dataType.contentType;

    final response = await _dio
        .fetch(
          Options(
                method: method.toUpperCase(),
                headers: headers,
                responseType: responseType == null ? null : ResponseType.bytes,
                extra: extra,
                sendTimeout: sendTimeout,
                receiveTimeout: receiveTimeout,
                contentType: contentType,
              )
              .compose(
                _dio.options,
                path,
                queryParameters: queryParameters,
                data: data,
                onReceiveProgress: onReceiveProgress,
                onSendProgress: onSendProgress,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        )
        .onError((error, stackTrace) {
          if (error is DioException) {
            var type = ClientExceptionType.values.byName(error.type.name);
            throw ClientException(
              requestOptions: error.requestOptions,
              statusCode: error.response?.statusCode,
              response: error.response,
              data: error.response?.data,
              error: error,
              type: type,
            );
          } else {
            throw ClientException(error: error, stackTrace: stackTrace);
          }
        });

    return ApiResponse(
      data: response.data,
      statusCode: response.statusCode,
      response: response,
    );
  }

  // @override
  // Future<ApiResponse> delete(String path, dynamic data) async {
  //   // final response = await _dio
  //   //     .delete(
  //   //   path,
  //   //   data: data,
  //   // )
  //   //     .onError((error, stackTrace) {
  //   //   if (error is DioException) {
  //   //     throw ClientException(
  //   //       requestOptions: error.requestOptions,
  //   //       statusCode: error.response?.statusCode,
  //   //       response: error.response,
  //   //       error: error,
  //   //     );
  //   //   } else {
  //   //     throw ClientException(
  //   //       error: error,
  //   //       stackTrace: stackTrace,
  //   //     );
  //   //   }
  //   // });

  //   // return ApiResponse(
  //   //   data: response.data,
  //   //   statusCode: response.statusCode,
  //   //   response: response,
  //   // );

  //   throw UnimplementedError();
  // }

  @override
  Future<ApiResponse> get(String path) async {
    final response = await _dio.get(path).onError((error, stackTrace) {
      if (error is DioException) {
        throw ClientException(
          requestOptions: error.requestOptions,
          statusCode: error.response?.statusCode,
          response: error.response,
          data: error.response?.data,
          error: error,
        );
      } else {
        throw ClientException(error: error, stackTrace: stackTrace);
      }
    });
    return ApiResponse(
      data: response.data,
      statusCode: response.statusCode,
      response: response,
    );
  }

  @override
  Future<ApiResponse> post(String path, dynamic data) async {
    final response = await _dio.post(path, data: data).onError((
      error,
      stackTrace,
    ) {
      if (error is DioException) {
        throw ClientException(
          requestOptions: error.requestOptions,
          statusCode: error.response?.statusCode,
          response: error.response,
          data: error.response?.data,
          error: error,
        );
      } else {
        throw ClientException(error: error, stackTrace: stackTrace);
      }
    });
    return ApiResponse(
      data: response.data,
      statusCode: response.statusCode,
      response: response,
    );
  }

  // @override
  // Future<ApiResponse> put(String path, dynamic data) async {
  //   // final response = await _dio
  //   //     .put(
  //   //   path,
  //   //   data: data,
  //   // )
  //   //     .onError((error, stackTrace) {
  //   //   if (error is DioException) {
  //   //     throw ClientException(
  //   //       requestOptions: error.requestOptions,
  //   //       statusCode: error.response?.statusCode,
  //   //       response: error.response,
  //   //       data: error.response?.data,
  //   //       error: error,
  //   //     );
  //   //   } else {
  //   //     throw ClientException(
  //   //       error: error,
  //   //       stackTrace: stackTrace,
  //   //     );
  //   //   }
  //   // });
  //   // return ApiResponse(
  //   //   data: response.data,
  //   //   statusCode: response.statusCode,
  //   //   response: response,
  //   // );
  //   throw UnimplementedError();
  // }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}

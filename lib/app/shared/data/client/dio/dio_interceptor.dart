import 'package:dio/dio.dart';
import '../../../../app_controller.dart';
import '../../../config/environment.dart';
import '../../../constants/constants.dart' as header;

class DioInterceptor extends Interceptor {
  final String? authorization;

  DioInterceptor({this.authorization});

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    options.baseUrl = Environment.baseUrl;

    // var token = await StorageUtils.getTokenResponse();
    var token = AppSession.getInstance().getAuth();

    var timeOut = const Duration(minutes: 2);
    options.connectTimeout = timeOut;
    options.sendTimeout = timeOut;
    options.receiveTimeout = timeOut;

    if (authorization != null) {
      options.headers[header.Headers.AUTH] = authorization;
    } else {
      if (token.isNotEmpty) {
        options.headers[header.Headers.AUTH] = token;
      }
    }

    options.headers['Content-type'] = 'application/json';

    options.headers[header.Headers.USER_AGENT] =
        AppSession.getInstance().userAgent;

    options.headers[header.Headers.APP_IDENTIFIER] =
        AppSession.getInstance().uniqueDeviceID;

    options.headers[header.Headers.APP_VERSION] =
        AppSession.getInstance().versaoAppHeader;

    // LoggerUtils.d(msg: "HEADERS: ${options.headers.toString()}");

    super.onRequest(options, handler);
  }
}

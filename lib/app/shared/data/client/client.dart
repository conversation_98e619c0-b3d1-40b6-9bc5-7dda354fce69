import 'api_response.dart';

enum DataTypes {
  json,
  formData,
  none,
  xWwwFormUrlencoded;

  String? get contentType {
    switch (this) {
      case DataTypes.none:
        return null;
      case DataTypes.json:
        return 'application/json';
      case DataTypes.xWwwFormUrlencoded:
        return 'application/x-www-form-urlencoded';
      case DataTypes.formData:
        return 'multipart/form-data';
    }
  }
}

abstract class IClient {
  Future<ApiResponse<T>> fetch<T>({
    required String method,
    required String path,
    String? responseType,
    dynamic data,
    DataTypes dataType = DataTypes.json,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    Map<String, dynamic>? extra,
    Map<String, dynamic>? headers,
    String? baseUrl,
    void Function(int count, int total)? onSendProgress,
    void Function(int count, int total)? onReceiveProgress,
  });
}

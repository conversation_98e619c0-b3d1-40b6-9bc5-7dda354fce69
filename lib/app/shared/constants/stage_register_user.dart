enum StageEnum {
  // BASIC_DATA,
  ADDRESS,
  PHONE_CHECK,
  PASSWORD,
  TERMS,
  DOCUMENTS,
  ANALYSIS_DOCUMENTS,
  DENIED_DOCUMENTS,
  FINISHED;

  static StageEnum? getByName(String? name) {
    final result = switch (name) {
      'ENDERECO' => StageEnum.ADDRESS,
      'VERIFICACAO_CELULAR' => StageEnum.PHONE_CHECK,
      'SENHA' => StageEnum.PASSWORD,
      'TERMOS' => StageEnum.TERMS,
      'DOCUMENTOS' => StageEnum.DOCUMENTS,
      'DOCUMENTACAO_PENDENTE' => StageEnum.DOCUMENTS,
      'ANALISE_DOCUMENTACAO' => StageEnum.ANALYSIS_DOCUMENTS,
      'DOCUMENTACAO_NEGADA' => StageEnum.DENIED_DOCUMENTS,
      'FINALIZADO' => StageEnum.FINISHED,
      _ => null,
    };
    return result;
  }

  /// Retorna o nome do stage em String, no mesmo padrao
  /// que vem da API
  String get label {
    final stage = this;
    final result = switch (stage) {
      StageEnum.ADDRESS => 'ENDERECO',
      StageEnum.PHONE_CHECK => 'VERIFICACAO_CELULAR',
      StageEnum.PASSWORD => 'SENHA',
      StageEnum.TERMS => 'TERMOS',
      StageEnum.DOCUMENTS => 'DOCUMENTOS',
      StageEnum.ANALYSIS_DOCUMENTS => 'ANALISE_DOCUMENTACAO',
      StageEnum.DENIED_DOCUMENTS => 'DOCUMENTACAO_NEGADA',
      StageEnum.FINISHED => 'FINALIZADO',
    };
    return result;
  }
}

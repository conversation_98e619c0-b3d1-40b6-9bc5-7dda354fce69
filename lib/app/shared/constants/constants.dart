import 'package:flutter/foundation.dart';

import '../config/flavor.dart';

class Constants {
  static const bool BUILD_LOJA = (!kDebugMode);
  static final bool mock = Flavor.isDevelopment;
  static const String EMAIL_SUPORTE_SICLOS = "<EMAIL>";

  static const int COD_BARRAS = 44;
  static const int LINHA_DIGITAVEL = 47;
  static const int LIMITE_MERCADO_INDICADO = 3;
  static const int LIMITE_MERCADO = 10;
  static const int TAMANHO_IMAGEM_BANCO = 786;
  static const double MIN_EMPRESTIMO_PESSOAL = 100.0;
  static const double MIN_EMPRESTIMO_CONSIGNADO = 300.0;
  static const double BOTTOM_NOTCH_HEIGHT = 16;
  // static const String URL_CHAT_BOT =
  //     "https://chat.movidesk.com/ChatWidgetNew/WidgetGroup/4E4FBB7F0C43492BA28ABD67C609D4FF";

  static const String BANK_ACCOUNT_NAME = "Siclos (Celcoin)";
}

class Headers {
  static const String AUTH = "Authorization";
  static const String USER_AGENT = "User-Agent";
  static const String APP_VERSION = "X-App-Version";
  static const String APP_IDENTIFIER = "X-App-Identifier";
}

class PerfilUsuario {
  static const String TOMADOR = "tomador";
  static const String INVESTIDOR = "investidor";
}

class LoginDigital {
  static const String IS_ENABLED = "isEnabled";
  static const String KEY = "k";
  static const String LAST_LOGIN = "log";
  static const String LAST_SENHA = "pas";
  static const String SHOW_SHEET = "show_sheet";
}

// class TipoDocumentoCadastro {
//   static const String SELFIE = "selfie_identificacao";
//   static const String CARTAO_CNPJ = "cartao_cnpj";
//   static const String ENDERECO_EMPRESA = "endereco_empresa";
//   static const String IDENTIFICACAO_FRENTE = "identificacao_frente";
//   static const String IDENTIFICACAO_VERSO = "identificacao_verso";
// }

class ContaCorrenteContants {
  static const String MOSTRA_BANNER = "mostra_banner";
  static const String SHOW_DIALOG_CARTAO_DEBITO = "show_dialog_cartao_debito";
}

// class CartoesEspecificos {
//   static const String SADA = "sada1";
// }

class ChannelMetodos {
  static const IMAGE_PICKER_CHANNEL = "imagePickerChanner";
  static const IMAGE_METODO = "image";
  // static const DOCUMENTO_METODO = "documento";
}

abstract class ConstantDevice {
  static final List<String> IOS_MACHINES_WITH_BOTTOM_NOTCH = [
    'iPhone10,3',
    'iPhone10,6',
    'iPhone11,2',
    'iPhone11,4',
    'iPhone11,6',
    'iPhone11,8',
    'iPhone12,1',
    'iPhone12,3',
    'iPhone12,5',
    'iPhone12,8',
    'iPhone13,1',
    'iPhone13,2',
    'iPhone13,3',
    'iPhone13,4',
    'iPhone14,2',
    'iPhone14,3',
    'iPhone14,4',
    'iPhone14,5',
    'iPhone14,6',
    'iPhone14,7',
    'iPhone14,8',
    'iPhone15,2',
    'iPhone15,3'
  ];
}

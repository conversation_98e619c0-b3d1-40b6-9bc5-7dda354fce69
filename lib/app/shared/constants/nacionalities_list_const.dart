abstract class Nacionalities {
  static const list = [
    'Afeg<PERSON> (a)',
    '<PERSON><PERSON><PERSON><PERSON> (a)',
    '<PERSON><PERSON>o (a)',
    'Andorrano (a)',
    'Angolano (a)',
    'Antiguano (a)',
    '<PERSON>rab<PERSON>',
    '<PERSON><PERSON><PERSON> (a)',
    'Argentino (a)',
    '<PERSON><PERSON><PERSON> (a)',
    'Australiano (a)',
    'Austríaco (a)',
    'Azerbaijano (a)',
    'Bahamense',
    'Bareinita',
    'Bangladeshiano (a)',
    'Barbadiano (a)',
    'Belga',
    'Belizeano (a)',
    'Beninense',
    'Bielorrusso (a)',
    'Boliviano (a)',
    'Bósnio (a)',
    'Botsuanense',
    'Brasileiro (a)',
    'Britânico (a)',
    '<PERSON><PERSON><PERSON><PERSON> (a)',
    'Búlgaro (a)',
    'Burquinense',
    'Burundiano (a)',
    'But<PERSON><PERSON>s (a)',
    'Cabo-verdiano (a)',
    'Camaronês (a)',
    'Cambojano (a)',
    'Canadense',
    'Catariano (a)',
    'Cazaque',
    'Centro-africano (a)',
    '<PERSON><PERSON><PERSON> (a)',
    'Chileno (a)',
    '<PERSON><PERSON>s (a)',
    'Cipriota',
    'Colombiano (a)',
    '<PERSON>rense',
    'Congolês (a)',
    'Costarriquenho (a)',
    'Croata',
    'Cubano (a)',
    'Dinamarquês (a)',
    'Djiboutiano (a)',
    'Dominicano (a)',
    'Equatoriano (a)',
    'Egípcio (a)',
    'Emiradense',
    'Eritreu (a)',
    'Eslovaco (a)',
    'Esloveno (a)',
    'Espanhol (a)',
    'Estadunidense',
    'Estoniano (a)',
    'Etíope',
    'Fijiano (a)',
    'Filipino (a)',
    'Finlandês (a)',
    'Francês (a)',
    'Gabonês (a)',
    'Gambiano (a)',
    'Ganês (a)',
    'Georgiano (a)',
    'Grego (a)',
    'Granadino (a)',
    'Guatemalteco (a)',
    'Guianense',
    'Guineense',
    'Haitiano (a)',
    'Hondurenho (a)',
    'Húngaro (a)',
    'Iemenita',
    'Indiano (a)',
    'Indonésio (a)',
    'Iraniano (a)',
    'Iraquiano (a)',
    'Irlandês (a)',
    'Islandês (a)',
    'Israelense',
    'Italiano (a)',
    'Jamaicano (a)',
    'Japonês (a)',
    'Jordaniano (a)',
    'Queniano (a)',
    'Quirguiz',
    'Kuwaitiano (a)',
    'Laociano (a)',
    'Letão (a)',
    'Libanês (a)',
    'Liberiano (a)',
    'Líbio (a)',
    'Liechtensteinense',
    'Lituano (a)',
    'Luxemburguês (a)',
    'Macedônio (a)',
    'Madagascarense',
    'Malaio (a)',
    'Malauiano (a)',
    'Maldivo (a)',
    'Maliano (a)',
    'Maltês (a)',
    'Marroquino (a)',
    'Mauriciano (a)',
    'Mauritano (a)',
    'Mexicano (a)',
    'Micronésio (a)',
    'Moçambicano (a)',
    'Moldavo (a)',
    'Monegasco (a)',
    'Mongol',
    'Montenegrino (a)',
    'Mianmarense',
    'Namibiano (a)',
    'Nauruano (a)',
    'Nepalês (a)',
    'Nigerino (a)',
    'Nigeriano (a)',
    'Norueguês (a)',
    'Neozelandês (a)',
    'Omanense',
    'Paquistanês (a)',
    'Palauense',
    'Palestino (a)',
    'Panamenho (a)',
    'Papua-nova-guineense',
    'Paraguaio (a)',
    'Peruano (a)',
    'Polonês (a)',
    'Português (a)',
    'Qatariano (a)',
    'Romeno (a)',
    'Ruandês (a)',
    'Russo (a)',
    'Samoano (a)',
    'Santa-lucense',
    'São-cristovense',
    'São-marinense',
    'São-tomense',
    'Saudita',
    'Senegalês (a)',
    'Sérvio (a)',
    'Seichelense',
    'Singapurense',
    'Sírio (a)',
    'Somali',
    'Sudanês (a)',
    'Surinamês (a)',
    'Sueco (a)',
    'Suíço (a)',
    'Tailandês (a)',
    'Tanzaniano (a)',
    'Togolês (a)',
    'Tunisiano (a)',
    'Turco (a)',
    'Tuvaluano (a)',
    'Ucraniano (a)',
    'Ugandense',
    'Uruguaio (a)',
    'Uzbeque',
    'Venezuelano (a)',
    'Vietnamita',
    'Zambiano (a)',
    'Zimbabuano (a)',
  ];
}

import 'dart:developer';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

abstract class FirebaseNotifications {
  static FirebaseMessaging? _firebaseMessaging;

  static FirebaseMessaging get _instance {
    _firebaseMessaging ??= FirebaseMessaging.instance;
    return _firebaseMessaging!;
  }
  // @pragma('vm:entry-point')
  // static Future<void> _firebaseMessagingBackgroundHandler(
  //     RemoteMessage message) async {
  //   String valueData = message.data.toString();

  //   if (onMessagingRecived != null) {
  //     onMessagingRecived!(valueData);
  //     // }
  //   }

  //   log("Handling a background message: ${message.messageId}");
  // }

  static Future<String> getToken() async {
    // await _getPermission(_firebaseMessaging);s
    String? tokenDevice;

    if (!kIsWeb && Platform.isIOS) {
      String? apnsToken = await _instance.getAPNSToken();
    }
    try {
      tokenDevice = await _instance.getToken();
    } catch (e) {
      rethrow;
    }
    // LoggerUtils.d(msg: "Token : $tokenDevice");

    return tokenDevice ?? "none";
  }

  static getPermissions() async {
    try {
      await _getPermission(_instance);
    } on Exception catch (e) {}
  }

  static firebaseMessagingNotify() async {
    try {
      FirebaseMessaging.onMessage.listen(
        (event) {
          log("on message ${event.toMap()}");
          // onNotify();
          if (Platform.isAndroid) {
            showSimpleNotification(
              title: event.notification!.title,
              message: event.notification!.body,
            );
          }
          if (Platform.isIOS) {
            showSimpleNotification(
              title: event.notification!.title,
              message: event.notification!.body,
            );
          }
        },
      );
    } on Exception catch (e) {}
  }

  static Future<void> showSimpleNotification(
      {String? title = '', String? message = ''}) async {
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    var initializationSettingsAndroid =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    var initializationSettingsIOS = const DarwinInitializationSettings();

    //-------------------
    var initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

    await flutterLocalNotificationsPlugin.initialize(initializationSettings);

    //-------------------
    var androidPlatformChannelSpecifics = const AndroidNotificationDetails(
        'your_channel_id', 'your_channel_name',
        importance: Importance.max, priority: Priority.high, ticker: 'ticker');
    var iosPlatformChannelSpecifics = const DarwinNotificationDetails();
    var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iosPlatformChannelSpecifics,
    );

    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      message,
      platformChannelSpecifics,
    );
  }

  static _getPermission(FirebaseMessaging firebaseMessaging) async =>
      await firebaseMessaging.requestPermission();
}

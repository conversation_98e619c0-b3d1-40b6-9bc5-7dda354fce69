import 'dart:developer';

import 'package:siclosbank/app/shared/errors/errors.dart';

import '../../../localization/generated/i18n.dart';
import 'error_response.dart';
import 'client_exception.dart';

abstract class IServerMethods {}

abstract class ServerErrorHandling implements IServerMethods {
  static Future handleError(Object obj) async {
    if (obj is ClientException) {
      if (obj.type == ClientExceptionType.connectionTimeout ||
          obj.type == ClientExceptionType.connectionError) {
        return Future.error(
          NoInternet(message: const I18n().error_no_internet),
        );
      }

      final result = switch (obj.statusCode) {
        400 => BadRequest(statusCode: 400, message: obj.data['message']),
        401 => TokenInvalid(
          message:
              "Acesso não autorizado. Verifique suas credenciais e tente novamente.",
        ),
        403 || 406 => ErrorForbidden(
          message: obj.data['message'].toString(),
          statusCode: obj.statusCode,
        ),
        404 => NotFound(),
        410 => PixKeyUsedOtherUser(
          message:
              'Cadastro de chave não permitido. Essa chave já pertence a outra pessoa.',
        ),
        411 => PixKeyUsed(message: 'Chave já registrada.'),
        412 => PixKeyUsedOtherAccount(
          message: 'Chave já cadastrada em outra conta.',
        ),
        413 => PixKeyUsedOtherAccount(
          message: 'Chave já em processo de portabilidade/reivindicação.',
        ),
        500 => ErrorResponse(
          statusCode: 500,
          message: const I18n().errorsGenericIntern,
        ),
        503 => SistemaManutencao(message: const I18n().sistema_manutencao),
        _ => _erroGenerico(),
      };
      return Future.error(result);
    }
    return Future.error(_erroGenerico());
  }

  // var data = Map.from(obj.data);

  // if (obj.statusCode == 406) {
  //   return Future.error(ErrorResponse(
  //     code: obj.statusCode,
  //     message: data['message'].toString(),
  //     error: obj.error,
  //     statusCode: obj.statusCode,
  //   ));
  // }

  // if (obj.statusCode == 407) {
  //   return Future.error(ErrorResponse(
  //     code: obj.statusCode,
  //     message: data['message'].toString(),
  //     error: obj.error,
  //     statusCode: obj.statusCode,
  //   ));
  // }

  static ErrorResponse _erroGenerico() {
    return ErrorResponse(statusCode: 500, message: const I18n().errorsGeneric);
  }
}

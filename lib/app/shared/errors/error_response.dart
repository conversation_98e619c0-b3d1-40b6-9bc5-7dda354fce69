// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class ErrorResponse implements Exception {
  final int? statusCode;
  final String? message;
  int? code;
  final dynamic error;

  ErrorResponse({
    this.statusCode,
    this.message,
    this.code,
    this.error,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'statusCode': statusCode,
      'message': message,
      'code': code,
      'error': error,
    };
  }

  factory ErrorResponse.fromMap(Map<String, dynamic> map) {
    return ErrorResponse(
      statusCode: map['statusCode'] as int?,
      message: map['message'] as String?,
      code: map['code'] as int?,
      error: map['error'],
    );
  }

  String toJson() => json.encode(toMap());

  factory ErrorResponse.fromJson(String source) =>
      ErrorResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  ErrorResponse copyWith({
    int? errorCode,
    String? message,
    int? code,
    ErrorResponse? error,
  }) {
    return ErrorResponse(
      statusCode: errorCode ?? statusCode,
      message: message ?? this.message,
      code: code ?? this.code,
      error: error ?? this.error,
    );
  }

  @override
  String toString() {
    return 'ErrorResponse(errorCode: $statusCode, message: $message, code: $code, error: $error)';
  }

  @override
  bool operator ==(covariant ErrorResponse other) {
    if (identical(this, other)) return true;

    return other.statusCode == statusCode &&
        other.message == message &&
        other.code == code &&
        other.error == error;
  }

  @override
  int get hashCode {
    return statusCode.hashCode ^
        message.hashCode ^
        code.hashCode ^
        error.hashCode;
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

enum ClientExceptionType {
  connectionTimeout,
  connectionError,
  sendTimeout,
  receiveTimeout,
  badCertificate,
  badResponse,
  cancel,
  unknown,
}

class ClientException extends Equatable implements Exception {
  // final int? errorCode;
  final dynamic data;
  final Object? requestOptions;
  final Object? error;
  final Object? response;
  final StackTrace? stackTrace;
  final int? statusCode;
  final ClientExceptionType? type;

  const ClientException({
    this.statusCode,
    this.data,
    this.requestOptions,
    this.response,
    this.error,
    this.stackTrace,
    this.type,
  });

  @override
  List<Object?> get props =>
      [data, statusCode, requestOptions, error, response, stackTrace, type];

  @override
  bool get stringify => true;

  @override
  String toString() {
    return 'ClientException{data=$data, statusCode=$statusCode, response=$response, error=$error, stackTrace=$stackTrace, statusCode=$statusCode, type=$type}';
  }
}

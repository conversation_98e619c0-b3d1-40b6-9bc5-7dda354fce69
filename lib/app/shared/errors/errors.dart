import 'error_response.dart';

class NotFound extends ErrorResponse {
  NotFound({super.message = 'Não encontrado', super.statusCode = 404});
}

class BadRequest extends ErrorResponse {
  BadRequest({super.message, super.statusCode = 400});
}

class NoInternet extends ErrorResponse {
  NoInternet({super.message});
}

class SistemaManutencao extends ErrorResponse {
  SistemaManutencao({super.message});
}

class TokenInvalid extends ErrorResponse {
  TokenInvalid({super.message, super.statusCode = 401})
      : super(code: statusCode);
}

class ErrorForbidden extends ErrorResponse {
  ErrorForbidden({super.message, super.statusCode});
}

class ErrorFile extends ErrorResponse {
  ErrorFile(message, {super.error})
      : super(
          message: message,
        );
}

// chave cadastrada por outra pessoa.
// precisa solicitar reivindicacao
class PixKeyUsedOtherUser extends ErrorResponse {
  PixKeyUsedOtherUser({super.statusCode = 410, super.message});
}

// chave já cadastrada pelo usuario
class PixKeyUsed extends ErrorResponse {
  PixKeyUsed({super.statusCode = 411, super.message});
}

// chave cadastrada pelo usuario em outra conta/banco
// precisa solicitar portabilidade
class PixKeyUsedOtherAccount extends ErrorResponse {
  PixKeyUsedOtherAccount({super.statusCode = 412, super.message});
}

/// chave já em processo de portabilidade/reivindicação
class PixKeyAlreadyClaim extends ErrorResponse {
  PixKeyAlreadyClaim({super.statusCode = 413, super.message});
}

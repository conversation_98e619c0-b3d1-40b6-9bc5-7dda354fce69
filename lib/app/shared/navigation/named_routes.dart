abstract class Routes {
  static const initial = '/';
  static const intro = '/intro';
  static const login = '/login';

  static const pin = '/pin';
  static const pinRegistration = '$pin/pin-registration';
  static const checkPin = '$pin/check-pin';
  static const pinBlocked = '$pin/pin-blocked';

  // 2FA
  static const authorizeDevice = '/authorize-device';
  static const loginAuthorizeDevice = '$login$authorizeDevice';
  static const checkDevice = '/check-device';
  static const loginCheckDevice = '$login$checkDevice';
  static const codeSendSms = '/code-send-sms';
  static const loginCodeSendSms = '$login$codeSendSms';
  static const codeSendEmail = '/code-send-email';
  static const loginCodeSendEmail = '$login$codeSendEmail';
  static const unauthorizedDevicePage = '/unauthorized-device';
  static const loginUnauthorizedDevicePage = '$login$unauthorizedDevicePage';
  static const recoveryPassword = '/recover-password';
  static const loginRecoveryPassword = '$login$recoveryPassword';

  static const signUp = '/sign-up';
  static const errorCollaboratorOnly = '/error-collaborator-only';
  static const errorAlreadyRegistered = '/error-already-registered';
  static const signUpRegister = '$signUp/register';
  static const webviewDocuments = '/webview-documents';
  static const signUpErrorCollaboratorOnly = '$signUp$errorCollaboratorOnly';
  static const signUpErrorAlreadyRegistered = '$signUp$errorAlreadyRegistered';
  static const signUpWebviewDocumentos = '$signUp$webviewDocuments';

  // tela principal
  static const layout = '/layout';
  static const layoutInvestments = '$layout$investments';

  /// home
  static const home = '/home';
  static const layoutHome = '$layout$home';
  static const giftCardPage = '$home/gift-card';
  static const transaction = '/transaction';
  static const deposit = '/deposit';
  // static const paymentPage = '$home/payment';
  // static const rechargePage = '$home/recharge';

  // static const cardsPage = '$home/cards';
  static const notifications = '$home/notifications';

  /// emprestimos page
  static const loan = '/loan';
  static const layoutLoan = '$layout$loan';
  static const loanFastCredit = '$loan/fast-credit-simulation';
  static const loanConventional = '$loan/conventional-simulation';
  static const loanSendToAnalysis = '$loan/send-to-analysis';
  static const loanDetails = '$loan/details';
  static const loanDetailsInstallment = '$loan/details-installment';
  static const loanSignature = '$loan/signature-terms';
  static const loanHomeCards = '$loan/home-cards';

  ///
  static const investments = '/investments';

  /// Gestao page
  // static const management = '/management';
  static const professional = '/professional';
  static const layoutProfessional = '$layout$professional';
  static const payslipPage = '$professional/payslip';
  static const hoursPage = '$professional/hours';
  static const vacationPage = '$professional/vacation';
  static const irPage = '$professional/ir';

  /// perfil/ajustes page
  static const profile = '/profile';
  static const layoutProfile = '$layout$profile';
  static const devicesPage = '$profile/manage-devices';
  static const profileCodeSendEmail = '$devicesPage$codeSendEmail';

  static const irCCPage = '$profile/ircc';
  static const tariffsPage = '$profile/tariffs';
  static const changePasswordPage = '$profile/change-password';
  static const changeAddressPage = '$profile/change-address';
  static const changeEmailPage = '$profile/change-email';
  static const changeDisplayNamePage = '$profile/change-display-name';
  static const changeProfilePicturePage = '$profile/change-profile-picture';
  //   static const devicesPage = '$layoutProfile/devices';
  static const termsPage = '$profile/terms';
  static const privacyPolicyPage = '$profile/privacy-policy';
  static const adjustmentsPage = '$profile/adjustments';

  // static const termsPage = '$profilePage/terms';
  // static const privacyPolicyPage = '$profilePage/privacy-policy';
  // static const adjustmentsPage = '$profilePage/adjustments';
  // static const changeImgPage = '$adjustmentsPage/changeImg';
  // static const changeDisplayNamePage = '$adjustmentsPage/changeName';
  //
  // static const changePasswordPage = '$adjustmentsPage/changePassword';
  // static const changePINPage = '$adjustmentsPage/changePIN';
  // static const changeAddressPage = '$adjustmentsPage/changeAddress';
  // static const changeBiometryPage = '$adjustmentsPage/changeBiometry';

  static const statement = '/statement';
  static const statementDetails = '$statement/details';

  static const help = '/help';

  static const pix = '/pix';
  static const pixMyKeys = '$pix/my-keys';
  static const pixOnboarding = '$pix/onboarding-home';
  static const onboardingMyKeys = '$pix/onboarding-my-keys';
  static const questions = '$pix/questions';
  static const myLimitsPix = '$pix/my-limits-pix';
  static const newKey = '$pix/new-key';
  static const inputNewKey = '$pix/input-new-key';
  static const refund = '$pix/refund';
  static const chargePix = '$pix/charge-pix';
  static const dynamicQrcodePix = '$pix/dynamic-qrcode';
  static const pixWithKey = '$pix/pix-with-key';
  static const pixTransfer = '$pix/pix-transfer';
  static const pixConfirmation = '$pix/pix-confirmation';
}

String getRoute(String route) {
  if (route.endsWith('/')) {
    return route;
  } else {
    return '$route/';
  }
}

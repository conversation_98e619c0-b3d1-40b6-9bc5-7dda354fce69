import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

pop<T>([result]) {
  return Modular.to.pop<T>(result);
}

/// Para disponibilizar a opcao de voltar recomendado usar o 'push'.
/// Esta ação substitui todas as rotas anteriores.
void navigate(String route, {dynamic args}) {
  return Modular.to.navigate(route, arguments: {
    'args': args,
    'transitionType': TransitionType.leftToRight,
  });
}

Future<T?> push<T>(String route, {dynamic args}) {
  return Modular.to.pushNamed<T>(route, arguments: {
    'args': args,
    'transitionType': TransitionType.leftToRight,
  });
}

Future pushReplacement(String route, {dynamic args}) {
  return Modular.to.pushReplacementNamed(route, arguments: {
    'args': args,
    'transitionType': TransitionType.leftToRight,
  });
}

Future pushReplaceFade(String route, {dynamic args}) {
  return Modular.to.pushReplacementNamed(route, arguments: {
    'args': args,
    'transitionType': TransitionType.leftToRight,
  });
}

// Future pushAndRemoveUntil(String newRouteName,
//     {String? predicateNameRoute, dynamic args}) {
//   return Modular.to.pushNamedAndRemoveUntil(newRouteName,
//       (ModalRoute.withName(predicateNameRoute ?? Routes.layoutHome)),
//       arguments: {
//         'args': args,
//         'transitionType': TransitionType.leftToRight,
//       });
// }

Future<T?> pushSlideResult<T>(BuildContext context, Widget page) {
  return Navigator.push<T>(
    context,
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) =>
          child,
    ),
  );
}

enum FlavorType { dev, prod, stag }

abstract class Flavor {
  Flavor._instance();

  static late FlavorType flavorType;

  static void configure(String flavor) {
    switch (flavor) {
      case 'dev':
        flavorType = FlavorType.dev;
        break;
      case 'stag':
        flavorType = FlavorType.stag;
        break;
      case 'prod':
        flavorType = FlavorType.prod;
        break;
      default:
        throw Exception('Unknown Flavor: $flavor');
    }
  }

  static bool get isDevelopment => flavorType == FlavorType.dev;
  static bool get isStaging => flavorType == FlavorType.stag;
  static bool get isProduction => flavorType == FlavorType.prod;
}

/// condicional para verificar se o ambiente é desenvolvoimento.
/// Usado para ocultar componentes do ambiente de homologacao e producao.
final isHomologOrDev = Flavor.isDevelopment; // || Flavor.isStaging;

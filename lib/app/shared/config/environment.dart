import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';

import 'flavor.dart';

class Environment {
  static late Map<String, dynamic> _config;

  static Future<void> initialize(String flavor) async {
    final jsonStr = await rootBundle.loadString('assets/env/env_$flavor.json');
    _config = json.decode(jsonStr);

    Flavor.configure(flavor);
  }

  static String get baseUrl => _config['API_BASE_URL'];

  static String get newRelicToken => Platform.isIOS
      ? _config['NEW_RELIC_TOKEN_IOS']
      : _config['NEW_RELIC_TOKEN_ANDROID'];
}

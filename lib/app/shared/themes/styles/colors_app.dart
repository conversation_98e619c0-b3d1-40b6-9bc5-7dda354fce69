import 'package:flutter/material.dart';

abstract class ColorsApp {
  ColorsApp._();

  static const Color scaffoldColor = Color.fromARGB(255, 235, 235, 235);
  static const Color appBarColor = Color(0xFFF7F7F7);
  static const Color lineColor = Color(0xFFDADADA);
  static const Color textColor = Color(0xFF4B4B4B);
  static const Color iconColor = Color(0xFF4B4B4B);
  static const Color disableTextColor = Color(0xFF8B8B8B);
  static const Color bgQrcode = Color(0x80000000);

  // this basically makes it so you can instantiate this class
  static const MaterialColor verde = MaterialColor(
    0xFF8ADF4F,
    <int, Color>{
      50: Color(0xFFF4FCE3),
      100: Color(0xFFE9FAC8),
      200: Color(0xFFD8F5A2),
      300: Color(0xFFC0EB75),
      400: Color(0xFFB1E564),
      500: Color(0xFF8ADF4F),
      600: Color(0xFF5DC543),
      700: Color(0xFF237547),
      800: Color(0xFF1D5C2B),
      900: Color(0xFF0E2F1C),
    },
  );

  static const MaterialColor azul = MaterialColor(
    0xFF00424B,
    <int, Color>{
      100: Color(0xFF4BB3B4),
      800: Color(0xFF00424B),
    },
  );

  static const Color cinzaSolo = Color(0xFF4B4B4B);

  static const Color cinzaDetalhesContatoConta = Color(0xFF767676);
  static const Color cinzaDetalhesDadosUsuario = Color(0xFFF8F9FA);
  static const Color whiteForegroundLogin = Color(0xFFF8F9FA);
  static const Color colorRatingRiscoBMais = Color(0xFF1CB0F6);
  static const Color colorRatingRiscoB = Color(0xFFACD4F1);
  static const Color colorRatingRiscoCMais = Color(0xFFFFB800);
  static const Color colorRatingRiscoC = Color(0xFFFAE87D);
  static const Color colorRatingRiscoDMais = Color(0xFFFFB266);
  static const Color colorRatingRiscoD = Color(0xfffab673);
  static const Color colorRatingRiscoEMais = Color(0xFFFE9796);
  // static const Color colorRatingRiscoE = Color(0xFFFEBFB8);
  static const Color greyScaleCinza0 = Color(0xFFF8F9FA);
  static const Color colorRatingRiscoE = Color(0xFFF3B3B3);

  static const Color colorInfoAlert = Color(0XFFE53616);

  static const MaterialColor info = MaterialColor(
    0xFF00424B,
    <int, Color>{
      100: Color(0xFFE8F4FD),
      200: Color(0xFFD0E9FB),
      300: Color(0xFF1CB0F6),
    },
  );

  static const MaterialColor error = MaterialColor(
    0xFF00424B,
    <int, Color>{
      100: Color(0xFFFAEAEA),
      200: Color(0xFFF4D2D2),
      300: Color(0xFFFF4B4B),
    },
  );

  static const MaterialColor atencao = MaterialColor(
    0xFFD7EAD9,
    <int, Color>{
      100: Color(0xFFFFF9B1),
      200: Color(0xFFFFE965),
      300: Color(0xFFFFB800),
    },
  );

  static const MaterialColor correto = MaterialColor(
    0xFFD7EAD9,
    <int, Color>{
      100: Color(0xFFEBF4EC),
      200: Color(0xFFD7EAD9),
      300: Color(0xFF2D7738),
    },
  );
  static const Color bgDesativado = Color(0xFFE2E2E2);

  static const MaterialColor cinza = MaterialColor(
    0xFF201F1F,
    <int, Color>{
      100: Color(0xFFF7F7F7),
      200: Color(0xFFE9ECEF),
      300: Color(0xFFDEE2E6),
      400: Color(0xFFDBDDE0),
      500: Color(0xFFADB5BD),
      600: Color(0xFF777777),
      700: Color(0xFF495057),
      800: Color(0xFF343A40),
      900: Color(0xFF201F1F),
    },
  );

  static const MaterialColor snow = MaterialColor(
    0xFFFAFAFA,
    <int, Color>{
      100: Color(0xFFFAFAFA),
      200: Color(0xFFFAFAFA),
      300: Color(0xFFFAFAFA),
      400: Color(0xFFFAFAFA),
      500: Color(0xFFFAFAFA),
      600: Color(0xFFFAFAFA),
      700: Color(0xFFFAFAFA),
      800: Color(0xFFFAFAFA),
      900: Color(0xFFFAFAFA),
    },
  );

  static const Color drop1 = Color(0x14000000);
  static const Color drop2 = Color(0x294F4F4F);
  static const Color drop3 = Color(0x142F4055);
  static const Color bgCamera = Color(0xFF1B1B1B);
  static const Color bgAvatarImg = Color(0xFFD7EAD9);
  // static const Color bgCheck = Color(0xFF343A40);

  static const List<Color> gradientColors = <Color>[
    Color(0XFF00CEDF),
    Color(0XFF1ED1C3),
    Color(0XFF9CE04E)
  ];

  static const List<Color> gradientColorsText = <Color>[
    Color(0XFF9CE04E),
    Color(0XFF1ED1C3),
    Color(0XFF00CEDF)
  ];

  static const List<Color> gradientColorsDividerConservador = <Color>[
    Color(0XFF9AE542),
    Color(0XFF00CEDF),
  ];

  static const List<Color> gradientColorsDividerModerado = <Color>[
    Color(0XFF9AE542),
    Color(0XFF00CEDF),
    Color(0XFFEEE400),
  ];

  static const List<Color> gradientColorsDividerArrojado = <Color>[
    Color(0XFF9AE542),
    Color(0XFF00CEDF),
    Color(0XFFEEE400),
    Color(0XFFE99446),
    Color(0XFFE76E6E),
  ];
}

import 'package:cached_network_image/cached_network_image.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';

class ImagesApp {
  static Image imgCadastroNaoAprovado() {
    return Image.asset(
      'assets/images/img_cadastro_nao_aprovado.png',
    );
  }

  static Image icSessaoExpirada() {
    return Image.asset(
      'assets/images/img_sessao_expirada.png',
    );
  }

  static SvgPicture bgCardDegrade({double? width, double? heigth}) {
    return SvgPicture.asset(
      'assets/images/bg_card_degrade.svg',
      width: width,
      height: heigth,
    );
  }

  static SvgPicture bgCardDegradeDemonstrativo(
      {double? width, double? heigth}) {
    return SvgPicture.asset(
      'assets/images/bg_degrade_desmonstrativo.svg',
      width: width,
      height: heigth,
    );
  }

  static Image imgCreditoConsignado() {
    return Image.asset(
      'assets/images/img_credito_consignado.png',
      height: 69.65,
    );
  }

  static Image bgIntroView() {
    return Image.asset(
      'assets/images/img_tela_inicial.png',
      alignment: Alignment.center,
      fit: BoxFit.cover,
    );
  }

  static Image imgBgDuvidasFrequentes() {
    return Image.asset(
      'assets/images/bg_duvidas_frequentes.png',
      height: 54,
    );
  }

  static Image imgUsuario6MesesEmpresa() {
    return Image.asset(
      'assets/images/img_6meses_empresitmo.png',
      height: 427,
    );
  }

  static Image imgUsuarioSemVinculo({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_usuario_sem_vinculo.png',
      height: height,
      width: width,
    );
  }

  static Image imgEmprestimoSimulacao({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_emprestimo_simulacao.png',
      height: height,
      width: width,
    );
  }

  static Image imgEmprestimoSimulacao6Meses({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_emprestimo_simulacao_6_messes.png',
      height: height,
      width: width,
    );
  }

  static Image imgBackgroundSelfie({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_selfie.png',
      fit: BoxFit.cover,
      height: height,
      width: width,
    );
  }

  static Image imgBackgroundCnh({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_cnh.png',
      fit: BoxFit.cover,
      width: width,
      height: height,
    );
  }

  static Image imgBackgroundRg({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_rg.png',
      fit: BoxFit.cover,
      width: width,
      height: height,
    );
  }

  static Image imgBackgroundIntro({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_intro.png',
      fit: BoxFit.cover,
    );
  }

  static Image imgEmprestimoSimulacaoEnviada() {
    return Image.asset(
      'assets/images/img_emprestimo_simulacao_enviada.png',
      width: 297,
      height: 446,
    );
  }

  static Image imgCartaoVirtual({double? height, double? width}) {
    return Image.asset(
      'assets/images/cartao_virtual.png',
      fit: BoxFit.fill,
      width: width,
      height: height,
    );
  }

  static Widget imgRendimento({double? height, double? width}) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Image.asset(
        'assets/images/rendimentos.png',
        fit: BoxFit.fill,
        width: width,
        height: height,
      ),
    );
  }

  static Image imgBackgoundCardGestao({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_backgrond_card_gestao.png',
      fit: BoxFit.fill,
      width: width,
      height: height,
    );
  }

  static CachedNetworkImage card1() {
    return buildImageNetworkCache(
        'https://cdn.siclos.net/cards/card_credito_rapido_siclos.png');
  }

  static CachedNetworkImage card2() {
    return buildImageNetworkCache(
        'https://cdn.siclos.net/cards/card_virtual.png');
  }

  static CachedNetworkImage card3() {
    return buildImageNetworkCache(
      'https://cdn.siclos.net/cards/card_siclos.png',
    );
  }

  static Image card4() {
    return Image.asset(
      'assets/images/card_4.png',
      height: 183,
    );
  }

  static Image imgSolicitarEmprestimo() {
    return Image.asset(
      'assets/images/img_simular_emprestimo.png',
      height: 431,
    );
  }

  static ClipRRect imgNaoFuncionario({double? height, double? width}) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(bottom: Radius.circular(11)),
      child: Image.asset(
        'assets/images/img_background_nao_funcionario.png',
        height: height,
        width: width,
        // fit: BoxFit.contai,
      ),
    );
  }

  static Image imgTermoPendente({double? width, double? height}) {
    return Image.asset(
      "assets/images/img_termo_pendente.png",
      width: width,
      height: height,
    );
  }

  static Image imgBackgroundMercadoIntro({double? width, double? height}) {
    return Image.asset(
      "assets/images/img_background_mercado_intro.png",
      width: width,
      height: height,
      fit: BoxFit.cover,
    );
  }

  static Image imgBackgroundInvestimentoNaoDisponivel(
      {double? width, double? height}) {
    return Image.asset(
      "assets/images/img_background_investimento_nao_disponivel.png",
      width: width,
      height: height,
      fit: BoxFit.fill,
    );
  }

  static Image imgQuestionarioInvestidor(
      {Color? color, double? width, double? height}) {
    return Image.asset(
      "assets/images/img_questionario_perfil_investidor.png",
      width: width,
      height: height,
      color: color,
    );
  }

  static CachedNetworkImage buildImageNetworkCache(String url) {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      progressIndicatorBuilder: (context, child, loadingProgress) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 255, 255, 255),
              borderRadius: BorderRadius.all(Radius.circular(5)),
            ),
          ),
        );
      },
    );
  }
}

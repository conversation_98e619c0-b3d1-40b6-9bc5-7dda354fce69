import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

TextStyle textBottomNavigation(BuildContext context, bool isSelected) {
  var theme = Theme.of(context).textTheme;
  return theme.labelSmall!.copyWith(
      color: isSelected ? ColorsApp.verde[500] : ColorsApp.cinza[300]);
}

TextStyle textTitleAppBar(BuildContext context, {bool primary = false}) {
  return Theme.of(context).textTheme.titleSmall!.copyWith(
        color: primary ? Colors.white : ColorsApp.cinza[500],
      );
}

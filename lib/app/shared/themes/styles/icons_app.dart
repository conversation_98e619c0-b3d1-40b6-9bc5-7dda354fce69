import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'colors_app.dart';

class IconsApp {
  static SvgPicture icVoltarView({Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_voltar_view.svg",
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
      // color: color,
    );
  }

  static SvgPicture icHelp([color]) {
    return SvgPicture.asset(
      "assets/icons/ic_ajuda.svg",
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  static SvgPicture icDropDown() {
    return SvgPicture.asset("assets/icons/ic_drop_down.svg");
  }

  static Widget icVisibilityOff({Color? color, double? size}) {
    return SvgPicture.asset(
      "assets/icons/ic_visibility_off.svg",
      color: color,
      width: size,
    );
  }

  static Widget icVisibility({Color? color, double? size}) {
    return SvgPicture.asset(
      "assets/icons/ic_visibility.svg",
      color: color ?? ColorsApp.azul[100],
      width: size,
    );
  }

  static SvgPicture icCheck({Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_check.svg",
      color: color ?? ColorsApp.verde[600],
    );
  }

  static SvgPicture icCheckConfirm({Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_check_confirm.svg",
      // color: color ?? ColorsApp.verde[600],
      colorFilter: ColorFilter.mode(
        color ?? ColorsApp.verde[600]!,
        BlendMode.srcIn,
      ),
    );
  }

  static SvgPicture icUnCheck() {
    return SvgPicture.asset("assets/icons/ic_uncheck.svg");
  }

  static SvgPicture icInfo({
    Color? color,
    double height = 16,
    double width = 16,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_info.svg",
      color: color,
      width: width,
      height: height,
    );
  }

  static SvgPicture icArrowRight({
    Color? color,
    double? height,
    double? width,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_arrow_right.svg",
      color: color,
      height: height,
      width: width,
    );
  }

  static SvgPicture icArrowRight2({Color? color, double? size}) {
    return SvgPicture.asset("assets/icons/ic_arrow_right_2.svg");
  }

  static SvgPicture icAlternarCamera({Color? color}) {
    return SvgPicture.asset('assets/icons/ic_alternar_camera.svg');
  }

  static SvgPicture icTirarFoto({Color? color}) {
    return SvgPicture.asset('assets/icons/ic_tirar_foto.svg');
  }

  static SvgPicture imgBemVindo({Color? color}) {
    return SvgPicture.asset("assets/icons/img_bem_vindo.svg", height: 256);
  }

  static SvgPicture icHomeSelected() {
    return SvgPicture.asset('assets/icons/ic_home_selected.svg');
  }

  static SvgPicture icHomeUnselected() {
    return SvgPicture.asset('assets/icons/ic_home_unselected.svg');
  }

  static SvgPicture icCarteiraSelected() {
    return SvgPicture.asset('assets/icons/ic_carteira_selected.svg');
  }

  static SvgPicture icCarteiraUnselected() {
    return SvgPicture.asset('assets/icons/ic_carteira_unselected.svg');
  }

  static SvgPicture icGetaoSelected() {
    return SvgPicture.asset('assets/icons/ic_gestao_selected.svg');
  }

  static SvgPicture icGestaoUnselected() {
    return SvgPicture.asset('assets/icons/ic_gestao_unselected.svg');
  }

  static SvgPicture icEmprestimoSelected() {
    return SvgPicture.asset('assets/icons/ic_emprestimo_selected.svg');
  }

  static SvgPicture icEmprestimoUnselected() {
    return SvgPicture.asset('assets/icons/ic_emprestimo_unselected.svg');
  }

  static SvgPicture icPerfilSelected() {
    return SvgPicture.asset('assets/icons/ic_perfil_selected.svg');
  }

  static SvgPicture icPerfilUnselected({Color? color}) {
    return SvgPicture.asset(
      'assets/icons/ic_perfil_unselected.svg',
      color: color,
    );
  }

  static SvgPicture siclosAppBar({double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/siclos_app_bar.svg',
      width: width,
      height: height,
    );
  }

  static SvgPicture icSiclosLogoBranco() {
    return SvgPicture.asset('assets/icons/ic_siclos_logo.svg');
  }

  static SvgPicture icVisualizarSaldoOff() {
    return SvgPicture.asset('assets/icons/ic_visualizar_saldo_off.svg');
  }

  static SvgPicture icVisualizarSaldoOn() {
    return SvgPicture.asset('assets/icons/ic_visualizar_saldo_on.svg');
  }

  static SvgPicture icEmprestimos() {
    return SvgPicture.asset('assets/icons/ic_emprestimo.svg');
  }

  static SvgPicture icTransferencia({double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/ic_transferencia.svg',
      width: width,
      height: height,
    );
  }

  static SvgPicture icPagamento() {
    return SvgPicture.asset('assets/icons/ic_pagamento.svg');
  }

  static SvgPicture icRecarga() {
    return SvgPicture.asset('assets/icons/ic_recarga.svg');
  }

  static SvgPicture icDeposito({double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/ic_deposito.svg',
      width: width,
      height: height,
    );
  }

  static SvgPicture icPix({double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/ic_baseline-pix.svg',
      width: width,
      height: height,
    );
  }

  static SvgPicture icPixUnfilled({double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/ic_pix_unfilled.svg',
      width: width,
      height: height,
    );
  }

  static SvgPicture icExtrato() {
    return SvgPicture.asset('assets/icons/ic_extrato.svg');
  }

  static SvgPicture icNotificacaoNew() {
    return SvgPicture.asset('assets/icons/ic_notificacao_new.svg');
  }

  static SvgPicture icNotificacao() {
    return SvgPicture.asset('assets/icons/ic_notificacao.svg');
  }

  static SvgPicture icArrowExpanded() {
    return SvgPicture.asset('assets/icons/ic_arrow_expand.svg');
  }

  static SvgPicture icArrowCompress() {
    return SvgPicture.asset('assets/icons/ic_arrow_compress.svg');
  }

  static SvgPicture icShare() {
    return SvgPicture.asset('assets/icons/ic_share.svg');
  }

  static SvgPicture icBoletoCopiar({
    double? width,
    double? heigth,
    Color? color,
  }) {
    return SvgPicture.asset(
      'assets/icons/ic_boleto_copiar.svg',
      width: width,
      height: heigth,
      color: color,
    );
  }

  static SvgPicture icCamera() {
    return SvgPicture.asset('assets/icons/ic_camera.svg');
  }

  static SvgPicture icDelete({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/ic_delete.svg',
      color: color,
      width: width,
      height: height,
    );
  }

  static SvgPicture icCopyData({double? height, double? width, Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_copy_data.svg",
      height: height,
      width: width,
      color: color,
    );
  }

  static SvgPicture icEdit({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_edit.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icSiclos({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_siclos.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icAdd({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_add.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icBank({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_bank.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icCifrao() {
    return SvgPicture.asset("assets/icons/ic_cifrao.svg", height: 96);
  }

  static SvgPicture icIR() {
    return SvgPicture.asset("assets/icons/ic_ir.svg", height: 72, width: 72);
  }

  /// Icone Holerite
  static SvgPicture icPayslip() {
    return SvgPicture.asset(
      "assets/icons/ic_holerite.svg",
      height: 72,
      width: 72,
    );
  }

  static Widget icHoursProfessional({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.access_time,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icVacation({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.beach_access,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icEmail({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.alternate_email,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icSenha({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.lock_outline,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icPIN({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.password,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icAddress({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.home_outlined,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icDispositivos({double? size}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.phonelink_setup_outlined,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static Widget icBiometry({double? size, Colors? color}) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Icon(
        Icons.fingerprint,
        size: size,
        color: Colors.black87,
        weight: 0.5,
      ),
    );
  }

  static SvgPicture icDownload() {
    return SvgPicture.asset("assets/icons/ic_dowload.svg");
  }

  static SvgPicture icExcluirCartao() {
    return SvgPicture.asset('assets/icons/ic_excluir_cartao.svg');
  }

  static SvgPicture icBloquearCartao() {
    return SvgPicture.asset('assets/icons/ic_bloquear_cartao.svg');
  }

  static SvgPicture icVisualizarDadosCartao() {
    return SvgPicture.asset('assets/icons/ic_visualizar_cartao.svg');
  }

  static SvgPicture icNaoVisualizarDadosCartao() {
    return SvgPicture.asset('assets/icons/ic_nao_visualizar_cartao.svg');
  }

  static SvgPicture icDesbloquearCartao() {
    return SvgPicture.asset('assets/icons/ic_desbloquear_cartao.svg');
  }

  static Image imgBackgroundErroSimulacao() {
    return Image.asset('assets/icons/img_background_erro_simulacao.png');
  }

  static SvgPicture icCartao() {
    return SvgPicture.asset('assets/icons/ic_cartao.svg');
  }

  static SvgPicture icCartaoGiftCard() {
    return SvgPicture.asset('assets/icons/ic_gift_card.svg');
  }

  static SvgPicture icOptions() {
    return SvgPicture.asset('assets/icons/ic_options.svg');
  }

  static SvgPicture icCalendario({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_calendario.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icInvestimentNaoDisponivelText() {
    return SvgPicture.asset(
      "assets/icons/ic_investimento_nao_disponivel_text.svg",
    );
  }

  static SvgPicture icInvestimentosUnselected() {
    return SvgPicture.asset("assets/icons/ic_investimentos_unselected.svg");
  }

  static SvgPicture icInvestimentos() {
    return SvgPicture.asset("assets/icons/ic_investimentos.svg");
  }

  static SvgPicture icInfoBottomBar() {
    return SvgPicture.asset("assets/icons/ic_info_bottom_bar.svg");
  }

  static SvgPicture icInfoBottomBarUnselected() {
    return SvgPicture.asset("assets/icons/ic_info_bottom_bar_unselected.svg");
  }

  static SvgPicture icClose({double? width, double? height, Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_close.svg",
      width: width,
      height: height,
      color: color,
    );
  }

  static SvgPicture icFiltroOn({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_filtro_on.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icFiltroOff({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_filtro_off.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icDoc({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_doc.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icArrowLeft({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_arrow_left.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icQrCode({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_qrcode_black.svg",
      width: width,
      height: height,
      colorFilter: ColorFilter.mode(color ?? Colors.black, BlendMode.srcIn),
    );
  }

  static SvgPicture icsSendPix({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_send_pix.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icReceivePix({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_receber_pix.svg",
      width: width,
      height: height,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  static SvgPicture icDepositPix({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_deposito_pix.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icCopyPastPix({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_copy_past_pix.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icKeyGreen({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_key_green.svg",
      width: width,
      height: height,
      // colorFilter: ColorFilter.mode(color ?? Colors.black, BlendMode.srcIn),
    );
  }

  static SvgPicture icAdjustmentsPix({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_adjustments_pix.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icHelpPix({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_help_green.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icKeyBlack({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_key_black.svg",
      width: width,
      height: height,
      colorFilter: ColorFilter.mode(color ?? Colors.black, BlendMode.srcIn),
    );
  }

  static SvgPicture idCard({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/id_card.svg",
      width: width,
      height: height,
      colorFilter: ColorFilter.mode(color ?? Colors.black, BlendMode.srcIn),
    );
  }

  static SvgPicture icBancoCentral({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_banco_central.svg",
      width: width,
      height: height,
      colorFilter: ColorFilter.mode(color ?? Colors.black, BlendMode.srcIn),
    );
  }

  static SvgPicture icChatRounded({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_chat_rounded.svg",
      width: width,
      height: height,
      colorFilter: ColorFilter.mode(color ?? Colors.black, BlendMode.srcIn),
    );
  }
}

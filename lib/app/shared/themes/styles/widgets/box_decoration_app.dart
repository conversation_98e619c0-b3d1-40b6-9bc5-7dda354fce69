import 'package:flutter/material.dart';

import '../colors_app.dart';

class BoxDecorationApp {
  static dropShadowNew() {
    return const BoxDecoration(
      boxShadow: <BoxShadow>[
        BoxShadow(
            color: Colors.black54, blurRadius: 15.0, offset: Offset(0.0, 0.75))
      ],
      color: Colors.white,
    );
  }

  static dropShadowNewCircle(
      {Color? color, double? radius, Color? backgroundColor}) {
    return BoxDecoration(
      boxShadow: <BoxShadow>[
        BoxShadow(
            color: color ?? ColorsApp.drop2,
            blurRadius: 15.0,
            offset: const Offset(0.0, 0.75))
      ],
      color: backgroundColor ?? Colors.white,
      borderRadius: BorderRadius.all(Radius.circular(radius ?? 4)),
    );
  }

  static dropShadowDecoration({
    required Color color,
    required Offset offset,
    double blurRadius = 16,
    Color backgroudColor = Colors.transparent,
    double radius = 0,
  }) {
    return BoxDecoration(
      color: backgroudColor,
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(radius),
        topRight: Radius.circular(radius),
        bottomLeft: Radius.circular(radius),
        bottomRight: Radius.circular(radius),
      ),
      boxShadow: [
        BoxShadow(
          color: color,
          offset: offset,
          blurRadius: blurRadius,
        ),
      ],
    );
  }

  static BoxDecoration bgWhiteBorderDark200() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: const BorderRadius.all(Radius.circular(4)),
      border: Border.all(color: ColorsApp.cinza[200]!),
    );
  }

  static BoxDecoration bgWhiteBorderDark500() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: const BorderRadius.all(Radius.circular(4)),
      border: Border.all(color: ColorsApp.cinza[500]!),
    );
  }
}

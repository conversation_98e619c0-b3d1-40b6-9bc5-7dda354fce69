import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'styles/colors_app.dart';

final ThemeData defaultTheme = ThemeData(
  buttonTheme: const ButtonThemeData(minWidth: 5),
  appBarTheme: const AppBarTheme(
    color: ColorsApp.appBarColor,
    elevation: 1,
    iconTheme: IconThemeData(color: ColorsApp.iconColor),
    systemOverlayStyle: SystemUiOverlayStyle.dark,
  ),
  scaffoldBackgroundColor: ColorsApp.cinza[100],
  fontFamily: 'Roboto',
  brightness: Brightness.light,
  iconTheme: const IconThemeData(color: ColorsApp.iconColor),
  primaryColor: ColorsApp.verde[500],
  highlightColor: ColorsApp.verde[500],
  dialogBackgroundColor: Colors.white,
  canvasColor: ColorsApp.snow[500],
  dropdownMenuTheme: DropdownMenuThemeData(
    inputDecorationTheme: InputDecorationTheme(
      fillColor: Colors.white,
      filled: true,
      labelStyle: TextStyle(
        fontFamily: 'Roboto',
        fontSize: 16,
        color: ColorsApp.cinza[600],
        fontWeight: FontWeight.w400,
        height: 1.14,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          width: 0.5,
          color: ColorsApp.cinza[600]!,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          width: 0.5,
          color: ColorsApp.azul[100]!,
        ),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: BorderSide.none,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
    ),
  ),
  menuButtonTheme: MenuButtonThemeData(
    style: ButtonStyle(
      textStyle: WidgetStateProperty.all<TextStyle>(TextStyle(
        fontFamily: 'Roboto',
        fontSize: 16,
        color: ColorsApp.cinza[800],
        fontWeight: FontWeight.w400,
        height: 1.14,
      )),
    ),
  ),
  bottomSheetTheme:
      const BottomSheetThemeData(backgroundColor: Colors.transparent),
  textTheme: TextTheme(
    displaySmall: TextStyle(
      fontFamily: 'Roboto',
      fontSize: 12,
      color: ColorsApp.cinza[900],
      fontWeight: FontWeight.w400,
      height: 1.33,
    ),
    displayMedium: TextStyle(
      fontFamily: 'Roboto',
      fontWeight: FontWeight.w500,
      color: ColorsApp.cinza[900],
      fontSize: 16,
      height: 1.5,
    ),
    displayLarge: TextStyle(
      fontFamily: 'Roboto',
      fontWeight: FontWeight.w700,
      color: ColorsApp.cinza[900],
      fontSize: 28,
      height: 1.14,
    ),
    headlineMedium: TextStyle(
      fontFamily: 'Roboto',
      fontSize: 12,
      color: ColorsApp.cinza[900],
      fontWeight: FontWeight.w400,
      height: 1.33,
    ),
    // headlineLarge: TextStyle(
    //   fontFamily: 'Roboto',
    //   fontSize: 16,
    //   color: ColorsApp.cinza[900],
    //   fontWeight: FontWeight.w400,
    //   height: 1.33,
    // ),
    titleMedium: TextStyle(
      fontFamily: 'Roboto',
      fontSize: 18,
      color: ColorsApp.cinza[900],
      fontWeight: FontWeight.w700,
      height: 1.5,
    ),
    bodySmall: TextStyle(
      fontFamily: 'Roboto',
      color: ColorsApp.cinza[900],
      fontSize: 10,
      fontWeight: FontWeight.w500,
      height: 1.6,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'Roboto',
      fontSize: 14,
      color: ColorsApp.cinza[800],
      fontWeight: FontWeight.w400,
      height: 1.22,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'Roboto',
      fontSize: 16,
      color: ColorsApp.cinza[900],
      fontWeight: FontWeight.w700,
      height: 1.14,
    ),
    labelSmall: TextStyle(
      fontFamily: 'Roboto',
      color: ColorsApp.cinza[900],
      fontSize: 10,
      fontWeight: FontWeight.w400,
      height: 1.6,
      letterSpacing: 0,
    ),
    labelLarge: TextStyle(
      fontFamily: 'Roboto',
      fontSize: 14,
      color: ColorsApp.cinza[900],
      fontWeight: FontWeight.w500,
      height: 1.14,
    ),
  ),
  colorScheme: ColorScheme.fromSwatch(primarySwatch: ColorsApp.verde)
      .copyWith(secondary: ColorsApp.verde[500])
      .copyWith(
        background: ColorsApp.snow[500],
        brightness: Brightness.light,
      ),
);

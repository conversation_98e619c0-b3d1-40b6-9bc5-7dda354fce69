bool isBusinessHour({
  int startHour = 6,
  int startMinute = 30,
  int endHour = 17,
  int endMinute = 0,
}) {
  final now = DateTime.now();

  // Verificar se é dia útil
  bool isWeekday =
      now.weekday >= DateTime.monday && now.weekday <= DateTime.friday;

  // Construir objetos DateTime para o início e fim do expediente no dia atual
  final startTime =
      DateTime(now.year, now.month, now.day, startHour, startMinute);
  final endTime = DateTime(now.year, now.month, now.day, endHour, endMinute);

  // Verificar se está dentro do intervalo de horário comercial
  bool isBusinessTime = now.isAfter(startTime) && now.isBefore(endTime);

  return isWeekday && isBusinessTime;
}

import 'package:cpf_cnpj_validator/cnpj_validator.dart';
import 'package:cpf_cnpj_validator/cpf_validator.dart';
import 'package:flutter/cupertino.dart';

import '../../../localization/generated/i18n.dart';
import 'utils.dart';
import 'package:string_validator/string_validator.dart';

class ValidatorUtils {
  static validarCPFCNPJ(BuildContext context, String? text) {
    if (text == null) {
      return null;
    }
    if (text.isEmpty) {
      return I18n.of(context)!.campo_nao_pode_ser_vazio;
    } else if (!CPFValidator.isValid(text) && !CNPJValidator.isValid(text)) {
      return I18n.of(context)!.cpf_cnpj_invalido;
    } else {
      return null;
    }
  }

  static String? validateEmail(BuildContext context, String? value) {
    if (value == null) {
      return null;
    }
    const pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = RegExp(pattern);
    if (!regex.hasMatch(value) || value.isEmpty)
      return I18n.of(context)!.email_error;
    else
      return null;
  }

  static String? validateCpf({required BuildContext context, String? value}) {
    if (value == null || CPFValidator.isValid(value)) {
      return null;
    } else {
      return I18n.of(context)!.cpf_error;
    }
  }

  static String? validadeCnpj(
      {required BuildContext context, required String value}) {
    if (CNPJValidator.isValid(value)) {
      return null;
    } else {
      return I18n.of(context)!.cnpj_error;
    }
  }

  static String? validateSenha({String? senha}) {
    if (senha == null || senha.isEmpty) {
      return " ";
    } else if (senha.length < 6) {
      return " ";
    } else if (!_containsUppercase(senha)) {
      return " ";
    } else if (!_containsNumber(senha)) {
      return " ";
    } else if (!_containsSpecial(senha)) {
      return " ";
    }
    return null;
  }

  static bool _containsUppercase(String text) {
    List<String> char = text.split('');
    for (String c in char) {
      var status = isUppercase(c) && !isNumeric(c) && !_containsSpecial(c);
      if (status) return true;
    }

    return false;
  }

  static bool _containsNumber(String text) {
    List<String> char = text.split('');
    for (String c in char) {
      var status = isNumeric(c);
      if (status) return true;
    }

    return false;
  }

  static bool _containsSpecial(String text) {
    List<String> char = text.split('');
    for (String c in char) {
      var status = isAlphanumeric(c);
      if (!status) return true;
    }

    return false;
  }

  static String? validaValor({required String valor, required bool centavos}) {
    var valorDouble = Utils.getValueDouble(valor, centavos);
    if (valorDouble == 0 || valorDouble < 0) {
      return const I18n().error_valor_vazio_emprestimo;
    }
    return null;
  }

  static String? validateDisplayName(BuildContext context, String? text) {
    if (text == null || text.isEmpty) {
      return I18n.of(context)!.campo_obrigatorio;
    }

    // Remove espaços no início e fim
    text = text.trim();

    // Verifica se contém apenas letras e espaços
    if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(text)) {
      return I18n.of(context)!.nome_exibicao_invalido;
    }

    // Verifica se há múltiplos espaços consecutivos
    if (text.contains(RegExp(r'\s{2,}'))) {
      return I18n.of(context)!.nome_exibicao_espacos_consecutivos;
    }

    return null;
  }
}

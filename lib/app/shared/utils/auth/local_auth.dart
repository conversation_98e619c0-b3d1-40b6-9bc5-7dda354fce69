import 'dart:io';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

class LocalAuthUtils {
  final _auth = LocalAuthentication();

  // final iosStrings = const IOSAuthMessages(
  //     cancelButton: 'cancel',
  //     goToSettingsButton: 'settings',
  //     goToSettingsDescription: 'Please set up your Touch ID.',
  //     lockOut: 'Please reenable your Touch ID');

  // final androidStrings = AndroidAuthMessages(
  //   cancelButton: I18n().cancelar,
  //   signInTitle: I18n().autenticar,
  //   biometricHint: I18n().facilite_seu_acesso,
  // );

  static canCheck() async {
    LocalAuthentication _auth = LocalAuthentication();
    List<BiometricType> availableBiometrics =
        await _auth.getAvailableBiometrics();
    var result = await _auth.canCheckBiometrics;
    return result && availableBiometrics.length > 0;
  }

  static useFaceID() async {
    LocalAuthentication _auth = LocalAuthentication();
    List<BiometricType> availableBiometrics =
        await _auth.getAvailableBiometrics();

    if (Platform.isIOS) {
      if (availableBiometrics.length == 0) return false;
      if (availableBiometrics.contains(BiometricType.face)) {
        return true;
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return false;
      }
    } else {
      return false;
    }
  }

  Future<bool?> auth({required bool isCheck, Function? showAlert}) async {
    try {
      bool didAuthenticate = await _auth.authenticate(
        // .authenticateWithBiometrics(
        localizedReason: const I18n().por_favor_login_digital,
        options: const AuthenticationOptions(
          useErrorDialogs: true,
          biometricOnly: true,
        ),
        // authMessages: [iosStrings, androidStrings],
      );

      // LoggerUtils.d(msg: "Retorno Auth: $didAuthenticate");
      // if (didAuthenticate && !isCheck) {
      //   // await SharedPreferencesUtils.setLoginDigitalHabilitado(true);
      //   // await SharedPreferencesUtils.saveLastAuth();
      // }
      return didAuthenticate;
    } catch (e) {
      if (e is PlatformException) {
        print(e);
        // if (e.code == auth_error.notAvailable) {
        // } else if (e.code == auth_error.lockedOut) {
        //   if (showAlert != null) {
        //     showAlert(I18n().bloqueio_tentativar_login_digital);
        //   }
        // }
      }
    }
  }

  cancel() async {
    await _auth.stopAuthentication();
  }
}

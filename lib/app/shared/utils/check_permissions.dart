import 'dart:io';
import 'package:permission_handler/permission_handler.dart';

class CheckPermissions {
  static Future<bool> checkPermissionFiles() async {
    if (Platform.isAndroid) {
      // Nao ha mais acesso a galeria por pacotes.
      // Nova politica do Android exige uso de galeria nativa
      return true;
    } else {
      /// Case IOS
      var status = await Permission.storage.status;
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        status = await Permission.storage.request();
        if (status == PermissionStatus.granted) {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  static Future<bool> checkCamera() async {
    // if (Platform.isIOS) {
    var status = await Permission.camera.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      status = await Permission.camera.request().onError(
        (error, stackTrace) {
          return Future.error(error!);
        },
      );
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }
}

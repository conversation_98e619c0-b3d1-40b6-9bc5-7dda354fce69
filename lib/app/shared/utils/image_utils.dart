import 'package:cached_network_image/cached_network_image.dart';

import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class ImageUtils {
  static SvgPicture icVoltarView({Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_voltar_view.svg",
      colorFilter:
          color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
      // color: color,
    );
  }

  static SvgPicture icAjuda(color) {
    return SvgPicture.asset(
      "assets/icons/ic_ajuda.svg",
      colorFilter:
          color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
    );
  }

  static SvgPicture icDropDown() {
    return SvgPicture.asset(
      "assets/icons/ic_drop_down.svg",
    );
  }

  static Widget icVisibilityOff({Color? color, double? size}) {
    return SvgPicture.asset(
      "assets/icons/ic_visibility_off.svg",
      color: color,
      width: size,
    );
  }

  static Widget icVisibility({Color? color, double? size}) {
    return SvgPicture.asset(
      "assets/icons/ic_visibility.svg",
      color: color ?? ColorsApp.azul[100],
      width: size,
    );
  }

  static SvgPicture icCheck({Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_check.svg",
      color: color ?? ColorsApp.verde[600],
    );
  }

  static SvgPicture icUnCheck() {
    return SvgPicture.asset(
      "assets/icons/ic_uncheck.svg",
    );
  }

  static SvgPicture icInfo(
      {Color? color, double height = 16, double width = 16}) {
    return SvgPicture.asset(
      "assets/icons/ic_info.svg",
      color: color,
      width: width,
      height: height,
    );
  }

  static SvgPicture icArrowRight({
    Color? color,
    double? height,
    double? width,
  }) {
    return SvgPicture.asset(
      "assets/icons/ic_arrow_right.svg",
      color: color,
      height: height,
      width: width,
    );
  }

  static SvgPicture icArrowRight2({Color? color, double? size}) {
    return SvgPicture.asset(
      "assets/icons/ic_arrow_right_2.svg",
    );
  }

  static SvgPicture icAlternarCamera({Color? color}) {
    return SvgPicture.asset(
      'assets/icons/ic_alternar_camera.svg',
    );
  }

  static SvgPicture icTirarFoto({Color? color}) {
    return SvgPicture.asset('assets/icons/ic_tirar_foto.svg');
  }

  static SvgPicture imgBemVindo({Color? color}) {
    return SvgPicture.asset(
      "assets/images/img_bem_vindo.svg",
      height: 256,
    );
  }

  static Image imgCadastroNaoAprovado() {
    return Image.asset(
      'assets/images/img_cadastro_nao_aprovado.png',
    );
  }

  static Image icSessaoExpirada() {
    return Image.asset(
      'assets/images/img_sessao_expirada.png',
    );
  }

  static SvgPicture icHomeSelected() {
    return SvgPicture.asset(
      'assets/icons/ic_home_selected.svg',
    );
  }

  static SvgPicture icHomeUnselected() {
    return SvgPicture.asset(
      'assets/icons/ic_home_unselected.svg',
    );
  }

  static SvgPicture icCarteiraSelected() {
    return SvgPicture.asset(
      'assets/icons/ic_carteira_selected.svg',
    );
  }

  static SvgPicture icCarteiraUnselected() {
    return SvgPicture.asset(
      'assets/icons/ic_carteira_unselected.svg',
    );
  }

  static SvgPicture icGetaoSelected() {
    return SvgPicture.asset(
      'assets/icons/ic_gestao_selected.svg',
    );
  }

  static SvgPicture icGestaoUnselected() {
    return SvgPicture.asset(
      'assets/icons/ic_gestao_unselected.svg',
    );
  }

  static SvgPicture icEmprestimoSelected() {
    return SvgPicture.asset(
      'assets/icons/ic_emprestimo_selected.svg',
    );
  }

  static SvgPicture icEmprestimoUnselected() {
    return SvgPicture.asset(
      'assets/icons/ic_emprestimo_unselected.svg',
    );
  }

  static SvgPicture icPerfilSelected() {
    return SvgPicture.asset(
      'assets/icons/ic_perfil_selected.svg',
    );
  }

  static SvgPicture icPerfilUnselected({Color? color}) {
    return SvgPicture.asset(
      'assets/icons/ic_perfil_unselected.svg',
      color: color,
    );
  }

  static SvgPicture siclosAppBar({
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      'assets/icons/siclos_app_bar.svg',
      width: width,
      height: height,
    );
  }

  static SvgPicture icSiclosLogoBranco() {
    return SvgPicture.asset(
      'assets/icons/ic_siclos_logo.svg',
    );
  }

  static SvgPicture icVisualizarSaldoOff() {
    return SvgPicture.asset(
      'assets/icons/ic_visualizar_saldo_off.svg',
    );
  }

  static SvgPicture icVisualizarSaldoOn() {
    return SvgPicture.asset(
      'assets/icons/ic_visualizar_saldo_on.svg',
    );
  }

  static SvgPicture bgCardDegrade({double? width, double? heigth}) {
    return SvgPicture.asset(
      'assets/images/bg_card_degrade.svg',
      width: width,
      height: heigth,
    );
  }

  static SvgPicture bgCardDegradeDemonstrativo(
      {double? width, double? heigth}) {
    return SvgPicture.asset(
      'assets/images/bg_degrade_desmonstrativo.svg',
      width: width,
      height: heigth,
    );
  }

  static Image imgCreditoConsignado() {
    return Image.asset(
      'assets/images/img_credito_consignado.png',
      height: 69.65,
    );
  }

  static SvgPicture icEmprestimos() {
    return SvgPicture.asset(
      'assets/icons/ic_emprestimo.svg',
    );
  }

  static SvgPicture ic_transferencia({double? height, double? width}) {
    return SvgPicture.asset(
      'assets/icons/ic_transferencia.svg',
      height: height,
      width: width,
    );
  }

  static SvgPicture icPagamento() {
    return SvgPicture.asset(
      'assets/icons/ic_pagamento.svg',
    );
  }

  static SvgPicture icRecarga() {
    return SvgPicture.asset(
      'assets/icons/ic_recarga.svg',
    );
  }

  static SvgPicture icDeposito({double? height, double? width}) {
    return SvgPicture.asset(
      'assets/icons/ic_deposito.svg',
      height: height,
      width: width,
    );
  }

  static SvgPicture icExtrato() {
    return SvgPicture.asset(
      'assets/icons/ic_extrato.svg',
    );
  }

  static SvgPicture icNotificacaoNew() {
    return SvgPicture.asset(
      'assets/icons/ic_notificacao_new.svg',
    );
  }

  static SvgPicture icNotificacao() {
    return SvgPicture.asset(
      'assets/icons/ic_notificacao.svg',
    );
  }

  static Image imgBgDuvidasFrequentes() {
    return Image.asset(
      'assets/images/bg_duvidas_frequentes.png',
      height: 54,
    );
  }

  static SvgPicture icArrowExpanded() {
    return SvgPicture.asset(
      'assets/icons/ic_arrow_expand.svg',
    );
  }

  static SvgPicture icArrowCompress() {
    return SvgPicture.asset(
      'assets/icons/ic_arrow_compress.svg',
    );
  }

  static SvgPicture icShare() {
    return SvgPicture.asset(
      'assets/icons/ic_share.svg',
    );
  }

  static SvgPicture icBoletoCopiar(
      {double? width, double? heigth, Color? color}) {
    return SvgPicture.asset(
      'assets/icons/ic_boleto_copiar.svg',
      width: width,
      height: heigth,
      color: color,
    );
  }

  static Image imgUsuario6MesesEmpresa() {
    return Image.asset(
      'assets/images/img_6meses_empresitmo.png',
      height: 427,
    );
  }

  static Image imgUsuarioSemVinculo({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_usuario_sem_vinculo.png',
      height: height,
      width: width,
    );
  }

  static Image imgEmprestimoSimulacao({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_emprestimo_simulacao.png',
      height: height,
      width: width,
    );
  }

  static Image imgEmprestimoSimulacao6Meses({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_emprestimo_simulacao_6_messes.png',
      height: height,
      width: width,
    );
  }

  static Image imgBackgroundSelfie({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_selfie.png',
      fit: BoxFit.cover,
      height: height,
      width: width,
    );
  }

  static Image imgBackgroundCnh({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_cnh.png',
      fit: BoxFit.cover,
      width: width,
      height: height,
    );
  }

  static Image imgBackgroundRg({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_rg.png',
      fit: BoxFit.cover,
      width: width,
      height: height,
    );
  }

  static Image imgBackgroundIntro({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_background_intro.png',
      fit: BoxFit.cover,
    );
  }

  static Image imgEmprestimoSimulacaoEnviada() {
    return Image.asset(
      'assets/images/img_emprestimo_simulacao_enviada.png',
      width: 297,
      height: 446,
    );
  }

  static SvgPicture icCamera() {
    return SvgPicture.asset(
      'assets/icons/ic_camera.svg',
    );
  }

  static SvgPicture icDelete({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      'assets/icons/ic_delete.svg',
      color: color,
      width: width,
      height: height,
    );
  }

  static SvgPicture icCopyData({double? height, double? width, Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_copy_data.svg",
      height: height,
      width: width,
      color: color,
    );
  }

  static SvgPicture icEdit({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_edit.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icSiclos({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_siclos.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icAdd({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_add.svg",
      height: height,
      width: width,
    );
  }

  static SvgPicture icBank({double? height, double? width}) {
    return SvgPicture.asset(
      "assets/icons/ic_bank.svg",
      height: height,
      width: width,
    );
  }

  static Image imgCartaoVirtual({double? height, double? width}) {
    return Image.asset(
      'assets/images/cartao_virtual.png',
      fit: BoxFit.fill,
      width: width,
      height: height,
    );
  }

  static Image imgBackgoundCardGestao({double? height, double? width}) {
    return Image.asset(
      'assets/images/img_backgrond_card_gestao.png',
      fit: BoxFit.fill,
      width: width,
      height: height,
    );
  }

  static SvgPicture icCifrao() {
    return SvgPicture.asset(
      "assets/icons/ic_cifrao.svg",
      height: 96,
    );
  }

  static CachedNetworkImage card1() {
    return buildImageNetworkCache(
        'https://cdn.siclos.net/cards/card_credito_rapido_siclos.png');
  }

  static CachedNetworkImage card2() {
    return buildImageNetworkCache(
        'https://cdn.siclos.net/cards/card_virtual.png');
  }

  static CachedNetworkImage card3() {
    return buildImageNetworkCache(
      'https://cdn.siclos.net/cards/card_siclos.png',
    );
  }

  static Image card4() {
    return Image.asset(
      'assets/images/card_4.png',
      height: 183,
    );
  }

  static Image imgSolicitarEmprestimo() {
    return Image.asset(
      'assets/images/img_simular_emprestimo.png',
      height: 431,
    );
  }

  static SvgPicture icIR() {
    return SvgPicture.asset(
      "assets/icons/ic_ir.svg",
      height: 72,
      width: 72,
    );
  }

  static SvgPicture icHolerite() {
    return SvgPicture.asset(
      "assets/icons/ic_holerite.svg",
      height: 72,
      width: 72,
    );
  }

  static Widget icHorasProfissionais() {
    return const Padding(
      padding: EdgeInsets.all(14.0),
      child: Icon(
        Icons.access_time,
        size: 45,
        color: Colors.black,
      ),
    );
  }

  static Widget icFerias() {
    return const Padding(
      padding: EdgeInsets.all(14.0),
      child: Icon(
        Icons.beach_access,
        size: 45,
        color: Colors.black,
      ),
    );
  }

  static SvgPicture icDownload() {
    return SvgPicture.asset(
      "assets/icons/ic_dowload.svg",
    );
  }

  static ClipRRect imgNaoFuncionario({double? height, double? width}) {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(bottom: Radius.circular(11)),
      child: Image.asset(
        'assets/images/img_background_nao_funcionario.png',
        height: height,
        width: width,
        // fit: BoxFit.contai,
      ),
    );
  }

  static SvgPicture icExcluirCartao() {
    return SvgPicture.asset(
      'assets/icons/ic_excluir_cartao.svg',
    );
  }

  static SvgPicture icBloquearCartao() {
    return SvgPicture.asset(
      'assets/icons/ic_bloquear_cartao.svg',
    );
  }

  static SvgPicture icVisualizarDadosCartao() {
    return SvgPicture.asset(
      'assets/icons/ic_visualizar_cartao.svg',
    );
  }

  static SvgPicture icNaoVisualizarDadosCartao() {
    return SvgPicture.asset(
      'assets/icons/ic_nao_visualizar_cartao.svg',
    );
  }

  static SvgPicture icDesbloquearCartao() {
    return SvgPicture.asset(
      'assets/icons/ic_desbloquear_cartao.svg',
    );
  }

  static Image imgBackgroundErroSimulacao() {
    return Image.asset(
      'assets/images/img_background_erro_simulacao.png',
    );
  }

  static SvgPicture icCartao() {
    return SvgPicture.asset(
      'assets/icons/ic_cartao.svg',
    );
  }

  static SvgPicture icCartaoGiftCard() {
    return SvgPicture.asset(
      'assets/icons/ic_gift_card.svg',
    );
  }

  static SvgPicture icOptions() {
    return SvgPicture.asset(
      'assets/icons/ic_options.svg',
    );
  }

  static Image imgTermoPendente({double? width, double? height}) {
    return Image.asset(
      "assets/images/img_termo_pendente.png",
      width: width,
      height: height,
    );
  }

  static SvgPicture icCalendario({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_calendario.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icInvestimentosUnselected() {
    return SvgPicture.asset(
      "assets/icons/ic_investimentos_unselected.svg",
    );
  }

  static SvgPicture icInvestimentos() {
    return SvgPicture.asset(
      "assets/icons/ic_investimentos.svg",
    );
  }

  static SvgPicture icInfoBottomBar() {
    return SvgPicture.asset(
      "assets/icons/ic_info_bottom_bar.svg",
    );
  }

  static SvgPicture icInfoBottomBarUnselected() {
    return SvgPicture.asset(
      "assets/icons/ic_info_bottom_bar_unselected.svg",
    );
  }

  static Image imgBackgroundMercadoIntro({double? width, double? height}) {
    return Image.asset(
      "assets/images/img_background_mercado_intro.png",
      width: width,
      height: height,
      fit: BoxFit.cover,
    );
  }

  static Image imgBackgroundInvestimentoNaoDisponivel(
      {double? width, double? height}) {
    return Image.asset(
      "assets/images/img_background_investimento_nao_disponivel.png",
      width: width,
      height: height,
      fit: BoxFit.fill,
    );
  }

  static SvgPicture icLogoEmCashComTexto({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_logo_emcash_com_texto.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icLogoEmCash({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_logo_emcash.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icClose({double? width, double? height, Color? color}) {
    return SvgPicture.asset(
      "assets/icons/ic_close.svg",
      width: width,
      height: height,
      color: color,
    );
  }

  static SvgPicture icFiltroOn({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_filtro_on.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icFiltroOff({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_filtro_off.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icDoc({double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_doc.svg",
      width: width,
      height: height,
    );
  }

  static SvgPicture icArrowLeft({Color? color, double? width, double? height}) {
    return SvgPicture.asset(
      "assets/icons/ic_arrow_left.svg",
      width: width,
      height: height,
      color: color, // TODO: REFACT TO NEW UPDATE
    );
  }

  static Image imgQuestionarioInvestidor(
      {Color? color, double? width, double? height}) {
    return Image.asset(
      "assets/images/img_questionario_perfil_investidor.png",
      width: width,
      height: height,
      color: color,
    );
  }

  static CachedNetworkImage buildImageNetworkCache(String url) {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      progressIndicatorBuilder: (context, child, loadingProgress) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 255, 255, 255),
              borderRadius: BorderRadius.all(Radius.circular(5)),
            ),
          ),
        );
      },
    );
  }
}

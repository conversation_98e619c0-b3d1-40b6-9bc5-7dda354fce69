// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:encrypt/encrypt.dart';
import 'package:random_string_generator/random_string_generator.dart';
import 'package:siclosbank/app/shared/data/database/storage_adapter.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/data/models/wallet/bank_account_user_response.dart';

import '../constants/storage_constants.dart';
import '../data/models/collaborator_response.dart';
import '../../app_controller.dart';
import '../data/database/shared_preference_store.dart';
import '../data/models/token_response.dart';
import '../data/models/wallet/bank_model.dart';
import '../data/models/wallet/ted/tec_usuario.dart';
import '../constants/constants.dart';
import 'crypto_utils.dart';

abstract class StorageUtils {
  static IStorageAdapter? _storage;

  static IStorageAdapter _get() {
    if (_storage != null) {
      return _storage!;
    } else {
      _storage = SharedPreferenceStore();
      return _storage!;
    }
  }

  static setShowBalance({required bool show}) async {
    var shared = _get();
    await shared.put(SharedConstants.SHOW_BALANCE, show);
  }

  static Future<bool> showBalance() async {
    var shared = _get();
    final status = await shared.get(SharedConstants.SHOW_BALANCE) as bool?;
    if (status == null) {
      return true;
    } else {
      return status;
    }
  }

  static showTutorialRegister() async {
    var shared = _get();
    await shared.put(SharedConstants.SHOW_TUTORIAL_REGISTER, true);
  }

  static Future<bool> isFirstRun() async {
    var shared = _get();
    final status =
        await shared.get(SharedConstants.SHOW_TUTORIAL_REGISTER) as bool?;
    if (status == null) {
      return true;
    } else {
      return false;
    }
  }

  static setUserRegister({String userId = ''}) async {
    var shared = _get();
    await shared.put(SharedConstants.REGISTER_USER, userId);
  }

  static Future<String?> getUserRegister() async {
    final shared = _get();
    String? userId = await shared.get(SharedConstants.REGISTER_USER) as String?;
    return userId;
  }

  static clearUserRegister() async {
    var shared = _get();
    await shared.put(SharedConstants.REGISTER_USER, "");
    await shared.delete(SharedConstants.REGISTER_USER);
  }

  static saveListContacts(List<TecUsuario> usuarios) async {
    var shared = _get();
    String jsonString = json.encode(usuarios);
    // LoggerUtils.d("Contatos Salvo: $jsonString");
    await shared.put(SharedConstants.LIST_CONTACTS_AGENDA, jsonString);
  }

  static Future<List?> getListContacts() async {
    var shared = _get();
    String? jsonString =
        await shared.get(SharedConstants.LIST_CONTACTS_AGENDA) as String?;
    if (jsonString == null || jsonString.isEmpty) return null;
    var map = json.decode(jsonString);
    List<TecUsuario> list = [];
    for (var item in map) {
      list.add(TecUsuario.fromJson(item));
    }
    return list;
  }

  static clearListContacts() async {
    var shared = _get();
    await shared.put(SharedConstants.LIST_CONTACTS_AGENDA, "");
  }

  static saveLastLogin(String lastLogin) async {
    var shared = _get();
    await shared.put(SharedConstants.LAST_LOGIN, lastLogin);
  }

  static Future<String?> getLastLogin() async {
    var shared = _get();
    return await shared.get(SharedConstants.LAST_LOGIN) as String?;
  }

  static showTutorialInitQuestionnaire() async {
    var shared = _get();
    bool? status =
        await shared.get(SharedConstants.SHOW_START_INVESTOR_QUESTIONNAIRE)
            as bool?;
    if (status == null) {
      return true;
    } else {
      return status;
    }
  }

  static saveInitQuestionnaire({bool show = false}) async {
    var shared = _get();
    await shared.put(SharedConstants.SHOW_START_INVESTOR_QUESTIONNAIRE, show);
  }

  static saveTokenResponse({required TokenResponse? token}) async {
    var shared = _get();
    await shared.put(SharedConstants.AUTH_TOKEN, token?.toJson());
  }

  static Future<TokenResponse?> getAuthTokenResponse() async {
    var shared = _get();
    String? token = await shared.get(SharedConstants.AUTH_TOKEN) as String?;
    if (token == null || token.isEmpty) {
      return null;
    }
    return TokenResponse.fromJson(token);
  }

  static saveTokenFirebaseMessaging({required String token}) async {
    var shared = _get();
    await shared.put(
      SharedConstants.TOKEN_FIREBASE_MESSAGING,
      token.toString(),
    );
  }

  static Future<String?>? getTokenFirebaseMessaging() async {
    var shared = _get();
    String? token =
        await shared.get(SharedConstants.TOKEN_FIREBASE_MESSAGING) as String?;
    if (token == null || token.isEmpty) {
      return null;
    }
    return token;
  }

  static saveUser({required User? user}) async {
    var shared = _get();
    if (user == null) {
      await shared.delete(SharedConstants.USER);
    } else {
      await shared.put(SharedConstants.USER, user.toJson());
    }
  }

  static Future<User?> getUser() async {
    var shared = _get();
    String? user = await shared.get(SharedConstants.USER) as String?;
    if (user == null || user.isEmpty) {
      return null;
    }
    return User.fromJson(user);
  }

  static saveColaborator({required CollaboratorResponse? collaborator}) async {
    var shared = _get();
    if (collaborator == null) {
      await shared.delete(SharedConstants.COLLABORATOR);
    } else {
      await shared.put(SharedConstants.COLLABORATOR, collaborator.toJson());
    }
  }

  static Future<CollaboratorResponse?>? getColaborator() async {
    var shared = _get();
    String? result = await shared.get(SharedConstants.COLLABORATOR) as String?;
    if (result == null || result.isEmpty) {
      return null;
    }
    return CollaboratorResponse.fromJson(result);
  }

  static saveBankAccountUser({required BankAccountResponse accountBank}) async {
    var shared = _get();
    await shared.put(SharedConstants.USER, accountBank.toJson());
  }

  static getBankAccountUser() async {
    var shared = _get();
    String? result = await shared.get(SharedConstants.USER) as String?;
    if (result == null || result.isEmpty) {
      return null;
    }
    return BankAccountResponse.fromJson(result);
  }

  // static getEmpresaSiclos() async {
  //   var shared = _get();
  //   String? empresa =
  //       await shared.get(SharedConstants.EMPRESA_SICLOS) as String?;
  //   if (empresa == null || empresa.isEmpty) {
  //     return null;
  //   }
  //   // return EmpresaSiclos.fromJson(json.decode(empresa));
  // }

  static clearDataLogin() async {
    var shared = _get();
    await shared.delete(SharedConstants.AUTH_TOKEN);
    // await shared.deleteAll();
    // await shared.delete(ContaCorrenteContants.MOSTRA_BANNER);
    // await shared.delete(ContaCorrenteContants.SHOW_DIALOG_CARTAO_DEBITO);
    AppSession.getInstance().user = null;
    AppSession.getInstance().collaborator = null;
    await shared.delete(SharedConstants.USER);
    await shared.delete(SharedConstants.COLLABORATOR);

    AppSession.clear();

    if (!await loginDigitalEnable()) {
      // await shared.delete(SharedConstants.USER);
    }
  }

  static loginDigitalEnable() async {
    var shared = _get();
    bool? status = await shared.get(LoginDigital.IS_ENABLED) as bool?;
    if (status == null) {
      return false;
    } else {
      return status;
    }
  }

  static setLoginDigitalHabilitado(bool status) async {
    var shared = _get();
    await shared.put(LoginDigital.IS_ENABLED, status);
  }

  static getKey() async {
    var shared = _get();
    String? key = await shared.get(LoginDigital.KEY) as String?;

    if (key == null || key.isEmpty) {
      final secureRandom = RandomStringGenerator(fixedLength: 32);
      final keyRandom = secureRandom.generate(); // nextString(length: 32);
      final iv = IV.fromLength(16).base64;

      await shared.put(LoginDigital.KEY, "$iv $keyRandom");
      key = "$iv $keyRandom";
    }

    return key;
  }

  //TODO: static saveLastAuth() async {
  //   if (AppSession.getInstance.lastLogin != null &&
  //       AppSession.getInstance.lastSenha != null) {
  //     var shared = _get();
  //     final login = AppSession.getInstance.lastLogin;
  //     final senha = AppSession.getInstance.lastSenha;
  //     await shared.put(
  //         LoginDigital.LAST_LOGIN, await CryptoUtils.encode(login!));
  //     await shared.put(
  //         LoginDigital.LAST_SENHA, await CryptoUtils.encode(senha!));
  //   }
  // }

  static atualizaSenhaDigital({required String senha}) async {
    var shared = _get();
    await shared.put(LoginDigital.LAST_SENHA, await CryptoUtils.encode(senha));
  }

  static getLastLoginDigital() async {
    var shared = _get();
    var loginCrypt = await shared.get(LoginDigital.LAST_LOGIN) as String?;
    return await CryptoUtils.decode(loginCrypt);
  }

  static getLastSenhaDigital() async {
    var shared = _get();
    var senhaCrypt = await shared.get(LoginDigital.LAST_SENHA) as String?;
    return await CryptoUtils.decode(senhaCrypt);
  }

  static clearDigitalData() async {
    var shared = _get();
    await shared.delete(LoginDigital.KEY);
    await shared.delete(LoginDigital.LAST_LOGIN);
    await shared.delete(LoginDigital.LAST_SENHA);
    await shared.delete(LoginDigital.SHOW_SHEET);
    await shared.put(LoginDigital.IS_ENABLED, false);
  }

  static showSheetLoginDigital() async {
    var shared = _get();
    bool? result = await shared.get(LoginDigital.SHOW_SHEET) as bool?;
    if (result == null) {
      return true;
    } else {
      return result;
    }
  }

  static setShowSheetLoginDigital(bool status) async {
    var shared = _get();
    await shared.put(LoginDigital.SHOW_SHEET, status);
  }

  static showBanner() async {
    var shared = _get();
    bool? result =
        await shared.get(ContaCorrenteContants.MOSTRA_BANNER) as bool?;
    if (result == null) {
      return true;
    } else {
      return result;
    }
  }

  static hideBanner() async {
    var shared = _get();
    await shared.put(ContaCorrenteContants.MOSTRA_BANNER, false);
  }

  static isShowDialogCartaoDebito() async {
    var shared = _get();
    bool? result =
        await shared.get(ContaCorrenteContants.SHOW_DIALOG_CARTAO_DEBITO)
            as bool?;
    if (result == null) {
      return true;
    } else {
      return result;
    }
  }

  static setShowDialogCartaoDebito(bool status) async {
    var shared = _get();
    await shared.put(ContaCorrenteContants.SHOW_DIALOG_CARTAO_DEBITO, status);
  }

  static setModeloDevice(String? nome) async {
    var shared = _get();
    await shared.put(SharedConstants.MODEL_DEVICE, nome);
  }

  static Future<String?>? getModelDevice() async {
    var shared = _get();
    return await shared.get(SharedConstants.MODEL_DEVICE) as String?;
  }

  static setVersaoDevice(String? nome) async {
    var shared = _get();
    await shared.put(SharedConstants.VERSION_DEVICE, nome);
  }

  static Future<String?>? getVersionDevice() async {
    var shared = _get();
    return await shared.get(SharedConstants.VERSION_DEVICE) as String?;
  }

  static setLastLoginQa({required String login}) async {
    var shared = _get();
    await shared.put(SharedConstants.LAST_LOGIN_TESTE, login);
  }

  static getLastLoginQa() async {
    var shared = _get();
    return await shared.get(SharedConstants.LAST_LOGIN_TESTE) as String?;
  }

  static setBanksList(List<BanksModel> banks) async {
    var shared = _get();
    String jsonString = json.encode(banks);
    await shared.put(SharedConstants.BANKS_LIST, jsonString);
  }

  static Future<List<BanksModel>?> getBanksList() async {
    var shared = _get();
    final result = await shared.get(SharedConstants.BANKS_LIST) as String?;
    if (result == null || result.isEmpty) {
      return null;
    }
    final map = json.decode(result);
    List<BanksModel> list = [];
    for (var item in map) {
      list.add(BanksModel.fromJson(item));
    }
    return list;
  }
}

import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'dart:developer';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:image_picker/image_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';

import 'package:string_validator/string_validator.dart';

import '../../modules/professional/data/models/payslip_response.dart';
import '../../app_controller.dart';
import '../errors/errors.dart';
import '../navigation/named_routes.dart';
import '../navigation/navigator_app.dart';
import 'fields_utils.dart';

abstract class Utils {
  static final _regexOnlyNumbers = RegExp(r'[^0-9]');

  static ocultaEmail({required String email}) {
    int qtde = email.indexOf("@") - 2;
    String replaceString = "*";
    while (qtde > 1) {
      replaceString += "*";
      qtde--;
    }

    return email.replaceRange(1, email.indexOf("@") - 1, replaceString);
  }

  static Future<String> createPDFWithBase64(
    List<int> buffer,
    String fileName,
  ) async {
    try {
      final directory = await getTemporaryDirectory();
      File file = File('${directory.path}/$fileName.pdf');
      var raf = file.openSync(mode: FileMode.write);
      raf.writeFromSync(buffer);
      await raf.close();
      return file.path;
    } catch (e) {
      throw ErrorFile('Erro ao criar arquivo PDF', error: e);
    }
  }

  // static Future<String> createPDFWithFile(
  //     String fileString, String fileName) async {
  //   try {
  //     final directory = await getTemporaryDirectory();
  //     File file = File('${directory.path}/$fileName.pdf');
  //     var raf = file.openSync(mode: FileMode.write);
  //     await raf.writeString(fileString);
  //     await raf.close();
  //     return file.path;
  //   } catch (e) {
  //     throw ErrorFile('Erro ao criar arquivo PDF', error: e);
  //   }
  // }

  static Future<File> createPDFWithBytes(
    Uint8List buffer,
    String fileName,
  ) async {
    final directory = await getTemporaryDirectory();
    File file = File('${directory.path}/$fileName.pdf');
    await file.writeAsBytes(buffer);
    return file;
  }

  static Future openFile(String path, {String type = 'application/pdf'}) =>
      OpenFile.open(path, type: type).onError((error, stackTrace) {
        return Future.error(ErrorFile('Erro ao abrir arquivo.', error: error));
      });

  static String getDateTimeToApi(DateTime? dateTime) {
    if (dateTime == null) return "-";
    return DateFormat("yyyy-MM-dd").format(dateTime);
  }

  static String formatDateBrToApi(String date) {
    DateTime dateTime = DateFormat("dd/MM/yyyy").parse(date).toUtc();
    return dateTime.toIso8601String();
  }

  /// for dates in the format "MM/dd/yyyy HH:mm:ss"
  static DateTime formatDateToDateTime(String date) {
    DateTime dateTime = DateFormat('MM/dd/yyyy HH:mm:ss').parse(date);
    return dateTime;
  }

  static String formatPhoneToApi(String phone) {
    var phoneFormat = '+55${FieldsUtils.removeCharacters(phone)}';

    return phoneFormat;
  }

  static bool enableDisableDownloadPayslip(PayslipResponse holeriteResponse) {
    const startDayOfDownload = 8;
    final currentDay = DateTime.now().day;

    final currentDate = DateTime(
      DateTime.now().year,
      DateTime.now().month,
      DateTime.now().day,
    );

    final lastMonth = DateTime(DateTime.now().year, DateTime.now().month - 1);

    final payslipDate = DateTime(holeriteResponse.year, holeriteResponse.month);

    // Tudo que está antes do mês anterior fica hobilitado para download
    if (payslipDate.isBefore(lastMonth)) {
      return true;
    }

    // Se o mês do holerite for o mês anterior
    // só hobilita a partir do dia 8 do mês atual.
    // Ex: Abril é o mês atual e Março o mês anterior,
    // então só habilita o botão de download do mês de Março a partir do dia 8 de Abril
    if (payslipDate == lastMonth && currentDay >= startDayOfDownload) {
      return true;
    }

    // Tudo que está após do mês o dia atual fica desobilitado para download
    if (payslipDate.isAfter(currentDate)) {
      return false;
    }

    return false;
  }

  static String getMesFormatado(String data) {
    DateTime dateTime = DateFormat("yyyy-MM-dd").parse(data);
    String dataString = DateFormat("MMMM", "pt_BR").format(dateTime);
    return dataString.substring(0, 1).toUpperCase() +
        dataString.substring(1, dataString.length);
  }

  static String getAnoFormatado(String data) {
    DateTime dateTime = DateFormat("yyyy-MM-dd").parse(data);
    return DateFormat("yyyy", "pt_BR").format(dateTime);
  }

  static String getMesDescricao({required int month}) {
    if (month > 12 || month < 1) return "-";
    String result = '';
    switch (month) {
      case 1:
        result = 'Janeiro';
        break;
      case 2:
        result = 'Fevereiro';
        break;
      case 3:
        result = 'Março';
        break;
      case 4:
        result = 'Abril';
        break;
      case 5:
        result = 'Maio';
        break;
      case 6:
        result = 'Junho';
        break;
      case 7:
        result = 'Julho';
        break;
      case 8:
        result = 'Agosto';
        break;
      case 9:
        result = 'Setembro';
        break;
      case 10:
        result = 'Outubro';
        break;
      case 11:
        result = 'Novembro';
        break;
      case 12:
        result = 'Dezembro';
        break;
      default:
        result = 'Mês inválido';
    }

    return result;
  }

  static buttonIcHelp(BuildContext context, {Color? colorIcon}) {
    return IconButton(
      icon: ImageUtils.icAjuda(colorIcon),
      onPressed: () {
        push(Routes.help);
      },
    );
  }

  static String formatDataWithDay({required String dataApi}) {
    var data = DateTime.parse(dataApi);
    return DateFormat("EEE, dd/MM/yyyy", "pt_BR").format(data);
  }

  static String formatDataMonthAndDay({required DateTime date}) {
    return DateFormat("dd 'de' MMMM", "pt_BR").format(date);
  }

  static takeScreenShot({
    required GlobalKey key,

    required String fileName,
  }) async {
    RenderRepaintBoundary boundary =
        key.currentContext!.findRenderObject() as RenderRepaintBoundary;

    var image = await boundary.toImage(pixelRatio: 3);
    var byteData = await image.toByteData(format: ImageByteFormat.png);
    var pngBytes = byteData!.buffer.asUint8List();

    Share.shareXFiles([
      XFile.fromData(pngBytes, name: fileName, mimeType: 'image/png'),
    ]);
  }

  static String formatDateFirstPayment({required String? dataApi}) {
    if (dataApi == null || dataApi.isEmpty) return "-";
    var data = DateTime.parse(dataApi);
    String dataString = DateFormat("MM/yyyy", "pt_BR").format(data);
    return dataString.substring(0, 1).toUpperCase() +
        dataString.substring(1, dataString.length);
  }

  static String formatValueInstatement(double valor) {
    if (valor == 0.0) return "R\$0,00";
    final controller = MoneyMaskedTextController(
      initialValue: valor,
      thousandSeparator: '.',
      decimalSeparator: ',',
      precision: 2,
      leftSymbol: 'R\$',
    );
    return controller.text;
  }

  static String formatDecimal(double? valor) {
    if (valor == null) return "-";
    if (valor == 0.0) return "0,00";
    final controller = MoneyMaskedTextController(initialValue: valor);
    if (valor < 0) {
      return "- ${controller.text.replaceAll("-", "")}";
    } else {
      if (valor.toString().endsWith(".0")) {
        return controller.text;
      } else {
        return controller.text;
      }
    }
  }

  static String formatBalance(double? value) {
    if (value == null) return "-";
    if (value == 0.0) return "R\$ 0,00";
    final controller = MoneyMaskedTextController(
      initialValue: value,
      // leftSymbol: "R\$",
    );
    if (value < 0) {
      return "- R\$ ${controller.text.replaceAll("-", "")}";
    } else {
      return "R\$ ${controller.text}";
    }
  }

  static String getLastAccessDate({required String dataString}) {
    if (dataString.isEmpty) return "";
    DateTime data;
    try {
      if (dataString.contains("T")) {
        data = DateTime.parse(dataString);
      } else {
        data = DateFormat("yyyy-MM-dd HH:mm:ss").parse(dataString);
      }
    } catch (e) {
      return "";
    }
    return DateFormat("HH:mm:ss dd/MM/yyyy").format(data);
  }

  static String formatDateTimeToStringDDMMYYYY({required DateTime date}) {
    return DateFormat("dd-MM-yyyy", "pt_BR").format(date);
  }

  static String formatDateTimeToStringWithHours({required DateTime date}) {
    return DateFormat("dd/MM/yyyy - HH:mm:ss", "pt_BR").format(date);
  }

  /// return the date in the format dd/MM/yyyy
  static String formatDateToBr([String? date]) {
    if (date == null || date.isEmpty) return "";
    var data = DateFormat("yyyy-MM-ddTHH:mm:ss").parse(date);
    return DateFormat("dd/MM/yyyy").format(data);
  }

  /// return the date in the format dd/MM/yyyy
  static String formatDateTimeToBr(DateTime date) {
    return DateFormat("dd/MM/yyyy").format(date);
  }

  static double getValueDouble(String text, bool centavos) {
    if (text.isEmpty) return 0;
    var clean = centavos
        ? text.replaceAll("R\$", "").replaceAll(".", "").replaceAll(",", ".")
        : text.replaceAll("R\$", "").replaceAll(".", "");
    var value = double.parse(clean);
    return value;
  }

  static Widget circularProgressButton({double? size, Color? color}) {
    return SizedBox(
      height: size ?? 20,
      width: size ?? 20,
      child: CircularProgressIndicator(
        backgroundColor: color ?? Colors.white,
        valueColor: AlwaysStoppedAnimation<Color>(
          color != null ? Colors.white : ColorsApp.verde[500]!,
        ),
        strokeWidth: 2,
      ),
    );
  }

  static setScreeenResponsive({
    required BuildContext context,
    double? height,
    double? width,
  }) {
    ResponsiveWidgets.init(
      context,
      height: height ?? 720, // Optional
      // height: height ?? 690, // Optional
      width: width ?? 360, // Optional
      allowFontScaling: true, // Optional
    );
  }

  static String formatOnlyNumbers(String value) {
    return value.replaceAll(_regexOnlyNumbers, '');
  }

  static bool validDate(String date) {
    try {
      int day = int.parse(date.substring(0, 2));
      int month = int.parse(date.substring(3, 5));
      int year = int.parse(date.substring(6, 10));
      if (day > 31) return true;
      if (month > 12) return true;
      if (year < 1900) return true;
      if (year > 2100) return true;
    } catch (error) {
      return true;
    }
    return false;
  }

  static int calculateYearsOld(String birthDate) {
    DateTime dtBirthday = DateFormat('dd/MM/yyyy').parse(birthDate);
    DateTime dateNow = DateTime.now();
    final diff = DateTime(
      dateNow.year,
      dateNow.month,
      dateNow.day,
    ).difference(dtBirthday);
    final yearsOld = diff.inDays ~/ 365;
    return yearsOld;
  }

  static bool hasNumber({String? texto}) {
    if (texto == null || texto.isEmpty) return false;
    for (int i = 0; i < texto.length; i++) {
      if (isNumeric(texto[i])) {
        return true;
      }
    }
    return false;
  }

  static bool isNumber(String result) {
    return double.tryParse(result) != null;
  }

  static bool hasUppercase({String? text}) {
    if (text == null || text.isEmpty) return false;
    text = text.trim();
    for (int i = 0; i < text.length; i++) {
      if (!hasSpecialCharacter(text: text[i]) &&
          !isNumeric(text[i]) &&
          text[i] != " ") {
        String uppercase = text[i].toUpperCase();
        if (uppercase == text[i]) {
          return true;
        }
      }
    }
    return false;
  }

  static bool hasSpecialCharacter({required String text}) {
    final validCharacters = RegExp(r'[!@#$%^&*(),.?":{}|<>]');
    return validCharacters.hasMatch(text);
  }

  static buildIcDocumento(bool envidado, bool enable) {
    return envidado
        ? ImageUtils.icCheck()
        : ImageUtils.icArrowRight(
            color: enable ? null : ColorsApp.disableTextColor,
          );
  }

  static unFocus(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);

    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
  }

  static fieldFocusChange(
    BuildContext context,
    FocusNode currentFocus,
    FocusNode nextFocus,
  ) {
    currentFocus.unfocus();
    FocusScope.of(context).requestFocus(nextFocus);
  }

  static String getInitialName({required String nome}) {
    List<String> names = nome.split(" ");
    List<String> namesSemEspaco = [];
    for (String name in names) {
      if (name.isNotEmpty) {
        namesSemEspaco.add(name);
      }
    }
    if (namesSemEspaco.length > 1) {
      return "${namesSemEspaco[0].substring(0, 1)}${namesSemEspaco[1].substring(0, 1)}";
    } else {
      return namesSemEspaco[0].substring(0, 1);
    }
  }

  static dropShadowNewCircle({
    Color? color,
    double? radius,
    Color? backgroundColor,
  }) {
    return BoxDecoration(
      boxShadow: <BoxShadow>[
        BoxShadow(
          color: color ?? ColorsApp.drop2,
          blurRadius: 15.0,
          offset: const Offset(0.0, 0.75),
        ),
      ],
      color: backgroundColor ?? Colors.white,
      borderRadius: BorderRadius.all(Radius.circular(radius ?? 4)),
    );
  }

  // static takeScreenShot({
  //   required GlobalKey key,
  //   required String title,
  //   required String fileName,
  // }) async {
  //   RenderRepaintBoundary boundary =
  //       key.currentContext!.findRenderObject() as RenderRepaintBoundary;

  //   var image = await boundary.toImage(pixelRatio: 3);
  //   var byteData = await image.toByteData(format: ImageByteFormat.png);
  //   var pngBytes = byteData!.buffer.asUint8List();

  //   LoggerUtils.d(msg: 'Share with package share_plus...');
  //   Share.shareXFiles([
  //     XFile.fromData(
  //       pngBytes,
  //       name: title,
  //       mimeType: 'image/png',
  //     )
  //   ]);
  // }

  static Future<File?>? _openCommonGallery() async {
    var image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      return File(image.path);
    }
    return null;
  }

  static Future<File?> getImageGallery() async {
    if (Platform.isAndroid) {
      var androidInfo = await DeviceInfoPlugin().androidInfo;
      var sdkInt = androidInfo.version.sdkInt;
      return await _openCommonGallery();
    } else {
      return await _openCommonGallery();
    }
  }

  static String maskCpf(String document) {
    var cpfFormat = FieldsUtils.obterCpf(document);

    var cpfMask = cpfFormat.substring(3, cpfFormat.length - 2);

    return '***$cpfMask**';
  }

  static copyText({
    required BuildContext context,
    String? text,
    String? mensagemSucesso,
  }) {
    Clipboard.setData(ClipboardData(text: text ?? ''));
    SnackBarApp.showSnack(
      context: context,
      message: mensagemSucesso ?? I18n.of(context)!.codigo_barra_copiado,
      erroConnection: false,
      success: true,
    );
  }

  static Shimmer shimmerLoading({double? height, double? width}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      enabled: true,
      child: Container(
        height: height ?? 60,
        width: width ?? double.infinity,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  static String getBankName(String? isbp, {shortName = false}) {
    isbp ??= '********';
    final result = AppSession.getInstance().getBankByISBPCode(isbp);
    return result != null
        ? ('${result.code} - ${shortName ? result.name : result.fullName}')
        : '-';
  }
}




  // static getColorProgressParcela(Parcelas parcela) {
  //   try {
  //     return parcela.isPago || parcela.isPagoAdiantado || parcela.isPagoAtraso
  //         ? ColorsApp.verde[500]
  //         : parcela.isProcessando
  //             ? ColorsApp.info[300]
  //             // ? ColorsApp.cinza[300]
  //             : parcela.isVencido
  //                 ? ColorsApp.error[300]
  //                 : parcela.isAVencer
  //                     ? ColorsApp.cinza[200]
  //                     : ColorsApp.cinza[300];
  //   } catch (error) {
  //     return ColorsApp.cinza[400];
  //   }
  // }

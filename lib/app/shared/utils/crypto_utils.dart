import 'storage_utils.dart';

import 'package:encrypt/encrypt.dart';

class CryptoUtils {
  static encode(String? term) async {
    try {
      if (term == null || term.isEmpty) return null;
      String keyString = await StorageUtils.getKey();

      final keysValues = keyString.split(' ');
      final key = Key.fromUtf8(keysValues[1]);
      final iv = IV.fromBase64(keysValues[0]);
      final encrypter = Encrypter(AES(key));
      final encrypted = encrypter.encrypt(term, iv: iv);
      return encrypted.base64;
    } catch (error) {
      return null;
    }
  }

  static decode(String? term) async {
    try {
      String keyString = await StorageUtils.getKey();

      if (term == null || term.isEmpty) return null;
      final keysValues = keyString.split(' ');
      final key = Key.fromUtf8(keysValues[1]);
      final iv = IV.fromBase64(keysValues[0]);
      final encrypter = Encrypter(AES(key));

      final decrypted = encrypter.decrypt(Encrypted.from64(term), iv: iv);
      return decrypted;
    } catch (error) {
      return null;
    }
  }
}

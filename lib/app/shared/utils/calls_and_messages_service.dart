import 'dart:developer';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';

import 'package:url_launcher/url_launcher_string.dart';

class CallsAndMessagesService {
  void call(String number) => launchUrlString("tel:$number");
  void sendSms(String number) => launchUrlString("sms:$number");
  void sendEmail({required String email, String? emailCc}) {
    if (emailCc != null && emailCc.isNotEmpty) {
      launchUrlString("mailto:$email" "?cc=$emailCc");
    } else {
      launchUrlString("mailto:$email");
    }
  }

  static sendEmailSuporte() async {
    var email1 = AppSession.getInstance().emailHomeSupport;
    var email2 = AppSession.getInstance().emailCcSuporte;

    if (email1 == null || email1.isEmpty) {
      email1 = Constants.EMAIL_SUPORTE_SICLOS;
      email2 = '';
    }

    await launchUrlString("mailto:$email1?cc=$email2").then((value) {
      log('EMAIL ENVIADO PARA SUPORTE COM SUCESSO: $email1');
    }).catchError((e) {
      log('ERRO AO ENVIAR EMAIL PARA SUPORTE: $e');
    });
  }

  void openURL(String url) => launchUrlString("https:$url");
}

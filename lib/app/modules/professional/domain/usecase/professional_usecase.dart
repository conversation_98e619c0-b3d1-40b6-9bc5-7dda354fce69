import 'package:siclosbank/app/modules/professional/data/models/hour_base_response.dart';
import 'package:siclosbank/app/modules/professional/data/models/vacation_response.dart';
import 'package:siclosbank/app/modules/professional/domain/repository/i_professional_repository.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../../../../app_controller.dart';
import '../../../../shared/utils/utils.dart';
import '../../data/models/time_sheet_response.dart';

abstract class IProfessionalUsecase {
  /// download de imposto de renda
  Future<void> downloadIR();

  /// download do recibo de ferias
  Future<void> downloadVacationReceipt({
    required String relatedCode,
    required String plate,
    required DateTime datePerAquis,
    required DateTime datePayment,
    required int installment,
  });

  /// obter frequencia/folha de pontos
  Future<List<TimeSheetResponse>> getTimeSheet({
    required String relatedCode,
    required String plate,
    String? initDate,
    String? finallyDate,
  });

// obter banco de horas
  Future<HourBaseResponse> getHourBase(
      {required String relatedCode, required String plate});

// ferias
  Future<List<VacationResponse>> getVacationList(
      {required String relatedCode, required String plate});

// download Holerite
  Future<void> downloadPayslip({
    required int month,
    required int year,
  });

  Future<bool> checkIsCollaborator();
}

class ProfessionalUsecase implements IProfessionalUsecase {
  final IProfessionalRepository _repository;

  ProfessionalUsecase(this._repository);

  @override
  Future<void> downloadIR() async {
    final cpf = AppSession.getInstance().user!.cpf!;
    try {
      final buffer = await _repository.downloadIRProfessional(cpf);

      Utils.openFile(buffer.path, type: 'application/pdf');

      return;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> downloadVacationReceipt({
    required String relatedCode,
    required String plate,
    required DateTime datePerAquis,
    required DateTime datePayment,
    required int installment,
  }) async {
    final cpf = AppSession.getInstance().user!.cpf!;
    try {
      final buffer = await _repository.downloadVacationReceipt(
          cpf: cpf,
          relatedCode: relatedCode,
          plate: plate,
          datePerAquis: Utils.getDateTimeToApi(datePerAquis),
          datePayment: Utils.getDateTimeToApi(datePayment),
          installment: installment);

      await Utils.openFile(buffer.path, type: 'application/pdf');

      return;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<TimeSheetResponse>> getTimeSheet({
    required String relatedCode,
    required String plate,
    String? initDate,
    String? finallyDate,
  }) async {
    try {
      final newDateInitial = initDate ??
          Utils.getDateTimeToApi(
              DateTime(DateTime.now().year, DateTime.now().month));
      final newDateFinally = finallyDate ??
          Utils.getDateTimeToApi(
              DateTime(DateTime.now().year, DateTime.now().month + 1)
                  .subtract(const Duration(days: 1)));

      List<TimeSheetResponse> timeSheet = await _repository.getTimeSheet(
        relatedCode: relatedCode,
        plate: plate,
        initDate: newDateInitial,
        finallyDate: newDateFinally,
      );

      return timeSheet;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<HourBaseResponse> getHourBase(
      {required String relatedCode, required String plate}) async {
    try {
      HourBaseResponse hoursBase =
          await _repository.getHourBase(relatedCode: relatedCode, plate: plate);

      return hoursBase;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<VacationResponse>> getVacationList(
      {required String relatedCode, required String plate}) async {
    try {
      List<VacationResponse> result =
          await _repository.getVacation(relatedCode: relatedCode, plate: plate);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> downloadPayslip({
    required int month,
    required int year,
  }) async {
    final cpf = AppSession.getInstance().user!.cpf!;
    try {
      final file = await _repository.downloadPayslip(
          cpf: cpf, month: month.toString(), year: year.toString());

      await Utils.openFile(file.path);

      return;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> checkIsCollaborator() async {
    try {
      final result = await _repository.checkIsCollaborator();

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

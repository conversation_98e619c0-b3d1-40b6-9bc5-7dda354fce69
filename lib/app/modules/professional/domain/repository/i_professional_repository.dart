import 'dart:io';

abstract class IProfessionalRepository {
  /// obter folha de pontos
  Future getTimeSheet({
    required String relatedCode,
    required String plate,
    required String initDate,
    required String finallyDate,
  });

// banco de horas
  Future getHourBase({required String relatedCode, required String plate});

// ferias
  Future getVacation({required String relatedCode, required String plate});

// download IR
  Future<File> downloadIRProfessional(String cpf);

// download recibo de ferias
  Future<File> downloadVacationReceipt({
    required String cpf,
    required String relatedCode,
    required String plate,
    required String datePerAquis,
    required String datePayment,
    required int installment,
  });

// download Holerite
  Future<File> downloadPayslip({
    required String cpf,
    required String month,
    required String year,
  });

  Future<bool> checkIsCollaborator();
}

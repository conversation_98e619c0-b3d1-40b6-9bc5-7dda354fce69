import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import '../../../domain/usecase/professional_usecase.dart';
import '../../../../../shared/errors/error_response.dart';

import 'professional_event.dart';
import 'professional_state.dart';

class ProfessionalHomeBloc extends Bloc<ProfessionalEvent, ProfessionalState> {
  final IProfessionalUsecase _usecase;
  ProfessionalHomeBloc(
    this._usecase,
  ) : super(InitialProfessionalState()) {
    on((event, emit) async {
      if (event is InitalEvent) {
        try {
          // emit(LoadingProfessionalState());
          final collaborator = AppSession.getInstance().collaborator;
          if (collaborator == null) {
            emit(InvalidUserProfessionalState());
            return;
          }
          // final reuslt = await _usecase.checkIsCollaborator();

          // if (!reuslt) {
          //   emit(InvalidUserProfessionalState());
          //   return;
          // }

          emit(InitialProfessionalState());
        } on ErrorResponse catch (error) {
          emit(ErrorProfessionalState(error));
        }
      }

      if (event is DownloadIREvent) {
        try {
          emit(LoadingProfessionalState());
          await _usecase.downloadIR();

          emit(SuccessProfessionalState());
        } on ErrorResponse catch (error) {
          emit(ErrorProfessionalState(error));
        }
      }
    });
  }
}

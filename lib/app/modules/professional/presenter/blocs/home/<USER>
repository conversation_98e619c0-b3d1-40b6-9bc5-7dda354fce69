import '../../../../../shared/errors/error_response.dart';

sealed class ProfessionalState {
  T when<T>({
    required T Function(InitialProfessionalState state) init,
    required T Function(LoadingProfessionalState state) load,
    required T Function(ErrorProfessionalState state) error,
    required T Function(SuccessProfessionalState state) success,
    required T Function(InvalidUserProfessionalState state) invalid,
  }) {
    return switch (this) {
      InitialProfessionalState s => init(s),
      LoadingProfessionalState s => load(s),
      ErrorProfessionalState s => error(s),
      SuccessProfessionalState s => success(s),
      InvalidUserProfessionalState s => invalid(s),
    };
  }
}

class InitialProfessionalState extends ProfessionalState {}

class LoadingProfessionalState extends ProfessionalState {}

class ErrorProfessionalState extends ProfessionalState {
  final ErrorResponse error;
  ErrorProfessionalState(this.error);
}

class InvalidUserProfessionalState extends ProfessionalState {
  InvalidUserProfessionalState();
}

class SuccessProfessionalState extends ProfessionalState {}

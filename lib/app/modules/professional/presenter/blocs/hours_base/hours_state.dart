part of 'hours_bloc.dart';

sealed class HoursState extends Equatable {
  const HoursState();

  @override
  List<Object> get props => [];
}

final class HoursInitial extends HoursState {}

final class HoursLoading extends HoursState {}

final class HoursError extends HoursState {
  final ErrorResponse error;

  const HoursError(this.error);
}

final class HoursSuccess extends HoursState {
  final HourBaseResponse hourBase;

  const HoursSuccess(this.hourBase);
}

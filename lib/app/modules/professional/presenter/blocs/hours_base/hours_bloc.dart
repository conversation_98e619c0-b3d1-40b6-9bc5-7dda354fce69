import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/professional/data/models/hour_base_response.dart';
import 'package:siclosbank/app/modules/professional/domain/usecase/professional_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../app_controller.dart';

part 'hours_event.dart';
part 'hours_state.dart';

class HoursBloc extends Bloc<HoursEvent, HoursState> {
  final IProfessionalUsecase _usecase;

  HoursBloc(this._usecase) : super(HoursInitial()) {
    on<HoursEvent>((event, emit) async {
      if (event is LoadHours) {
        emit(HoursLoading());
        try {
          var collaborator = AppSession.getInstance().collaborator!;
          final result = await _usecase.getHourBase(
              relatedCode: collaborator.relatedCode!,
              plate: collaborator.plate!);
          emit(HoursSuccess(result));
        } on ErrorResponse catch (e) {
          emit(HoursError(e));
        }
      }
    });
  }
}

import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/modules/professional/domain/usecase/professional_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../data/models/payslip_response.dart';

part 'payslip_event.dart';
part 'payslip_state.dart';

class PayslipBloc extends Bloc<PayslipEvent, PayslipState> {
  final IProfessionalUsecase _usecase;
  PayslipBloc(this._usecase) : super(PayslipInitial(monthsOfCurrentYear())) {
    on<PayslipEvent>((event, emit) async {
      if (event is DownloadPayslip) {
        try {
          final month = event.payslipResponse.month;
          final year = event.payslipResponse.year;
          emit(PayslipLoading(event.payslipResponse, state.payslipList));
          await _usecase.downloadPayslip(month: month, year: year);
          emit(PayslipSuccess(event.payslipResponse, state.payslipList));
        } on ErrorResponse catch (e) {
          emit(PayslipError(state.payslipList,
              error: e, payslipSelected: event.payslipResponse));
        }
      }
    });
  }

  static List<PayslipResponse> monthsOfCurrentYear() {
    var listOfMonths = <PayslipResponse>[];
    const totalMonth = 12;
    const initialMonth = 1;

    // Adicionando Dezembro do ano anterior
    listOfMonths.add(
      PayslipResponse(
        month: 12,
        year: DateTime.now().year - 1,
        // downloading: false,
      ),
    );

    for (var month = initialMonth; month <= totalMonth; month++) {
      listOfMonths.add(
        PayslipResponse(
          month: month,
          year: DateTime.now().year,
          // downloading: false,
        ),
      );
    }
    return listOfMonths;
  }
}

part of 'payslip_bloc.dart';

class PayslipState {
  PayslipState([this.payslipList]);

  List<PayslipResponse>? payslipList;
}

final class PayslipInitial extends PayslipState {
  PayslipInitial(super.payslipList);
}

final class PayslipLoading extends PayslipState {
  final PayslipResponse payslipSelected;

  PayslipLoading(this.payslipSelected, super.payslipList);
}

final class PayslipError extends PayslipState {
  final ErrorResponse error;
  final PayslipResponse payslipSelected;

  PayslipError(super.payslipList,
      {required this.error, required this.payslipSelected});
}

final class PayslipSuccess extends PayslipState {
  final PayslipResponse payslipSelected;

  PayslipSuccess(this.payslipSelected, [super.payslipList]);
}

import 'package:siclosbank/app/modules/professional/data/models/vacation_response.dart';

import '../../../../../shared/errors/error_response.dart';

sealed class VacationState {
  T when<T>({
    required T Function(InitialVacationState state) init,
    required T Function(LoadingPageVacationState state) load,
    required T Function(ErrorVacationState state) error,
    required T Function(SuccessVacationState state) success,
  }) {
    return switch (this) {
      InitialVacationState s => init(s),
      LoadingPageVacationState s => load(s),
      ErrorVacationState s => error(s),
      SuccessVacationState s => success(s),
    };
  }
}

class InitialVacationState extends VacationState {}

class LoadingPageVacationState extends VacationState {
  LoadingPageVacationState();
}

class ErrorVacationState extends VacationState {
  final ErrorResponse error;
  ErrorVacationState(this.error);
}

class SuccessVacationState extends VacationState {
  SuccessVacationState(
      {required this.vacationResponseList,
      this.isDownloadingRecibo = false,
      this.yearReciboDownloading});

  final List<VacationResponse> vacationResponseList;
  final bool isDownloadingRecibo;
  final int? yearReciboDownloading;

  SuccessVacationState copyWith({
    List<VacationResponse>? vacationResponseList,
    bool? isDownloadingRecibo,
    int? yearReciboDownloading,
  }) {
    return SuccessVacationState(
        vacationResponseList: vacationResponseList ?? this.vacationResponseList,
        isDownloadingRecibo: isDownloadingRecibo ?? false,
        yearReciboDownloading: yearReciboDownloading ?? yearReciboDownloading);
  }
}

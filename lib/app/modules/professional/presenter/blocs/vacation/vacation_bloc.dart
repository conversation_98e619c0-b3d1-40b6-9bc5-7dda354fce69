import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/vacation/vacation_event.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/vacation/vacation_state.dart';

import '../../../../../app_controller.dart';
import '../../../domain/usecase/professional_usecase.dart';
import '../../../../../shared/errors/error_response.dart';

class VacationBloc extends Bloc<VacationEvent, VacationState> {
  final IProfessionalUsecase _usecase;
  VacationBloc(this._usecase) : super(InitialVacationState()) {
    on((event, emit) async {
      if (event is LoadVacation) {
        try {
          emit(LoadingPageVacationState());
          var collaborator = AppSession.getInstance().collaborator!;
          final result = await _usecase.getVacationList(
              relatedCode: collaborator.relatedCode!,
              plate: collaborator.plate!);
          emit(SuccessVacationState(vacationResponseList: result));
          //
        } on ErrorResponse catch (error) {
          emit(ErrorVacationState(error));
        }
      }

      if (event is OpenVacationReceipt) {
        if (state is SuccessVacationState) {
          var success = (state as SuccessVacationState);
          if (success.vacationResponseList.isNotEmpty) {
            emit(setLoadingDownloadReciboFerias(event, true));
            try {
              final selected = event.vacationResponseSelected;
              await _usecase.downloadVacationReceipt(
                  relatedCode: selected.codColigada.toString(),
                  plate: selected.chapa,
                  datePerAquis: selected.fimPerAquis,
                  datePayment: selected.dataPagto,
                  installment: selected.parcela);
              //
              emit(setLoadingDownloadReciboFerias(event, false));
            } on ErrorResponse catch (error) {
              emit(ErrorVacationState(error));
            }

            emit(setLoadingDownloadReciboFerias(event, false));
          }
        }
      }
    });
  }

  SuccessVacationState setLoadingDownloadReciboFerias(
      OpenVacationReceipt event, bool loading) {
    if (state is SuccessVacationState) {
      var stateSuccess = (state as SuccessVacationState);
      stateSuccess
          .vacationResponseList[stateSuccess.vacationResponseList.indexWhere(
              (element) => element == event.vacationResponseSelected)]
          .isDownloading = loading;

      return stateSuccess.copyWith(
        vacationResponseList: stateSuccess.vacationResponseList,
        isDownloadingRecibo: loading,
        yearReciboDownloading: event.vacationResponseSelected.dataFim.year,
      );
    } else {
      throw Exception('State type error. Expect <SuccessVacationState>.');
    }
  }
}

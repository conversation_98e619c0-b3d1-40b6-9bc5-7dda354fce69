import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/modules/professional/data/models/time_sheet_response.dart';
import 'package:siclosbank/app/modules/professional/domain/usecase/professional_usecase.dart';

import '../../../../../app_controller.dart';
import '../../../../../shared/errors/error_response.dart';
import '../../../../../shared/utils/utils.dart';

part 'time_sheet_event.dart';
part 'time_sheet_state.dart';

class TimeSheetBloc extends Bloc<TimeSheetEvent, TimeSheetState> {
  final IProfessionalUsecase _usecase;
  TimeSheetBloc(this._usecase) : super(InitialTimeSheetState()) {
    on<TimeSheetEvent>((event, emit) async {
      if (event is LoadTimeSheet) {
        emit(LoadingPageTimeSheet());
        try {
          var collaborator = AppSession.getInstance().collaborator!;
          final dates = _getCurrentMonthDates();
          final List<TimeSheetResponse> result = await _usecase.getTimeSheet(
              relatedCode: collaborator.relatedCode!,
              plate: collaborator.plate!,
              initDate: dates.first,
              finallyDate: dates.last);

          emit(SuccessTimeSheetState(result));
        } on ErrorResponse catch (e) {
          emit(TimeSheetError(e));
        }
      }

      if (event is ChangeMonth) {
        emit(LoadingPageTimeSheet());
        var result = changeMonthView(event.currentDate, event.changeMonthType);
        var collaborator = AppSession.getInstance().collaborator!;
        try {
          List<TimeSheetResponse> frequencia = await _usecase.getTimeSheet(
            relatedCode: collaborator.relatedCode!,
            plate: collaborator.plate!,
            initDate: result.first,
            finallyDate: result.last,
          );
          emit(SuccessTimeSheetState(frequencia));
        } catch (e) {
          emit(TimeSheetError(ErrorResponse(
              message:
                  'Erro ao carregar folha de pontos. Por favor, tente novamente mais tarde.')));
        }
      }
    });
  }

  Set<String> _getCurrentMonthDates() {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

    return {
      Utils.getDateTimeToApi(firstDayOfMonth),
      Utils.getDateTimeToApi(lastDayOfMonth),
    };
  }

  Set<String> changeMonthView(
      String currentDate, ChangeMonthType changeMonthType) {
    var currentMonth = int.parse(currentDate.substring(3, 5));
    var currentYear = int.parse(currentDate.substring(6));

    if (changeMonthType == ChangeMonthType.next) {
      if (currentMonth == 12) {
        currentYear++;
        currentMonth = 1;
      } else {
        currentMonth++;
      }
    } else {
      if (currentMonth == 1) {
        currentYear--;
        currentMonth = 12;
      } else {
        currentMonth--;
      }
    }

    final firstDayOfMonth = DateTime(currentYear, currentMonth, 1);
    final lastDayOfMonth = DateTime(currentYear, currentMonth + 1, 0);

    return {
      Utils.getDateTimeToApi(firstDayOfMonth),
      Utils.getDateTimeToApi(lastDayOfMonth),
    };
  }
}

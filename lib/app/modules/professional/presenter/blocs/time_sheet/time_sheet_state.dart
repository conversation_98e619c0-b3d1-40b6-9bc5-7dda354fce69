part of 'time_sheet_bloc.dart';

abstract class TimeSheetState {}

class InitialTimeSheetState extends TimeSheetState {}

class LoadingPageTimeSheet extends TimeSheetState {}

class TimeSheetError extends TimeSheetState {
  final ErrorResponse error;
  TimeSheetError(this.error);
}

class SuccessTimeSheetState extends TimeSheetState {
  SuccessTimeSheetState(this.timeSheetList);

  final List<TimeSheetResponse> timeSheetList;
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/hours_base/hours_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/time_sheet/time_sheet_bloc.dart';
import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'components/frequencia_view_widget.dart';

import 'components/card_hours_base.dart';

class HoursPage extends StatelessWidget {
  const HoursPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<TimeSheetBloc>(
          create: (BuildContext context) => Modular.get<TimeSheetBloc>(),
        ),
        BlocProvider<HoursBloc>(
          create: (BuildContext context) => Modular.get<HoursBloc>(),
        ),
      ],
      child: const _HorasView(),
    );
  }
}

class _HorasView extends StatefulWidget {
  const _HorasView({super.key});

  @override
  State<_HorasView> createState() => __HorasViewState();
}

class __HorasViewState extends State<_HorasView> {
  @override
  void initState() {
    refresh();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.horas_profissionais.toUpperCase(),
      ),
      body: RefreshIndicator(
        onRefresh: refresh,
        child: ContainerResponsive(
          padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 8),
          child: const CustomScrollView(
            slivers: <Widget>[
              CardResumHours(),
              TimeSheetViewWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> refresh() {
    BlocProvider.of<HoursBloc>(context).add(LoadHours());
    BlocProvider.of<TimeSheetBloc>(context).add(LoadTimeSheet());

    return Future.value();
  }
}

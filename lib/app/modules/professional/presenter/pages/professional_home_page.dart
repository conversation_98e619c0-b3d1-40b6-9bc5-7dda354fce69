import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/modules/professional/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/modules/professional/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/tab_bar_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/images_app.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'components/item_icone_text.dart';

class ProfessionalHomePage extends StatelessWidget {
  const ProfessionalHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ProfessionalHomeBloc>(
      create: (_) => Modular.get<ProfessionalHomeBloc>(),
      child: const _ProfessionalHomePage(),
    );
  }
}

class _ProfessionalHomePage extends StatefulWidget {
  const _ProfessionalHomePage();

  @override
  State<_ProfessionalHomePage> createState() => __ProfessionalHomePageState();
}

class __ProfessionalHomePageState extends State<_ProfessionalHomePage> {
  @override
  void initState() {
    BlocProvider.of<ProfessionalHomeBloc>(context).add(InitalEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.gestao_profissional.toUpperCase(),
        showBack: false,
      ),
      body: BlocConsumer<ProfessionalHomeBloc, ProfessionalState>(
          listener: (context, state) {
        if (state is ErrorProfessionalState) {
          DialogUtils.showSnackError(context, state.error);
        }
      }, builder: (context, state) {
        if (state is LoadingProfessionalState) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        if (state is InvalidUserProfessionalState) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SizedBox(height: size.height * 0.1),
                const SizedBox(height: 16),
                Text(
                  const I18n().nao_habilitado,
                  style: theme.textTheme.displayLarge,
                ),
                const SizedBox(
                  height: 16,
                ),
                Text(
                  const I18n().texto_usuario_sem_viculo_empresa,
                  style: theme.textTheme.bodyMedium,
                ),
                const Expanded(
                  child: SizedBox(
                    height: 0,
                  ),
                ),
                ImagesApp.imgNaoFuncionario(),
              ],
            ),
          );
        }
        return GridView(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 160 / 130,
          ),
          padding: const EdgeInsets.only(left: 16, top: 26, right: 16),
          children: <Widget>[
            ItemIconeText(
              icon: IconsApp.icIR(),
              text: I18n.of(context)!.imposto_renda,
              click: _showSheetDownloadIR,
              contentCenter: true,
              iconWallet: true,
            ),
            ItemIconeText(
              icon: IconsApp.icPayslip(),
              text: I18n.of(context)!.holerite,
              contentCenter: true,
              iconWallet: true,
              click: () {
                push(Routes.payslipPage);
              },
            ),
            ItemIconeText(
              icon: IconsApp.icHoursProfessional(size: 40),
              text: I18n.of(context)!.horas_profissionais,
              contentCenter: true,
              iconWallet: true,
              click: () {
                push(Routes.hoursPage);
              },
            ),
            ItemIconeText(
              icon: IconsApp.icVacation(size: 40),
              text: I18n.of(context)!.ferias,
              contentCenter: true,
              iconWallet: true,
              click: () {
                push(Routes.vacationPage);
              },
            ),
          ],
        );
      }),
    );
  }

  _showSheetDownloadIR() {
    SheetAlertConfirm.showSheet(
      context,
      title: I18n.of(context)!.imposto_renda,
      message:
          "${I18n.of(context)!.desejaDownloadImpostoRenda(DateTime.now().year.toString(), (DateTime.now().year - 1).toString())}\n\n${I18n.of(context)!.senha_cpf_irpf}",
      textPositive: I18n.of(context)!.sim_fazer_download,
      textNegative: I18n.of(context)!.nao,
      clickPositive: () {
        BlocProvider.of<ProfessionalHomeBloc>(context).add(DownloadIREvent());
      },
    );
  }
}

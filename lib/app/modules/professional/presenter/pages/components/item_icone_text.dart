import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/widgets/box_decoration_app.dart';

class ItemIconeText extends StatelessWidget {
  final Widget? icon;
  final String? text;
  final Function? click;
  final bool iconWallet;
  final bool contentCenter;

  const ItemIconeText({
    super.key,
    this.icon,
    this.text,
    this.click,
    this.iconWallet = false,
    this.contentCenter = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (click != null) click!();
      },
      child: Container(
        padding: const EdgeInsets.only(bottom: 8, top: 8),
        decoration: BoxDecorationApp.dropShadowNewCircle(radius: 10),
        child: Column(
          mainAxisAlignment: contentCenter
              ? MainAxisAlignment.center
              : MainAxisAlignment.start,
          children: <Widget>[
            if (icon != null) icon!,
            SizedBox(
              height: iconWallet ? 8 : 0,
            ),
            Text(
              text ?? '',
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(height: contentCenter ? 1.0 : null),
            ),
          ],
        ),
      ),
    );
  }
}

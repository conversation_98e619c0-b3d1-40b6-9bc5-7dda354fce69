import 'package:flutter/material.dart';

import 'package:siclosbank/app/modules/professional/data/models/payslip_response.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/utils/utils.dart';

class ItemPayslip extends StatelessWidget {
  final PayslipResponse payslipResponse;
  final bool isPrimeiro;
  final bool isUltimo;
  final VoidCallback? onTap;
  final bool isLoading;

  const ItemPayslip({
    super.key,
    required this.payslipResponse,
    this.isPrimeiro = false,
    this.isUltimo = false,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: Utils.enableDisableDownloadPayslip(payslipResponse) && !isLoading
          ? onTap
          : null,
      child: ContainerResponsive(
        margin: EdgeInsetsResponsive.only(bottom: 4),
        child: ContainerResponsive(
          decoration: BoxDecoration(
              color: Utils.enableDisableDownloadPayslip(payslipResponse)
                  ? Colors.white
                  : ColorsApp.bgDesativado,
              borderRadius: BorderRadius.only(
                topLeft: isPrimeiro
                    ? const Radius.circular(5)
                    : const Radius.circular(0),
                topRight: isPrimeiro
                    ? const Radius.circular(5)
                    : const Radius.circular(0),
                bottomLeft: isUltimo
                    ? const Radius.circular(5)
                    : const Radius.circular(0),
                bottomRight: isUltimo
                    ? const Radius.circular(5)
                    : const Radius.circular(0),
              )),
          padding: EdgeInsetsResponsive.only(
              left: 16, right: 16, top: 24, bottom: 24),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Row(
                children: <Widget>[
                  TextResponsive(
                    Utils.getMesFormatado(
                      DateTime(payslipResponse.year, payslipResponse.month)
                          .toString(),
                    ),
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(color: ColorsApp.cinza[900]),
                  ),
                  SizedBoxResponsive(width: 4),
                  TextResponsive(
                    Utils.getAnoFormatado(
                      DateTime(payslipResponse.year, payslipResponse.month)
                          .toString(),
                    ),
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(color: ColorsApp.cinza[800]),
                  ),
                ],
              ),
              isLoading ? loading() : IconsApp.icDownload(),
            ],
          ),
        ),
      ),
    );
  }

  SizedBox loading() {
    return SizedBox(
      height: 20,
      width: 20,
      child: CircularProgressIndicator(
        backgroundColor: Colors.white,
        valueColor: AlwaysStoppedAnimation<Color>(ColorsApp.verde[500]!),
        strokeWidth: 2,
      ),
    );
  }
}

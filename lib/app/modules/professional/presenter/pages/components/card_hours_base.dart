import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/hours_base/hours_bloc.dart';
import '../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';

class CardResumHours extends StatelessWidget {
  const CardResumHours({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
        child: BlocConsumer<HoursBloc, HoursState>(listener: (context, state) {
      if (state is HoursError) {
        DialogUtils.showSnackError(context, state.error);
      }
    }, builder: (context, state) {
      var hourBase = state is HoursSuccess ? state.hourBase : null;

      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: hourBase == null && state is! HoursLoading
              ? Colors.black.withOpacity(0.05)
              : Colors.white,
          boxShadow: const [
            BoxShadow(
              color: ColorsApp.drop1,
              blurRadius: 2,
              offset: Offset(0, 2),
            )
          ],
        ),
        child: Builder(builder: (context) {
          if (state is HoursLoading) {
            return const SizedBox(
                height: 180, child: Center(child: CircularProgressIndicator()));
          }

          return Stack(
            children: <Widget>[
              Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Padding(
                    padding: EdgeInsetsResponsive.only(
                        left: 16, top: 6, right: 16, bottom: 6),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        _MounthAndHours(
                          month: hourBase?.mesAExpirar ?? '----', // 'Agosto',
                          hours: hourBase?.bhMesAExpirar ?? '00:00:00',
                          colorHours:
                              hourBase == null ? Colors.black12 : Colors.red,
                        ),
                        _MounthAndHours(
                          month: hourBase?.mesAnterior ?? '----', // 'Setembro',
                          hours: hourBase?.bhMesAnterior ?? '00:00:00',
                          colorHours:
                              hourBase == null ? Colors.black12 : Colors.amber,
                        ),
                        _MounthAndHours(
                          month: hourBase?.mesAtual ?? '----', // 'Outubro',
                          hours: hourBase?.bhMesAtual ?? '00:00:00',
                          colorHours:
                              hourBase == null ? Colors.black12 : Colors.blue,
                        ),
                        _MounthAndHours(
                          month: hourBase == null ? '----' : 'Saldo Expirado',
                          hours: hourBase?.saldoExpirado ?? '00:00:00',
                          colorHours:
                              hourBase == null ? Colors.black12 : Colors.red,
                        ),
                        _MounthAndHours(
                          month: hourBase == null ? '----' : 'Saldo atual',
                          hours: hourBase?.saldoAtual ?? '00:00:00',
                          colorHours:
                              hourBase == null ? Colors.black12 : Colors.green,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          );
        }),
      );
      // }
      // return Container(
      //   height: 100,
      // );
    }));
  }
}

class _MounthAndHours extends StatelessWidget {
  const _MounthAndHours({
    required this.month,
    required this.hours,
    required this.colorHours,
  });
  final String month;
  final String hours;
  final Color colorHours;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            month,
            style: Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(fontWeight: FontWeight.bold),
          ),
          Container(
            // width: 80,
            decoration: _boxDecoration(colorHours),
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
            child: Text(
              hours, // '00:00:00',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium!
                  .copyWith(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}

BoxDecoration _boxDecoration(color) {
  return BoxDecoration(
    color: color,
    borderRadius: BorderRadius.circular(5),
  );
}

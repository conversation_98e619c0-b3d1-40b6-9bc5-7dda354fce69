import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/time_sheet/time_sheet_bloc.dart';
import '../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/utils/utils.dart';

class TimeSheetViewWidget extends StatelessWidget {
  const TimeSheetViewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    var currentMonth = DateTime.now().month;
    var currentYear = DateTime.now().year;
    return SliverToBoxAdapter(
      child: BlocConsumer<TimeSheetBloc, TimeSheetState>(
        listener: (context, state) {
          if (state is TimeSheetError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          var loading = state is LoadingPageTimeSheet;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Center(
                  child: Material(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(15),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 8),
                      child: Row(
                        // mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          IconButton(
                              icon: const Icon(Icons.arrow_back_ios),
                              onPressed: loading
                                  ? null
                                  : () {
                                      if (state is SuccessTimeSheetState) {
                                        BlocProvider.of<TimeSheetBloc>(context)
                                            .add(ChangeMonth(
                                                state.timeSheetList.first.date!,
                                                ChangeMonthType.previous));
                                      }
                                    }),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: Builder(builder: (context) {
                              if (state is SuccessTimeSheetState) {
                                currentMonth = int.parse(state
                                    .timeSheetList.first.date!
                                    .substring(3, 5));
                                currentYear = int.parse(state
                                    .timeSheetList.first.date!
                                    .substring(6, 10));
                              }
                              return Text(
                                '${Utils.getMesDescricao(month: currentMonth)} $currentYear',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall!
                                    .copyWith(
                                        fontSize: 16,
                                        color: state is SuccessTimeSheetState
                                            ? null
                                            : ColorsApp.cinza[500]),
                              );
                            }),
                          ),
                          IconButton(
                              icon: const Icon(Icons.arrow_forward_ios),
                              onPressed: loading
                                  ? null
                                  : () {
                                      if (state is SuccessTimeSheetState) {
                                        BlocProvider.of<TimeSheetBloc>(context)
                                            .add(ChangeMonth(
                                                state.timeSheetList.first.date!,
                                                ChangeMonthType.next));
                                      }
                                    }),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Builder(builder: (context) {
                if (loading) {
                  return const SizedBox(
                    height: 200,
                    child: Center(child: CircularProgressIndicator()),
                  );
                } else if (state is SuccessTimeSheetState) {
                  if (state.timeSheetList.isEmpty) {
                    return Container();
                  }
                  return DataTable(
                      columnSpacing: 6,
                      horizontalMargin: 2,
                      columns: const [
                        DataColumn(label: Text('Dia')),
                        DataColumn(label: Text('TIPO')),
                        DataColumn(label: Text('CH')),
                        DataColumn(label: Text('BH')),
                      ],
                      rows: state.timeSheetList
                          .map((registro) => DataRow(cells: [
                                DataCell(
                                    Text(registro.date?.substring(0, 2) ?? '')),
                                DataCell(Material(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(5),
                                    child: SizedBox(
                                      width: 150,
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 12),
                                        child: Text(registro.type ??
                                            '.::SEM REGISTRO::.'),
                                      ),
                                    ))),
                                DataCell(Text(registro.timeLoggedIn ?? '')),
                                DataCell(Text(
                                  registro.bh ?? '-',
                                  style: TextStyle(
                                      color: registro.bh != null &&
                                              registro.bh!.isNotEmpty
                                          ? (registro.bh!.contains('-')
                                              ? ColorsApp.error[300]
                                              : ColorsApp.verde[600])
                                          : null),
                                )),
                              ]))
                          .toList());
                }
                return Container();
              }),
              SizedBoxResponsive(height: 63),
            ],
          );
        },
      ),
    );
  }
}

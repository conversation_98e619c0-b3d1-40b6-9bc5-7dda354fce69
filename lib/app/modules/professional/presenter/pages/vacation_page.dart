import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/professional/data/models/vacation_response.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/vacation/vacation_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/vacation/vacation_event.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/vacation/vacation_state.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../shared/themes/styles/colors_app.dart';
import '../../../../shared/utils/utils.dart';

class VacationPage extends StatelessWidget {
  const VacationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<VacationBloc>(
      create: (context) => Modular.get<VacationBloc>(),
      child: const _VacationView(),
    );
  }
}

class _VacationView extends StatefulWidget {
  const _VacationView();

  @override
  State<_VacationView> createState() => __HoleriteViewState();
}

class __HoleriteViewState extends State<_VacationView> {
  @override
  void initState() {
    BlocProvider.of<VacationBloc>(context).add(LoadVacation());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.ferias.toUpperCase(),
      ),
      body: BlocConsumer<VacationBloc, VacationState>(
        listener: (context, state) {
          if (state is ErrorVacationState) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is LoadingPageVacationState) {
            return const SizedBox(
                height: 300, child: Center(child: CircularProgressIndicator()));
          }

          if (state is SuccessVacationState) {
            if (state.vacationResponseList.isEmpty) {
              return Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 40, horizontal: 16),
                child: Container(
                    // height: 60,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: ColorsApp.cinza[300]),
                    padding: const EdgeInsets.all(16),
                    // alignment: Alignment.centerLeft,
                    child: const Row(
                      children: <Widget>[
                        Icon(
                          Icons.error_outline,
                        ),
                        // color: ColorsApp.error.withOpacity(0.7)),
                        SizedBox(width: 16),
                        Text('Nenhum histórico de férias encontrado.',
                            style: TextStyle(
                                // fontWeight: FontWeight.bold,
                                )),
                      ],
                    )),
              );
            } else {
              return _buildBody(state);
            }
          }

          return Container();
        },
      ),
    );
  }

  _buildBody(SuccessVacationState state) {
    // final holeriteResponselList = state.holeriteResponselList;

    state.vacationResponseList
        .sort((a, b) => a.fimPerAquis.year.compareTo(b.fimPerAquis.year));

    final listFerias = state.vacationResponseList;
    // print(listFerias.map((e) => e.toJson()));

    Map<int, List<VacationResponse>> mapaPorAno = {};

    for (var dado in listFerias) {
      int ano = dado.fimPerAquis.year;
      if (mapaPorAno[ano] == null) {
        mapaPorAno[ano] = [dado];
      } else {
        mapaPorAno[ano]?.add(dado);
      }
    }

// lista de ferias, colocada em outra lista
// separada pelo ano do fim aquisitivo

    List<List<VacationResponse>> listForYear = mapaPorAno.values.toList();
    // print(listForYear);

    var ultimo = listForYear.isNotEmpty ? listForYear.length - 1 : 0;
    // var index = ultimo;
    final result = state.isDownloadingRecibo
        ? listForYear.indexWhere((listElement) =>
            listElement.first.dataFim.year == state.yearReciboDownloading)
        : ultimo;

    final indexState = ValueNotifier(result);

    return ContainerResponsive(
      padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 24 /* 32 */),
      child: ValueListenableBuilder<int>(
          valueListenable: indexState,
          builder: (context, index, _) {
            List<VacationResponse> feriasOnYear =
                (listForYear[index].isNotEmpty) ? listForYear[index] : [];

            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                TextResponsive(
                  I18n.of(context)!.abrir_ferias,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(color: ColorsApp.cinza[800]),
                ),
                SizedBoxResponsive(height: 24),
                ValueListenableBuilder(
                    valueListenable: indexState,
                    builder: (context, __, _) {
                      return Material(
                        color: Colors.grey[100],
                        elevation: 10,
                        borderRadius: BorderRadius.circular(15),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 8),
                          child: Row(
                            // mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              IconButton(
                                  icon: const Icon(Icons.arrow_back_ios),
                                  onPressed: index > 0
                                      ? () {
                                          indexState.value = index - 1;
                                        }
                                      : null),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0),
                                child: Text(
                                  'Exercício ${feriasOnYear.first.fimPerAquis.year}',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium!
                                      .copyWith(
                                        fontSize: 18,
                                      ),
                                ),
                              ),
                              IconButton(
                                  icon: const Icon(Icons.arrow_forward_ios),
                                  onPressed: index < ultimo
                                      ? () {
                                          indexState.value = index + 1;
                                        }
                                      : null),
                            ],
                          ),
                        ),
                      );
                    }),
                const SizedBox(height: 16),
                Expanded(
                  child: CustomScrollView(
                    slivers: <Widget>[
                      SliverToBoxAdapter(
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const ClampingScrollPhysics(),
                          itemCount: feriasOnYear.length,
                          itemBuilder: (context, i) {
                            var ferias = feriasOnYear[i];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: Material(
                                color: Colors.grey[100],
                                elevation: 5,
                                borderRadius: BorderRadius.circular(15),
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Text(
                                        '${ferias.parcela}ª Parcela',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium,
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: <Widget>[
                                          Text(
                                            'Início',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(fontSize: 16),
                                          ),
                                          Text(
                                            'Dias',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(fontSize: 16),
                                          ),
                                          Text(
                                            'Término',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(fontSize: 16),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: <Widget>[
                                          Text(
                                            Utils
                                                .formatDateTimeToStringDDMMYYYY(
                                                    date: ferias.dataInicio),
                                          ),
                                          Text(
                                            ferias.nroDiasFeriasResponse
                                                .toString(),
                                          ),
                                          Text(
                                            Utils
                                                .formatDateTimeToStringDDMMYYYY(
                                                    date: ferias.dataFim),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      const Divider(),
                                      InkWell(
                                        onTap: state.isDownloadingRecibo
                                            ? null
                                            : () {
                                                BlocProvider.of<VacationBloc>(
                                                        context)
                                                    .add(OpenVacationReceipt(
                                                        ferias));
                                              },
                                        child: Padding(
                                          padding: const EdgeInsets.all(12.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Text(
                                                'Ver recibo de férias',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .titleSmall!
                                                    .copyWith(fontSize: 16),
                                              ),
                                              ferias.isDownloading
                                                  ? const SizedBox(
                                                      height: 24,
                                                      width: 24,
                                                      child:
                                                          CircularProgressIndicator())
                                                  : const Icon(
                                                      Icons.launch,
                                                    )
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                          // itemCount: holeriteResponselList.length,
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: SizedBoxResponsive(height: 63),
                      ),
                    ],
                  ),
                ),
              ],
            );
          }),
    );
  }
}

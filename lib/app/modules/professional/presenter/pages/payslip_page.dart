import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/payslip/payslip_bloc.dart';
import '../../../../../localization/generated/i18n.dart';
import '../../../../app_controller.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../shared/themes/styles/colors_app.dart';
import '../../../../shared/utils/utils.dart';
import 'components/item_holerite.dart';

class PayslipPage extends StatelessWidget {
  const PayslipPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<PayslipBloc>(
      create: (context) {
        return Modular.get<PayslipBloc>();
      },
      child: const _PayslipView(),
    );
  }
}

class _PayslipView extends StatefulWidget {
  const _PayslipView();

  @override
  State<_PayslipView> createState() => _PayslipViewState();
}

class _PayslipViewState extends State<_PayslipView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.holerite.toUpperCase(),
      ),
      body: BlocConsumer<PayslipBloc, PayslipState>(
        listener: (context, state) {
          if (state is PayslipError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          return _buildBody(state);
        },
      ),
    );
  }

  _buildBody(PayslipState state) {
    final payslipList = state.payslipList!;

    return ContainerResponsive(
      padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // TextResponsive(
          //   I18n.of(context)!.mensagem_vigencia_holerite,
          //   style: Theme.of(context)
          //       .textTheme
          //       .bodyText2
          //       .copyWith(color: ColorsApp.cinza[800]),
          // ),
          // SizedBoxResponsive(height: 30),
          TextResponsive(
            I18n.of(context)!.abrir_holerite,
            style: Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(color: ColorsApp.cinza[800]),
          ),
          SizedBoxResponsive(height: 24),
          Expanded(
            child: CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    itemBuilder: (context, i) {
                      final payslip = payslipList[i];
                      bool isLoading = false;

                      if (state is PayslipLoading) {
                        if (state.payslipSelected == payslip) {
                          isLoading = true;
                        }
                      }
                      return ItemPayslip(
                        onTap: () => BlocProvider.of<PayslipBloc>(context).add(
                          DownloadPayslip(payslipResponse: payslip),
                        ),
                        isPrimeiro: i == 0,
                        isUltimo: i == (payslipList.length - 1),
                        payslipResponse: payslip,
                        isLoading: isLoading,
                      );
                    },
                    itemCount: payslipList.length,
                  ),
                ),
                SliverToBoxAdapter(
                  child: SizedBoxResponsive(height: 63),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

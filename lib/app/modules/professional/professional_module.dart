import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/hours_base/hours_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/payslip/payslip_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/time_sheet/time_sheet_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/blocs/vacation/vacation_bloc.dart';
import 'package:siclosbank/app/modules/professional/presenter/pages/payslip_page.dart';
import 'package:siclosbank/app/modules/professional/presenter/pages/vacation_page.dart';
import 'package:siclosbank/app/app_module.dart';
import 'package:siclosbank/app/modules/professional/presenter/pages/hour_base_page.dart';

import 'data/datasource/professional_datasouce_impl.dart';
import 'data/datasource/professional_datasource.dart';
import 'data/repository/professional_repository_impl.dart';
import 'domain/repository/i_professional_repository.dart';
import 'domain/usecase/professional_usecase.dart';
import 'presenter/blocs/home/<USER>';
import 'presenter/pages/professional_home_page.dart';

class ProfessionalModule extends Module {
  @override
  void binds(i) {
    i.addSingleton<IProfessionalDatasource>(ProfessionalDatasourceImpl.new);
    i.addSingleton<IProfessionalRepository>(ProfessionalRepositoryImpl.new);
    i.addSingleton<IProfessionalUsecase>(ProfessionalUsecase.new);
    i.add<ProfessionalHomeBloc>(
      ProfessionalHomeBloc.new,
      config: BindConfig(onDispose: (state) => state.close()),
    );
    i.add<HoursBloc>(
      HoursBloc.new,
      config: BindConfig(onDispose: (state) => state.close()),
    );
    i.add<TimeSheetBloc>(
      TimeSheetBloc.new,
      config: BindConfig(onDispose: (state) => state.close()),
    );
    i.add<PayslipBloc>(
      PayslipBloc.new,
      config: BindConfig(onDispose: (state) => state.close()),
    );
    i.add<VacationBloc>(
      VacationBloc.new,
      config: BindConfig(onDispose: (state) => state.close()),
    );
  }

  @override
  List<Module> get imports => [
        AppModule(),
      ];

  @override
  void routes(r) {
    // r.child('/', child: (context) => const ExamplePage());
    r.child('/', child: (_) => const ProfessionalHomePage());
    r.child('/hours', child: (_) => const HoursPage());
    r.child('/vacation', child: (_) => const VacationPage());
    r.child('/payslip', child: (_) => const PayslipPage());
  }
}

class ProfessionalEndPoints {
  ///Banco de Horas
  static String hourBase(
          {required String relatedCode, required String plate}) =>
      '/user/hours/$relatedCode/$plate';

  /// Folha de pontos
  static String timeSheet({
    required String relatedCode,
    required String plate,
    required String initDate,
    required String finallyDate,
  }) =>
      '/user/time-sheet/$relatedCode/$plate/$initDate/$finallyDate';

  /// Férias
  static String vacation(
          {required String relatedCode, required String plate}) =>
      '/vacation/historic-vacation/$relatedCode/$plate';

  /// Download do recibo de ferias
  static String downloadVacationReceipt(
          {required String cpf,
          required String relatedCode,
          required String plate,
          required String dtperiodo,
          required String dtpagamento}) =>
      '/vacation/export/$cpf/$relatedCode/$plate/$dtperiodo/$dtpagamento';

  /// download do Imposto de renda
  static String downloadIRProfessional(String cpf) => '/ir/export/$cpf';

  /// download do holerite
  static String downloadPayslip(String cpf, String month, String year) =>
      '/holerite/export/$cpf/$month/$year';

  static checkIsCollaborator(String cpf) => '/user/verify-cpf/$cpf';
}

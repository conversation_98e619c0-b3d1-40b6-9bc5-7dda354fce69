import 'package:siclosbank/app/shared/data/client/client.dart';

import '../../../../shared/data/client/api_response.dart';
import 'professional_endpoints.dart';
import 'professional_datasource.dart';

class ProfessionalDatasourceImpl implements IProfessionalDatasource {
  final IClient _client;

  ProfessionalDatasourceImpl(this._client);

  @override
  Future<ApiResponse> getHourBase(String relatedCode, String plate) async {
    final path = ProfessionalEndPoints.hourBase(
      relatedCode: relatedCode,
      plate: plate,
    );

    final result = await _client.fetch<dynamic>(
      method: 'GET',
      path: path,
    );
    return result;
  }

  @override
  Future<ApiResponse> getTimeSheet(
      {required String relatedCode,
      required String plate,
      required String initDate,
      required String finallyDate}) async {
    final path = ProfessionalEndPoints.timeSheet(
      relatedCode: relatedCode,
      plate: plate,
      initDate: initDate,
      finallyDate: finallyDate,
    );
    final result = await _client.fetch<dynamic>(
      method: 'GET',
      path: path,
    );
    return result;
  }

  @override
  Future<ApiResponse> getVacation(String relatedCode, String plate) async {
    final path =
        ProfessionalEndPoints.vacation(relatedCode: relatedCode, plate: plate);

    final result = await _client.fetch<dynamic>(
      method: 'GET',
      path: path,
    );
    return result;
  }

  @override
  Future<ApiResponse> downloadIRProfessional(String cpf) async {
    final path = ProfessionalEndPoints.downloadIRProfessional(cpf);

    final result = await _client.fetch(
      method: 'GET',
      path: path,
      responseType: 'bytes',
      headers: {
        'Content-Type': 'application/pdf',
        'Accept': 'application/json',
      },
      dataType: DataTypes.none,
    );

    return result;
  }

  @override
  Future<ApiResponse> downloadVacationReceipt(
      {required String cpf,
      required String relatedCode,
      required String plate,
      required String datePerAquis,
      required String datePayment}) async {
    final path = ProfessionalEndPoints.downloadVacationReceipt(
      cpf: cpf,
      relatedCode: relatedCode,
      plate: plate,
      dtperiodo: datePerAquis,
      dtpagamento: datePayment,
    );

    final result = await _client.fetch(
      method: 'GET',
      path: path,
      responseType: 'bytes',
      headers: {
        'Content-Type': 'application/pdf',
        'Accept': 'application/json',
      },
      dataType: DataTypes.none,
    );

    return result;
  }

  @override
  Future<ApiResponse> downloadPayslip(
      {required String cpf, required String mes, required String ano}) async {
    final path = ProfessionalEndPoints.downloadPayslip(cpf, mes, ano);

    final result = await _client.fetch(
      method: 'GET',
      path: path,
      responseType: 'bytes',
      headers: {
        'Content-Type': 'application/pdf',
        'Accept': 'application/json',
      },
      dataType: DataTypes.none,
    );
    if (result.data == null) {
      throw Exception('Nullable object result.data');
    }

    return result;
  }

  @override
  Future<ApiResponse> checkIsCollaborator(String cpf) async {
    final path = ProfessionalEndPoints.checkIsCollaborator(cpf);
    final result = await _client.fetch<dynamic>(
      method: 'GET',
      path: path,
    );
    return result;
  }
}

import '../../../../shared/data/client/api_response.dart';

abstract class IProfessionalDatasource {
  Future<ApiResponse> getTimeSheet({
    required String relatedCode,
    required String plate,
    required String initDate,
    required String finallyDate,
  });

  Future<ApiResponse> getHourBase(String idEmpresa, String plate);

  Future<ApiResponse> getVacation(String idEmpresa, String plate);

  Future<ApiResponse> downloadIRProfessional(String cpf);

  Future<ApiResponse> downloadVacationReceipt({
    required String cpf,
    required String relatedCode,
    required String plate,
    required String datePerAquis,
    required String datePayment,
  });

  Future<ApiResponse> downloadPayslip({
    required String cpf,
    required String mes,
    required String ano,
  });

  Future<ApiResponse> checkIsCollaborator(String cpf);
}

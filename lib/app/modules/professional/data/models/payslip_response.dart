import 'package:equatable/equatable.dart';

/// Holerite model
class PayslipResponse implements Equatable {
  final int month;
  final int year;

  // bool downloading;

  PayslipResponse({
    required this.month,
    required this.year,
    // required this.downloading,
  });

  @override
  List<Object?> get props => [
        month,
        year,
        // downloading,
      ];

  @override
  bool get stringify => true;
}

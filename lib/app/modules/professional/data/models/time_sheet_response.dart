class TimeSheetResponse {
  int coligada;
  String registration;
  String dayOfWeek;
  String? date;
  String? type;
  String? timeLoggedIn;
  String? bh;

  TimeSheetResponse({
    required this.coligada,
    required this.registration,
    required this.dayOfWeek,
    required this.date,
    required this.type,
    required this.timeLoggedIn,
    required this.bh,
  });

  factory TimeSheetResponse.fromMap(Map<String, dynamic> json) {
    return TimeSheetResponse(
      coligada: json['COLIGADA'] ?? 0,
      registration: json['MATRICULA'] ?? '',
      dayOfWeek: json['DIA_DA_SEMANA'] ?? '',
      date: json['DATA'],
      type: json['TIPO'],
      timeLoggedIn: json['TEMPO_LOGADO'],
      bh: json['BH'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'COLIGADA': coligada,
      'MATRICULA': registration,
      'DIA_DA_SEMANA': dayOfWeek,
      'DATA': date,
      'TIPO': type,
      'TEMPO_LOGADO': timeLoggedIn,
      'BH': bh,
    };
  }
}

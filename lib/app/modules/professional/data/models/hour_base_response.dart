import 'package:equatable/equatable.dart';

// ignore: must_be_immutable
class HourBaseResponse implements Equatable {
  // Empresa
  int idCompany;
  // codigo funcionario
  String chapa;
  String mesAExpirar;
  String bhMesAExpirar;
  String mesAnterior;
  String bhMesAnterior;
  String mesAtual;
  String bhMesAtual;
  String saldoExpirado;
  String saldoAtual;

  HourBaseResponse(
      {required this.idCompany,
      required this.chapa,
      required this.mesAExpirar,
      required this.bhMesAExpirar,
      required this.mesAnterior,
      required this.bhMesAnterior,
      required this.mesAtual,
      required this.bhMesAtual,
      required this.saldoExpirado,
      required this.saldoAtual});

  factory HourBaseResponse.fromMap(Map<String, dynamic> map) {
    return HourBaseResponse(
      idCompany: map['COLIGADA'] ?? 0,
      chapa: map['CHAPA'] ?? '',
      mesAExpirar: map['MES_A_EXPIRAR'] ?? '',
      bhMesAExpirar: map['BH_MES_A_EXPIRAR'] ?? '',
      mesAnterior: map['MES_ANTERIOR'] ?? '',
      bhMesAnterior: map['BH_MES_ANTERIOR'] ?? '',
      mesAtual: map['MES_ATUAL'] ?? '',
      bhMesAtual: map['BH_MES_ATUAL'] ?? '',
      saldoExpirado: map['SALDO_EXPIRADO'] ?? '',
      saldoAtual: map['SALDO_ATUAL'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'COLIGADA': idCompany,
      'CHAPA': chapa,
      'MES_A_EXPIRAR': mesAExpirar,
      'BH_MES_A_EXPIRAR': bhMesAExpirar,
      'MES_ANTERIOR': mesAnterior,
      'BH_MES_ANTERIOR': bhMesAnterior,
      'MES_ATUAL': mesAtual,
      'BH_MES_ATUAL': bhMesAtual,
      'SALDO_EXPIRADO': saldoExpirado,
      'SALDO_ATUAL': saldoAtual,
    };
  }

  @override
  List<Object?> get props => [
        idCompany,
        chapa,
        mesAExpirar,
        bhMesAExpirar,
        mesAnterior,
        bhMesAnterior,
        mesAtual,
        bhMesAtual,
        saldoExpirado,
        saldoAtual
      ];

  @override
  bool? get stringify => true;
}

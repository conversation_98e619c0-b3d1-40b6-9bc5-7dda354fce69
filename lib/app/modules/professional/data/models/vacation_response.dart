class VacationResponse {
  int codColigada;
  String chapa;
  DateTime fimPerAquis;
  DateTime dataPagto;
  DateTime dataInicio;
  DateTime dataFim;
  DateTime dataAviso;
  int nroDiasFeriasResponse;
  String situacaoFeriasResponse;
  int parcela;

  bool isDownloading;

  VacationResponse({
    required this.codColigada,
    required this.chapa,
    required this.fimPerAquis,
    required this.dataPagto,
    required this.dataInicio,
    required this.dataFim,
    required this.dataAviso,
    required this.nroDiasFeriasResponse,
    required this.situacaoFeriasResponse,
    required this.parcela,
    this.isDownloading = false,
  });

  factory VacationResponse.fromMap(Map<String, dynamic> data) {
    return VacationResponse(
      codColigada: data['CODCOLIGADA'],
      chapa: data['CHAPA'],
      fimPerAquis: DateTime.parse(data['FIMPERAQUIS']),
      dataPagto: DateTime.parse(data['DATAPAGTO']),
      dataInicio: DateTime.parse(data['DATAINICIO']),
      dataFim: DateTime.parse(data['DATAFIM']),
      dataAviso: DateTime.parse(data['DATAAVISO']),
      nroDiasFeriasResponse: data['NRODIASFERIAS'],
      situacaoFeriasResponse: data['SITUACAOFERIAS'],
      parcela: data['PARCELA'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CODCOLIGADA': codColigada,
      'CHAPA': chapa,
      'FIMPERAQUIS': fimPerAquis.toIso8601String(),
      'DATAPAGTO': dataPagto.toIso8601String(),
      'DATAINICIO': dataInicio.toIso8601String(),
      'DATAFIM': dataFim.toIso8601String(),
      'DATAAVISO': dataAviso.toIso8601String(),
      'NRODIASFERIAS': nroDiasFeriasResponse,
      'SITUACAOFERIAS': situacaoFeriasResponse,
      'PARCELA': parcela,
    };
  }
}

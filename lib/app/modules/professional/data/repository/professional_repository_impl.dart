import 'dart:io';

import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../shared/errors/server_error_handling.dart';
import '../../domain/repository/i_professional_repository.dart';
import '../datasource/professional_datasource.dart';
import '../models/vacation_response.dart';
import '../models/time_sheet_response.dart';
import '../models/hour_base_response.dart';

class ProfessionalRepositoryImpl implements IProfessionalRepository {
  final IProfessionalDatasource _datasource;

  ProfessionalRepositoryImpl(this._datasource);

  @override
  Future<HourBaseResponse> getHourBase(
      {required String relatedCode, required String plate}) async {
    try {
      final response = await _datasource.getHourBase(relatedCode, '6$plate');
      var data = (response.data as List)[0];
      return HourBaseResponse.fromMap(data);
    } catch (erro) {
      return await ServerErrorHandling.handleError(erro);
    }
  }

  @override
  Future<List<TimeSheetResponse>> getTimeSheet(
      {required String relatedCode,
      required String plate,
      required String initDate,
      required String finallyDate}) async {
    try {
      final response = await _datasource.getTimeSheet(
          relatedCode: relatedCode,
          plate: '6$plate',
          initDate: initDate,
          finallyDate: finallyDate);

      return (response.data as List<dynamic>)
          .map((map) => TimeSheetResponse.fromMap(map))
          .toList();
    } catch (erro) {
      return await ServerErrorHandling.handleError(erro);
    }
  }

  @override
  Future<List<VacationResponse>> getVacation(
      {required String relatedCode, required String plate}) async {
    try {
      final response = await _datasource.getVacation(relatedCode, '6$plate');
      return (response.data as List<dynamic>)
          .map((data) => VacationResponse.fromMap(data))
          .toList();
    } catch (erro) {
      return await ServerErrorHandling.handleError(erro);
    }
  }

  @override
  Future<File> downloadIRProfessional(String cpf) async {
    try {
      final response = await _datasource.downloadIRProfessional(cpf);
      return Utils.createPDFWithBytes(
          response.data, 'Informe_Rendimentos_Siclos');
    } catch (erro) {
      return await ServerErrorHandling.handleError(erro);
    }
  }

  @override
  Future<File> downloadVacationReceipt(
      {required String cpf,
      required String relatedCode,
      required String plate,
      required String datePerAquis,
      required String datePayment,
      required int installment}) async {
    try {
      final response = await _datasource.downloadVacationReceipt(
          cpf: cpf,
          relatedCode: relatedCode,
          plate: plate,
          datePerAquis: datePerAquis,
          datePayment: datePayment);

      return Utils.createPDFWithBytes(response.data,
          'ReciboFerias_${datePerAquis}_($installmentª parcela)');
    } catch (erro) {
      return await ServerErrorHandling.handleError(erro);
    }
  }

  @override
  Future<File> downloadPayslip(
      {required String cpf,
      required String month,
      required String year}) async {
    try {
      final response =
          await _datasource.downloadPayslip(cpf: cpf, mes: month, ano: year);
      return Utils.createPDFWithBytes(
          response.data, 'Holerite_$year-${month}_Siclos');
    } catch (erro) {
      return await ServerErrorHandling.handleError(erro);
    }
  }

  @override
  Future<bool> checkIsCollaborator() async {
    try {
      final user = AppSession.getInstance().user;
      assert(user != null);
      final cpf = user!.cpf!;
      final response = await _datasource.checkIsCollaborator(cpf);

      final result = response.data["isColaborator"] ?? false;

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

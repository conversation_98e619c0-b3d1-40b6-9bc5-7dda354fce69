import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/data/datasource/pix_datasource.dart';
import 'package:siclosbank/app/modules/pix/data/repository/pix_repository_impl.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/charge_pix/charge_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/input_new_key/input_new_key_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/my_keys/pix_my_keys_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/register_key/register_key_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_home/onboarding_home_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_key/onboarding_key_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/refund/refund_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/search_pix/search_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/transfer_pix/transfer_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/tariff_pix/tariff_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/area_pix/pix_onboarding_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/charge_pix/dynamic_qrcode/dynamic_qrcode_pageview.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/my_keys/keys_onboarding_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/my_keys/my_keys_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/area_pix/area_pix_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/new_kew/register_key_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/my_limits/my_limits_pix_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/refund/refund_pageview.dart';
import 'package:siclosbank/app/app_module.dart';

import '../pin/pin_module.dart';
import 'presenter/blocs/my_limits_pix/my_limits_pix_bloc.dart';
import 'presenter/pages/charge_pix/static_qrcode/charge_pix_page.dart';
import 'presenter/pages/new_kew/input_new_key/input_new_key_pageview.dart';
import 'presenter/pages/questions/questions_page.dart';
import 'presenter/pages/pix_with_key/pix_with_key_page.dart';
import 'presenter/pages/pix_transfer/pix_transfer_page.dart';
import 'presenter/pages/pix_confirmation/pix_confirmation_page.dart';

class PixModule extends Module {
  @override
  List<Module> get imports => [AppModule(), PinModule()];
  @override
  void binds(i) {
    i.addLazySingleton<IPixDatasource>(PixDatasourceImpl.new);
    i.addLazySingleton<IPixRepository>(PixRepositoryImpl.new);

    i.add(MyKeysBloc.new);
    i.add(RegisterKeyBloc.new);
    i.add(InputNewKeyBloc.new);
    i.add(RefundBloc.new);
    i.add(MyLimitsPixBloc.new);
    i.add(OnboardingHomeBloc.new);
    i.add(OnboardingKeyBloc.new);
    i.add(ChargePixBloc.new);
    i.add(SearchPixBloc.new);
    i.add(TransferPixBloc.new);
    i.add(TariffPixBloc.new);
    super.binds(i);
  }

  @override
  void routes(RouteManager r) {
    r.child('/', child: (_) => const AreaPixPage());
    r.child('/onboarding-home', child: (_) => const PixOnboardingPage());
    r.child('/my-keys', child: (_) => const MyKeysPage());
    r.child('/onboarding-my-keys', child: (_) => const KeysOnboardingPage());
    r.child('/questions', child: (_) => const QuestionsPage());
    r.child('/refund', child: (_) => const RefundPageview());
    r.child('/my-limits-pix', child: (_) => const MyLimitsPixPage());
    r.child('/new-key', child: (_) => const RegisterKeyPage());
    r.child('/input-new-key', child: (_) => const InputNewKeyPageView());
    r.child('/charge-pix', child: (_) => const ChargePixPage());
    r.child('/dynamic-qrcode', child: (_) => const DynamicQrcodePageview());
    r.child('/dynamic-qrcode', child: (_) => const DynamicQrcodePageview());
    r.child('/pix-with-key', child: (_) => const PixWithKeyPage());
    r.child('/pix-transfer', child: (_) => const PixTransferPage());
    r.child('/pix-confirmation', child: (_) => const PixConfirmationPage());
  }
}

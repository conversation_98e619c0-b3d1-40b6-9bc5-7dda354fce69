import 'dart:async';

import 'package:siclosbank/app/modules/pix/data/datasource/endpoints.dart';
import 'package:siclosbank/app/modules/pix/models/refund_pix_model.dart';
import 'package:siclosbank/app/modules/pix/models/transfer_pix_model.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

abstract class IPixDatasource {
  Future<ApiResponse> getKeys(String account);

  Future<ApiResponse> toRefund(RefundPixModel refundPixModel);

  Future<ApiResponse> getMyLimits();

  Future<ApiResponse> setViewOnboardingHomePix();

  Future<ApiResponse> setViewOnboardingKeysPix();

  Future<ApiResponse> deleteKey(String key, String account);

  Future<ApiResponse> createKey({
    required String keyType,
    required String account,
    String? key,
  });

  Future<ApiResponse> sendEmailVerificationCodeKey(String email);

  Future<ApiResponse> sendPhoneVerificationCodeKey(String phone);

  Future<ApiResponse> checkCodePhoneKey(String code);

  Future<ApiResponse> checkCodeEmailKey(String code);
  Future<ApiResponse> requestPortabilityKey({
    required String keyType,
    required String key,
    required String account,
    required String claimType,
  });

  Future<ApiResponse> consultRequestedClaims();
  Future<ApiResponse> cancelRequestClaim({required String claimId});
  Future<ApiResponse> createStaticQrCode(String key);
  Future<ApiResponse> createDynamicQrCode({
    required String key,
    required String amount,
    String? description,
  });

  Future<ApiResponse> searchPix(String key);

  Future<ApiResponse> transferPix(TransferPixModel transferPixModel);

  Future<ApiResponse> getTransactionSentPix(String idTransaction);

  Future<ApiResponse> getTariffService();
}

class PixDatasourceImpl implements IPixDatasource {
  final IClient _client;

  PixDatasourceImpl(this._client);

  @override
  Future<ApiResponse> getKeys(String account) async {
    final path = PixEndpoints.myKeys(account);

    final result = await _client.fetch(method: 'GET', path: path);
    return result;
  }

  @override
  Future<ApiResponse> toRefund(RefundPixModel refundPixModel) async {
    const path = PixEndpoints.refund;

    final result = await _client.fetch(
      method: 'POST',
      path: path,
      data: refundPixModel.toMap(),
    );
    return result;
  }

  @override
  Future<ApiResponse> getMyLimits() async {
    const path = PixEndpoints.myLimits;

    final result = await _client.fetch(method: 'GET', path: path);
    return result;
  }

  @override
  Future<ApiResponse> setViewOnboardingHomePix() async {
    const path = PixEndpoints.setViewOnboardingHomePix;

    final result = await _client.fetch(method: 'PUT', path: path);
    return result;
  }

  @override
  Future<ApiResponse> setViewOnboardingKeysPix() async {
    const path = PixEndpoints.setViewOnboardingKeysPix;

    final result = await _client.fetch(method: 'PUT', path: path);
    return result;
  }

  @override
  Future<ApiResponse> deleteKey(String key, String account) async {
    final path = PixEndpoints.deleteKey(key);

    final result = await _client.fetch(
      method: 'POST',
      path: path,
      data: {'account': account},
    );
    return result;
  }

  @override
  Future<ApiResponse> createKey({
    required String keyType,
    required String account,
    String? key,
  }) async {
    final path = PixEndpoints.createKey();

    Map<String, dynamic> data = {'keyType': keyType, 'account': account};

    if (key != null) {
      data['key'] = key;
    }

    final reuslt = await _client.fetch(method: 'POST', path: path, data: data);
    return reuslt;
  }

  @override
  Future<ApiResponse> checkCodeEmailKey(String code) async {
    Map<String, dynamic> data = {'code': code};

    const path = PixEndpoints.checkVerificationEmailKey;

    final result = await _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> checkCodePhoneKey(String code) async {
    Map<String, dynamic> data = {'code': code};

    const path = PixEndpoints.checkVerificationPhoneKey;

    final result = await _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> sendEmailVerificationCodeKey(String email) async {
    Map<String, dynamic> data = {'email': email};

    const path = PixEndpoints.sendEmailKeyVerificationCode;

    final result = await _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> sendPhoneVerificationCodeKey(String phone) async {
    Map<String, dynamic> data = {'phone': phone};

    const path = PixEndpoints.sendPhoneKeyVerificationCode;

    final result = await _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> requestPortabilityKey({
    required String keyType,
    required String key,
    required String account,
    required String claimType,
  }) async {
    Map<String, dynamic> data = {
      "key": key,
      "keyType": keyType,
      "account": account,
      "claimType": claimType,
    };

    const path = PixEndpoints.requestPortabilityKey;

    final result = await _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> cancelRequestClaim({required String claimId}) async {
    Map<String, dynamic> data = {
      "id": claimId, // "d63146ff-d7f5-4e6f-a821-0286e04c133e",
      "reason": "USER_REQUESTED",
    };

    const path = PixEndpoints.cancelRequestClaim;
    final result = await _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> consultRequestedClaims() async {
    const path = PixEndpoints.consultRequestedClaims;
    final result = await _client.fetch(method: 'GET', path: path);
    return result;
  }

  @override
  Future<ApiResponse> createDynamicQrCode({
    required String key,
    required String amount,
    String? description,
  }) {
    Map<String, dynamic> data = {'key': key, 'amount': amount};

    if (description != null) {
      data['payerQuestion'] = description;
    }
    const path = PixEndpoints.createDynamicQrCode;

    final result = _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> createStaticQrCode(String key) {
    Map<String, dynamic> data = {'key': key};

    const path = PixEndpoints.createStaticQrCode;

    final result = _client.fetch(method: 'POST', path: path, data: data);
    return result;
  }

  @override
  Future<ApiResponse> searchPix(String key) async {
    try {
      final path = PixEndpoints.searchPix(key);

      final result = await _client.fetch(method: 'GET', path: path);

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ApiResponse> transferPix(TransferPixModel transferPixModel) async {
    final path = PixEndpoints.transferPix;
    final requestData = transferPixModel.toMap();

    final userId = transferPixModel.clientRequestId;
    final fullPath = userId != null ? '$path?user_id=$userId' : path;

    final result = await _client.fetch(
      method: 'POST',
      path: fullPath,
      data: requestData,
    );
    return result;
  }

  @override
  Future<ApiResponse> getTransactionSentPix(String idTransaction) async {
    final path = PixEndpoints.getTransactionSentPix(idTransaction);

    try {
      final result = await _client.fetch(method: 'GET', path: path);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ApiResponse> getTariffService() {
    const path = PixEndpoints.getTariffService;
    final result = _client.fetch(method: 'GET', path: path);
    return result;
  }
}

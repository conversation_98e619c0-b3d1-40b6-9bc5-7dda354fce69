import 'package:siclosbank/app/modules/pix/models/tariff_service_response.dart';
import 'package:siclosbank/app/modules/pix/models/transaction_sent_pix_response.dart';
import 'package:siclosbank/app/shared/data/models/pix/cancel_claim_model.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';
import 'package:siclosbank/app/modules/pix/models/limit_pix_response.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/modules/pix/models/pix_search_response.dart';
import 'package:siclosbank/app/modules/pix/models/refund_pix_model.dart';
import 'package:siclosbank/app/modules/pix/models/refund_pix_response.dart';
import 'package:siclosbank/app/modules/pix/models/transfer_pix_model.dart';

abstract class IPixRepository {
  Future<RefundPixResponse> toRefund(RefundPixModel refundPixModel);
  Future<LimitPixResponse> getMyLimits();
  Future<List<KeyPixModel>> getKeys(String account);

  Future setViewOnboardingHomePix();
  Future setViewOnboardingKeysPix();

  Future<bool> deleteKey(String key, String account);

  Future<bool> createKey({
    required String keyType,
    required String account,
    String? key,
  });

  Future sendCodeEmailKey(String email);
  Future sendCodePhoneKey(String phone);

  Future checkCodeEmail(String code);
  Future checkCodePhone(String code);
  Future<ClaimRequestedModel> requestPortabilityKey({
    required String keyType,
    required String key,
    required String account,
    required String claimType,
  });

  Future<List<ClaimRequestedModel>> consultRequestedClaims();
  Future<CancelClaimModel> cancelRequestClaim({required String claimId});
  Future<String> createStaticQrCode(String key);
  Future<String> createDynamicQrCode({
    required String key,
    required String amount,
    String? description,
  });

  Future<PixSearchResponse> searchPix(String key);

  Future<TransferPixResponse> transferPix(TransferPixModel transferPixModel);

  Future<TransactionSentPixResponse> getTransactionSentPix(
    String idTransaction,
  );

  Future<TariffServiceResponse> getTariffService();
}

import 'package:siclosbank/app/modules/pix/models/tariff_service_response.dart';
import 'package:siclosbank/app/modules/pix/models/transaction_sent_pix_response.dart';
import 'package:siclosbank/app/shared/data/models/pix/cancel_claim_model.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/modules/pix/models/pix_search_response.dart';
import 'package:siclosbank/app/modules/pix/models/transfer_pix_model.dart';
import 'package:siclosbank/app/app_controller.dart';

import '../datasource/pix_datasource.dart';
import '../../models/limit_pix_response.dart';
import '../../models/refund_pix_model.dart';
import '../../models/refund_pix_response.dart';
import '../../../../shared/errors/server_error_handling.dart';
import 'i_pix_repositoy.dart';

class PixRepositoryImpl implements IPixRepository {
  final IPixDatasource _datasource;

  PixRepositoryImpl(this._datasource);

  @override
  Future<RefundPixResponse> toRefund(RefundPixModel refundPixModel) async {
    try {
      final result = await _datasource.toRefund(refundPixModel);

      return RefundPixResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<LimitPixResponse> getMyLimits() async {
    try {
      final result = await _datasource.getMyLimits();
      return LimitPixResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<KeyPixModel>> getKeys(String account) async {
    try {
      final result = await _datasource.getKeys(account);
      if (result.data is List) {
        return (result.data as List)
            .map((e) => KeyPixModel.fromMap(e))
            .toList();
      } else {
        return [];
      }
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future setViewOnboardingHomePix() async {
    try {
      await _datasource.setViewOnboardingHomePix();
      return;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future setViewOnboardingKeysPix() async {
    try {
      await _datasource.setViewOnboardingKeysPix();
      return;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> createKey({
    required String keyType,
    required String account,
    String? key,
  }) async {
    try {
      final result = await _datasource.createKey(
        keyType: keyType,
        account: account,
        key: key,
      );
      return result.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> deleteKey(String key, String account) async {
    try {
      final result = await _datasource.deleteKey(key, account);
      return result.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future sendCodeEmailKey(String email) async {
    try {
      final result = await _datasource.sendEmailVerificationCodeKey(email);
      return result.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future sendCodePhoneKey(String phone) async {
    try {
      final result = await _datasource.sendPhoneVerificationCodeKey(phone);
      return result.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future checkCodeEmail(String code) async {
    try {
      final result = await _datasource.checkCodeEmailKey(code);
      return result.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future checkCodePhone(String code) async {
    try {
      final result = await _datasource.checkCodePhoneKey(code);
      return result.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<ClaimRequestedModel> requestPortabilityKey({
    required String keyType,
    required String key,
    required String account,
    required String claimType,
  }) async {
    try {
      final result = await _datasource.requestPortabilityKey(
        key: key,
        keyType: keyType,
        account: account,
        claimType: claimType,
      );
      return ClaimRequestedModel.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<CancelClaimModel> cancelRequestClaim({required String claimId}) async {
    try {
      final result = await _datasource.cancelRequestClaim(claimId: claimId);
      return CancelClaimModel.fromMap(result.data['body']);
    } on Exception catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<ClaimRequestedModel>> consultRequestedClaims() async {
    try {
      final result = await _datasource.consultRequestedClaims();

      final claims = result.data as List;
      return claims.map((e) => ClaimRequestedModel.fromMap(e)).toList();
    } on Exception catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> createDynamicQrCode({
    required String key,
    required String amount,
    String? description,
  }) async {
    try {
      final result = await _datasource.createDynamicQrCode(
        key: key,
        amount: amount,
        description: description,
      );
      return result.data['body']['body']['dynamicBRCodeData']['emvqrcps']
          as String;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> createStaticQrCode(String key) async {
    try {
      final result = await _datasource.createStaticQrCode(key);
      return result.data['emv_qr_code'] as String;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<PixSearchResponse> searchPix(String key) async {
    try {
      final result = await _datasource.searchPix(key);

      if (result.data == null) {
        throw Exception('Nenhum dado retornado da API');
      }

      return PixSearchResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<TransferPixResponse> transferPix(
    TransferPixModel transferPixModel,
  ) async {
    try {
      final user = AppSession.getInstance().user;
      final bankAccount = AppSession.getInstance().bankAccount;

      if (user != null && bankAccount != null) {
        final updatedModel = TransferPixModel(
          amount: transferPixModel.amount,
          debitParty: DebitPartyModel(account: bankAccount.accountNumber!),
          creditParty: transferPixModel.creditParty,
          remittanceInformation: transferPixModel.remittanceInformation,
          clientRequestId: user.id!,
        );

        final result = await _datasource.transferPix(updatedModel);
        return TransferPixResponse.fromMap(result.data);
      }

      throw Exception("User not found");
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<TransactionSentPixResponse> getTransactionSentPix(
    String idTransaction,
  ) async {
    try {
      print(
        '🔍 PIX Repository: Iniciando getTransactionSentPix para ID: $idTransaction',
      );

      final result = await _datasource.getTransactionSentPix(idTransaction);

      print('📊 PIX Repository: Status Code: ${result.statusCode}');
      print('📊 PIX Repository: Response Data: ${result.data}');

      if (result.statusCode == null ||
          result.statusCode! < 200 ||
          result.statusCode! >= 300) {
        throw Exception('Erro HTTP: Status ${result.statusCode}');
      }

      if (result.data == null) {
        throw Exception('Dados da resposta são nulos');
      }

      final response = TransactionSentPixResponse.fromMap(result.data);
      print('✅ PIX Repository: Modelo criado com sucesso');
      return response;
    } catch (e) {
      print('❌ PIX Repository Error: $e');
      print('❌ PIX Repository Error Type: ${e.runtimeType}');
      if (e is Error) {
        print('❌ PIX Repository Stack Trace: ${e.stackTrace}');
      }
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<TariffServiceResponse> getTariffService() async {
    try {
      final result = await _datasource.getTariffService();
      final services = result.data as List;

      return TariffServiceResponse.fromList(services);
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }
}

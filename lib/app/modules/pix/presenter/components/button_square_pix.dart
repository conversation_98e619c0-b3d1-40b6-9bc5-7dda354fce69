import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class ButtonSquarePix extends StatelessWidget {
  const ButtonSquarePix({
    super.key,
    required this.label,
    required this.icon,
    required this.onTap,
    this.enabled = true,
    this.isLoading = false,
  });

  final String label;
  final Widget icon;
  final VoidCallback onTap;
  final bool enabled;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: enabled && !isLoading ? onTap : null,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        height: 120,
        padding: const EdgeInsets.only(left: 16, right: 16, top: 0, bottom: 8),
        decoration: ShapeDecoration(
          color: enabled && !isLoading ? Colors.white : Colors.grey[100],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          shadows: const [
            BoxShadow(
              color: Color.fromARGB(27, 0, 0, 0),
              blurRadius: 10,
              offset: Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Stack(
          // fit: StackFit.expand,
          children: [
            Positioned.fill(
              child: isLoading
                  ? Center(child: Utils.circularProgressButton(size: 20))
                  : Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      spacing: 3,
                      children: [
                        Expanded(
                          child: Align(
                            alignment: Alignment.bottomCenter,
                            child: icon,
                          ),
                        ),
                        Container(
                          height: 38,
                          alignment: Alignment.topCenter,
                          child: Text(
                            label,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:shimmer/shimmer.dart';
// import 'package:siclosbank/app/shared/utils/utils.dart';

// import '../../../../shared/themes/styles/colors_app.dart';

// class ButtonSquarePix extends StatelessWidget {
//   const ButtonSquarePix({
//     super.key,
//     required this.label,
//     required this.icon,
//     required this.onTap,
//     // this.enabled = true,
//     this.isLoading = false,
//   });

//   final String label;
//   final Widget icon;
//   final VoidCallback onTap;
//   // final bool enabled;
//   final bool isLoading;

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: !isLoading ? onTap : null,
//       borderRadius: BorderRadius.circular(10),
//       child: Container(
//         height: 120,
//         decoration: ShapeDecoration(
//           color: !isLoading ? Colors.white : Colors.grey[100],
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(10),
//           ),
//           shadows: const [
//             BoxShadow(
//               color: Color.fromARGB(27, 0, 0, 0),
//               blurRadius: 10,
//               offset: Offset(0, 2),
//               spreadRadius: 0,
//             ),
//           ],
//         ),
//         child: Stack(
//           // fit: StackFit.expand,
//           children: [
//             if (isLoading)
//               Positioned.fill(
//                 child: Shimmer.fromColors(
//                   baseColor: Colors.grey[300]!,
//                   highlightColor: Colors.grey[100]!,
//                   child: Container(
//                     width: 150,
//                     height: 32,
//                     decoration: BoxDecoration(
//                       color: ColorsApp.cinza[200],
//                       borderRadius: BorderRadius.circular(5),
//                     ),
//                   ),
//                 ),
//               ),
//             Positioned.fill(
//               child: Padding(
//                 padding: const EdgeInsets.only(
//                   left: 16,
//                   right: 16,
//                   top: 0,
//                   bottom: 8,
//                 ),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.max,
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.stretch,
//                   spacing: 3,
//                   children: [
//                     Expanded(
//                       child: Align(
//                         alignment: Alignment.bottomCenter,
//                         child: icon,
//                       ),
//                     ),
//                     Container(
//                       height: 38,
//                       alignment: Alignment.topCenter,
//                       child: Text(
//                         label,
//                         style: TextStyle(
//                           fontSize: 15,
//                           fontWeight: FontWeight.w400,
//                           color: isLoading ? ColorsApp.disableTextColor : null,
//                         ),
//                         textAlign: TextAlign.center,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

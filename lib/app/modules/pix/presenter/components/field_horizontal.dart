import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../shared/themes/styles/colors_app.dart';
import '../../../../shared/utils/utils.dart';

class FieldHorizontal extends StatelessWidget {
  final String? title;
  final String? info;
  final bool showCopy;
  final bool compact;

  const FieldHorizontal({
    super.key,
    this.title,
    this.info,
    this.showCopy = false,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      padding: EdgeInsetsResponsive.only(
        top: compact ? 2 : 4,
        bottom: compact ? 4 : 8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: 18,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            width: size.width * 0.34,
            // constraints: BoxConstraints(maxWidth: size.width * 0.35),
            child: TextResponsive(
              title ?? '',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: ColorsApp.cinza[700],
                    fontSize: compact ? 12 : null,
                  ),
            ),
          ),
          Expanded(
            child: TextResponsive(
              info ?? '',
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    // color: ColorsApp.cinza[900],
                    fontWeight: FontWeight.w600,
                    fontSize: compact ? 12 : null,
                  ),
            ),
          ),
          Visibility(
            visible: showCopy,
            child: InkWell(
              onTap: () {
                Utils.copyText(
                    context: context,
                    text: info ?? '',
                    mensagemSucesso: I18n.of(context)!.texto_copiado);
              },
              child: ContainerResponsive(
                margin: EdgeInsetsResponsive.only(left: 16),
                child: IconsApp.icCopyData(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

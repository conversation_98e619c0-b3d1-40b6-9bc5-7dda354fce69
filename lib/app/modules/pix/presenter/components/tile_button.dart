import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../shared/themes/styles/icons_app.dart';

class TileButton extends StatelessWidget {
  const TileButton({
    super.key,
    required this.label,
    required this.icon,
    this.onTap,
    this.showPadding = true,
    this.isLoading = false,
  });

  final String label;
  final Widget icon;
  final VoidCallback? onTap;
  final bool showPadding;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme.displaySmall;
    return Container(
      margin: showPadding ? const EdgeInsets.symmetric(horizontal: 16) : null,
      height: 70,
      decoration: const BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: isLoading ? Colors.grey[200] : Colors.white,
        borderRadius: BorderRadius.circular(10),
        child: InkWell(
          onTap: isLoading ? null : onTap,
          borderRadius: BorderRadius.circular(10),
          child: Row(
            spacing: 8,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 12),
                child: icon,
              ),
              Text(
                label,
                style: textTheme!.copyWith(
                    fontSize: 16, color: isLoading ? Colors.grey[800] : null),
                overflow: TextOverflow.fade,
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: isLoading
                      ? Utils.circularProgressButton()
                      : IconsApp.icArrowRight(
                          height: 14,
                          color: Colors.black,
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../../shared/themes/styles/colors_app.dart';
import '../../../../shared/utils/fields_utils.dart';
import '../../models/my_keys_pix_model.dart';

class ListTileKey extends StatelessWidget {
  const ListTileKey({
    super.key,
    required this.keyPix,
    this.trailing,
    this.showBorder = false,
  });
  final KeyPixModel keyPix;
  final Widget? trailing;
  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        border: showBorder
            ? Border.all(color: ColorsApp.cinzaDetalhesContatoConta, width: 0.3)
            : null,
        boxShadow: const [
          BoxShadow(
            blurRadius: 10,
            spreadRadius: -7,
            color: Color.fromARGB(170, 0, 0, 0),
          )
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.only(
          left: 16,
          right: 0,
          top: 4,
          bottom: 4,
        ),
        tileColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        style: ListTileStyle.drawer,
        title: Text(
          keyPix.keyType.description.toUpperCase(),
          style: textTheme.bodyLarge?.copyWith(color: ColorsApp.cinzaSolo),
        ),
        subtitle: Text(
          keyPix.keyType.isCPF ? FieldsUtils.obterCpf(keyPix.key) : keyPix.key,
          style: textTheme.bodyMedium,
        ),
        trailing: trailing,
      ),
    );
  }
}

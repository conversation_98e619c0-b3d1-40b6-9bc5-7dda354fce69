import 'package:flutter/material.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/navigation/navigator_app.dart';
import '../../../../shared/presenter/view/components/others/sheet_positive_actions.dart';
import '../../../../shared/themes/styles/colors_app.dart';
import '../../models/my_keys_pix_model.dart';

abstract class ModalSelectKeyPix {
  static Future show(context, {required List<KeyPixModel> listKeys}) {
    return SheetPositiveActions.showSuggestionsSheet(
      context,
      titulo: I18n().alterar_email,
      conteudo: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: listKeys
            .map(
              (key) => _button(
                context,
                key: key,
                onPressed: () {
                  pop(key);
                },
              ),
            )
            .toList(),
      ),
    );
  }

  static Widget _button(
    context, {
    required KeyPixModel key,
    VoidCallback? onPressed,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      // height: 60,
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: ColorsApp.cinzaSolo, width: 1),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(10),
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  key.keyType.description,
                  style: textTheme.bodyMedium!.copyWith(),
                  overflow: TextOverflow.fade,
                ),
                Text(
                  key.key,
                  style: textTheme.bodyMedium!.copyWith(color: Colors.grey),
                  overflow: TextOverflow.fade,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

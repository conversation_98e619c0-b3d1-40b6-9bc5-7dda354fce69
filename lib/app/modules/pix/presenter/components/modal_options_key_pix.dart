import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/pix/models/enums/key_types_enum.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../shared/presenter/view/components/others/sheet_positive_actions.dart';
import '../../../../shared/utils/utils.dart';

abstract class ModalOptionsKeyPix {
  static Future show(
    context, {
    required String key,
    required KeyType keyType,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return SheetPositiveActions.showSuggestionsSheet(
      context,
      titulo: keyType.description.toUpperCase(),
      conteudo: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Text(
            keyType.isCPF ? FieldsUtils.obterCpf(key) : key,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 28),
          ButtonApp(
            width: MediaQuery.of(context).size.width,
            height: 50,
            border: 1,
            borderColor: ColorsApp.cinzaSolo,
            buttonColor: Colors.transparent,
            text: I18n.of(context)!.share_key,
            onPress: () {
              Navigator.pop(context);
              Utils.copyText(
                  context: context,
                  text: key,
                  mensagemSucesso: 'Chave copiada para área de transferência');
            },
            contentAlign: Alignment.centerLeft,
            textStyle: textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ButtonApp(
            width: MediaQuery.of(context).size.width,
            height: 50,
            border: 1,
            borderColor: ColorsApp.colorInfoAlert,
            buttonColor: Colors.transparent,
            text: I18n.of(context)!.delete_key,
            onPress: () async {
              Navigator.pop(context, true);
            },
            contentAlign: Alignment.centerLeft,
            textStyle:
                textTheme.bodyMedium!.copyWith(color: ColorsApp.colorInfoAlert),
          ),
          const SizedBox(height: 38)
        ],
      ),
    );
  }
}

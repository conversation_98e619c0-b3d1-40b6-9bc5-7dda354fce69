import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/pix/models/enums/key_types_enum.dart';
import 'package:siclosbank/app/shared/config/flavor.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/navigation/navigator_app.dart';
import '../../../../shared/presenter/view/components/others/sheet_positive_actions.dart';
import '../../../../shared/themes/styles/icons_app.dart';

abstract class ModalNewKeyPix {
  static Future show(
    context, {
    required List<KeyType> keyTypesActives,
  }) {
    return SheetPositiveActions.showSuggestionsSheet(
      context,
      titulo: I18n.of(context)!.new_key,
      conteudo: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          const Text(
            'Selecione o tipo de chave',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 26),
          _button(
            context,
            icon: IconsApp.icKeyBlack(
              height: 22,
            ),
            label: 'Chave aleatória',
            keyType: KeyType.EVP,
            enabled: true,
            onPressed: () => pop({
              'keyType': KeyType.EVP,
            }),
          ),
          if (isHomologOrDev)
            _button(
              context,
              icon: IconsApp.idCard(),
              label: 'CPF',
              keyType: KeyType.CPF,
              enabled: !keyTypesActives.contains(KeyType.CPF),
              onPressed: () => pop({
                'keyType': KeyType.CPF,
              }),
            ),
          if (isHomologOrDev)
            _button(
              context,
              icon: const Icon(Icons.phone_iphone),
              label: 'Celular',
              keyType: KeyType.PHONE,
              enabled: true,
              // !keyTypesActives.contains(KeyTypesEnum.PHONE),
              onPressed: () => pop({
                'keyType': KeyType.PHONE,
              }),
            ),
          if (isHomologOrDev)
            _button(
              context,
              icon: const Icon(Icons.mail_outline_rounded),
              label: 'E-mail',
              keyType: KeyType.EMAIL,
              enabled: true, // !keyTypesActives.contains(KeyTypesEnum.EMAIL),
              onPressed: () => pop({
                'keyType': KeyType.EMAIL,
              }),
            ),
          const SizedBox(height: 26)
        ],
      ),
    );
  }

  static Widget _button(
    context, {
    required Widget icon,
    required String label,
    required KeyType keyType,
    required bool enabled,
    VoidCallback? onPressed,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return Visibility(
      visible: enabled,
      child: Container(
        height: 60,
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: enabled ? ColorsApp.cinzaSolo : ColorsApp.disableTextColor,
            width: 1,
          ),
          color: enabled ? Colors.white : ColorsApp.bgDesativado,
          boxShadow: enabled
              ? const [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 10,
                    offset: Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Material(
          color: enabled ? Colors.white : ColorsApp.bgDesativado,
          borderRadius: BorderRadius.circular(8),
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(10),
            child: Row(
              children: [
                const SizedBox(width: 12),
                icon,
                const SizedBox(width: 10),
                Text(
                  label,
                  style: textTheme.bodyMedium!.copyWith(
                      color: enabled ? null : ColorsApp.disableTextColor),
                  overflow: TextOverflow.fade,
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Icon(
                      Icons.keyboard_arrow_right,
                      color: enabled ? null : ColorsApp.disableTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/my_keys/pix_my_keys_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/button_square_pix.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../shared/navigation/named_routes.dart';

class GridButtonsPixHome extends StatelessWidget {
  const GridButtonsPixHome({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<MyKeysBloc>(context),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          spacing: 16,
          children: [
            Row(
              spacing: 12,
              children: [
                Expanded(
                  child: ButtonSquarePix(
                    label: I18n.of(context)!.pix_with_key,
                    icon: IconsApp.icsSendPix(height: 55),
                    onTap: () {
                      push(Routes.pixWithKey);
                    },
                  ),
                ),
                // Expanded(
                //   child: ButtonSquarePix(
                //     label: I18n.of(context)!.pix_copy_paste,
                //     icon: Padding(
                //       padding: const EdgeInsets.all(8.0),
                //       child: IconsApp.icCopyPastPix(height: 35),
                //     ),
                //     onTap: () {},
                //   ),
                // ),
                // Expanded(
                //   child: ButtonSquarePix(
                //     label: I18n.of(context)!.read_qr_code,
                //     icon: IconsApp.icQrCode(height: 52, color: ColorsApp.verde),
                //     onTap: () {},
                //   ),
                // ),
              ],
            ),
            Row(
              spacing: 12,
              children: [
                Expanded(
                  child: BlocBuilder<MyKeysBloc, MyKeysState>(
                    builder: (context, state) {
                      return ButtonSquarePix(
                        label: I18n.of(context)!.charge_with_pix,
                        icon: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: IconsApp.icReceivePix(
                            height: 38,
                            // color: state is MyKeysLoading
                            //     ? const Color.fromARGB(87, 139, 139, 139)
                            //     : null,
                          ),
                        ),
                        // enabled: false,
                        isLoading: state is MyKeysLoading,
                        onTap: () {
                          final bloc = BlocProvider.of<MyKeysBloc>(context);

                          if (bloc.myKeys == null) {
                            SnackBarApp.showSnack(
                              context: context,
                              message:
                                  'Ocorreu um erro ao buscar chaves. Tente novamente em alguns instantes.',
                              success: false,
                            );
                          } else {
                            if (bloc.myKeys!.isEmpty) {
                              SheetAlertConfirm.showSheet(
                                context,
                                title:
                                    "Você ainda não tem uma chave Pix. \n Quer cadastrar?",
                                message:
                                    "Para agora, vamos cadastrar uma chave Pix aleatória, futuramente você pode criar outros tipos de chave. Ela é obrigatória para receber a cobrança via Pix.",
                                textPositive: I18n.of(context)!.cadastrar,
                                clickPositive: () {
                                  bloc.add(RegisterNewKeyEvent());
                                  Navigator.pop(context);
                                },
                                showBtnNegative: false,
                              );
                            } else if (bloc.myKeys!.isNotEmpty) {
                              push(Routes.chargePix, args: bloc.myKeys!);
                            }
                          }
                        },
                      );
                    },
                  ),
                ),
                // Expanded(
                //   child: ButtonSquarePix(
                //     label: I18n.of(context)!.depositar,
                //     icon: Padding(
                //       padding: const EdgeInsets.all(10.0),
                //       child: IconsApp.icDepositPix(height: 38),
                //     ),
                //     onTap: () {},
                //   ),
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

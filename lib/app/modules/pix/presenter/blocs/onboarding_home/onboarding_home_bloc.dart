import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_home/onboarding_home_events.dart';

import '../../../../../app_controller.dart';
import '../../../data/repository/i_pix_repositoy.dart';

part 'onboarding_home_state.dart';

class OnboardingHomeBloc
    extends Bloc<OnboardingHomeEvents, OnboardingHomeState> {
  final IPixRepository _repository;
  OnboardingHomeBloc(this._repository) : super(OnboardingHomeInitial()) {
    on<OnboardingHomeEvents>((event, emit) async {
      if (event is OnboardingHomeSaveView) {
        emit(OnboardingHomeLoading());
        await _repository.setViewOnboardingHomePix().then((_) {
          debugPrint('OnboardingHome: visualizacao salva');
          AppSession.getInstance().user!.viewOnboardingHomePix = true;
          emit(OnboardingHomeSuccess());
        }, onError: (_) {
          debugPrint('OnboardingHome: erro ao salvar view');
          emit(OnboardingHomeError());
        });
      }
    });
  }
}

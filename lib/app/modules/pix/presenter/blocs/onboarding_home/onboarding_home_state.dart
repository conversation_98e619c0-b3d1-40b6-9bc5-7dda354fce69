part of 'onboarding_home_bloc.dart';

sealed class OnboardingHomeState extends Equatable {
  const OnboardingHomeState();

  @override
  List<Object> get props => [];
}

final class OnboardingHomeInitial extends OnboardingHomeState {}

final class OnboardingHomeLoading extends OnboardingHomeState {}

final class OnboardingHomeSuccess extends OnboardingHomeState {}

final class OnboardingHomeError extends OnboardingHomeState {}

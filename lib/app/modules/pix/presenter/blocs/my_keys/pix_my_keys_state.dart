part of 'pix_my_keys_bloc.dart';

sealed class <PERSON><PERSON><PERSON>sState extends Equatable {
  const MyKeysState();

  @override
  List<Object> get props => [];
}

final class MyKeysInitial extends MyKeysState {}

final class MyKeysLoading extends MyKeysState {}

final class MyKeysError extends MyKeysState {
  final ErrorResponse error;
  const MyKeysError(this.error);
}

final class DeleteKeySuccess extends MyKeysState {}

final class MyKeysSuccess extends MyKeysState {
  final List<KeyPixModel> myKeysList;

  const MyKeysSuccess(this.myKeysList);

  @override
  List<Object> get props => [myKeysList];
}

// final class ClaimPixLoading extends MyKeysState {}

final class CancelClaimPixSucess extends MyKeysState {
  final CancelClaimModel claim;

  const CancelClaimPixSucess(this.claim);
}

final class ConfirmClaimPixSuccess extends MyKeysState {
  final CancelClaimModel claim;

  const ConfirmClaimPixSuc<PERSON>(this.claim);
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/models/enums/key_types_enum.dart';
import 'package:siclosbank/app/shared/data/models/pix/cancel_claim_model.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/errors.dart';

import '../../../models/my_keys_pix_model.dart';

part 'pix_my_keys_event.dart';
part 'pix_my_keys_state.dart';

class MyKeysBloc extends Bloc<MyKeysEvent, MyKeysState> {
  final IPixRepository _repository;

  List<KeyPixModel>? myKeys;

  MyKeysBloc(this._repository) : super(MyKeysInitial()) {
    on<MyKeysEvent>((event, emit) async {
      if (event is FetchMyKeys) {
        final account =
            AppSession.getInstance().bankAccount?.accountNumber ?? '';

        emit(MyKeysLoading());
        await _repository
            .getKeys(account)
            .then((keys) {
              emit(MyKeysSuccess(keys));
              myKeys = keys;
            })
            .catchError((error) {
              if (error is NotFound) {
                myKeys = [];
                emit(const MyKeysSuccess([]));
              } else {
                emit(MyKeysError(error as ErrorResponse));
              }
            });
      }

      if (event is DeleteKey) {
        final account =
            AppSession.getInstance().bankAccount?.accountNumber ?? '';
        emit(MyKeysLoading());
        await _repository
            .deleteKey(event.key, account)
            .then((_) {
              emit(DeleteKeySuccess());
            })
            .catchError((error) {
              emit(MyKeysError(error as ErrorResponse));
            });
      }
      if (event is CancelClaimPix) {
        emit(MyKeysLoading());
        await _repository
            .cancelRequestClaim(claimId: event.claimId)
            .then((result) {
              emit(CancelClaimPixSucess(result));
            })
            .catchError((e) {
              emit(MyKeysError(e as ErrorResponse));
            });
      }
      if (event is ConfirmChangePix) {
        emit(MyKeysLoading());
        await _repository
            .cancelRequestClaim(claimId: event.claimId)
            .then((result) {
              emit(ConfirmClaimPixSuccess(result));
            })
            .catchError((e) {
              emit(MyKeysError(e as ErrorResponse));
            });
      }

      if (event is FetchMyClaims) {
        emit(MyKeysLoading());
        await _repository
            .consultRequestedClaims()
            .then((claims) {
              AppSession.getInstance().listClaimsPix = claims;
            })
            .catchError((error) {
              emit(MyKeysError(error as ErrorResponse));
            });
      }
      if (event is RegisterNewKeyEvent) {
        emit(MyKeysLoading());
        final account =
            AppSession.getInstance().bankAccount?.accountNumber ?? '';

        await _repository
            .createKey(keyType: KeyType.EVP.name, account: account)
            .then((success) {
              emit(MyKeysSuccess([]));
              add(FetchMyKeys());
            })
            .catchError((e) {
              emit(MyKeysError(e as ErrorResponse));
            });
      }
    });
  }
}

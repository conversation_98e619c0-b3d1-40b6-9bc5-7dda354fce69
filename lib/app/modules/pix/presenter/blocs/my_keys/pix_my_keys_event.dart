part of 'pix_my_keys_bloc.dart';

sealed class <PERSON><PERSON>eysEvent extends Equatable {
  const MyKeysEvent();

  @override
  List<Object> get props => [];
}

final class RegisterNewKeyEvent extends MyKeysEvent {}

final class FetchMyKeys extends <PERSON>KeysEvent {}

final class FetchMyClaims extends My<PERSON>eysEvent {}

final class Delete<PERSON>ey extends MyKeysEvent {
  final String key;

  const DeleteKey(this.key);
}

final class CancelClaimPix extends My<PERSON><PERSON>sEvent {
  final String claimId;

  const CancelClaimPix(this.claimId);
}

final class ConfirmChangePix extends MyKeysEvent {
  final String claimId;

  const ConfirmChangePix(this.claimId);
}

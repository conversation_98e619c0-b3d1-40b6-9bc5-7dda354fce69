part of 'tariff_pix_bloc.dart';

sealed class TariffPixState extends Equatable {
  const TariffPixState();

  @override
  List<Object> get props => [];
}

final class TariffPixInitial extends TariffPixState {}

final class TariffPixLoading extends TariffPixState {}

final class TariffPixError extends TariffPixState {
  final ErrorResponse error;
  const TariffPixError(this.error);

  @override
  List<Object> get props => [error];
}

final class TariffPixSuccess extends TariffPixState {
  final TariffServiceResponse response;
  const TariffPixSuccess(this.response);

  @override
  List<Object> get props => [response];
}

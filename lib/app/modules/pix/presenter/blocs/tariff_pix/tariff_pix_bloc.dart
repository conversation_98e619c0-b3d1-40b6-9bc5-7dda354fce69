import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/models/tariff_service_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'dart:developer' as developer;

part 'tariff_pix_event.dart';
part 'tariff_pix_state.dart';

class TariffPixBloc extends Bloc<TariffPixEvent, TariffPixState> {
  final IPixRepository _repository;

  TariffPixBloc(this._repository) : super(TariffPixInitial()) {
    on<TariffPixEvent>((event, emit) async {
      if (event is GetTariffPixEvent) {
        developer.log(
          'TariffPixBloc: Iniciando busca de tarifas',
          name: 'TariffPixBloc',
        );
        emit(TariffPixLoading());

        try {
          final response = await _repository.getTariffService();
          developer.log(
            'TariffPixBloc: Tarifas carregadas com sucesso: ${response.services.length} serviços',
            name: 'TariffPixBloc',
          );

          emit(TariffPixSuccess(response));
        } catch (error) {
          developer.log(
            'TariffPixBloc: Erro ao carregar tarifas: $error',
            name: 'TariffPixBloc',
          );
          emit(TariffPixError(error as ErrorResponse));
        }
      } else if (event is TariffPixReset) {
        developer.log('TariffPixBloc: Reset do estado', name: 'TariffPixBloc');
        emit(TariffPixInitial());
      }
    });
  }
}

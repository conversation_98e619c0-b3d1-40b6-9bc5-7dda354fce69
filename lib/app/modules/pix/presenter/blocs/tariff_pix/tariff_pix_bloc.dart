import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/models/tariff_service_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'tariff_pix_event.dart';
part 'tariff_pix_state.dart';

class TariffPixBloc extends Bloc<TariffPixEvent, TariffPixState> {
  final IPixRepository _repository;

  TariffPixBloc(this._repository) : super(TariffPixInitial()) {
    on<TariffPixEvent>((event, emit) async {
      if (event is GetTariffPixEvent) {
        emit(TariffPixLoading());
        try {
          final response = await _repository.getTariffService();

          emit(TariffPixSuccess(response));
        } catch (error) {
          emit(TariffPixError(error as ErrorResponse));
        }
      } else if (event is TariffPixReset) {
        emit(TariffPixInitial());
      }
    });
  }
}

part of 'onboarding_key_bloc.dart';

sealed class OnboardingKeyState extends Equatable {
  const OnboardingKeyState();

  @override
  List<Object> get props => [];
}

final class OnboardingKeyInitial extends OnboardingKeyState {}

final class OnboardingKeyLoading extends OnboardingKeyState {}

final class OnboardingKeySuccess extends OnboardingKeyState {}

final class OnboardingKeyError extends OnboardingKeyState {}

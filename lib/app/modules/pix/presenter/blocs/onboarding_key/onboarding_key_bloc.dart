import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_key/onboarding_key_events.dart';

part 'onboarding_key_state.dart';

class OnboardingKeyBloc extends Bloc<OnboardingKeyEvents, OnboardingKeyState> {
  final IPixRepository _repository;

  OnboardingKeyBloc(this._repository) : super(OnboardingKeyInitial()) {
    on<OnboardingKeyEvents>((event, emit) async {
      if (event is OnboardingKeySaveView) {
        emit(OnboardingKeyLoading());

        await _repository.setViewOnboardingKeysPix().then((_) {
          debugPrint('OnboardingKeys: view salva');
          AppSession.getInstance().user!.viewOnboardingKeysPix = true;
          emit(OnboardingKeySuccess());
        }, onError: (_) {
          debugPrint('OnboardingKeys: erro ao salvar view');
          emit(OnboardingKeyError());
        });
      }
    });
  }
}

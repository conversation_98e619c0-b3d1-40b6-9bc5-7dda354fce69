import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/pix/models/refund_pix_model.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';

import '../../../../../shared/data/models/wallet/details_pix_model.dart';
import '../../../../../shared/errors/error_response.dart';
import '../../../models/refund_pix_response.dart';

part 'refund_event.dart';
part 'refund_state.dart';

class RefundBloc extends Bloc<RefundEvent, RefundState> {
  IPixRepository _repository;
  double? value;
  String? description;
  RefundBloc(this._repository) : super(RefundInitial()) {
    on<RefundEvent>((event, emit) async {
      if (event is ChangePageRefundEvent) {
        emit(RefundLoading());
        value = event.value;
        emit(ChangeRefundState(
          changePage: event.changePageRefundType,
          value: event.value,
          description: description,
        ));
      }
      if (event is ChangeDescriptionEvent) {
        emit(RefundLoading());
        description = event.description;
        emit(ChangeRefundState(
          description: event.description,
          value: value,
        ));
      }

      if (event is ConfirmRefundEvent) {
        emit(RefundLoading());
        try {
          final refundPix = RefundPixModel(
            id: event.transactionId,
            amount: value!,
            endToEndId: event.details.endToEndId,
            reversaldescription: description,
          );
          final result = await _repository.toRefund(refundPix);

          emit(RefundSuccessful(
              // transaction: event.transactionId,
              details: event.details,
              refundPixResponse: result,
              description: description,
              value: value));
        } catch (e) {
          _handleError(emit, e);
        }
      }
    });
  }
  void _handleError(Emitter<RefundState> emit, Object e) {
    emit(RefundError(e as ErrorResponse));
  }
}

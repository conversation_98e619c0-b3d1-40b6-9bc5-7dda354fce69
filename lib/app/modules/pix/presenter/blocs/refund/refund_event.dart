part of 'refund_bloc.dart';

sealed class RefundEvent extends Equatable {
  const RefundEvent();

  @override
  List<Object> get props => [];
}

final class ChangePageRefundEvent extends RefundEvent {
  final double? value;
  final ChangePageRefundType? changePageRefundType;

  const ChangePageRefundEvent({
    this.value,
    this.changePageRefundType = ChangePageRefundType.next,
  });
}

final class ChangeDescriptionEvent extends RefundEvent {
  final String? description;

  const ChangeDescriptionEvent({
    this.description,
  });
}

final class ConfirmRefundEvent extends RefundEvent {
  final String transactionId;
  final DetailsPixModel details;
  const ConfirmRefundEvent({
    required this.transactionId,
    required this.details,
  });
}

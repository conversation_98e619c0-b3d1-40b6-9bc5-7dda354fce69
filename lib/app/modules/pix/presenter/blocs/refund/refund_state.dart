part of 'refund_bloc.dart';

enum ChangePageRefundType { next, previous }

sealed class RefundState extends Equatable {
  const RefundState({this.changePage, this.value, this.description});

  final ChangePageRefundType? changePage;
  final double? value;
  final String? description;

  @override
  List<dynamic> get props => [changePage];
}

final class RefundInitial extends RefundState {}

final class RefundLoading extends RefundState {}

final class RefundError extends RefundState {
  final ErrorResponse error;
  const RefundError(this.error);
}

final class ChangeRefundState extends RefundState {
  const ChangeRefundState({
    super.changePage,
    super.value,
    super.description,
  });
}

final class RefundSuccessful extends RefundState {
  const RefundSuccessful({
    required this.refundPixResponse,
    required this.details,
    super.description,
    super.value,
  });

  // final Transaction transaction;
  final DetailsPixModel details;
  final RefundPixResponse refundPixResponse;
}

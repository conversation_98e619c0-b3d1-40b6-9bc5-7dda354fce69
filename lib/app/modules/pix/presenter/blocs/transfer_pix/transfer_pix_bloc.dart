import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/models/transaction_sent_pix_response.dart';
import 'package:siclosbank/app/modules/pix/models/transfer_pix_model.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'transfer_pix_event.dart';
part 'transfer_pix_state.dart';

class TransferPixBloc extends Bloc<TransferPixEvent, TransferPixState> {
  final IPixRepository _repository;
  TransferPixBloc(this._repository) : super(TransferPixInitial()) {
    on<TransferPixEvent>((event, emit) async {
      if (event is TransferPixSubmit) {
        emit(TransferPixLoading());
        try {
          final response = await _repository.transferPix(event.transferData);
          emit(TransferPixSuccess(response));
        } catch (error) {
          emit(TransferPixError(error as ErrorResponse));
        }
      } else if (event is TransferPixReset) {
        emit(TransferPixInitial());
      }

      if (event is GetTransactionSentPix) {
        emit(TransferPixLoading());
        try {
          final response = await _repository.getTransactionSentPix(
            event.idTransaction,
          );

          emit(GetTransactionSentPixSuccess(response));
        } catch (error) {
          emit(TransferPixError(error as ErrorResponse));
        }
      }
    });
  }
}

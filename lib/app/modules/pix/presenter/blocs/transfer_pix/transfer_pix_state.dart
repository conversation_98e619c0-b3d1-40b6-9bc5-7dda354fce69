part of 'transfer_pix_bloc.dart';

sealed class TransferPixState extends Equatable {
  const TransferPixState();

  @override
  List<Object> get props => [];
}

final class TransferPixInitial extends TransferPixState {}

final class TransferPixLoading extends TransferPixState {}

final class TransferPixError extends TransferPixState {
  final ErrorResponse error;
  const TransferPixError(this.error);

  @override
  List<Object> get props => [error];
}

final class TransferPixSuccess extends TransferPixState {
  final TransferPixResponse response;
  const TransferPixSuccess(this.response);

  @override
  List<Object> get props => [response];
}

final class GetTransactionSentPixSuccess extends TransferPixState {
  final TransactionSentPixResponse response;
  const GetTransactionSentPixSuccess(this.response);

  @override
  List<Object> get props => [response];
}

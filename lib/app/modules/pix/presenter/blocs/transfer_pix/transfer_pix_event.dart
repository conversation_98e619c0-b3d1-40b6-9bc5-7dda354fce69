part of 'transfer_pix_bloc.dart';

sealed class TransferPixEvent extends Equatable {
  const TransferPixEvent();

  @override
  List<Object> get props => [];
}

final class TransferPixSubmit extends TransferPixEvent {
  final TransferPixModel transferData;
  const TransferPixSubmit(this.transferData);
}

final class TransferPixReset extends TransferPixEvent {
  const TransferPixReset();
}

final class GetTransactionSentPix extends TransferPixEvent {
  final String idTransaction;
  const GetTransactionSentPix(this.idTransaction);
}

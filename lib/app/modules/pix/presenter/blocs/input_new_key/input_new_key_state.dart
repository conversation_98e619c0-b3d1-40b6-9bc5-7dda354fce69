part of 'input_new_key_bloc.dart';

sealed class InputNewKeyState extends Equatable {
  const InputNewKeyState();

  @override
  List<Object> get props => [];
}

final class InputNewKeyInitial extends InputNewKeyState {}

final class InputNewKeyLoading extends InputNewKeyState {}

final class InputNewKeyError extends InputNewKeyState {
  final Exception error;

  const InputNewKeyError(this.error);
}

final class InputNewKeySendCodeSucces extends InputNewKeyState {}

final class InputNewKeySucces extends InputNewKeyState {
  final String keyValue;

  const InputNewKeySucces({required this.keyValue});
}

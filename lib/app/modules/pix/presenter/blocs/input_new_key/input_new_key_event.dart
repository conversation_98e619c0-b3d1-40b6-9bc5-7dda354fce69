part of 'input_new_key_bloc.dart';

sealed class InputNewKeyEvent extends Equatable {
  const InputNewKeyEvent();

  @override
  List<Object> get props => [];
}

final class VerifyPhoneEvent extends InputNewKeyEvent {
  final String inputPhone;

  const VerifyPhoneEvent({required this.inputPhone});
}

final class VerifyEmailEvent extends InputNewKeyEvent {
  final String inputEmail;

  const VerifyEmailEvent({required this.inputEmail});
}

final class CheckVerificationCodeEmailEvent extends InputNewKeyEvent {
  final String code;

  const CheckVerificationCodeEmailEvent({required this.code});
}

final class CheckVerificationCodePhoneEvent extends InputNewKeyEvent {
  final String code;

  const CheckVerificationCodePhoneEvent({required this.code});
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';

import '../../../data/repository/i_pix_repositoy.dart';

part 'input_new_key_event.dart';
part 'input_new_key_state.dart';

class InputNewKeyBloc extends Bloc<InputNewKeyEvent, InputNewKeyState> {
  final IPixRepository _repo;
  String? keyValue;
  InputNewKeyBloc(this._repo) : super(InputNewKeyInitial()) {
    on<InputNewKeyEvent>((event, emit) async {
      if (event is VerifyEmailEvent) {
        if (keyValue != event.inputEmail) keyValue = event.inputEmail;
        emit(InputNewKeyLoading());
        await _repo.sendCodeEmailKey(event.inputEmail).then((_) {
          emit(InputNewKeySendCodeSucces());
        }).catchError((e) {
          emit(InputNewKeyError(e));
        });
      }
      if (event is VerifyPhoneEvent) {
        if (keyValue != event.inputPhone) keyValue = event.inputPhone;
        emit(InputNewKeyLoading());
        await _repo
            .sendCodePhoneKey(
                '+55${FieldsUtils.removeCharacters(event.inputPhone)}')
            .then((_) {
          emit(InputNewKeySendCodeSucces());
        }).catchError((e) {
          emit(InputNewKeyError(e));
        });
      }
      if (event is CheckVerificationCodeEmailEvent) {
        emit(InputNewKeyLoading());
        await _repo.checkCodeEmail(event.code).then((_) {
          emit(InputNewKeySucces(keyValue: keyValue!));
        }).catchError((e) {
          emit(InputNewKeyError(e));
        });
      }
      if (event is CheckVerificationCodePhoneEvent) {
        emit(InputNewKeyLoading());
        await _repo.checkCodePhone(event.code).then((_) {
          emit(InputNewKeySucces(keyValue: keyValue!));
        }).catchError((e) {
          emit(InputNewKeyError(e));
        });
      }
    });
  }
}

part of 'search_pix_bloc.dart';

sealed class SearchPixState extends Equatable {
  const SearchPixState();

  @override
  List<Object> get props => [];
}

final class SearchPixInitial extends SearchPixState {}

final class SearchPixLoading extends SearchPixState {}

final class SearchPixError extends SearchPixState {
  final ErrorResponse error;
  const SearchPixError(this.error);

  @override
  List<Object> get props => [error];
}

final class SearchPixSuccess extends SearchPixState {
  final PixSearchResponse response;
  const SearchPixSuccess(this.response);

  @override
  List<Object> get props => [response];
}

final class SearchPixNotFound extends SearchPixState {}

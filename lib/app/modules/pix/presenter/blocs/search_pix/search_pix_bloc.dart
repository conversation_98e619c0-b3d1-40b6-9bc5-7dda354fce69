import "package:bloc/bloc.dart";
import "package:equatable/equatable.dart";
import "package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart";
import "package:siclosbank/app/modules/pix/models/pix_search_response.dart";
import "package:siclosbank/app/shared/errors/error_response.dart";

part "search_pix_event.dart";
part "search_pix_state.dart";

class SearchPixBloc extends Bloc<SearchPixEvent, SearchPixState> {
  final IPixRepository _repository;
  SearchPixBloc(this._repository) : super(SearchPixInitial()) {
    on<SearchPixEvent>((event, emit) async {
      if (event is SearchPix) {
        emit(SearchPixLoading());
        try {
          final response = await _repository.searchPix(event.key);
          emit(SearchPixSuccess(response));
        } catch (error) {
          emit(SearchPixError(error as ErrorResponse));
        }
      }
    });
  }
}

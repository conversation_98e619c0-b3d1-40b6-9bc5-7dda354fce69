part of 'my_limits_pix_bloc.dart';

sealed class MyLimitsPixState extends Equatable {
  const MyLimitsPixState();

  @override
  List<Object> get props => [];
}

final class MyLimitsPixInitial extends MyLimitsPixState {}

final class MyLimitsPixLoading extends MyLimitsPixState {}

final class MyLimitsPixError extends MyLimitsPixState {
  final ErrorResponse error;
  const MyLimitsPixError(this.error);
}

final class MyLimitsPixSuccess extends MyLimitsPixState {
  final LimitPixResponse limitPixResponse;

  const MyLimitsPixSuccess(this.limitPixResponse);

  @override
  List<Object> get props => [limitPixResponse];
}

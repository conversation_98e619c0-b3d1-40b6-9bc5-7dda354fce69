import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../models/limit_pix_response.dart';

part 'my_limits_pix_event.dart';
part 'my_limits_pix_state.dart';

class MyLimitsPixBloc extends Bloc<MyLimitsPixEvent, MyLimitsPixState> {
  final IPixRepository _repository;
  MyLimitsPixBloc(this._repository) : super(MyLimitsPixInitial()) {
    on<MyLimitsPixEvent>((event, emit) async {
      if (event is GetMyLimitsPixEvent) {
        emit(MyLimitsPixLoading());

        await _repository.getMyLimits().then((value) {
          emit(MyLimitsPixSuccess(value));
        }).catchError((error) {
          emit(MyLimitsPixError(
            error is ErrorResponse
                ? error
                : ErrorResponse(message: 'Erro desconhecido ao obter limites'),
          ));
        });
      }
    });
  }
}

part of 'charge_pix_bloc.dart';

sealed class ChargePixState extends Equatable {
  const ChargePixState();

  @override
  List<Object> get props => [];
}

final class ChargePixInitial extends ChargePixState {}

final class ChargePixLoadingState extends ChargePixState {}

final class ChargePixErrorState extends ChargePixState {
  final ErrorResponse error;
  const ChargePixErrorState(this.error);
}

final class ChargePixSuccessState extends ChargePixState {
  final String qrcode;
  const ChargePixSuccessState(this.qrcode);
}

final class ChargePixDynamicSuccessState extends ChargePixState {
  final String qrcode;
  const ChargePixDynamicSuccessState(this.qrcode);
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/pix/data/repository/i_pix_repositoy.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'charge_pix_event.dart';
part 'charge_pix_state.dart';

class ChargePixBloc extends Bloc<ChargePixEvent, ChargePixState> {
  final IPixRepository _pixRepository;
  List<KeyPixModel> listKeys = [];
  KeyPixModel? currentKey;
  Map<String, String> qrcodes = {};
  late double value;

  ChargePixBloc(this._pixRepository) : super(ChargePixInitial()) {
    value = 0;

    on<ChargePixEvent>((event, emit) async {
      if (event is GetStaticQrcodeEvent) {
        emit(ChargePixLoadingState());
        currentKey = event.key;
        if (qrcodes.containsKey(event.key.key)) {
          emit(ChargePixSuccessState(qrcodes[event.key.key]!));
        } else {
          await _pixRepository
              .createStaticQrCode(event.key.key)
              .then((qrcode) {
                emit(ChargePixSuccessState(qrcode));
                qrcodes[event.key.key] = qrcode;
              })
              .catchError((error) {
                emit(ChargePixErrorState(error as ErrorResponse));
              });
        }
      }
      if (event is GetDynamicQrcodeEvent) {
        emit(ChargePixLoadingState());

        assert(currentKey != null);
        await _pixRepository
            .createDynamicQrCode(
              key: currentKey!.key,
              amount: value.toStringAsFixed(2),
              description: event.description,
            )
            .then((qrcode) {
              emit(ChargePixDynamicSuccessState(qrcode));
              qrcodes[currentKey!.key] = qrcode;
            })
            .catchError((error) {
              emit(ChargePixErrorState(error as ErrorResponse));
            });
      }
    });
  }
}

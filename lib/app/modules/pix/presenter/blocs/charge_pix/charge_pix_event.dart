part of 'charge_pix_bloc.dart';

sealed class ChargePixEvent extends Equatable {
  const ChargePixEvent();

  @override
  List<Object> get props => [];
}

final class GetStaticQrcodeEvent extends ChargePixEvent {
  final KeyPixModel key;
  const GetStaticQrcodeEvent(this.key);

  @override
  List<Object> get props => [key];
}

final class GetDynamicQrcodeEvent extends ChargePixEvent {
  // final KeyPixModel key;
  // final double amount;
  final String? description;
  const GetDynamicQrcodeEvent(this.description);
}

part of 'register_key_bloc.dart';

sealed class RegisterKeyEvent extends Equatable {
  const RegisterKeyEvent();

  @override
  List<Object> get props => [];
}

final class Register<PERSON><PERSON><PERSON><PERSON> extends RegisterKeyEvent {
  final String? key;
  final KeyType keyType;

  const RegisterNewKey({this.key, required this.keyType});
}

// Portabilidade para CPF, email e telefone
// Reivindicacao somente para telefone
final class StartClaim extends RegisterKeyEvent {
  final ClaimType claimType;
  final KeyType keyType;
  final String key;

  const StartClaim(
      {required this.claimType, required this.keyType, required this.key});
}

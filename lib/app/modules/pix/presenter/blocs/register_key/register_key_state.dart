part of 'register_key_bloc.dart';

sealed class RegisterKeyState extends Equatable {
  const RegisterKeyState();

  @override
  List<Object> get props => [];
}

final class Register<PERSON>eyInitial extends Register<PERSON>eyState {}

final class RegisterKeyLoading extends Register<PERSON>eyState {}

final class Register<PERSON>eyError extends Register<PERSON>eyState {
  final ErrorResponse error;
  const RegisterKeyError(this.error);
}

final class RegisterNewKeySuccess extends RegisterKeyState {}

final class StarClaimSuccess extends Register<PERSON><PERSON>State {
  final ClaimType claimType;
  final ClaimRequestedModel claim;

  const StarClaimSuccess({required this.claimType, required this.claim});
}

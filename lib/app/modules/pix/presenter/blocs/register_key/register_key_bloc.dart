import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';

import '../../../../../app_controller.dart';
import '../../../../../shared/errors/error_response.dart';
import '../../../../../shared/utils/fields_utils.dart';
import '../../../models/enums/claim_type_enum.dart';
import '../../../models/enums/key_types_enum.dart';
import '../../../data/repository/i_pix_repositoy.dart';

part 'register_key_event.dart';
part 'register_key_state.dart';

class RegisterKeyBloc extends Bloc<RegisterKeyEvent, RegisterKeyState> {
  final IPixRepository _repository;
  RegisterKeyBloc(this._repository) : super(RegisterKeyInitial()) {
    final account = AppSession.getInstance().bankAccount!.accountNumber!;
    on<RegisterKeyEvent>((event, emit) async {
      if (event is RegisterNewKey) {
        emit(RegisterKeyLoading());
        String? _key = (event.keyType.isCPF || event.keyType.isPhone)
            ? FieldsUtils.removeCharacters(event.key!)
            : event.key;
        if (event.keyType.isPhone) {
          _key = '+55$_key';
        }
        await _repository
            .createKey(
                keyType: event.keyType.name,
                account: account,
                key: event.keyType.isPhone ? _key : event.key)
            .then((success) {
          emit(RegisterNewKeySuccess());
        }).catchError((e) {
          emit(RegisterKeyError(e as ErrorResponse));
        });
      }
      if (event is StartClaim) {
        emit(RegisterKeyLoading());
        String key = (event.keyType.isCPF || event.keyType.isPhone)
            ? FieldsUtils.removeCharacters(event.key)
            : event.key;

        if (event.keyType.isPhone) {
          key = '+55$key';
        }
        await _repository
            .requestPortabilityKey(
                keyType: event.keyType.name,
                key: key,
                account: account,
                claimType: event.claimType.name)
            .then((claim) {
          emit(StarClaimSuccess(claimType: event.claimType, claim: claim));
        }).catchError((e) {
          emit(RegisterKeyError(e as ErrorResponse));
        });
      }
    });
  }
}

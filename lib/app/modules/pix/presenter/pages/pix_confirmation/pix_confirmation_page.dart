import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/transfer_pix/transfer_pix_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import '../pix_receipt/pix_receipt_page.dart';

class PixConfirmationPage extends StatelessWidget {
  const PixConfirmationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TransferPixBloc>(
      create: (context) => Modular.get<TransferPixBloc>(),
      child: const _PixConfirmationPage(),
    );
  }
}

class _PixConfirmationPage extends StatefulWidget {
  const _PixConfirmationPage();

  @override
  State<_PixConfirmationPage> createState() => _PixConfirmationPageState();
}

class _PixConfirmationPageState extends State<_PixConfirmationPage> {
  String? transactionId;
  Map<String, dynamic>? fallbackData;
  int retryCount = 0;
  static const int maxRetries = 3;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments;

    if (args is Map<String, dynamic>) {
      transactionId = args['transactionId'] as String?;
      fallbackData = args;
    } else if (args is String) {
      transactionId = args;
    }

    if (transactionId != null && transactionId!.isNotEmpty) {
      _loadTransactionData();
    }
  }

  @override
  void initState() {
    super.initState();
    _loadTransactionData();
  }

  void _loadTransactionData() async {
    try {
      if (transactionId == null || transactionId!.isEmpty) {
        return;
      }

      if (retryCount == 0) {
        await Future.delayed(const Duration(seconds: 3));
      }

      if (!mounted) return;

      final bloc = BlocProvider.of<TransferPixBloc>(context);
      bloc.add(GetTransactionSentPix(transactionId!));
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBarApp(title: 'PIX'),
      body: BlocConsumer<TransferPixBloc, TransferPixState>(
        listener: (context, state) {
          if (state is GetTransactionSentPixSuccess) {
            retryCount = 0;
          } else if (state is TransferPixError) {
            if (retryCount < maxRetries) {
              retryCount++;

              final delaySeconds = 3 * retryCount;

              Future.delayed(Duration(seconds: delaySeconds), () {
                if (mounted && transactionId != null) {
                  _loadTransactionData();
                }
              });
            }
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(18),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Transferência com Pix',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: const Color(0xFF343A40),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _getFormattedDateTime(state),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF343A40),
                    fontWeight: FontWeight.w500,
                  ),
                ),

                const SizedBox(height: 16),

                _buildContent(state),

                const SizedBox(height: 100), // Espaço para o footer
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: BlocBuilder<TransferPixBloc, TransferPixState>(
        builder: (context, state) {
          return _buildButtons(state);
        },
      ),
    );
  }

  String _getFormattedDateTime(TransferPixState state) {
    if (state is GetTransactionSentPixSuccess) {
      final date = state.response.date;
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year} - ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    }
    return DateTime.now()
        .toString()
        .substring(0, 16)
        .replaceAll('-', '/')
        .replaceAll('T', ' - ');
  }

  Widget _buildContent(TransferPixState state) {
    if (state is TransferPixLoading) {
      return const SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (state is GetTransactionSentPixSuccess) {
      return _buildTransactionInfoCard(state.response);
    }

    if (state is TransferPixError) {
      return const SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return const SizedBox(
      height: 200,
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildTransactionInfoCard(dynamic response) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSimpleDetailRow('Nome', response.creditParty.name),
              const SizedBox(height: 12),
              _buildSimpleDetailRow(
                'CPF',
                _formatTaxId(response.creditParty.taxId),
              ),
              const SizedBox(height: 12),
              _buildSimpleDetailRow('Instituição', response.creditParty.bank),
              const SizedBox(height: 12),
              _buildSimpleDetailRow(
                'Valor',
                'R\$ ${response.amount.toStringAsFixed(2).replaceAll('.', ',')}',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleDetailRow(String label, String value) {
    if (label == 'ID da Transação' && value.length > 20) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, color: Color(0xFF6C757D)),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: GestureDetector(
              onTap: () {
                Clipboard.setData(ClipboardData(text: value));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ID copiado para a área de transferência'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF343A40),
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(Icons.copy, size: 16, color: Colors.grey.shade600),
                ],
              ),
            ),
          ),
        ],
      );
    }

    // Layout horizontal - label e valor lado a lado
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF6C757D),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 3,
          child: Text(
            value.isEmpty ? '-' : value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF343A40),
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  String _formatTaxId(String taxId) {
    if (taxId.length == 11) {
      // CPF
      return '${taxId.substring(0, 3)}.${taxId.substring(3, 6)}.${taxId.substring(6, 9)}-${taxId.substring(9)}';
    } else if (taxId.length == 14) {
      // CNPJ
      return '${taxId.substring(0, 2)}.${taxId.substring(2, 5)}.${taxId.substring(5, 8)}/${taxId.substring(8, 12)}-${taxId.substring(12)}';
    }
    return taxId;
  }

  Widget _buildButtons(TransferPixState state) {
    return Container(
      padding: const EdgeInsets.all(18),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ButtonApp(
              text: 'Ver comprovante',
              onPress: () {
                // Passamos os dados da transação se disponíveis no state
                final transactionData = state is GetTransactionSentPixSuccess
                    ? state.response
                    : null;

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        PixReceiptPage(transactionData: transactionData),
                    settings: RouteSettings(arguments: transactionId),
                  ),
                );
              },
              width: double.infinity,
            ),
            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey.shade400),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'Sair',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/refund/confirm_refund_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/refund/value_refund_page.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/shared/data/models/wallet/details_pix_model.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';

import '../../../../../shared/data/models/wallet/transaction_response.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../shared/presenter/view/components/others/keep_alive_page.dart';
import '../../blocs/refund/refund_bloc.dart';

class RefundPageview extends StatelessWidget {
  const RefundPageview({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RefundBloc>(
      create: (context) => Modular.get(),
      child: const _RefundPageView(),
    );
  }
}

class _RefundPageView extends StatefulWidget {
  const _RefundPageView({super.key});

  @override
  State<_RefundPageView> createState() => __RefundPageViewState();
}

class __RefundPageViewState extends State<_RefundPageView> {
  List<Widget> pages = [];

  late String transactionId;
  late DetailsPixModel details;

  late PageController pageController;
  int currentPage = 0;

  @override
  void initState() {
    final args = Modular.args.data?['args'];
    if (args != null) {
      transactionId = args['transactionId'] as String;
      details = args['details'] as DetailsPixModel;
    }

    final value = args?['amountAvailable'] as double? ?? 0;

    pages = [
      KeepAlivePage(
          child: ValueRefundPage(
        details: details,
        amountAvailable: value,
      )),
      ConfirmRefundPage(transactionId: transactionId, details: details),
    ];
    pageController = PageController(
      initialPage: currentPage,
      keepPage: true,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.devolucao_pix.toUpperCase(),
        clickBack: _onClickBack,
        showBack: true,
      ),
      body: BlocConsumer<RefundBloc, RefundState>(
        listener: (context, state) {
          if (state is RefundSuccessful) {
            pop();
            pop();
            pop();
            push(
              Routes.statementDetails,
              args: {
                'refundPixResponse': state.refundPixResponse,
                'transaction': TransactionResponse(
                    id: state.refundPixResponse.id,
                    amount: state.refundPixResponse.amount,
                    description: state.refundPixResponse.description,
                    createDate: null,
                    movementType: MovementType.pixReversalOut),
              },
            );
          }

          if (state.changePage != null) {
            if (state.changePage == ChangePageRefundType.next) {
              _nextPage();
            } else {
              _backPage();
            }
          }

          if (state is RefundError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is RefundLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return PageView(
            controller: pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: pages,
          );
        },
      ),
    );
  }

  void _nextPage() {
    setState(() {
      currentPage++;
      _movePage();
    });
  }

  void _movePage({bool cancel = false}) {
    pageController.animateToPage(currentPage,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
    if (cancel) {
      pop(cancel);
    }
  }

  void _backPage() {
    setState(() {
      currentPage--;
      _movePage();
    });
  }

  _onClickBack() async {
    if (currentPage == 0) {
      pop();
    } else {
      _backPage();
    }
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/refund/refund_bloc.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/data/models/wallet/details_pix_model.dart';
import '../../../../../shared/data/models/wallet/transaction_response.dart';
import '../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../shared/utils/utils.dart';

class ValueRefundPage extends StatefulWidget {
  const ValueRefundPage(
      {super.key, required this.details, required this.amountAvailable});

  final DetailsPixModel details;
  final double amountAvailable;

  @override
  State<ValueRefundPage> createState() => _ValueRefundPageState();
}

class _ValueRefundPageState extends State<ValueRefundPage> {
  final _formKey = GlobalKey<FormState>();
  late MoneyMaskedTextController _valorController;

  bool _habilitarBotaoContinuar = true;
  late double limitValue;

  DetailsPixModel get details => widget.details;

  @override
  void initState() {
    limitValue = widget.amountAvailable;

    _valorController = MoneyMaskedTextController(
      decimalSeparator: ',',
      thousandSeparator: '.',
      leftSymbol: r'R$',
      initialValue: limitValue,
    );
    super.initState();
  }

  @override
  void dispose() {
    _valorController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: CustomScrollView(
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: Form(
                key: _formKey,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16, top: 30, right: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Text(
                        I18n.of(context)!.quanto_deseja_devolver,
                        style: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(fontWeight: FontWeight.normal),
                      ),
                      const SizedBox(height: 16),
                      TextFormFieldApp(
                        controller: _valorController,
                        label: const I18n().valor,
                        textInputType: TextInputType.number,
                        suffixIcon: const Icon(Icons.mode_edit),
                        onChanged: (text) {
                          setState(() {
                            _habilitarBotaoContinuar =
                                _valorController.numberValue > 0.0;
                          });
                        },
                        validator: (value) {
                          if (_valorController.numberValue <= 0.0) {
                            return I18n.of(context)!.campo_obrigatorio;
                          }
                          // colocar variavel com valor minimo de emprestimo
                          // double valMin = 300; // .valorLiberadoMinimo!;

                          // const valMin =
                          //     0.01; // Replace with actual minimum value logic
                          // if (_valorController.numberValue < valMin) {
                          //   return I18n.of(context)!.errorValorMinimoEmprestimo(
                          //       Utils.formatBalance(valMin.toDouble()));
                          // }

                          if (_valorController.numberValue > limitValue) {
                            return I18n.of(context)!.errorValorMaximoEmprestimo(
                                Utils.formatBalance(limitValue));
                          }

                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      RichText(
                        text: TextSpan(
                          text: I18n.of(context)!.voce_pode_devolver_ate,
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(fontWeight: FontWeight.normal),
                          children: <TextSpan>[
                            TextSpan(
                              text: Utils.formatBalance(limitValue),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverFillRemaining(
                hasScrollBody: false,
                fillOverscroll: false,
                child: Container(
                  alignment: Alignment.bottomCenter,
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 24),
                  child: ButtonApp(
                    enabled: _habilitarBotaoContinuar,
                    width: MediaQuery.of(context).size.width,
                    text: I18n.of(context)!.continuar,
                    onPress: () {
                      _clickContinuar(_valorController.numberValue);
                    },
                  ),
                ))
          ],
        ),
      ),
    );
  }

  void _clickContinuar(double valor) {
    final valido = _formKey.currentState?.validate() ?? false;
    if (valido) {
      FocusScope.of(context).unfocus();
      log('next page with value: $valor');
      BlocProvider.of<RefundBloc>(context).add(
        ChangePageRefundEvent(value: valor),
      );
    }
  }
}

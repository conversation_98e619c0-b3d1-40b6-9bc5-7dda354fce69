import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/pin/presenter/view/check_pin_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/refund/refund_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/field_horizontal.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../../shared/data/models/wallet/details_pix_model.dart';
import '../../../../../shared/data/models/wallet/transaction_response.dart';
import '../../../../../shared/navigation/named_routes.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';

class ConfirmRefundPage extends StatefulWidget {
  const ConfirmRefundPage(
      {super.key, required this.transactionId, required this.details});

  final String transactionId;
  final DetailsPixModel details;

  @override
  State<ConfirmRefundPage> createState() => _ConfirmRefundPageState();
}

class _ConfirmRefundPageState extends State<ConfirmRefundPage> {
  String get transactionId => widget.transactionId;
  DetailsPixModel get details => widget.details;
  final descriptionController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<RefundBloc>(context),
      child: Scaffold(
        body: SafeArea(
            child: BlocConsumer<RefundBloc, RefundState>(
          listener: (context, state) {
            if (state is RefundError) {
              DialogUtils.showSnackError(context, state.error);
            }
            if (state is RefundSuccessful) {
              pushReplacement(
                Routes.statementDetails,
                args: {
                  'transaction': TransactionResponse(
                      id: state.refundPixResponse.id,
                      description: state.description,
                      amount: state.refundPixResponse.amount,
                      createDate:
                          state.refundPixResponse.createdAt ?? DateTime.now(),
                      movementType: MovementType.pixReversalOut),
                },
              );
            }
          },
          builder: (context, state) {
            return CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          I18n.of(context)!.devolucao_para,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        FieldHorizontal(
                          title: I18n.of(context)!.nome,
                          info: details.debitParty.name,
                        ),
                        FieldHorizontal(
                          title: I18n.of(context)!.cpf_cnpj,
                          info:
                              FieldsUtils.isCNPJValido(details.debitParty.taxId)
                                  ? details.debitParty.taxId
                                  : Utils.maskCpf(details.debitParty.taxId),
                        ),
                        FieldHorizontal(
                          title: I18n.of(context)!.instituicao,
                          info: Utils.getBankName(details.debitParty.bank,
                              shortName: true),
                        ),
                        FieldHorizontal(
                          title: I18n.of(context)!.id_transacao_originial,
                          info: transactionId,
                        ),
                        FieldHorizontal(
                          title: I18n.of(context)!.endToEnd,
                          info: details.endToEndId,
                        ),
                        _dividerCustom(),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Text(
                            I18n.of(context)!.valor,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                Utils.formatBalance(state.value),
                                style: Theme.of(context)
                                    .textTheme
                                    .titleLarge!
                                    .copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 28,
                                    ),
                              ),
                              IconButton(
                                onPressed: () {
                                  BlocProvider.of<RefundBloc>(context)
                                      .add(const ChangePageRefundEvent(
                                    changePageRefundType:
                                        ChangePageRefundType.previous,
                                  ));
                                },
                                icon: const Icon(Icons.edit),
                              ),
                            ],
                          ),
                        ),
                        _dividerCustom(),
                        const SizedBox(height: 16),
                        Text(
                          I18n.of(context)!.devolucao_para,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        FieldHorizontal(
                          title: I18n.of(context)!.tipo_transacao,
                          info: I18n.of(context)!.pix,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextResponsive(
                              I18n.of(context)!.descricao,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    color: ColorsApp.cinza[700],
                                  ),
                            ),
                            state.description == null
                                ? TextButton(
                                    onPressed: _showEditDescription,
                                    child: Text(
                                      I18n.of(context)!.adicionar_descricao,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: ColorsApp.verde[600],
                                              decoration:
                                                  TextDecoration.underline,
                                              decorationColor:
                                                  ColorsApp.verde[600]),
                                    ),
                                  )
                                : TextButton.icon(
                                    onPressed: _showEditDescription,
                                    icon: const Icon(Icons.edit),
                                    iconAlignment: IconAlignment.end,
                                    label: Text(
                                      state.description ?? '',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                  ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                SliverFillRemaining(
                    hasScrollBody: false,
                    fillOverscroll: false,
                    child: Container(
                      alignment: Alignment.bottomCenter,
                      padding: const EdgeInsets.only(
                          left: 16, right: 16, bottom: 16),
                      child: ButtonApp(
                        width: MediaQuery.of(context).size.width,
                        text: I18n.of(context)!.confirmar,
                        onPress: () {
                          CheckPinPage.showSheet(
                            context,
                            () {
                              BlocProvider.of<RefundBloc>(context)
                                  .add(ConfirmRefundEvent(
                                transactionId: transactionId,
                                details: details,
                              ));
                            },
                          );
                        },
                      ),
                    ))
              ],
            );
          },
        )),
      ),
    );
  }

  Divider _dividerCustom() {
    return const Divider(
      thickness: 0.5,
      color: Colors.black26,
    );
  }

  _showEditDescription() {
    SheetAlertConfirm.showSheet(
      context,
      title: I18n.of(context)!.adicionar_descricao,
      textPositive: I18n.of(context)!.salvar,
      child: Padding(
        padding: const EdgeInsets.only(top: 8.0, bottom: 24),
        child: TextFormFieldApp(
          hint: I18n.of(context)!.descricao,
          controller: descriptionController,
        ),
      ),
      clickPositive: () {
        BlocProvider.of<RefundBloc>(context).add(
            ChangeDescriptionEvent(description: descriptionController.text));
        pop(descriptionController.text);
      },
      showBtnNegative: false,
    );
  }
}

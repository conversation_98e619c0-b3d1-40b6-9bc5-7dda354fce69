import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/search_pix/search_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/models/pix_search_response.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'components/header_widget.dart';
import 'components/pix_key_input_widget.dart';
import 'components/pix_key_result_widget.dart';

enum PixKeyType { cpf, cnpj, phone }

class PixUserInfo {
  final String name;
  final String cpf;
  final String institution;
  final String pixKey;
  final String keyType;
  final String? endToEndId;
  final String? bankCode;

  const PixUserInfo({
    required this.name,
    required this.cpf,
    required this.institution,
    required this.pixKey,
    required this.keyType,
    this.endToEndId,
    this.bankCode,
  });

  factory PixUserInfo.fromSearchResponse(PixSearchResponse response) {
    return PixUserInfo(
      name: response.owner.name,
      cpf: response.owner.documentNumber,
      institution: response.account.branch,
      pixKey: response.key,
      keyType: response.keyType,
      endToEndId: response.endToEndId,
      bankCode: response.account.participant,
    );
  }
}

class PixWithKeyPage extends StatelessWidget {
  const PixWithKeyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SearchPixBloc>(
      create: (context) => Modular.get<SearchPixBloc>(),
      child: const _PixWithKeyPage(),
    );
  }
}

class _PixWithKeyPage extends StatefulWidget {
  const _PixWithKeyPage();

  @override
  State<_PixWithKeyPage> createState() => _PixWithKeyPageState();
}

class _PixWithKeyPageState extends State<_PixWithKeyPage> {
  PixKeyType selected = PixKeyType.cpf;
  int selectedKeyIndex = 0;
  final TextEditingController _controller = TextEditingController();
  PixUserInfo? _userInfo;

  // final contacts = const [
  //   'Camila Lima',
  //   'Davi Oliveira',
  //   'Eloisa Oliveira',
  //   'Vivian Oliveira',
  //   'Julia Oliveira',
  // ];

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onPixKeyChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onPixKeyChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onPixKeyChanged() {
    final pixKey = _controller.text.trim();

    if (pixKey.length >= 11) {
      BlocProvider.of<SearchPixBloc>(context).add(SearchPix(pixKey));
    } else {
      setState(() {
        _userInfo = null;
      });
    }
  }

  void _navigateToTransferScreen() {
    Navigator.pushNamed(context, Routes.pixTransfer, arguments: _userInfo);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(title: 'PIX'),
      body: BlocConsumer<SearchPixBloc, SearchPixState>(
        listener: (context, state) {
          if (state is SearchPixSuccess) {
            setState(() {
              _userInfo = PixUserInfo.fromSearchResponse(state.response);
            });
          } else if (state is SearchPixError) {
            setState(() {
              _userInfo = null;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Erro ao buscar chave PIX: ${state.error.message}',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          final isLoading = state is SearchPixLoading;

          return Column(
            children: [
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.all(18),
                  children: [
                    if (_userInfo == null)
                      const HeaderWidget(
                        title: 'Selecione a chave ou um contato',
                        subtitle:
                            'Insira a chave pix escolhida para realizar a transferência para o destinatário.',
                      ),

                    const SizedBox(height: 24),

                    PixKeyInputWidget(controller: _controller),

                    const SizedBox(height: 16),

                    if (isLoading)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else
                      PixKeyResultWidget(
                        userInfo: _userInfo,
                        onContactTap: (name) {},
                        onManualPixTap: () {},
                        onContinuePressed: _userInfo != null
                            ? () {
                                _navigateToTransferScreen();
                              }
                            : null,
                      ),
                  ],
                ),
              ),

              if (_userInfo != null)
                Container(
                  padding: const EdgeInsets.only(
                    left: 18,
                    right: 18,
                    bottom: 40,
                  ),
                  child: ButtonApp(
                    text: 'Continuar',
                    onPress: () {
                      _navigateToTransferScreen();
                    },
                    width: double.infinity,
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}

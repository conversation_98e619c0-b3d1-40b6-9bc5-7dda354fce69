import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class PixRemoveContactSheet extends StatelessWidget {
  final String contactName;
  final String title;
  final VoidCallback onConfirm;

  const PixRemoveContactSheet({
    super.key,
    required this.contactName,
    required this.title,
    required this.onConfirm,
  });

  static void showSheet(
    BuildContext context, {
    required String contactName,
    String? title,
    required VoidCallback onConfirm,
  }) {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return PixRemoveContactSheet(
          contactName: contactName,
          title: title ?? 'Remover $contactName de sua lista de contatos?',
          onConfirm: onConfirm,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _PixRemoveContactSheetContent(
      contactName: contactName,
      title: title,
      onConfirm: onConfirm,
    );
  }
}

class _PixRemoveContactSheetContent extends StatefulWidget {
  final String contactName;
  final String title;
  final VoidCallback onConfirm;

  const _PixRemoveContactSheetContent({
    required this.contactName,
    required this.title,
    required this.onConfirm,
  });

  @override
  State<_PixRemoveContactSheetContent> createState() =>
      _PixRemoveContactSheetContentState();
}

class _PixRemoveContactSheetContentState
    extends State<_PixRemoveContactSheetContent> {
  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return PopScope(
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          Navigator.pop(context);
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          bottomNavigationBar: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.4,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                ),
              ],
            ),
            padding: EdgeInsetsResponsive.only(
              top: 30,
              left: 24,
              right: 24,
              bottom: 16,
            ),
            child: _buildBody(),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final textTheme = Theme.of(context).textTheme;

    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        shrinkWrap: true,
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 16),

                // Título
                Text(
                  widget.title,
                  style: textTheme.titleLarge?.copyWith(
                    color: const Color(0xFF201F1F),
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Botões
                Column(
                  children: [
                    // Botão Remover
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          widget.onConfirm();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF8ADF4F),
                          foregroundColor: Color(0xFF201F1F),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: const Text(
                          'Remover',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Botão Cancelar
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'Cancelar',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF201F1F),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

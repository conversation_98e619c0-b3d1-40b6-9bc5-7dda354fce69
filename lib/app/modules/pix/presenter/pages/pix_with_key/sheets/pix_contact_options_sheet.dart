import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'pix_contact_edit_sheet.dart';

class PixContactOptionsSheet extends StatelessWidget {
  final String contactName;
  final List<Map<String, dynamic>> accounts;
  final VoidCallback? onTransfer;
  final VoidCallback? onCopyKey;
  final VoidCallback? onFavorite;
  final VoidCallback? onRemove;

  const PixContactOptionsSheet({
    super.key,
    required this.contactName,
    this.accounts = const [],
    this.onTransfer,
    this.onCopyKey,
    this.onFavorite,
    this.onRemove,
  });

  static void showSheet(
    BuildContext context, {
    required String contactName,
    List<Map<String, dynamic>> accounts = const [],
    VoidCallback? onTransfer,
    VoidCallback? onCopy<PERSON>ey,
    VoidCallback? onFavorite,
    VoidCallback? onRemove,
  }) {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return PixContactOptionsSheet(
          contactName: contactName,
          accounts: accounts,
          onTransfer: onTransfer,
          onCopyKey: onCopyKey,
          onFavorite: onFavorite,
          onRemove: onRemove,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _PixContactOptionsSheetContent(
      contactName: contactName,
      accounts: accounts,
      onTransfer: onTransfer,
      onCopyKey: onCopyKey,
      onFavorite: onFavorite,
      onRemove: onRemove,
    );
  }
}

class _PixContactOptionsSheetContent extends StatefulWidget {
  final String contactName;
  final List<Map<String, dynamic>> accounts;
  final VoidCallback? onTransfer;
  final VoidCallback? onCopyKey;
  final VoidCallback? onFavorite;
  final VoidCallback? onRemove;

  const _PixContactOptionsSheetContent({
    required this.contactName,
    required this.accounts,
    this.onTransfer,
    this.onCopyKey,
    this.onFavorite,
    this.onRemove,
  });

  @override
  State<_PixContactOptionsSheetContent> createState() =>
      _PixContactOptionsSheetContentState();
}

class _PixContactOptionsSheetContentState
    extends State<_PixContactOptionsSheetContent> {
  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return PopScope(
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          Navigator.pop(context);
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          bottomNavigationBar: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                ),
              ],
            ),
            padding: EdgeInsetsResponsive.only(
              top: 30,
              left: 24,
              right: 24,
              bottom: 16,
            ),
            child: _buildBody(),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final textTheme = Theme.of(context).textTheme;

    // Lista padrão de contas se não fornecida
    final defaultAccounts = [
      {
        'icon': Icons.pix,
        'title': 'Siclos',
        'subtitle': 'Ag 0001 • C.C 204396-4',
      },
      {
        'icon': Icons.account_balance,
        'title': 'Banco Itaú',
        'subtitle': 'Ag 0001 • C.C 204396-4',
      },
      {'icon': Icons.phone, 'title': '033.781.780-65', 'subtitle': 'Telefone'},
      {
        'icon': Icons.account_balance,
        'title': 'Banco Itaú',
        'subtitle': 'Ag 0001 • C.C 204396-4',
      },
    ];

    final accountsToShow = widget.accounts.isNotEmpty
        ? widget.accounts
        : defaultAccounts;

    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        shrinkWrap: true,
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.contactName,
                              style: textTheme.headlineSmall?.copyWith(
                                color: const Color(0xFF343A40),
                                fontWeight: FontWeight.w600,
                                fontSize: 24,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '•••.781.780-••',
                              style: textTheme.bodyMedium?.copyWith(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          PixContactEditSheet.showSheet(
                            context,
                            contactName: widget.contactName,
                            accounts: accountsToShow,
                            defaultAccounts: defaultAccounts,
                            onRemoveContact: () {
                              widget.onRemove?.call();
                            },
                            onRemoveAccount: (account) {
                              widget.onRemove?.call();
                            },
                          );
                        },
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.edit_outlined,
                            color: Colors.grey.shade600,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Lista de contas
                Column(
                  children: accountsToShow.asMap().entries.map((entry) {
                    final account = entry.value;

                    return Column(
                      children: [
                        _buildAccountTile(
                          icon: account['icon'] ?? Icons.account_balance,
                          title: account['title'] ?? '',
                          subtitle: account['subtitle'] ?? '',
                          canDelete: account['canDelete'] ?? false,
                          onTap: () {
                            Navigator.pop(context);
                            widget.onTransfer?.call();
                          },
                        ),
                      ],
                    );
                  }).toList(),
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool canDelete,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: Icon(icon, color: const Color(0xFF343A40), size: 20),
            ),
            const SizedBox(width: 16),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF343A40),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

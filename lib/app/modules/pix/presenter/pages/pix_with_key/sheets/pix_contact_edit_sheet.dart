import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'pix_remove_contact_sheet.dart';

class PixContactEditSheet extends StatelessWidget {
  final String contactName;
  final List<Map<String, dynamic>> accounts;
  final List<Map<String, dynamic>> defaultAccounts;
  final VoidCallback? onRemoveContact;
  final Function(Map<String, dynamic>)? onRemoveAccount;

  const PixContactEditSheet({
    super.key,
    required this.contactName,
    this.accounts = const [],
    this.defaultAccounts = const [],
    this.onRemoveContact,
    this.onRemoveAccount,
  });

  static void showSheet(
    BuildContext context, {
    required String contactName,
    List<Map<String, dynamic>> accounts = const [],
    List<Map<String, dynamic>> defaultAccounts = const [],
    VoidCallback? onRemoveContact,
    Function(Map<String, dynamic>)? onRemoveAccount,
  }) {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return PixContactEditSheet(
          contactName: contactName,
          accounts: accounts,
          defaultAccounts: defaultAccounts,
          onRemoveContact: onRemoveContact,
          onRemoveAccount: onRemoveAccount,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _PixContactEditSheetContent(
      contactName: contactName,
      accounts: accounts,
      defaultAccounts: defaultAccounts,
      onRemoveContact: onRemoveContact,
      onRemoveAccount: onRemoveAccount,
    );
  }
}

class _PixContactEditSheetContent extends StatefulWidget {
  final String contactName;
  final List<Map<String, dynamic>> accounts;
  final List<Map<String, dynamic>> defaultAccounts;
  final VoidCallback? onRemoveContact;
  final Function(Map<String, dynamic>)? onRemoveAccount;

  const _PixContactEditSheetContent({
    required this.contactName,
    required this.accounts,
    required this.defaultAccounts,
    this.onRemoveContact,
    this.onRemoveAccount,
  });

  @override
  State<_PixContactEditSheetContent> createState() =>
      _PixContactEditSheetContentState();
}

class _PixContactEditSheetContentState
    extends State<_PixContactEditSheetContent> {
  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return PopScope(
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          Navigator.pop(context);
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          bottomNavigationBar: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                ),
              ],
            ),
            padding: EdgeInsetsResponsive.only(
              top: 30,
              left: 24,
              right: 24,
              bottom: 16,
            ),
            child: _buildBody(),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final textTheme = Theme.of(context).textTheme;

    // final accountsToShow = widget.accounts.isNotEmpty
    //     ? widget.accounts
    //     : defaultAccounts;

    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        shrinkWrap: true,
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header com nome do contato e ícone de deletar
                Container(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.contactName,
                              style: textTheme.headlineSmall?.copyWith(
                                color: const Color(0xFF343A40),
                                fontWeight: FontWeight.w600,
                                fontSize: 24,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '•••.781.780-••',
                              style: textTheme.bodyMedium?.copyWith(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          PixRemoveContactSheet.showSheet(
                            context,
                            contactName: widget.contactName,
                            title:
                                'Remover a conta do contato ${widget.contactName}?',
                            onConfirm: () {
                              widget.onRemoveContact?.call();
                            },
                          );
                        },
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.delete_outline,
                            color: Colors.red.shade400,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Lista de contas com ícones de deletar
                Column(
                  children: widget.defaultAccounts.asMap().entries.map((entry) {
                    final account = entry.value;

                    return Column(
                      children: [
                        _buildEditAccountTile(
                          icon: account['icon'] ?? Icons.account_balance,
                          title: account['title'] ?? '',
                          subtitle: account['subtitle'] ?? '',
                          canDelete: account['canDelete'] ?? true,
                          onDelete: () {
                            Navigator.pop(context);
                            PixRemoveContactSheet.showSheet(
                              context,
                              contactName: account['title'] ?? '',
                              title:
                                  'Remover ${account['title']} de sua lista de contatos?',
                              onConfirm: () {
                                widget.onRemoveAccount?.call(account);
                              },
                            );
                          },
                        ),
                      ],
                    );
                  }).toList(),
                ),

                const SizedBox(height: 32),

                // Botão Cancelar
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Cancelar',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF343A40),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditAccountTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool canDelete,
    required VoidCallback onDelete,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          // Ícone
          SizedBox(
            width: 40,
            height: 40,

            child: Icon(icon, color: const Color(0xFF343A40), size: 20),
          ),
          const SizedBox(width: 16),

          // Textos
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),

          // Ícone de deletar sempre visível no modo de edição
          InkWell(
            onTap: onDelete,
            borderRadius: BorderRadius.circular(20),
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.delete_outline,
                color: Colors.red.shade400,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({super.key, required this.title, required this.subtitle});

  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      spacing: 22,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(),
        ),
      ],
    );
  }
}

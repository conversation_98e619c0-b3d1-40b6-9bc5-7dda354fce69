import 'package:flutter/material.dart';

class PixKeyInputWidget extends StatelessWidget {
  const PixKeyInputWidget({
    super.key,
    required this.controller,
    this.hintText = 'Digite a chave pix',
    this.keyboardType = TextInputType.text,
  });

  final TextEditingController controller;
  final String hintText;
  final TextInputType keyboardType;

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
        fillColor: Colors.white,
        filled: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      style: Theme.of(
        context,
      ).textTheme.bodyMedium?.copyWith(color: Colors.black),
      keyboardType: keyboardType,
    );
  }
}

import 'package:flutter/material.dart';
// import 'package:siclosbank/app/modules/transaction/presenter/widget/image_avatar.dart';
// import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
// import 'package:siclosbank/app/shared/navigation/named_routes.dart';
// import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
// import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../pix_with_key_page.dart';
// import '../sheets/sheets.dart';

class PixKeyResultWidget extends StatelessWidget {
  const PixKeyResultWidget({
    super.key,
    this.userInfo,
    // this.contacts = const [],
    this.onContactTap,
    this.onManualPixTap,
    this.onContinuePressed,
  });

  final PixUserInfo? userInfo;
  // final List<String> contacts;
  final Function(String)? onContactTap;
  final VoidCallback? onManualPixTap;
  final VoidCallback? onContinuePressed;

  @override
  Widget build(BuildContext context) {
    if (userInfo != null) {
      return _buildUserInfo(context);
    }

    return _buildDefaultContent(context);
  }

  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          elevation: 0,
          color: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Nome do usuário
                Text(
                  userInfo!.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    const Text(
                      'CPF',
                      style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
                    ),
                    const Spacer(),
                    Text(
                      userInfo!.cpf,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF343A40),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                Row(
                  children: [
                    const Text(
                      'Instituição',
                      style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
                    ),
                    const Spacer(),
                    Text(
                      userInfo!.institution,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF343A40),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                Row(
                  children: [
                    const Text(
                      'Chave pix',
                      style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
                    ),
                    const Spacer(),
                    Expanded(
                      child: Text(
                        userInfo!.pixKey,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF343A40),
                        ),
                        textAlign: TextAlign.right,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Item "Pix Manual (Agência e conta)"
        // Card(
        //   shape: RoundedRectangleBorder(
        //     borderRadius: BorderRadius.circular(10),
        //   ),
        //   elevation: 0,
        //   child: ListTile(
        //     leading: const Icon(
        //       Icons.account_balance,
        //       color: Color(0xFF343A40),
        //     ),
        //     title: const Text(
        //       'Pix Manual (Agência e conta)',
        //       style: TextStyle(color: Color(0xFF343A40)),
        //     ),
        //     trailing: const Icon(Icons.chevron_right, color: Color(0x99343A40)),
        //     onTap: onManualPixTap,
        //     shape: RoundedRectangleBorder(
        //       borderRadius: BorderRadius.circular(10),
        //       side: BorderSide(
        //         color: const Color(0xFF343A40).withOpacity(0.6),
        //         width: 0.4,
        //       ),
        //     ),
        //   ),
        // ),

        // const SizedBox(height: 24),

        // Título "Meus contatos"
        // Text(
        //   'Meus contatos',
        //   style: Theme.of(
        //     context,
        //   ).textTheme.titleMedium?.copyWith(color: const Color(0xFF343A40)),
        // ),

        // const SizedBox(height: 20),

        // Lista de contatos
        // ...contacts.map(
        //   (name) => Padding(
        //     padding: const EdgeInsets.only(bottom: 12),
        //     child: Card(
        //       elevation: 1,
        //       shadowColor: Colors.grey.shade300,
        //       surfaceTintColor: Colors.transparent,
        //       shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(10),
        //       ),
        //       child: ListTile(
        //         title: Text(name),
        //         textColor: const Color(0xFF343A40),
        //         trailing: InkWell(
        //           onTap: () {
        //             PixContactOptionsSheet.showSheet(
        //               context,
        //               contactName: name,
        //               onTransfer: () {
        //                 onContactTap?.call(name);
        //               },
        //             );
        //           },
        //           borderRadius: BorderRadius.circular(20),
        //           child: const Padding(
        //             padding: EdgeInsets.all(8.0),
        //             child: Icon(
        //               Icons.more_vert,
        //               color: Color(0x99343A40),
        //               size: 20,
        //             ),
        //           ),
        //         ),
        //         onTap: () => {Navigator.pushNamed(context, Routes.pixTransfer)},
        //         shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(10),
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }

  getSheetUserBank() {}
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/register_key/register_key_bloc.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';
import 'package:siclosbank/app/shared/errors/errors.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';

import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:string_validator/string_validator.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/themes/styles/icons_app.dart';
import '../../../../../shared/utils/fields_utils.dart';
import '../../../models/enums/claim_type_enum.dart';
import '../../../models/enums/key_types_enum.dart';

class RegisterKeyPage extends StatelessWidget {
  const RegisterKeyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final args = Modular.args.data?['args'];
    final keyType = args?['keyType'] as KeyType;
    final keyValue = args?['keyValue'] as String?;

    return BlocProvider<RegisterKeyBloc>(
      create: (_) => Modular.get<RegisterKeyBloc>(),
      child: _NewKeyPage(
        keyType: keyType,
        keyValue: keyValue ?? '',
      ),
    );
  }
}

class _NewKeyPage extends StatefulWidget {
  const _NewKeyPage({
    required this.keyType,
    required this.keyValue,
  });
  final KeyType keyType;
  final String keyValue;

  @override
  State<_NewKeyPage> createState() => __NewKeyPageState();
}

class __NewKeyPageState extends State<_NewKeyPage> {
  KeyType get keyType => widget.keyType;
  String get keyValue => widget.keyValue;

  late TextEditingController textController;
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    textController = TextEditingController();
    if (keyType.isCPF) {
      final cpf = AppSession.getInstance().user!.cpf!;
      textController.text = FieldsUtils.obterCpf(cpf);
    }
    if (keyType.isPhone) {
      textController =
          MaskedTextController(mask: '(00) 00000-0000', text: keyValue);
    }
    if (keyType.isEmail) {
      textController = TextEditingController(text: keyValue);
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final i18n = I18n.of(context)!;
    return BlocConsumer<RegisterKeyBloc, RegisterKeyState>(
      listener: (context, state) {
        if (state is StarClaimSuccess) {
          final type = state.claimType;
          _showSheetStartedPortability(context, i18n, state.claim);
        }
        if (state is RegisterNewKeySuccess) {
          SheetAlertConfirm.showSheet(context,
              title: i18n.registered_key,
              message: i18n.registered_key_message,
              showBtnNegative: false,
              textPositive: i18n.ok, clickPositive: () {
            Navigator.pop(context, true);
            Navigator.pop(context, true);
          });
        }
        if (state is RegisterKeyError) {
          if (state.error is PixKeyUsed) {
            // chave já cadastrada
            SheetAlertConfirm.showSheet(context,
                title: i18n.error_key_already_registered_title(
                    keyType.description.toLowerCase()),
                message: i18n.error_key_already_registered_message(
                    keyType.description.toLowerCase()),
                showBtnNegative: false,
                textPositive: i18n.fechar, clickPositive: () {
              Navigator.pop(context);
              Navigator.pop(context);
            });
          } else if (state.error is PixKeyUsedOtherAccount) {
            // SOLICITAR PORTABILIDADE
            SheetAlertConfirm.showSheet(
              context,
              title: i18n.confirm_change,
              message: i18n.error_key_used_other_account_message(
                  keyType.description.toLowerCase()),
              showBtnNegative: true,
              textPositive: i18n.start_portability,
              clickPositive: () {
                BlocProvider.of<RegisterKeyBloc>(context).add(
                  StartClaim(
                    keyType: keyType,
                    claimType: ClaimType.PORTABILITY,
                    key: keyValue,
                  ),
                );
                pop();
              },
              textNegative: i18n.cancelar,
              clickNegative: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
            );
          } else if (state.error is PixKeyUsedOtherUser) {
            // SOLICITAR REIVINDICAO
            if (keyType.isPhone) {
              SheetAlertConfirm.showSheet(
                context,
                title: i18n.confirm_change,
                message: i18n.error_key_used_other_user_message(
                    keyType.description.toLowerCase()),
                showBtnNegative: true,
                textPositive: i18n.start_claim,
                clickPositive: () {
                  BlocProvider.of<RegisterKeyBloc>(context).add(
                    StartClaim(
                      keyType: keyType,
                      claimType: ClaimType.OWNERSHIP,
                      key: keyValue,
                    ),
                  );
                  pop();
                },
                textNegative: i18n.cancelar,
                clickNegative: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
              );
            } else {
              SheetAlertConfirm.showSheet(context,
                  title: i18n.error_key_already_registered_title(
                      keyType.description.toLowerCase()),
                  message: i18n.error_key_already_registered_message(
                      keyType.description.toLowerCase()),
                  showBtnNegative: false,
                  textPositive: i18n.fechar, clickPositive: () {
                Navigator.pop(context);
                Navigator.pop(context);
              });
            }
          } else if (state.error is PixKeyAlreadyClaim) {
            // CHAVE JÁ EM PROCESSO DE PORTABILIDADE/REIVINDICAÇÃO
            SheetAlertConfirm.showSheet(context,
                title: i18n.error_registered_key,
                message: i18n.error_registered_key_message,
                showBtnNegative: false,
                textPositive: i18n.ok, clickPositive: () {
              Navigator.pop(context);
              Navigator.pop(context);
            });
          } else {
            SheetAlertConfirm.showSheet(context,
                title: i18n.error_registered_key,
                message: i18n.error_registered_key_message,
                showBtnNegative: false,
                textPositive: i18n.ok, clickPositive: () {
              Navigator.pop(context);
              Navigator.pop(context);
            });
          }
        }
      },
      builder: (context, state) {
        final isLoading = state is RegisterKeyLoading;
        return Scaffold(
          appBar: AppBarApp(
            title: i18n.new_key.toUpperCase(),
            showBack: !isLoading,
            clickBack: pop,
          ),
          body: Form(
            key: formKey,
            child: Padding(
              padding: const EdgeInsets.all(18),
              child: CustomScrollView(slivers: [
                SliverToBoxAdapter(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  spacing: 28,
                  children: [
                    Text(
                      i18n.register_new_key_pix,
                      style: textTheme.bodyLarge!
                          .copyWith(fontWeight: FontWeight.normal),
                    ),
                    keyLabel(textTheme, keyType),
                    fieldKeyText(textTheme)
                  ],
                )),
                SliverFillRemaining(
                  child: Container(
                    padding: const EdgeInsets.only(
                      top: 20,
                      bottom: 20,
                    ),
                    alignment: Alignment.bottomCenter,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      spacing: 24,
                      children: [
                        AlertBanner(
                          isShow: true,
                          typeAlertBanner: TypeAlertBanner.info,
                          message: i18n.register_new_key_alert_txt,
                        ),
                        ButtonApp(
                          width: double.infinity,
                          text: i18n.register,
                          progress:
                              isLoading ? Utils.circularProgressButton() : null,
                          onPress: () {
                            if (!keyType.isEvp) {
                              if (formKey.currentState!.validate()) {
                                BlocProvider.of<RegisterKeyBloc>(context)
                                    .add(RegisterNewKey(
                                  keyType: keyType,
                                  key: textController.text,
                                ));
                              }
                            } else {
                              BlocProvider.of<RegisterKeyBloc>(context)
                                  .add(RegisterNewKey(
                                keyType: keyType,
                              ));
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                )
              ]),
            ),
          ),
        );
      },
    );
  }

  void _showSheetStartedPortability(
      BuildContext context, I18n i18n, ClaimRequestedModel claim) {
    final date = DateTime.now().add(const Duration(days: 7));
    SheetAlertConfirm.showSheet(context,
        title: claim.claimType.isPortability
            ? i18n.started_portability
            : i18n.started_claim,
        message: claim.claimType.isPortability
            ? i18n.started_portability_message(
                originBank:
                    Utils.getBankName(claim.donorParticipant, shortName: true),
                date: Utils.formatDateTimeToBr(date))
            : i18n.started_claim_message,
        showBtnNegative: false,
        textPositive: i18n.ok, clickPositive: () {
      Navigator.pop(context, true);
      Navigator.pop(context, true);
    });
  }

  Widget fieldKeyText(TextTheme textTheme) {
    if (keyType.isEvp) {
      return const SizedBox();
    }
    Widget widget = switch (keyType) {
      KeyType.CPF => Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Text(
            textController.text,
            style: textTheme.titleMedium,
          ),
        ),
      KeyType.PHONE => Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Text(
            textController.text,
            style: textTheme.titleMedium,
          ),
        ),
      KeyType.EMAIL => Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Text(
            textController.text,
            style: textTheme.titleMedium,
          ),
        ),
      _ => const SizedBox(),
    };

    return widget;
  }

  Row keyLabel(TextTheme textTheme, KeyType keyType) {
    return Row(
      children: [
        const SizedBox(width: 12),
        _getIcon(keyType),
        const SizedBox(width: 10),
        Text(
          keyType.description.toUpperCase(),
          style: textTheme.bodyLarge?.copyWith(color: ColorsApp.cinzaSolo),
          overflow: TextOverflow.fade,
        ),
      ],
    );
  }

  Widget _getIcon(KeyType keyType) {
    return switch (keyType) {
      KeyType.CPF => IconsApp.idCard(),
      KeyType.PHONE => const Icon(Icons.phone_iphone),
      KeyType.EMAIL => const Icon(Icons.mail_outline_rounded),
      _ => IconsApp.icKeyBlack(height: 22),
    };
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/new_kew/input_new_key/page1_input_key_view.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/new_kew/input_new_key/page2_verification_code_view.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/navigation/named_routes.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../models/enums/key_types_enum.dart';
import '../../../blocs/input_new_key/input_new_key_bloc.dart';

class InputNewKeyPageView extends StatelessWidget {
  const InputNewKeyPageView({super.key});

  @override
  Widget build(BuildContext context) {
    final args = Modular.args.data?['args'];
    final keyType = args?['keyType'] as KeyType;

    return BlocProvider<InputNewKeyBloc>(
      create: (_) => Modular.get<InputNewKeyBloc>(),
      child: _InputNewKeyPage(
        keyType: keyType,
      ),
    );
  }
}

class _InputNewKeyPage extends StatefulWidget {
  const _InputNewKeyPage({
    required this.keyType,
  });
  final KeyType keyType;

  @override
  State<_InputNewKeyPage> createState() => _InputNewKeyPageState();
}

class _InputNewKeyPageState extends State<_InputNewKeyPage> {
  KeyType get keyType => widget.keyType;

  late TextEditingController textController;
  final formKey = GlobalKey<FormState>();
  late PageController pageController;
  bool trySaveForm = false;

  @override
  void initState() {
    pageController = PageController();
    if (keyType.isPhone) {
      textController = MaskedTextController(mask: '(00) 00000-0000');
    } else {
      textController = TextEditingController();
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final i18n = I18n.of(context)!;
    return BlocConsumer<InputNewKeyBloc, InputNewKeyState>(
        listener: (context, state) async {
      if (state is InputNewKeyError) {
        DialogUtils.showSnackError(context, state.error);
      }
      if (state is InputNewKeySendCodeSucces) {
        if (pageController.page != 1) {
          // verificacao para evitar bug quando usuario reenviar codigo da page2
          pageController.jumpToPage(1);
        }
      }
      if (state is InputNewKeySucces) {
        final isNewKeyCreated = await push(Routes.newKey,
            args: {'keyType': keyType, 'keyValue': state.keyValue});
        pop(isNewKeyCreated);
      }
    }, builder: (context, state) {
      final isLoading = state is InputNewKeyLoading;
      return Scaffold(
          appBar: AppBarApp(
            title: i18n.new_key.toUpperCase(),
            showBack: !isLoading,
            clickBack: pop,
          ),
          body: PageView(
            controller: pageController,
            children: [
              InputKeyView(keyType: keyType),
              VerificationCodeView(keyType: keyType)
            ],
          ));
    });
  }
}

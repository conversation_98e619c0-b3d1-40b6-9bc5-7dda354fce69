import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_reenviar_codigo.dart';
import '../../../../../../shared/presenter/view/components/others/pin_code_custom.dart';
import '../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../../models/enums/key_types_enum.dart';
import '../../../blocs/input_new_key/input_new_key_bloc.dart';

class VerificationCodeView extends StatelessWidget {
  const VerificationCodeView({
    super.key,
    required this.keyType,
  });

  final KeyType keyType;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<InputNewKeyBloc>(context),
      child: _VerificationCodeView(
        keyType: keyType,
      ),
    );
  }
}

class _VerificationCodeView extends StatefulWidget {
  const _VerificationCodeView({
    super.key,
    required this.keyType,
  });
  final KeyType keyType;

  @override
  State<_VerificationCodeView> createState() => __VerificationCodeViewState();
}

class __VerificationCodeViewState extends State<_VerificationCodeView> {
  KeyType get keyType => widget.keyType;

  final codePinController = TextEditingController();
  late StreamController<ErrorAnimationType> errorController;
  late CountdownController controller;
  bool enableButtomResend = false;
  bool enableButtomContinue = false;
  bool hasError = false;

  void _iniciaContador() {
    errorController = StreamController<ErrorAnimationType>();
    controller = CountdownController(autoStart: true);
  }

  @override
  void dispose() {
    errorController.close();
    super.dispose();
  }

  @override
  void initState() {
    _iniciaContador();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final i18n = I18n.of(context)!;

    return Scaffold(
      body: BlocConsumer<InputNewKeyBloc, InputNewKeyState>(
        listener: (context, state) {
          // TODO: implement listener
        },
        builder: (context, state) {
          return Container(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 32),
            child: CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Column(
                    children: <Widget>[
                      Text(
                        i18n.confirmation_phone_email_message(
                            keyType.description.toLowerCase()),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Container(
                        margin: const EdgeInsets.only(
                            left: 32, right: 32, top: 64, bottom: 32),
                        child: PinCodeAppCustom(
                          autoFocus: true,
                          autoDisposeControllers: false,
                          controller: codePinController,
                          onChanged: (value) {
                            setState(() {
                              enableButtomContinue = (value.length == 6);
                              hasError = false;
                            });
                          },
                          length: 6,
                          obsecureText: false,
                          animationType: AnimationType.fade,
                          textStyle:
                              textTheme.bodyLarge!.copyWith(fontSize: 24),
                          animationDuration: const Duration(milliseconds: 300),
                          pinTheme: PinTheme(
                            activeColor:
                                hasError ? Colors.red : ColorsApp.cinza[500],
                            shape: PinCodeFieldShape.underline,
                            inactiveColor: ColorsApp.cinza[500],
                            selectedColor: ColorsApp.cinza[500],
                            fieldHeight: 50,
                            fieldWidth: 32,
                          ),
                          backgroundColor: Colors.transparent,
                          textInputType: TextInputType.number,
                          errorAnimationController: errorController,
                        ),
                      ),
                      state is InputNewKeyLoading
                          ? Container(
                              child: Utils.circularProgressButton(size: 20),
                            )
                          : !enableButtomResend
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Text(
                                      i18n.reenviar_codigo_sms_timer,
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelLarge,
                                    ),
                                    const SizedBox(width: 4),
                                    Countdown(
                                      controller: controller,
                                      seconds: 60,
                                      build: (_, double time) {
                                        return Text(
                                          time == 60
                                              ? '1:00'
                                              : '0:${time.round().toString().padLeft(2, '0')}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelLarge,
                                        );
                                      },
                                      interval: const Duration(seconds: 1),
                                      onFinished: () {
                                        // controller.restart();
                                        setState(() {
                                          enableButtomResend = true;
                                        });
                                      },
                                    ),
                                  ],
                                )
                              : ButtonResendCode(
                                  onTap: () {
                                    final inputKey =
                                        BlocProvider.of<InputNewKeyBloc>(
                                                context)
                                            .keyValue;
                                    BlocProvider.of<InputNewKeyBloc>(context)
                                        .add(keyType.isEmail
                                            ? VerifyEmailEvent(
                                                inputEmail: inputKey!)
                                            : VerifyPhoneEvent(
                                                inputPhone: inputKey!));

                                    // controller.restart();
                                    _iniciaContador();
                                    setState(() {
                                      enableButtomResend = false;
                                    });
                                  },
                                )
                    ],
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  fillOverscroll: false,
                  child: Container(
                    alignment: Alignment.bottomCenter,
                    padding: const EdgeInsets.only(bottom: 16),
                    child: ButtonApp(
                      enabled: enableButtomContinue,
                      width: MediaQuery.of(context).size.width,
                      text: i18n.continuar,
                      progress: state is InputNewKeyLoading
                          ? Utils.circularProgressButton(size: 20)
                          : null,
                      onPress: () {
                        BlocProvider.of<InputNewKeyBloc>(context).add(
                            keyType.isEmail
                                ? CheckVerificationCodeEmailEvent(
                                    code: codePinController.text.trim())
                                : CheckVerificationCodePhoneEvent(
                                    code: codePinController.text.trim()));
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';

import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:string_validator/string_validator.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/utils/fields_utils.dart';
import '../../../../models/enums/key_types_enum.dart';
import '../../../blocs/input_new_key/input_new_key_bloc.dart';

class InputKeyView extends StatelessWidget {
  const InputKeyView({super.key, required this.keyType});
  final KeyType keyType;
  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<InputNewKeyBloc>(context),
      child: _InputNewKeyPage(
        keyType: keyType,
      ),
    );
  }
}

class _InputNewKeyPage extends StatefulWidget {
  const _InputNewKeyPage({
    required this.keyType,
  });
  final KeyType keyType;

  @override
  State<_InputNewKeyPage> createState() => _InputNewKeyPageState();
}

class _InputNewKeyPageState extends State<_InputNewKeyPage> {
  KeyType get keyType => widget.keyType;

  late TextEditingController textController;
  final formKey = GlobalKey<FormState>();

  bool trySaveForm = false;

  @override
  void initState() {
    if (keyType.isPhone) {
      textController = MaskedTextController(mask: '(00) 00000-0000');
    } else {
      textController = TextEditingController();
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final i18n = I18n.of(context)!;
    return BlocConsumer<InputNewKeyBloc, InputNewKeyState>(
      listener: (context, state) {},
      builder: (context, state) {
        final isLoading = state is InputNewKeyLoading;
        return Scaffold(
          body: Form(
            key: formKey,
            autovalidateMode: trySaveForm
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Padding(
              padding: const EdgeInsets.all(18),
              child: CustomScrollView(
                  // physics: const ClampingScrollPhysics(),
                  slivers: [
                    SliverToBoxAdapter(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      spacing: 8,
                      children: [
                        const SizedBox(height: 16),
                        // Text(
                        //   i18n.register_new_key_pix,
                        //   style: textTheme.titleMedium,
                        // ),
                        // keyLabel(textTheme, keyType),
                        fieldKeyText(textTheme)
                      ],
                    )),
                    SliverFillRemaining(
                      child: Container(
                        padding: const EdgeInsets.only(
                          top: 20,
                          bottom: 20,
                        ),
                        alignment: Alignment.bottomCenter,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          spacing: 24,
                          children: [
                            ButtonApp(
                              width: double.infinity,
                              text: i18n.continuar,
                              progress: isLoading
                                  ? Utils.circularProgressButton()
                                  : null,
                              onPress: () {
                                FocusScope.of(context).unfocus();
                                formKey.currentState!.save();
                                setState(() => trySaveForm = true);
                                if (formKey.currentState!.validate()) {
                                  if (keyType.isPhone) {
                                    BlocProvider.of<InputNewKeyBloc>(context)
                                        .add(VerifyPhoneEvent(
                                            inputPhone: (textController.text)));
                                  } else if (keyType.isEmail) {
                                    BlocProvider.of<InputNewKeyBloc>(context)
                                        .add(VerifyEmailEvent(
                                            inputEmail: textController.text));
                                  }
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    )
                  ]),
            ),
          ),
        );
      },
    );
  }

  Widget fieldKeyText(TextTheme textTheme) {
    if (keyType.isEvp) {
      return const SizedBox();
    }
    Widget widget = switch (keyType) {
      KeyType.PHONE => Column(
          spacing: 18,
          children: [
            const Text(
                'Insira seu número de telefone para fazer o cadastro da chave pix.'),
            TextFormFieldApp(
              controller: textController,
              hint: '(00) 00000-0000',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return const I18n().campo_obrigatorio;
                }

                if (value.length < 15) {
                  return const I18n().telefone_celular_invalido;
                }

                return null;
              },
            ),
          ],
        ),
      KeyType.EMAIL => Column(
          spacing: 18,
          children: [
            const Text('Insira seu email para fazer o cadastro da chave pix.'),
            TextFormFieldApp(
              controller: textController,
              hint: '<EMAIL>',
              formatter: TextInputFormatter.withFunction((oldValue, newValue) {
                var text =
                    newValue.text.toLowerCase().trim().replaceAll(' ', '');
                return newValue.copyWith(
                  text: text,
                  selection: TextSelection.collapsed(offset: text.length),
                );
              }),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return const I18n().campo_obrigatorio;
                }

                if (!isEmail(value)) {
                  return const I18n().email_invalido;
                }

                return null;
              },
            ),
          ],
        ),
      _ => const SizedBox(),
    };

    return widget;
  }
}

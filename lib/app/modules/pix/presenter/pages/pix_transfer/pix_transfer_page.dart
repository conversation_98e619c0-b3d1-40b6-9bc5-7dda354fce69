import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/pin/presenter/view/check_pin_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/transfer_pix/transfer_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/tariff_pix/tariff_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/models/transfer_pix_model.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import '../pix_with_key/pix_with_key_page.dart';

class PixTransferPage extends StatelessWidget {
  const PixTransferPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<TransferPixBloc>(
          create: (context) => Modular.get<TransferPixBloc>(),
        ),
        BlocProvider<TariffPixBloc>(
          create: (context) =>
              Modular.get<TariffPixBloc>()..add(GetTariffPixEvent()),
        ),
      ],
      child: const _PixTransferPage(),
    );
  }
}

class _PixTransferPage extends StatefulWidget {
  const _PixTransferPage();

  @override
  State<_PixTransferPage> createState() => _PixTransferPageState();
}

class _PixTransferPageState extends State<_PixTransferPage> {
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  late bool isAvaliableTransferPix = true;
  PixUserInfo? userInfo;
  bool isScheduled = false;
  bool addToFavorites = false;
  bool isTransferring = false; // Controla o loading da transferência
  int tariffRetryCount = 0; // Controla tentativas de reload de tarifa
  static const int maxTariffRetries = 2; // Máximo de tentativas

  final String userAccount =
      AppSession.getInstance().bankAccount?.accountNumber ?? '';

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is PixUserInfo) {
      userInfo = args;
    }
  }

  @override
  void dispose() {
    _valueController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TransferPixBloc, TransferPixState>(
      listener: (context, state) {
        if (state is TransferPixError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erro na transferência: ${state.error.message}'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            isAvaliableTransferPix = true;
          });
        }
      },
      builder: (context, state) {
        final isLoading = state is TransferPixLoading || isTransferring;

        return Scaffold(
          backgroundColor: Colors.grey.shade50,
          appBar: AppBarApp(title: 'PIX'),
          body: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informações pessoais',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: const Color(0xFF343A40),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),

                    if (userInfo != null) _buildUserInfoCard(),

                    if (isAvaliableTransferPix)
                      _buildAvaliableTransferPix(
                        widgetDescriptionInput: _buildDescriptionSection(),
                        onContinuePressed: isLoading
                            ? () {}
                            : _onContinuePressed,
                      ),

                    if (!isAvaliableTransferPix)
                      _buildConfirmTransferPix(
                        widgetDescriptionInput: _buildDescriptionSection(),
                        openVerificationScreen: isLoading
                            ? ({required bool isEmail}) async {}
                            : _openVerificationScreen,
                      ),
                  ],
                ),
              ),

              // Overlay de loading quando está transferindo
              if (isTransferring)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Processando transferência PIX...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.only(top: 16, bottom: 16),
      child: Column(
        children: [
          _buildInfoRow('Nome', userInfo!.name),
          const SizedBox(height: 8),
          _buildInfoRow('CPF', userInfo!.cpf),
          const SizedBox(height: 8),
          _buildInfoRow('Instituição', userInfo!.institution),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 14, color: Color(0xFF343A40)),
        ),
        const Spacer(),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF343A40),
          ),
        ),
      ],
    );
  }

  Widget _buildTarifaSection() {
    return BlocBuilder<TariffPixBloc, TariffPixState>(
      builder: (context, state) {
        if (state is TariffPixLoading) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tarifa',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: const Color(0xFF343A40),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 18),
              const Center(child: CircularProgressIndicator()),
            ],
          );
        }

        if (state is TariffPixError) {
          // Em caso de erro, carrega valores padrão silenciosamente
          // e tenta recarregar em background (com limite de tentativas)
          if (tariffRetryCount < maxTariffRetries) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              tariffRetryCount++;
              print(
                'TariffPixBloc: Tentativa $tariffRetryCount de $maxTariffRetries',
              );
              BlocProvider.of<TariffPixBloc>(context).add(GetTariffPixEvent());
            });
          } else {
            print(
              'TariffPixBloc: Limite de tentativas atingido, usando valores padrão',
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tarifa',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: const Color(0xFF343A40),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 18),
              _buildDefaultTariffRows(),
            ],
          );
        }

        if (state is TariffPixSuccess) {
          final pixTariff = state.response.getTariffByServiceName('pix');

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tarifa',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: const Color(0xFF343A40),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 18),

              // PIX para contas Siclos (sempre gratuito)
              Row(
                children: [
                  const Text(
                    'PIX para contas Siclos',
                    style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
                  ),
                  const Spacer(),
                  const Text(
                    'R\$ 0,00',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF343A40),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // PIX para outros bancos (dados da API)
              Row(
                children: [
                  const Text(
                    'PIX para outros bancos',
                    style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
                  ),
                  const Spacer(),
                  Text(
                    'R\$ ${pixTariff?.pricePerExceedingOp}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF343A40),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              Row(
                children: [
                  Text(
                    '*Isenção em ${pixTariff?.exemptQuantity} envios mensais  ',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '/ envio',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tarifa',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: const Color(0xFF343A40),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 18),
            _buildDefaultTariffRows(),
          ],
        );
      },
    );
  }

  Widget _buildDefaultTariffRows() {
    return Column(
      children: [
        Row(
          children: [
            const Text(
              'PIX para contas Siclos',
              style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
            ),
            const Spacer(),
            const Text(
              'R\$ 0,00',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF343A40),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            const Text(
              'PIX para outros bancos',
              style: TextStyle(fontSize: 14, color: Color(0xFF343A40)),
            ),
            const Spacer(),
            const Text(
              'R\$ 1,00',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF343A40),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Text(
              '*Isenção em 30 envios mensais  ',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Text(
              '/ envio',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _onContinuePressed() {
    final value = _valueController.text.trim();

    if (value.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Por favor, insira o valor da transferência'),
        ),
      );
      return;
    }

    final amount = double.tryParse(value.replaceAll(',', '.'));
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Valor inválido. Por favor, insira um valor válido.'),
        ),
      );
      return;
    }

    if (userInfo == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erro: informações do destinatário não encontradas.'),
        ),
      );
      return;
    }

    setState(() {
      isAvaliableTransferPix = false;
    });
  }

  _openVerificationScreen({required bool isEmail}) async {
    CheckPinPage.showSheet(context, () async {
      // Ativa o loading da transferência
      setState(() {
        isTransferring = true;
      });

      // Fecha o modal do PIN imediatamente após ativar o loading
      Navigator.pop(context);

      final transferModel = TransferPixModel(
        amount: double.parse(_valueController.text.trim()),
        debitParty: DebitPartyModel(account: userAccount),
        creditParty: CreditPartyModel.fromPixKey(
          key: userInfo!.pixKey,
          name: userInfo!.name,
          bank: userInfo!.bankCode,
        ),
        remittanceInformation: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      final bloc = BlocProvider.of<TransferPixBloc>(context);
      bloc.add(TransferPixSubmit(transferModel));

      await for (final state in bloc.stream) {
        if (state is TransferPixSuccess) {
          if (mounted) {
            // Navega para a página de confirmação PIX
            Navigator.pushNamed(
              context,
              Routes.pixConfirmation,
              arguments: {'transactionId': state.response.id},
            ).then((_) {
              // Desativa o loading após navegar
              if (mounted) {
                setState(() {
                  isTransferring = false;
                });
              }
            });
          }
          break;
        } else if (state is TransferPixError) {
          if (mounted) {
            // Desativa o loading em caso de erro
            setState(() {
              isTransferring = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erro na transferência: ${state.error.message}'),
                backgroundColor: Colors.red,
              ),
            );
          }
          break;
        }
      }
    }, autoClose: false);
  }

  Widget _buildValueSection() {
    return Container(
      padding: const EdgeInsets.only(top: 16, bottom: 16),
      child: Column(
        children: [
          Divider(color: Colors.grey.shade300, thickness: 1, height: 16),
          const SizedBox(height: 24),
          _buildInfoRow('Valor', 'R\$ ${_valueController.text.trim()}'),
          const SizedBox(height: 8),
          _buildInfoRow('Tarifa', 'R\$ 1,00'),
          const SizedBox(height: 24),
          Divider(color: Colors.grey.shade300, thickness: 1, height: 16),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Container(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,

        children: [
          Text(
            'Descrição (opcional)',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: const Color(0xFF343A40),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),

          TextField(
            controller: _descriptionController,
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              hintText: 'Descrição',
              hintStyle: TextStyle(color: Colors.grey.shade400, fontSize: 16),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                ),
              ),
            ),
            maxLines: 3,
            style: const TextStyle(fontSize: 16, color: Color(0xFF343A40)),
          ),
        ],
      ),
    );
  }

  Widget _buildInputValueTransfer(
    String label,
    TextEditingController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quanto você deseja transferir?',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: const Color(0xFF343A40),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.white,
            hintText: 'R\$ 0,00',
            hintStyle: TextStyle(color: Colors.grey.shade400, fontSize: 16),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 10,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
          ),
          style: const TextStyle(fontSize: 16, color: Color(0xFF343A40)),
        ),
        const SizedBox(height: 14),
        Row(
          children: [
            Text(
              'Saldo disponível: ',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'R\$ ${AppSession.getInstance().balance}',
              style: TextStyle(
                color: Colors.green.shade600,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAvaliableTransferPix({
    required Widget widgetDescriptionInput,
    onContinuePressed,
  }) {
    return Column(
      children: [
        Divider(color: Colors.grey.shade300, thickness: 1, height: 16),
        const SizedBox(height: 24),
        _buildInputValueTransfer(
          'Quanto você deseja transferir?',
          _valueController,
        ),
        const SizedBox(height: 24),

        Divider(color: Colors.grey.shade300, thickness: 1, height: 16),

        const SizedBox(height: 24),

        widgetDescriptionInput,

        Divider(color: Colors.grey.shade300, thickness: 1, height: 16),

        const SizedBox(height: 24),

        _buildTarifaSection(),

        const SizedBox(height: 24),

        ButtonApp(
          text: 'Continuar',
          onPress: () {
            _onContinuePressed();
          },
          width: double.infinity,
        ),
      ],
    );
  }

  Widget _buildConfirmTransferPix({
    required Widget widgetDescriptionInput,
    required Function({required bool isEmail}) openVerificationScreen,
  }) {
    return Column(
      children: [
        _buildValueSection(),
        widgetDescriptionInput,

        // _buildButtonSwitch(),
        const SizedBox(height: 100),
        ButtonApp(
          text: 'Concluir',
          onPress: () {
            openVerificationScreen(isEmail: false);
          },
          width: double.infinity,
        ),
        const SizedBox(height: 16),
        ButtonApp(
          border: 1,
          text: const I18n().cancelar,
          buttonColor: Colors.transparent,
          width: double.infinity,
          borderColor: Color(0xFF343A40),
          onPress: () async {
            setState(() {
              isAvaliableTransferPix = true;
            });
          },
        ),
      ],
    );
  }
}

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_home/onboarding_home_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_home/onboarding_home_events.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/onboarding/onboarding_pageview.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/navigation/named_routes.dart';
import '../onboarding/onboarding_widget.dart';

class PixOnboardingPage extends StatelessWidget {
  const PixOnboardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<OnboardingHomeBloc>(
        create: (context) => Modular.get<OnboardingHomeBloc>(),
        child: const _OnboardingHomePixPage());
  }
}

class _OnboardingHomePixPage extends StatefulWidget {
  const _OnboardingHomePixPage({super.key});

  @override
  State<_OnboardingHomePixPage> createState() => __OnboardingHomePixPageState();
}

class __OnboardingHomePixPageState extends State<_OnboardingHomePixPage> {
  @override
  Widget build(BuildContext context) {
    return OnboardingPageview(
      pages: [
        OnboardingWidget(
          icon: IconsApp.icPix(),
          title: I18n.of(context)!.title_onboarding_pix_1,
          description: I18n.of(context)!.description_onboarding_pix_1,
        ),
        OnboardingWidget(
          icon: IconsApp.icQrCode(color: Colors.black),
          title: I18n.of(context)!.title_onboarding_pix_2,
          description: I18n.of(context)!.description_onboarding_pix_2,
        ),
        OnboardingWidget(
          icon: IconsApp.icCheckConfirm(color: Colors.black),
          title: I18n.of(context)!.title_onboarding_pix_3,
          description: I18n.of(context)!.description_onboarding_pix_3,
        ),
      ],
      nextRoute: Routes.pix,
      onFinishView: () => BlocProvider.of<OnboardingHomeBloc>(context)
          .add(OnboardingHomeSaveView()),
    );
  }
}

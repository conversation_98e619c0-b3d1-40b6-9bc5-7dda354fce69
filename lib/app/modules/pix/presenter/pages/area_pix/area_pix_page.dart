import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/my_keys/pix_my_keys_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/grid_buttons_pix_home.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/tile_button.dart';
import 'package:siclosbank/app/shared/config/flavor.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../app_controller.dart';
import '../../../../../shared/utils/utils.dart';

class AreaPixPage extends StatelessWidget {
  const AreaPixPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<MyKeysBloc>(),
      child: const _AreaApixPage(),
    );
  }
}

class _AreaApixPage extends StatefulWidget {
  const _AreaApixPage({super.key});

  @override
  State<_AreaApixPage> createState() => __AreaApixPageState();
}

class __AreaApixPageState extends State<_AreaApixPage> {
  @override
  void initState() {
    BlocProvider.of<MyKeysBloc>(context).add(FetchMyKeys());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.area_pix.toUpperCase(),
        actions: [Utils.buttonIcHelp(context)],
      ),
      body: BlocListener<MyKeysBloc, MyKeysState>(
        listener: (context, state) {
          if (state is MyKeysError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        child: SingleChildScrollView(
          child: Column(
            spacing: 16,
            children: [
              const SizedBox(height: 32),
              // ocultar em staging e producao
              if (isHomologOrDev)
                const Padding(
                  padding: EdgeInsets.only(bottom: 28),
                  child: GridButtonsPixHome(),
                ),
              TileButton(
                label: I18n.of(context)!.my_keys,
                icon: IconsApp.icKeyGreen(height: 46),
                onTap: () {
                  final account =
                      AppSession.getInstance().bankAccount?.accountNumber ?? '';
                  if (account.isNotEmpty) {
                    final view =
                        AppSession.getInstance().user!.viewOnboardingKeysPix ==
                        true;
                    if (!view) {
                      push(
                        Routes.onboardingMyKeys,
                        args: BlocProvider.of<MyKeysBloc>(context),
                      );
                    } else {
                      push(
                        Routes.pixMyKeys,
                        args: BlocProvider.of<MyKeysBloc>(context),
                      );
                    }
                  } else {
                    SnackBarApp.showSnack(
                      context: context,
                      message:
                          'Ocorreu um erro ao buscar dados. Por favor, tente novamente.',
                      success: false,
                    );
                  }
                },
              ),
              // ocultar em staging e producao
              if (isHomologOrDev)
                TileButton(
                  label: I18n.of(context)!.my_limits_pix,
                  icon: IconsApp.icAdjustmentsPix(height: 46),
                  onTap: () {
                    push(Routes.myLimitsPix);
                  },
                ),
              TileButton(
                label: I18n.of(context)!.questions,
                icon: IconsApp.icHelpPix(height: 46),
                onTap: () {
                  push(Routes.questions);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

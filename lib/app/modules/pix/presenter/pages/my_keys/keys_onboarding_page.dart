import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/onboarding_key/onboarding_key_events.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/onboarding/onboarding_pageview.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/themes/styles/icons_app.dart';
import '../../blocs/onboarding_key/onboarding_key_bloc.dart';
import '../onboarding/onboarding_widget.dart';

class KeysOnboardingPage extends StatelessWidget {
  const KeysOnboardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final args = Modular.args.data['args'];
    return BlocProvider<OnboardingKeyBloc>(
      create: (context) => Modular.get(),
      child: const _OnboardingKeys(),
    );
  }
}

class _OnboardingKeys extends StatefulWidget {
  const _OnboardingKeys({super.key, this.args});
  final dynamic args;
  @override
  State<_OnboardingKeys> createState() => __OnboardingKeysState();
}

class __OnboardingKeysState extends State<_OnboardingKeys> {
  @override
  Widget build(BuildContext context) {
    return OnboardingPageview(
      pages: [
        OnboardingWidget(
          icon: IconsApp.icPix(),
          title: I18n.of(context)!.title_onboarding_keys_1,
          description: I18n.of(context)!.description_onboarding_keys_1,
        ),
        OnboardingWidget(
          icon: IconsApp.icKeyBlack(),
          title: I18n.of(context)!.title_onboarding_keys_2,
          description: I18n.of(context)!.description_onboarding_keys_2,
        ),
        OnboardingWidget(
          icon: IconsApp.icCheckConfirm(color: Colors.black),
          title: I18n.of(context)!.title_onboarding_keys_3,
          description: I18n.of(context)!.description_onboarding_keys_3,
        ),
      ],
      nextRoute: Routes.pixMyKeys,
      args: widget.args,
      onFinishView: () => BlocProvider.of<OnboardingKeyBloc>(
        context,
      ).add(OnboardingKeySaveView()),
    );
  }
}

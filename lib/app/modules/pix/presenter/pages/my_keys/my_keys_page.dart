import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/my_keys/pix_my_keys_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/list_tile_key.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/modal_new_key_pix.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/modal_options_key_pix.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/my_keys/modals/modal_claim_claimer.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/my_keys/modals/modal_claim_donor.dart';
import 'package:siclosbank/app/shared/data/models/pix/claim_requested_model.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/navigation/named_routes.dart';
import '../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../shared/utils/utils.dart';
import '../../../models/enums/key_types_enum.dart';
import 'components/message_error_widget.dart';
import 'modals/modal_confirm_delete_key.dart';

class MyKeysPage extends StatelessWidget {
  const MyKeysPage({super.key});

  @override
  Widget build(BuildContext context) {
    final bloc = Modular.args.data['args'];

    return BlocProvider<MyKeysBloc>.value(value: bloc, child: _MyKeysPage());
  }
}

class _MyKeysPage extends StatefulWidget {
  const _MyKeysPage({super.key});
  // final dynamic bloc;
  @override
  State<_MyKeysPage> createState() => _MyKeysPageState();
}

class _MyKeysPageState extends State<_MyKeysPage> {
  @override
  void initState() {
    myKeysList = BlocProvider.of<MyKeysBloc>(context).myKeys ?? [];
    super.initState();
  }

  List<KeyPixModel> myKeysList = [];

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context)!;
    final textTheme = Theme.of(context).textTheme;
    final bloc = BlocProvider.of<MyKeysBloc>(context);
    return Scaffold(
      appBar: AppBarApp(
        title: i18n.my_keys.toUpperCase(),
        actions: [Utils.buttonIcHelp(context)],
      ),
      body: BlocConsumer<MyKeysBloc, MyKeysState>(
        listener: (context, state) {
          if (state is MyKeysSuccess) {
            setState(() {
              myKeysList = state.myKeysList;
            });
          }
          if (state is MyKeysError) {
            DialogUtils.showSnackError(context, state.error);
          }
          if (state is DeleteKeySuccess) {
            SnackBarApp.showSnack(
              context: context,
              message: i18n.success_delete_key,
              success: true,
            );
            bloc.add(FetchMyKeys());
          }
          if (state is CancelClaimPixSucess) {
            SnackBarApp.showSnack(
              context: context,
              message: i18n.success_cancel_claim(
                state.claim.claimType.description,
              ),
              success: true,
            );

            bloc.add(FetchMyKeys());
            bloc.add(FetchMyClaims());
          }
          if (state is ConfirmClaimPixSuccess) {
            SnackBarApp.showSnack(
              context: context,
              message: i18n.success_confirm_claim(
                state.claim.claimType.description,
              ),
              success: true,
            );
            bloc.add(FetchMyKeys());
            bloc.add(FetchMyClaims());
          }
        },
        builder: (context, state) {
          if (state is MyKeysLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // final myKeysList = myKeysList;
          return RefreshIndicator(
            onRefresh: () {
              bloc.add(FetchMyKeys());
              bloc.add(FetchMyClaims());
              return Future.value();
            },
            child: Padding(
              padding: const EdgeInsets.all(18),
              child: ValueListenableBuilder(
                valueListenable: AppSession.getInstance().listClaimsPixState,
                builder: (context, state, _) {
                  final myClaims = state;
                  // Verifica se a chave já está em uso por uma claim
                  var keysWithoutClaims = myKeysList.where((key) {
                    return !myClaims.any((claim) => claim.key == key.key);
                  }).toList();

                  final totalMyKeys =
                      keysWithoutClaims.length + myClaims.length;

                  return CustomScrollView(
                    physics: const ClampingScrollPhysics(),
                    slivers: [
                      const SliverToBoxAdapter(child: SizedBox(height: 20)),
                      SliverToBoxAdapter(
                        child: myKeysList.isEmpty && myClaims.isEmpty
                            ? MessageErrorWidget(
                                title: i18n.no_keys_found,
                                body: i18n.register_new_key_pix_message,
                              )
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                spacing: 22,
                                children: [
                                  Text(
                                    I18n.of(
                                      context,
                                    )!.n_de_5_chaves(totalMyKeys),
                                    style: textTheme.bodyMedium,
                                  ),
                                  Column(
                                    // spacing: 16,
                                    mainAxisSize: MainAxisSize.min,
                                    children: myKeysList.map((key) {
                                      // Verifica se a chave já está em uso por uma claim
                                      // e não exibe a chave se já estiver em uso
                                      bool isKeyInClaim = myClaims.any(
                                        (claim) => claim.key == key.key,
                                      );

                                      return isKeyInClaim
                                          ? SizedBox()
                                          : Padding(
                                              padding: const EdgeInsets.only(
                                                bottom: 16,
                                              ),
                                              child: ListTileKey(
                                                keyPix: key,
                                                trailing: IconButton(
                                                  icon: const Icon(
                                                    Icons.more_vert,
                                                  ),
                                                  onPressed: () =>
                                                      _moreOptionsKey(
                                                        context,
                                                        i18n,
                                                        key,
                                                      ),
                                                  padding: const EdgeInsets.all(
                                                    0,
                                                  ),
                                                ),
                                              ),
                                            );
                                    }).toList(),
                                  ),
                                ],
                              ),
                      ),
                      SliverToBoxAdapter(
                        child: Column(
                          spacing: 16,
                          mainAxisSize: MainAxisSize.min,
                          children: myClaims
                              .map(
                                (claim) => ListTileKey(
                                  keyPix: KeyPixModel(
                                    keyType: claim.keyType,
                                    key: claim.key,
                                  ),
                                  trailing: InkWell(
                                    onTap: () =>
                                        _moreOptionsClaim(context, i18n, claim),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade300,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              claim.claimType.description,
                                            ),
                                          ),
                                          const Icon(Icons.more_vert),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ),
                      SliverFillRemaining(
                        child: Container(
                          padding: const EdgeInsets.only(top: 20, bottom: 20),
                          alignment: Alignment.bottomCenter,
                          child: Visibility(
                            visible: totalMyKeys < 5,
                            child: ButtonApp(
                              width: double.infinity,
                              text: i18n.new_key,
                              onPress: () async {
                                final result = await ModalNewKeyPix.show(
                                  context,
                                  keyTypesActives: myKeysList
                                      .map((key) => key.keyType)
                                      .toList(),
                                );

                                if (result != null) {
                                  final keyType = result['keyType'] as KeyType;
                                  dynamic isNewKeyCreated;
                                  if (keyType.isEmail || keyType.isPhone) {
                                    // precisam ser informados e verificados
                                    isNewKeyCreated = await push(
                                      Routes.inputNewKey,
                                      args: result,
                                    );
                                  } else {
                                    isNewKeyCreated = await push(
                                      Routes.newKey,
                                      args: result,
                                    );
                                  }

                                  if (isNewKeyCreated == true) {
                                    bloc.add(FetchMyKeys());
                                  }
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  _moreOptionsKey(BuildContext context, i18n, key) async =>
      await ModalOptionsKeyPix.show(
        context,
        key: key.key,
        keyType: key.keyType,
      ).then((deleteKey) {
        if (deleteKey) {
          ModalConfirmDeleteKey.show(
            context,
            key: key,
            keyType: key.keyType,
            confirmDelete: () {
              BlocProvider.of<MyKeysBloc>(context).add(DeleteKey(key.key));
              log('apagando...');
            },
          );
        }
      });

  void _moreOptionsClaim(
    BuildContext context,
    I18n i18n,
    ClaimRequestedModel claim,
  ) {
    if (claim.isClaimerById(AppSession.getInstance().user!.id!)) {
      ModalClaimClaimer.show(
        context,
        claim: claim,
        cancelClaimPortability: () {
          BlocProvider.of<MyKeysBloc>(context).add(CancelClaimPix(claim.id));
        },
      );
    } else {
      ModalClaimDonor.show(
        context,
        claim: claim,
        confirmChange: () {
          BlocProvider.of<MyKeysBloc>(context).add(ConfirmChangePix(claim.id));
        },
        cancelChange: () {
          BlocProvider.of<MyKeysBloc>(context).add(CancelClaimPix(claim.id));
        },
      );
    }
  }
}

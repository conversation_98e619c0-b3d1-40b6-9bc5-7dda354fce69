import 'package:flutter/material.dart';

class MessageErrorWidget extends StatelessWidget {
  const MessageErrorWidget(
      {super.key, required this.title, required this.body});
  final String title;
  final String body;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      spacing: 22,
      children: [
        Text(
          title,
          style: textTheme.titleMedium,
        ),
        Text(
          body,
          style: textTheme.bodyMedium,
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../../pin/presenter/view/check_pin_page.dart';
import '../../../../models/enums/key_types_enum.dart';
import '../../../components/list_tile_key.dart';

abstract class ModalConfirmDeleteKey {
  static Future show(
    context, {
    required KeyPixModel key,
    required KeyType keyType,
    required Function() confirmDelete,
  }) {
    final i18n = I18n.of(context)!;
    return SheetAlertConfirm.showSheet(
      context,
      title: i18n.confirm_delete_key,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 32),
            child: <PERSON><PERSON><PERSON><PERSON><PERSON>(
              keyPix: key,
              showBorder: true,
            ),
          ),
        ],
      ),
      textPositive: i18n.excluir,
      clickPositive: () {
        CheckPinPage.showSheet(context, () {
          confirmDelete();
          Navigator.pop(context);
        });
      },
      textNegative: i18n.cancelar,
      clickNegative: () {
        Navigator.pop(context);
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:extended_masked_text/extended_masked_text.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/data/models/pix/claim_requested_model.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../shared/utils/utils.dart';

abstract class ModalClaimClaimer {
  static Future show(
    context, {
    required ClaimRequestedModel claim,
    required Function() cancelClaimPortability,
  }) {
    final i18n = I18n.of(context)!;
    final controller = MaskedTextController(
        mask: '(00) 00000-0000', text: claim.key.replaceFirst('+55', ''));
    return SheetAlertConfirm.showSheet(
      context,
      dismissible: true,
      title: claim.claimType.isPortability
          ? i18n.started_portability
          : i18n.request_claimer_title,
      child: Column(spacing: 16, children: [
        TextFormFieldApp(
          controller: controller,
          enable: false,
        ),
        Text(claim.claimType.isPortability
                ? i18n.started_portability_message(
                    originBank: Utils.getBankName(claim.donorParticipant,
                        shortName: true),
                    date: Utils.formatDateTimeToBr(claim.resolutionPeriodEnd))
                : i18n.request_claimer_message(
                    Utils.formatDateTimeToBr(claim.resolutionPeriodEnd))
            // Utils.formatDateTimeToBr(claim.completionPeriodEnd)),
            ),
        const SizedBox(height: 16)
      ]),
      textPositive: i18n.fechar,
      clickPositive: () {
        Navigator.pop(context);
      },
      textNegative: claim.claimType.isPortability
          ? i18n.cancel_portability
          : i18n.cancel_claim,
      clickNegative: () {
        cancelClaimPortability();
        Navigator.pop(context);
      },
    );
  }
}

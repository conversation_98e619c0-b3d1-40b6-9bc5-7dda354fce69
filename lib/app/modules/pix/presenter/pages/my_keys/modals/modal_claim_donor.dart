import 'package:flutter/material.dart';
import 'package:extended_masked_text/extended_masked_text.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/data/models/pix/claim_requested_model.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../pin/presenter/view/check_pin_page.dart';

abstract class ModalClaimDonor {
  static Future show(
    context, {
    required ClaimRequestedModel claim,
    required Function() confirmChange,
    required Function() cancelChange,
  }) {
    final i18n = I18n.of(context)!;
    final controller = MaskedTextController(
        mask: '(00) 00000-0000', text: claim.key.replaceFirst('+55', ''));

    return SheetAlertConfirm.showSheet(
      context,
      title: claim.claimType.isPortability
          ? i18n.requested_portability_title
          : i18n.requested_claim_title,
      child: Column(
        spacing: 16,
        children: [
          TextFormFieldApp(
            controller: controller,
            enable: false,
          ),
          Text(
            claim.claimType.isPortability
                ? i18n.requested_portability_message('')
                // Utils.formatDateTimeToBr(claim.completionPeriodEnd))
                : i18n.requested_claim_message(''),
            // Utils.formatDateTimeToBr(claim.completionPeriodEnd));
          ),
          const SizedBox(height: 16),
        ],
      ),
      textPositive: i18n.keep_key_here,
      clickPositive: () {
        cancelChange();
        Navigator.pop(context);
      },
      textNegative: claim.claimType.isPortability
          ? i18n.confirm_requested_portability
          : i18n.confirm_requested_claim,
      clickNegative: () {
        CheckPinPage.showSheet(context, () {
          confirmChange();
          Navigator.pop(context);
        });
      },
    );
  }
}

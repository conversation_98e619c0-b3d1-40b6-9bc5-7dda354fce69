import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/transfer_pix/transfer_pix_bloc.dart';
import 'package:siclosbank/app/modules/pix/models/transaction_sent_pix_response.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/themes/styles/widgets/box_decoration_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class PixReceiptPage extends StatelessWidget {
  final TransactionSentPixResponse? transactionData;

  const PixReceiptPage({super.key, this.transactionData});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TransferPixBloc>(
      create: (context) => Modular.get<TransferPixBloc>(),
      child: _PixReceiptPage(transactionData: transactionData),
    );
  }
}

class _PixReceiptPage extends StatefulWidget {
  final TransactionSentPixResponse? transactionData;

  const _PixReceiptPage({this.transactionData});

  @override
  State<_PixReceiptPage> createState() => _PixReceiptPageState();
}

class _PixReceiptPageState extends State<_PixReceiptPage> {
  String? transactionId;
  var key = GlobalKey();
  bool isSharing = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (widget.transactionData != null) {
      return;
    }

    final args = ModalRoute.of(context)?.settings.arguments;

    if (args is String) {
      transactionId = args;
    } else if (args is Map<String, dynamic>) {
      transactionId = args['transactionId'] as String?;

      final response = args['response'];
      if (response is Map<String, dynamic> && response['id'] != null) {
        transactionId = response['id'].toString();
      }
    }

    if (transactionId != null && transactionId!.isNotEmpty) {
      BlocProvider.of<TransferPixBloc>(
        context,
      ).add(GetTransactionSentPix(transactionId!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TransferPixBloc, TransferPixState>(
      listener: (context, state) {
        if (state is TransferPixError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Erro ao carregar dados da transação: ${state.error.message}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: const Color(0xFF2D2D2D),
          appBar: AppBarApp(
            title: 'PIX',
            actions: [
              IconButton(
                onPressed: _shareReceipt,
                icon: const Icon(Icons.share, color: Colors.black),
              ),
            ],
          ),
          body: _buildReceiptBody(context, state),
        );
      },
    );
  }

  Widget _buildReceiptBody(BuildContext context, TransferPixState state) {
    if (widget.transactionData != null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecorationApp.dropShadowDecoration(
          backgroudColor: const Color.fromARGB(255, 255, 255, 255),
          color: Colors.black.withOpacity(0.1),
          offset: const Offset(0, 2),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: Column(
                children: [
                  RepaintBoundary(
                    key: key,
                    child: _buildReceiptCardWithData(widget.transactionData!),
                  ),

                  const SizedBox(height: 24),

                  SizedBox(width: double.infinity),
                ],
              ),
            ),
          ),
        ),
      );
    }

    // Fallback: comportamento original com BLoC
    if (state is TransferPixLoading) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecorationApp.dropShadowDecoration(
          backgroudColor: const Color.fromARGB(255, 255, 255, 255),
          color: Colors.black.withAlpha((0.1 * 255).round()),
          offset: const Offset(0, 2),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Carregando dados da transação...',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecorationApp.dropShadowDecoration(
        backgroudColor: const Color.fromARGB(255, 255, 255, 255),
        color: Colors.black.withOpacity(0.1),
        offset: const Offset(0, 2),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Center(
            child: Column(
              children: [
                RepaintBoundary(key: key, child: _buildReceiptCard(state)),

                const SizedBox(height: 24),

                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF343A40),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Fechar',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReceiptCardWithData(TransactionSentPixResponse transactionData) {
    String displayValue = transactionData.amount
        .toStringAsFixed(2)
        .replaceAll('.', ',');
    String displayDateTime = _formatDateTime(transactionData.date);
    String displayTransactionId = transactionData.transactionId.toString();
    String displayEndToEndId = transactionData.endToEndId;

    // Informações do pagador (debitParty)
    String pagadorInstituicao = transactionData.debitParty.bank;
    String pagadorNome = transactionData.debitParty.name;
    String pagadorCpf = _formatTaxId(transactionData.debitParty.taxId);

    // Informações do destinatário (creditParty)
    String destinatarioInstituicao = transactionData.creditParty.bank;
    String destinatarioCpf = _formatTaxId(transactionData.creditParty.taxId);
    String destinatarioNome = transactionData.creditParty.name;

    return Container(
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Cabeçalho
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Transferência com Pix',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w800,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  displayDateTime,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w800,
                    color: Color(0xFF6C757D),
                  ),
                ),
              ],
            ),
          ),

          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ID da Transação e Valor
                _buildSimpleDetailRow('Id/Transação', displayTransactionId),
                const SizedBox(height: 8),
                _buildSimpleDetailRow('Valor', 'R\$ $displayValue'),
                const SizedBox(height: 8),

                _buildSimpleDetailRow('Descrição', ''),

                const SizedBox(height: 20),

                Container(
                  width: double.infinity,
                  height: 1,
                  color: Colors.grey.shade300,
                ),

                const SizedBox(height: 20),

                const Text(
                  'Informações do pagador',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w800,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 8),
                _buildSimpleDetailRow('PSP/Instituição:', pagadorInstituicao),
                const SizedBox(height: 4),
                _buildSimpleDetailRow('Nome', pagadorNome),
                const SizedBox(height: 4),
                _buildSimpleDetailRow('CPF', pagadorCpf),

                const SizedBox(height: 20),

                Container(
                  width: double.infinity,
                  height: 1,
                  color: Colors.grey.shade300,
                ),

                const SizedBox(height: 20),
                const Text(
                  'Informações do receptor',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 8),
                _buildSimpleDetailRow('Instituição:', destinatarioInstituicao),
                const SizedBox(height: 4),
                _buildSimpleDetailRow('CPF', destinatarioCpf),
                const SizedBox(height: 4),
                _buildSimpleDetailRow('Nome', destinatarioNome),

                const SizedBox(height: 20),

                Container(
                  width: double.infinity,
                  height: 1,
                  color: Colors.grey.shade300,
                ),

                const SizedBox(height: 20),

                const Text(
                  'NSU:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    displayEndToEndId,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF343A40),
                      fontFamily: 'monospace',
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),

          // Logo
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Image.asset('assets/images/logo_siclos.png', height: 30),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptCard(TransferPixState state) {
    String displayValue = '0,00';
    String displayDateTime = _formatCurrentDateTime();
    String displayTransactionId = transactionId ?? '000000000';
    String displayDescription = '';

    String destinatarioNome = 'N/A';
    String destinatarioCpf = 'N/A';
    String destinatarioInstituicao = 'N/A';

    if (state is GetTransactionSentPixSuccess) {
      final response = state.response;
      displayValue = response.amount.toStringAsFixed(2).replaceAll('.', ',');
      displayDateTime = _formatDateTime(response.date);
      displayTransactionId = response.transactionId.toString();

      // Informações do destinatário
      destinatarioNome = response.creditParty.name;
      destinatarioCpf = _formatTaxId(response.creditParty.taxId);
      destinatarioInstituicao = response.creditParty.bank;
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Transferência com PIX',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  displayDateTime,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6C757D),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    size: 35,
                    color: Colors.green.shade600,
                  ),
                ),
                const SizedBox(height: 16),

                Text(
                  'R\$ $displayValue',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 24),

                _buildDetailRow('ID da Transação', displayTransactionId),

                // Data da transferência destacada
                _buildDetailRow('Data da Transferência', displayDateTime),

                if (displayDescription.isNotEmpty) ...[
                  _buildDetailRow('Descrição', displayDescription),
                ],

                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  height: 1,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),

                // Informações do destinatário
                const Text(
                  'Dados do Destinatário',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF343A40),
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow('Nome', destinatarioNome),
                _buildDetailRow('CPF/CNPJ', destinatarioCpf),
                _buildDetailRow('Instituição', destinatarioInstituicao),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF343A40),
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF343A40),
            fontWeight: FontWeight.w800,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w800,
              color: Color(0xFF343A40),
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Future<void> _shareReceipt() async {
    try {
      setState(() {
        isSharing = true;
      });

      await Future.delayed(const Duration(milliseconds: 100));

      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');

      await Utils.takeScreenShot(
        key: key,
        fileName: "comprovante-pix-$timestamp.png",
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comprovante compartilhado com sucesso!'),
            backgroundColor: Color(0xFF8ADF4F),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erro ao compartilhar comprovante. Tente novamente.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isSharing = false;
        });
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatCurrentDateTime() {
    final now = DateTime.now();
    return _formatDateTime(now);
  }

  String _formatTaxId(String taxId) {
    if (taxId.length == 11) {
      // CPF
      return '${taxId.substring(0, 3)}.${taxId.substring(3, 6)}.${taxId.substring(6, 9)}-${taxId.substring(9)}';
    } else if (taxId.length == 14) {
      // CNPJ
      return '${taxId.substring(0, 2)}.${taxId.substring(2, 5)}.${taxId.substring(5, 8)}/${taxId.substring(8, 12)}-${taxId.substring(12)}';
    }
    return taxId;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/charge_pix/components/card_qrcode_widget.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/pageview_select_indicator_widget.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../../models/my_keys_pix_model.dart';
import '../../../blocs/charge_pix/charge_pix_bloc.dart';
import '../components/print_share_qrcode_widget.dart';

class ChargePixPage extends StatelessWidget {
  const ChargePixPage({super.key});

  @override
  Widget build(BuildContext context) {
    final keys = Modular.args.data['args'] as List<KeyPixModel>?;
    return BlocProvider(
      create: (context) => Modular.get<ChargePixBloc>(),
      child: _ChargePixPage(keys: keys ?? []),
    );
  }
}

class _ChargePixPage extends StatefulWidget {
  const _ChargePixPage({required this.keys});
  final List<KeyPixModel> keys;
  @override
  State<_ChargePixPage> createState() => __ChargePixPageState();
}

class __ChargePixPageState extends State<_ChargePixPage> {
  final pageController = PageController();
  int currentPage = 0;
  List<KeyPixModel> get keys => widget.keys;

  final key0 = GlobalKey();
  final takePrintState = ValueNotifier(false);
  String qrcode = '';

  @override
  void initState() {
    super.initState();
    if (keys.isNotEmpty) {
      BlocProvider.of<ChargePixBloc>(context).listKeys = keys;
    }
    BlocProvider.of<ChargePixBloc>(
      context,
    ).add(GetStaticQrcodeEvent(widget.keys.first));
  }

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context)!;

    return ValueListenableBuilder(
      valueListenable: takePrintState,
      builder: (context, takePrint, child) {
        if (takePrint) {
          return PrintShareQrcodeWidget(
            key0: key0,
            qrcode: qrcode,
            keyPix: keys[currentPage],
            amount: BlocProvider.of<ChargePixBloc>(context).value,
          );
        }
        return Scaffold(
          appBar: AppBarApp(title: i18n.charge_with_pix.toUpperCase()),
          body: BlocConsumer<ChargePixBloc, ChargePixState>(
            listener: (context, state) {
              if (state is ChargePixErrorState) {
                DialogUtils.showSnackError(context, state.error);
              }
              if (state is ChargePixSuccessState) {
                setState(() {
                  qrcode = state.qrcode;
                });
              }
            },
            builder: (context, state) {
              final widget = switch (state) {
                ChargePixLoadingState _ => Center(
                  child: CircularProgressIndicator(),
                ),
                ChargePixSuccessState _ => CardQrcodeWidget(
                  qrcode: state.qrcode,
                  keyPix: keys[currentPage],
                ),
                ChargePixErrorState _ => Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.grey,
                ),
                _ => SizedBox(),
              };
              final height = MediaQuery.of(context).size.height * 0.33;
              return GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                },
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(
                      child: SizedBox(
                        height: height,
                        child: PageView.builder(
                          itemCount: keys.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 32),
                              child: widget,
                            );
                          },
                          onPageChanged: (value) {
                            setState(() {
                              currentPage = value;
                            });
                            BlocProvider.of<ChargePixBloc>(
                              context,
                            ).add(GetStaticQrcodeEvent(keys[value]));
                          },
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: PageviewSelectIndicatorWidget(
                        length: keys.length,
                        currentPage: currentPage,
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.only(
                          top: 28,
                          left: 18,
                          right: 18,
                        ),
                        child: InkWell(
                          onTap: () async {
                            // inserir valor buttao
                            final result = await push(
                              Routes.dynamicQrcodePix,
                              args: BlocProvider.of<ChargePixBloc>(context),
                            );

                            if (result == true) {
                              pop();
                            }
                          },
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              border: Border.all(),
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.white,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Row(
                                    spacing: 8,
                                    children: [
                                      Icon(Icons.edit),
                                      Text('Inserir valor'),
                                    ],
                                  ),
                                ),
                                Icon(Icons.keyboard_arrow_right),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SliverFillRemaining(
                      hasScrollBody: false,
                      fillOverscroll: false,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          top: 18,
                          left: 18,
                          right: 18,
                          bottom: 32,
                        ),
                        child: Visibility(
                          visible: state is! ChargePixLoadingState,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            spacing: 16,
                            children: [
                              ButtonApp(
                                text: i18n.share_qrcode,
                                width: double.infinity,
                                onPress: shareQrcode,
                              ),
                              ButtonApp(
                                text: i18n.share_copy_past,
                                width: double.infinity,
                                border: 1,
                                borderColor: Colors.black,
                                buttonColor: Colors.transparent,
                                onPress: () {
                                  Utils.copyText(
                                    context: context,
                                    mensagemSucesso:
                                        'Código PIX copiado com sucesso!',
                                    text: qrcode,
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  shareQrcode() async {
    takePrintState.value = true;
    final txt = I18n().share_qrcode.toLowerCase().replaceAll(' ', '-');
    await Future.delayed(Duration(milliseconds: 100));
    Utils.takeScreenShot(key: key0, fileName: txt);
    takePrintState.value = false;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/modules/pix/presenter/components/modal_select_key.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../../../../blocs/charge_pix/charge_pix_bloc.dart';

class ConfirmAmountPage extends StatelessWidget {
  const ConfirmAmountPage({
    super.key,
    required this.bloc,
    required this.backPage,
  });
  final ChargePixBloc bloc;
  final VoidCallback backPage;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: bloc,
      child: _ConfirmAmountPage(backPage: backPage),
    );
  }
}

class _ConfirmAmountPage extends StatefulWidget {
  const _ConfirmAmountPage({super.key, required this.backPage});
  final VoidCallback backPage;

  @override
  State<_ConfirmAmountPage> createState() => _InsertAmountPageState();
}

class _InsertAmountPageState extends State<_ConfirmAmountPage> {
  ChargePixBloc get bloc => BlocProvider.of<ChargePixBloc>(context);

  late double amount;
  late List<KeyPixModel> listKeys;
  late KeyPixModel currentKey;

  final descriptionController = TextEditingController();

  @override
  void initState() {
    amount = bloc.value;
    listKeys = bloc.listKeys;
    currentKey = bloc.currentKey!;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context)!;
    final themedata = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final value = Utils.formatBalance(amount);
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(18.0),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  spacing: 8,
                  children: [
                    _amountWidget(value, themedata, size, i18n),
                    _selectKeyWidget(themedata, context),
                    const SizedBox(height: 22),
                    Text(
                      i18n.descricao_opcional,
                      style: themedata.textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    TextFormFieldApp(
                      controller: descriptionController,
                      hint: 'Escreva aqui',
                      formatter: LengthLimitingTextInputFormatter(140),
                      maxLength: 140,
                    ),
                  ],
                ),
              ),
            ),
            SliverFillRemaining(
              child: Container(
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.only(bottom: 16),
                child: BlocBuilder<ChargePixBloc, ChargePixState>(
                  builder: (context, state) {
                    return ButtonApp(
                      text: i18n.create_qr_code,
                      width: double.infinity,
                      progress: state is ChargePixLoadingState
                          ? Utils.circularProgressButton()
                          : null,
                      onPress: () {
                        BlocProvider.of<ChargePixBloc>(context).add(
                          GetDynamicQrcodeEvent(
                            descriptionController.text.isEmpty
                                ? null
                                : descriptionController.text,
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Row _selectKeyWidget(ThemeData themedata, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 6,
          children: [
            Text(
              currentKey.keyType.description,
              style: themedata.textTheme.bodyMedium!.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              currentKey.key,
              style: themedata.textTheme.bodyMedium!.copyWith(
                color: ColorsApp.cinzaSolo,
              ),
            ),
          ],
        ),
        IconButton(
          onPressed: () async {
            final keySelected = await ModalSelectKeyPix.show(
              context,
              listKeys: listKeys,
            );
            if (keySelected != null) {
              bloc.currentKey = keySelected;
              setState(() {
                currentKey = keySelected;
              });
            }
          },
          icon: Icon(Icons.edit),
          color: ColorsApp.verde,
          iconSize: 20,
        ),
      ],
    );
  }

  Column _amountWidget(
    String value,
    ThemeData themedata,
    Size size,
    I18n i18n,
  ) {
    return Column(
      children: [
        if (amount > 0.0)
          Text(
            value,
            style: themedata.textTheme.headlineSmall!.copyWith(
              fontSize: 32,
              fontWeight: FontWeight.w700,
              // color: ColorsApp.verde[500],
            ),
          ),
        amount == 0
            ? SizedBox(
                width: size.width * 0.8,
                child: Text(
                  i18n.valor_a_receber_0_message,
                  textAlign: TextAlign.center,
                ),
              )
            : Text(i18n.valor_a_receber),
        TextButton(
          onPressed: () {
            widget.backPage();
          },
          child: Text(i18n.editar),
        ),
      ],
    );
  }
}

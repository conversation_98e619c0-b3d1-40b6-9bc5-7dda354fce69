import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/charge_pix/components/print_share_qrcode_widget.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../../../../blocs/charge_pix/charge_pix_bloc.dart';
import '../../components/card_qrcode_widget.dart';

class CreatedQrcodePage extends StatelessWidget {
  const CreatedQrcodePage({
    super.key,
    required this.bloc,
    required this.qrcode,
  });

  final ChargePixBloc bloc;
  final String qrcode;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: bloc,
      child: _CreatedQrcodePage(qrcode: qrcode),
    );
  }
}

class _CreatedQrcodePage extends StatefulWidget {
  const _CreatedQrcodePage({super.key, required this.qrcode});

  final String qrcode;

  @override
  State<_CreatedQrcodePage> createState() => _CreatedQrcodePageState();
}

class _CreatedQrcodePageState extends State<_CreatedQrcodePage> {
  final key0 = GlobalKey();
  final takePrintState = ValueNotifier(false);
  get bloc => BlocProvider.of<ChargePixBloc>(context);

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context)!;
    final value = Utils.formatBalance(bloc.value);
    final themedata = Theme.of(context);
    return ValueListenableBuilder(
      valueListenable: takePrintState,
      builder: (context, takePrint, child) {
        if (takePrint) {
          return PrintShareQrcodeWidget(
            key0: key0,
            qrcode: widget.qrcode,
            keyPix: bloc.currentKey!,
            amount: bloc.value,
          );
        }
        return Scaffold(
          body: Padding(
            padding: const EdgeInsets.all(18.0),
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 22),
                    child: CardQrcodeWidget(
                      qrcode: widget.qrcode,
                      keyPix: bloc.currentKey!,
                    ),
                  ),
                ),

                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      top: 28,
                      left: 18,
                      right: 18,
                    ),
                    child: Column(
                      children: [
                        Text(
                          value,
                          style: themedata.textTheme.headlineSmall!.copyWith(
                            fontSize: 32,
                            fontWeight: FontWeight.w700,
                            // color: ColorsApp.verde[500],
                          ),
                        ),
                        Text(i18n.valor_a_receber),
                      ],
                    ),
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  fillOverscroll: false,
                  child: Padding(
                    padding: const EdgeInsets.only(
                      top: 18,
                      // left: 18,
                      // right: 18,
                      bottom: 16,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      spacing: 16,
                      children: [
                        ButtonApp(
                          text: i18n.share_qrcode,
                          width: double.infinity,
                          onPress: shareQrcode,
                        ),
                        ButtonApp(
                          text: i18n.share_copy_past,
                          width: double.infinity,
                          border: 1,
                          borderColor: Colors.black,
                          buttonColor: Colors.transparent,
                          onPress: () {
                            Utils.copyText(
                              context: context,
                              mensagemSucesso:
                                  'Código PIX copiado com sucesso!',
                              text: widget.qrcode,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  shareQrcode() async {
    takePrintState.value = true;
    await Future.delayed(Duration(milliseconds: 100));
    final txt = I18n().share_qrcode.toLowerCase().replaceAll(' ', '-');
    Utils.takeScreenShot(key: key0, fileName: txt);
    takePrintState.value = false;
  }
}

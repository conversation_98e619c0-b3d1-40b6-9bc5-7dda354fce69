import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/pix/presenter/blocs/charge_pix/charge_pix_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';

class InsertAmountPage extends StatelessWidget {
  const InsertAmountPage({
    super.key,
    required this.bloc,
    required this.nextPage,
  });

  final ChargePixBloc bloc;
  final VoidCallback nextPage;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: bloc,
      child: _InsertAmountPage(nextPage: nextPage),
    );
  }
}

class _InsertAmountPage extends StatefulWidget {
  const _InsertAmountPage({super.key, required this.nextPage});
  final VoidCallback nextPage;
  @override
  State<_InsertAmountPage> createState() => _InsertAmountPageState();
}

class _InsertAmountPageState extends State<_InsertAmountPage> {
  final _valorController = MoneyMaskedTextController(
    leftSymbol: 'R\$ ',
    initialValue: 0,
  );
  get nextPage => widget.nextPage;
  final focusAmount = FocusNode();

  get bloc => BlocProvider.of<ChargePixBloc>(context);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      focusAmount.requestFocus();
    });
  }

  @override
  void dispose() {
    focusAmount.dispose(); // Sempre limpe o FocusNode
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context)!;
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(18.0),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  spacing: 8,
                  children: [
                    Text(i18n.valor_a_receber_opcional),
                    TextFormFieldApp(
                      controller: _valorController,
                      textInputType: TextInputType.number,
                      focusNode: focusAmount,
                    ),
                  ],
                ),
              ),
            ),
            SliverFillRemaining(
              child: Container(
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.only(bottom: 16),
                child: ButtonApp(
                  text: i18n.continuar,
                  width: double.infinity,
                  onPress: () {
                    bloc.value = _valorController.numberValue;
                    if (_valorController.numberValue == 0.0) {
                      SheetAlertConfirm.showSheet(
                        context,
                        title: i18n.atencao,
                        message: i18n.if_not_inform_value_pix_qrcode_message,
                        clickPositive: () {
                          nextPage();
                        },
                        textPositive: i18n.continuar,
                        showBtnNegative: false,
                      );
                    } else {
                      nextPage();
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

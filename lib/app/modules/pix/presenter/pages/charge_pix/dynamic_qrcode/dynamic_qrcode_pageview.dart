import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/charge_pix/dynamic_qrcode/pages/confirm_amount_page.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/charge_pix/dynamic_qrcode/pages/created_qrcode_page.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../models/my_keys_pix_model.dart';
import '../../../blocs/charge_pix/charge_pix_bloc.dart';
import 'pages/insert_amount_page.dart';

class DynamicQrcodePageview extends StatelessWidget {
  const DynamicQrcodePageview({super.key});

  @override
  Widget build(BuildContext context) {
    final bloc = Modular.args.data['args'] as ChargePixBloc?;
    assert(bloc != null);

    return BlocProvider.value(value: bloc!, child: _ChargePixPage());
  }
}

class _ChargePixPage extends StatefulWidget {
  const _ChargePixPage({super.key});

  @override
  State<_ChargePixPage> createState() => __ChargePixPageState();
}

class __ChargePixPageState extends State<_ChargePixPage> {
  final pageController = PageController();
  int currentPage = 0;
  late List<KeyPixModel> keys;

  @override
  void initState() {
    keys = BlocProvider.of<ChargePixBloc>(context).listKeys;
    super.initState();
    // BlocProvider.of<ChargePixBloc>(context).add(FetchQrcodeEvent(keys.first));
  }

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context)!;
    // final width = MediaQuery.of(context).size.width * 0.6;
    final bloc = BlocProvider.of<ChargePixBloc>(context);
    return Scaffold(
      appBar: AppBarApp(
        title: i18n.charge_with_pix.toUpperCase(),
        clickBack: () {
          if (currentPage == 0 || currentPage == 2) {
            pop(true);
          } else {
            _backPage();
          }
        },
      ),
      body: BlocConsumer<ChargePixBloc, ChargePixState>(
        listener: (context, state) {
          if (state is ChargePixErrorState) {
            DialogUtils.showSnackError(context, state.error);
          }
          if (state is ChargePixDynamicSuccessState) {
            setState(() {
              currentPage = 2;
            });
            _movePage();
          }
        },
        builder: (context, state) => PageView(
          physics: NeverScrollableScrollPhysics(),
          controller: pageController,
          children: [
            InsertAmountPage(bloc: bloc, nextPage: _nextPage),
            ConfirmAmountPage(bloc: bloc, backPage: _backPage),
            CreatedQrcodePage(
              bloc: bloc,
              qrcode: (state is ChargePixDynamicSuccessState)
                  ? state.qrcode
                  : '',
            ),
          ],
        ),
      ),
    );
  }

  void _nextPage() {
    setState(() {
      currentPage++;
      _movePage();
    });
  }

  void _movePage({bool cancel = false}) {
    pageController.animateToPage(
      currentPage,
      duration: const Duration(milliseconds: 300),
      curve: Curves.ease,
    );
    // if (cancel) {
    //   pop(cancel);
    // }
  }

  void _backPage() {
    setState(() {
      currentPage--;
      _movePage();
    });
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';

import '../../../blocs/charge_pix/charge_pix_bloc.dart';

class CardQrcodeWidget extends StatelessWidget {
  const CardQrcodeWidget({
    super.key,
    required this.qrcode,
    required this.keyPix,
  });

  final String qrcode;
  final KeyPixModel keyPix;
  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width * 0.5;
    return SizedBox(
      width: width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        spacing: 16,
        children: [
          SizedBox(
            width: width,
            child: PrettyQrView.data(
              data: qrcode,
              decoration: PrettyQrDecoration(shape: PrettyQrSquaresSymbol()),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${keyPix.keyType.description}: ',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(keyPix.key),
            ],
          ),
        ],
      ),
    );
  }
}

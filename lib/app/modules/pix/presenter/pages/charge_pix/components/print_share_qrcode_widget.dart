import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/pix/models/my_keys_pix_model.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class PrintShareQrcodeWidget extends StatelessWidget {
  const PrintShareQrcodeWidget({
    super.key,
    required this.key0,
    required this.qrcode,
    required this.keyPix,
    required this.amount,
  });

  final GlobalKey key0;
  final String qrcode;
  final KeyPixModel keyPix;
  final double amount;

  @override
  Widget build(BuildContext context) {
    final userName = AppSession.getInstance().user!.name;
    return SafeArea(
      child: Scaffold(
        body: RepaintBoundary(
          key: key0,
          child: Container(
            width: 360,
            height: 574,
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(color: const Color(0xFFF7F7F7)),
            child: Stack(
              children: [
                Positioned(
                  top: 160,
                  left: 72,
                  child: SizedBox(
                    width: 215,
                    height: 215,
                    child: PrettyQrView.data(
                      data: qrcode,
                      decoration: PrettyQrDecoration(
                        shape: PrettyQrSquaresSymbol(),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 75,
                  top: 62,
                  child: SizedBox(
                    width: 208.63,
                    child: Text(
                      'Pix',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF343A40),
                        fontSize: 14,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w400,
                        height: 1.14,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 75,
                  top: 86,
                  child: SizedBox(
                    width: 209,
                    height: 43,
                    child: Text(
                      amount > 0
                          ? Utils.formatBalance(amount)
                          : 'Informe o valor quando for pagar',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF343A40),
                        fontSize: amount > 0 ? 28 : 12,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w700,
                        height: 1.33,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 24,
                  top: 456,
                  child: SizedBox(
                    width: 256,
                    child: Text(
                      'Nome',
                      style: TextStyle(
                        color: const Color(0xFF343A40),
                        fontSize: 14,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w400,
                        height: 1.14,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 24,
                  top: 518,
                  child: SizedBox(
                    width: 256,
                    child: Text(
                      keyPix.keyType.description,
                      style: TextStyle(
                        color: const Color(0xFF343A40),
                        fontSize: 14,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w400,
                        height: 1.14,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 24,
                  top: 540,
                  child: SizedBox(
                    width: 312,
                    child: Text(
                      keyPix.key,
                      style: TextStyle(
                        color: const Color(0xB2343A40),
                        fontSize: 14,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w400,
                        height: 1.14,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 23,
                  top: 480,
                  child: SizedBox(
                    width: 312,
                    child: Text(
                      userName!,
                      style: TextStyle(
                        color: const Color(0xB2343A40),
                        fontSize: 14,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w400,
                        height: 1.14,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 135,
                  top: 31,
                  child: SizedBox(
                    width: 88,
                    height: 20,
                    child: IconsApp.siclosAppBar(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

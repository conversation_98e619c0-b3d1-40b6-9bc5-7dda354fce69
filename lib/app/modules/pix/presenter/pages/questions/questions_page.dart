import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/home/<USER>/components/home_button_in_row.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/questions/questions_const.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/modal_sheets_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../../shared/presenter/view/components/others/expansion_tile_app.dart';
import '../../components/tile_button.dart';

class QuestionsPage extends StatefulWidget {
  const QuestionsPage({super.key});

  @override
  State<QuestionsPage> createState() => _QuestionsPageState();
}

class _QuestionsPageState extends State<QuestionsPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    List<Widget> list = questions_const
        .map(
          (question) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: ExpansionTileApp(
              title: question['title']!,
              description: question['description']!,
            ),
          ),
        )
        .toList();

    return Scaffold(
        appBar: AppBarApp(
          title: I18n.of(context)!.questions.toUpperCase(),
        ),
        backgroundColor: ColorsApp.scaffoldColor,
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            spacing: 16,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: TextFormFieldApp(
                  controller: _searchController,
                  hint: I18n.of(context)!.what_your_question,
                  onChanged: (p0) {
                    setState(() {});
                  },
                ),
              ),
              Column(
                children: _searchController.text.isEmpty
                    ? list
                    : list.where((element) {
                        final title =
                            ((element as Padding).child as ExpansionTileApp)
                                .title
                                .toLowerCase();
                        return title
                            .contains(_searchController.text.toLowerCase());
                      }).toList(),
              ),
              // const SizedBox(height: 16),
              Container(
                alignment: Alignment.centerLeft,
                child: Text(
                  I18n.of(context)!.last_transactions,
                  style: textTheme.titleMedium,
                ),
              ),
              // const SizedBox(height: 16),
              TileButton(
                onTap: () {
                  push(Routes.help);
                },
                icon: IconsApp.icChatRounded(),
                label: I18n.of(context)!.contact_us,
                showPadding: false,
              ),
              // const SizedBox(height: 16),
              TileButton(
                onTap: () {
                  SheetAlertConfirm.showSheet(
                    context,
                    title: I18n.of(context)!.banco_central,
                    message: I18n.of(context)!.banco_central_message,
                    textNegative: I18n.of(context)!.acessar_banco_central,
                    clickNegative: () {
                      launchUrlString(
                          'https://www.bcb.gov.br/meubc/registrar_reclamacao');
                    },
                    textPositive: I18n.of(context)!.voltar,
                    clickPositive: () {
                      Navigator.maybePop(context);
                    },
                  );
                },
                icon: IconsApp.icBancoCentral(),
                label: I18n.of(context)!.banco_central,
                showPadding: false,
              ),
              const SizedBox(height: 32),
            ],
          ),
        ));
  }
}

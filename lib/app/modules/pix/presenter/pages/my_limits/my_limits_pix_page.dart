import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import '../../blocs/my_limits_pix/my_limits_pix_bloc.dart';
import 'components/card_my_limits.dart';

class MyLimitsPixPage extends StatelessWidget {
  const MyLimitsPixPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(title: I18n.of(context)!.my_limits_pix),
      body: BlocProvider(
        create: (context) =>
            Modular.get<MyLimitsPixBloc>()..add(const GetMyLimitsPixEvent()),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(18),
          child: BlocConsumer<MyLimitsPixBloc, MyLimitsPixState>(
            listener: (context, state) {
              if (state is MyLimitsPixError) {
                DialogUtils.showSnackError(context, state.error);
              }
            },
            builder: (context, state) {
              if (state is MyLimitsPixLoading) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height - 200,
                  child: const Center(child: CircularProgressIndicator()),
                );
              }
              if (state is MyLimitsPixSuccess) {
                return Column(
                  spacing: 16,
                  children: [
                    CardMyLimits(
                      label: 'Diurno (${state.limitPixResponse.diurnalPeriod})',
                      icon: Icons.wb_sunny_outlined,
                      value: state.limitPixResponse.diurnalLimit,
                    ),
                    CardMyLimits(
                      label:
                          'Noturno (${state.limitPixResponse.nocturnalPeriod})',
                      icon: Icons.nightlight_outlined,
                      value: state.limitPixResponse.nocturnalLimit,
                    ),
                    const AlertBanner(
                      isShow: true,
                      typeAlertBanner: TypeAlertBanner.info,
                      message:
                          'Estes são os limites atuais da sua conta. Para solicitar alterações, entre em contato com nossos canais de atendimento.',
                    ),
                  ],
                );
              }

              return const SizedBox();
            },
          ),
        ),
      ),
    );
  }
}

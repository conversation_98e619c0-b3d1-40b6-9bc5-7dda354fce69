import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/pix/presenter/pages/onboarding/onboarding_widget.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/pageview_select_indicator_widget.dart';

import '../../../../../shared/navigation/navigator_app.dart';

class OnboardingPageview extends StatefulWidget {
  const OnboardingPageview({
    super.key,
    required this.pages,
    required this.nextRoute,
    required this.onFinishView,
    this.args,
  });
  final List<OnboardingWidget> pages;
  final String nextRoute;
  final VoidCallback onFinishView;
  final dynamic args;

  @override
  State<OnboardingPageview> createState() => _OnboardingPageviewState();
}

class _OnboardingPageviewState extends State<OnboardingPageview> {
  final PageController pageController = PageController(
    initialPage: 0,
    keepPage: false,
  );

  late List<Widget> pages;
  late int _currentPage;

  @override
  void initState() {
    _currentPage = 0;
    pages = widget.pages;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarApp(
        showLine: false,
        // showBack: false,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraint) {
            return SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraint.maxHeight - 200,
                  maxHeight: constraint.maxHeight + 5,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: constraint.maxHeight * 0.7,
                            child: PageView(
                              physics: const BouncingScrollPhysics(),
                              controller: pageController,
                              children: pages,
                              onPageChanged: (value) {
                                setState(() {
                                  _currentPage = value;
                                });
                              },
                            ),
                          ),
                          const SizedBox(height: 8),
                          PageviewSelectIndicatorWidget(
                            length: pages.length,
                            currentPage: _currentPage,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16,
                      ),
                      child: ButtonApp(
                        text: I18n.of(context)!.continuar,
                        onPress: () {
                          if (_currentPage == pages.length - 1) {
                            widget.onFinishView();
                            pushReplacement(
                              widget.nextRoute,
                              args: widget.args,
                            );
                          } else {
                            _nextPage();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  _backPage() {
    setState(() {
      _currentPage--;
      _movePage();
    });
  }

  _nextPage() {
    setState(() {
      _currentPage++;
      _movePage();
    });
  }

  _movePage() {
    pageController.animateToPage(
      _currentPage,
      duration: const Duration(milliseconds: 300),
      curve: Curves.ease,
    );
  }
}

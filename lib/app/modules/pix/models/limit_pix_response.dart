import 'package:equatable/equatable.dart';
import 'package:string_validator/string_validator.dart';

class LimitPixResponse extends Equatable {
  final double diurnalLimit;
  final double nocturnalLimit;
  final String diurnalStartTime;
  final String diurnalEndTime;
  final String nocturnalStartTime;
  final String nocturnalEndTime;

  const LimitPixResponse({
    required this.diurnalLimit,
    required this.nocturnalLimit,
    required this.diurnalStartTime,
    required this.diurnalEndTime,
    required this.nocturnalStartTime,
    required this.nocturnalEndTime,
  });

  String get diurnalPeriod => _getPeriodStr(diurnalStartTime, diurnalEndTime);

  String get nocturnalPeriod =>
      _getPeriodStr(nocturnalStartTime, nocturnalEndTime);

  String _getPeriodStr(String startTime, String endTime) {
    final startHour = startTime.substring(0, 2);
    final endHour = endTime.substring(0, 2);
    return '${startHour}h às ${endHour}h';
  }

  @override
  List<Object?> get props => [
        diurnalLimit,
        nocturnalLimit,
        diurnalStartTime,
        diurnalEndTime,
        nocturnalStartTime,
        nocturnalEndTime,
      ];
  LimitPixResponse copyWith(
      {double? diurnalLimit,
      double? nocturnalLimit,
      String? diurnalStartTime,
      String? diurnalEndTime,
      String? nocturnalStartTime,
      String? nocturnalEndTime}) {
    return LimitPixResponse(
        diurnalLimit: diurnalLimit ?? this.diurnalLimit,
        nocturnalLimit: nocturnalLimit ?? this.nocturnalLimit,
        diurnalStartTime: diurnalStartTime ?? this.diurnalStartTime,
        diurnalEndTime: diurnalEndTime ?? this.diurnalEndTime,
        nocturnalStartTime: nocturnalStartTime ?? this.nocturnalStartTime,
        nocturnalEndTime: nocturnalEndTime ?? this.nocturnalEndTime);
  }

  factory LimitPixResponse.fromMap(Map<String, dynamic> json) {
    return LimitPixResponse(
      diurnalLimit: (num.parse(json['diurnal_limit'] as String)).toDouble(),
      nocturnalLimit: (num.parse(json['nocturnal_limit'] as String)).toDouble(),
      diurnalStartTime: json['diurnal_start_time'] as String,
      diurnalEndTime: json['diurnal_end_time'] as String,
      nocturnalStartTime: json['nocturnal_start_time'] as String,
      nocturnalEndTime: json['nocturnal_end_time'] as String,
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'diurnal_limit': diurnalLimit,
      'nocturnal_limit': nocturnalLimit,
      'diurnal_start_time': diurnalStartTime,
      'diurnal_end_time': diurnalEndTime,
      'nocturnal_start_time': nocturnalStartTime,
      'nocturnal_end_time': nocturnalEndTime,
    };
  }
}

class TransferPixModel {
  final double amount;
  final DebitPartyModel debitParty;
  final CreditPartyModel creditParty;
  final String initiationType;
  final String paymentType;
  final String? remittanceInformation;
  final String? clientRequestId;

  TransferPixModel({
    required this.amount,
    required this.debitParty,
    required this.creditParty,
    this.initiationType = 'DICT',
    this.paymentType = 'IMMEDIATE',
    this.remittanceInformation,
    this.clientRequestId,
  });

  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'amount': amount,
      'debitParty': debitParty.toMap(),
      'creditParty': creditParty.toMap(),
      'initiationType': initiationType,
      'paymentType': paymentType,
    };

    return map;
  }
}

class DebitPartyModel {
  final String account;

  DebitPartyModel({required this.account});

  Map<String, dynamic> toMap() {
    String accountFormatted = account.replaceAll('-', '').replaceAll('.', '');
    return {'account': accountFormatted};
  }
}

class CreditPartyModel {
  final String? key;
  final String accountType;
  final String name;
  final String? account;
  final String? taxId;
  final String? branch;
  final String? bank;

  CreditPartyModel({
    this.key,
    required this.accountType,
    required this.name,
    this.account,
    this.taxId,
    this.branch,
    this.bank,
  });

  factory CreditPartyModel.fromPixKey({
    required String key,
    required String name,
    String accountType = 'CACC',
    String? bank,
  }) {
    return CreditPartyModel(
      key: key,
      accountType: accountType,
      name: name,
      bank: bank,
    );
  }

  factory CreditPartyModel.fromBankData({
    required String name,
    required String account,
    required String taxId,
    required String branch,
    required String bank,
    String accountType = 'CACC',
  }) {
    return CreditPartyModel(
      accountType: accountType,
      name: name,
      account: account,
      taxId: taxId,
      branch: branch,
      bank: bank,
    );
  }

  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{'accountType': accountType, 'name': name};

    if (key != null) {
      map['key'] = key;
    }

    if (account != null) map['account'] = account;
    if (taxId != null) map['taxId'] = taxId;
    if (branch != null) map['branch'] = branch;
    if (bank != null) map['bank'] = bank;

    return map;
  }
}

class TransferPixResponse {
  final String id;
  final String status;
  final double amount;
  final String clientCode;
  final String endToEndId;
  final String? remittanceInformation;
  final String? initiationType;
  final String? paymentType;
  final DateTime? createdAt;

  TransferPixResponse({
    required this.id,
    required this.status,
    required this.amount,
    required this.clientCode,
    required this.endToEndId,
    this.remittanceInformation,
    this.initiationType,
    this.paymentType,
    this.createdAt,
  });

  factory TransferPixResponse.fromMap(Map<String, dynamic> json) {
    try {
      return TransferPixResponse(
        id: json['id'] as String,
        status: json['status'] as String,
        amount: (json['amount'] as num).toDouble(),
        clientCode: json['clientCode'] as String,
        endToEndId: json['endToEndId'] as String,
        remittanceInformation: json['remittanceInformation'] as String?,
        initiationType: json['initiationType'] as String?,
        paymentType: json['paymentType'] as String?,
        createdAt: json['createdAt'] != null
            ? DateTime.tryParse(json['createdAt'] as String)
            : null,
      );
    } catch (e) {
      print('Erro ao fazer parse do TransferPixResponse: $e');
      print('JSON recebido: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'status': status,
      'amount': amount,
      'clientCode': clientCode,
      'endToEndId': endToEndId,
      'remittanceInformation': remittanceInformation,
      'initiationType': initiationType,
      'paymentType': paymentType,
      'createdAt': createdAt?.toIso8601String(),
    };
  }
}

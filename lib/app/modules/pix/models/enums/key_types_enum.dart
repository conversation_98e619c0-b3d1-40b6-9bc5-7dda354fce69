enum KeyType {
  <PERSON>VP('Chave aleatória'),
  <PERSON><PERSON>('CPF'),
  CNPJ('CNPJ'),
  PHONE('Celular'),
  EMAIL('E-mail');

  const KeyType(this.description);

  /// "Celular" ou "E-mail" ou "Chave aleatória" ou "CPF"
  final String description;

  static KeyType fromString(String value) {
    return KeyType.values.firstWhere(
      (e) => e.toString().split('.').last.toUpperCase() == value.toUpperCase(),
      orElse: () => KeyType.EVP,
    );
  }

  get isEvp => this == KeyType.EVP;
  get isCPF => this == KeyType.CPF;
  get isPhone => this == KeyType.PHONE;
  get isEmail => this == KeyType.EMAIL;
}

enum ClaimType {
  OWNERSHIP,
  PORTABILITY;

  bool get isPortability => this == ClaimType.PORTABILITY;

  static ClaimType fromString(String value) {
    return ClaimType.values.firstWhere(
      (e) => e.name.toUpperCase() == value.toUpperCase(),
    );
  }

  String get description {
    switch (this) {
      case ClaimType.OWNERSHIP:
        return 'Reivindicação';
      case ClaimType.PORTABILITY:
        return 'Portabilidade';
    }
  }
}

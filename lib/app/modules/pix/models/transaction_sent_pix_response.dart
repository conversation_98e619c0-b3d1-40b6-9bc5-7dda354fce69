class TransactionSentPixResponse {
  final String endToEndId;
  final DebitParty debitParty;
  final CreditParty creditParty;
  final String initiationType;
  final String paymentType;
  final String urgency;
  final String transactionType;
  final String clientCode;
  final double amount;
  final String status;
  final int transactionId;
  final DateTime date;

  TransactionSentPixResponse({
    required this.endToEndId,
    required this.debitParty,
    required this.creditParty,
    required this.initiationType,
    required this.paymentType,
    required this.urgency,
    required this.transactionType,
    required this.clientCode,
    required this.amount,
    required this.status,
    required this.transactionId,
    required this.date,
  });

  factory TransactionSentPixResponse.fromMap(Map<String, dynamic> json) {
    return TransactionSentPixResponse(
      endToEndId: json['endToEndId'] as String,
      debitParty: DebitParty.fromMap(
        json['debitParty'] as Map<String, dynamic>,
      ),
      creditParty: CreditParty.fromMap(
        json['creditParty'] as Map<String, dynamic>,
      ),
      initiationType: json['initiationType'] as String,
      paymentType: json['paymentType'] as String,
      urgency: json['urgency'] as String,
      transactionType: json['transactionType'] as String,
      clientCode: json['clientCode'] as String,
      amount: (json['amount'] as num).toDouble(),
      status: json['status'] as String,
      transactionId: json['transactionId'] as int,
      date: DateTime.parse(json['date'] as String),
    );
  }
}

class DebitParty {
  final String account;
  final int branch;
  final String bank;
  final String taxId;
  final String accountType;
  final String name;

  DebitParty({
    required this.account,
    required this.branch,
    required this.bank,
    required this.taxId,
    required this.accountType,
    required this.name,
  });

  factory DebitParty.fromMap(Map<String, dynamic> json) {
    return DebitParty(
      account: json['account'] as String,
      branch: json['branch'] as int,
      bank: json['bank'] as String,
      taxId: json['taxId'] as String,
      accountType: json['accountType'] as String,
      name: json['name'] as String,
    );
  }
}

class CreditParty {
  final String key;
  final String bank;
  final String account;
  final int branch;
  final String taxId;
  final String accountType;
  final String name;

  CreditParty({
    required this.key,
    required this.bank,
    required this.account,
    required this.branch,
    required this.taxId,
    required this.accountType,
    required this.name,
  });

  factory CreditParty.fromMap(Map<String, dynamic> json) {
    return CreditParty(
      key: json['key'] as String,
      bank: json['bank'] as String,
      account: json['account'] as String,
      branch: json['branch'] as int,
      taxId: json['taxId'] as String,
      accountType: json['accountType'] as String,
      name: json['name'] as String,
    );
  }
}

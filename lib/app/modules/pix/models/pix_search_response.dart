class PixSearchResponse {
  final String keyType;
  final String key;
  final PixAccount account;
  final PixOwner owner;
  final String? endToEndId;
  final String? creationDate;
  final String? keyOwnershipDate;
  final String? openClaimCreationDate;
  final bool? isSameTaxId;

  PixSearchResponse({
    required this.keyType,
    required this.key,
    required this.account,
    required this.owner,
    this.endToEndId,
    this.creationDate,
    this.keyOwnershipDate,
    this.openClaimCreationDate,
    this.isSameTaxId,
  });

  factory PixSearchResponse.fromMap(Map<String, dynamic> json) {
    try {
      return PixSearchResponse(
        keyType: json['keyType'] as String,
        key: json['key'] as String,
        account: PixAccount.fromMap(json['account'] as Map<String, dynamic>),
        owner: PixOwner.fromMap(json['owner'] as Map<String, dynamic>),
        endToEndId:
            json['endtoEndId']
                as String?, // Note: API usa 'endtoEndId' não 'endToEndId'
        creationDate: json['creationDate'] as String?,
        keyOwnershipDate: json['keyOwnershipDate'] as String?,
        openClaimCreationDate: json['openClaimCreationDate'] as String?,
        isSameTaxId: json['isSameTaxId'] as bool?,
      );
    } catch (e) {
      print('Erro ao fazer parse do PixSearchResponse: $e');
      print('JSON recebido: $json');
      rethrow;
    }
  }
}

class PixAccount {
  final String participant;
  final String branch;
  final String account;
  final String accountType;
  final String createDate;

  PixAccount({
    required this.participant,
    required this.branch,
    required this.account,
    required this.accountType,
    required this.createDate,
  });

  factory PixAccount.fromMap(Map<String, dynamic> json) {
    try {
      return PixAccount(
        participant: json['participant'] as String,
        branch: json['branch'] as String,
        account: json['account'] as String,
        accountType: json['accountType'] as String,
        createDate: json['createDate'] as String,
      );
    } catch (e) {
      print('Erro ao fazer parse do PixAccount: $e');
      print('JSON recebido: $json');
      rethrow;
    }
  }
}

class PixOwner {
  final String type;
  final String documentNumber;
  final String name;

  PixOwner({
    required this.type,
    required this.documentNumber,
    required this.name,
  });

  factory PixOwner.fromMap(Map<String, dynamic> json) {
    try {
      return PixOwner(
        type: json['type'] as String,
        documentNumber: json['documentNumber'] as String,
        name: json['name'] as String,
      );
    } catch (e) {
      print('JSON recebido: $json');
      rethrow;
    }
  }
}

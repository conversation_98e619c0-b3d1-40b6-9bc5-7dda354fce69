// [{
//     "keyType": "EVP",
//     "key": "1520b1ea-77c9-4688-845f-7648e80093e8",
//     "account": {
//       "participant": "********",
//       "branch": "0001",
//       "account": "413179",
//       "accountType": "TRAN",
//       "createDate": "2025-03-28T13:36:52Z"
//     },
//     "owner": {
//       "type": "NATURAL_PERSON",
//       "documentNumber": "***********",
//       "name": "<PERSON><PERSON>e"
//     }
//   },
// ]

import 'enums/key_types_enum.dart';

class KeyPixModel {
  final KeyType keyType;
  final String key;

  KeyPixModel({
    required this.keyType,
    required this.key,
  });

  factory KeyPixModel.fromMap(Map<String, dynamic> json) {
    return KeyPixModel(
      keyType: KeyType.fromString(json['keyType'] as String),
      key: json['key'] as String,
    );
  }
}

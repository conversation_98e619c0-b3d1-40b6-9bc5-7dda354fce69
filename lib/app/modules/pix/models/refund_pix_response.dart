import 'dart:convert';

class RefundPixResponse {
  final String id;
  final String originalPaymentId;
  final String endToEndId;
  final String originalEndToEndId;
  final double amount;
  final String? description;
  final String reason;
  final DateTime? createdAt;

  RefundPixResponse({
    required this.id,
    required this.originalPaymentId,
    required this.endToEndId,
    required this.originalEndToEndId,
    required this.amount,
    this.description,
    required this.reason,
    required this.createdAt,
  });

  RefundPixResponse copyWith({
    String? id,
    String? originalPaymentId,
    String? endToEndId,
    String? originalEndToEndId,
    double? amount,
    String? description,
    String? reason,
    DateTime? createdAt,
  }) {
    return RefundPixResponse(
      id: id ?? this.id,
      originalPaymentId: originalPaymentId ?? this.originalPaymentId,
      endToEndId: endToEndId ?? this.endToEndId,
      originalEndToEndId: originalEndToEndId ?? this.originalEndToEndId,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      reason: reason ?? this.reason,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  factory RefundPixResponse.fromMap(Map<String, dynamic> map) {
    return RefundPixResponse(
      id: map['id'] as String,
      originalPaymentId: map['originalPaymentId'] as String,
      endToEndId: map['endToEndId'] as String,
      originalEndToEndId: map['returnIdentification'] as String,
      amount: map['amount'] as double,
      description: map['reversalDescription'] as String?,
      reason: map['reason'] as String,
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'] as String)
          : null,
    );
  }

  factory RefundPixResponse.fromJson(String source) =>
      RefundPixResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'RefundPixResponse(id: $id, originalPaymentId: $originalPaymentId, endToEndId: $endToEndId, originalEndToEndId: $originalEndToEndId, amount: $amount, description: $description, reason: $reason, createdAt: ${createdAt.toString()})';
  }

  @override
  bool operator ==(covariant RefundPixResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.originalPaymentId == originalPaymentId &&
        other.endToEndId == endToEndId &&
        other.originalEndToEndId == originalEndToEndId &&
        other.amount == amount &&
        other.description == description &&
        other.createdAt == createdAt &&
        other.reason == reason;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        originalPaymentId.hashCode ^
        endToEndId.hashCode ^
        originalEndToEndId.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        createdAt.hashCode ^
        reason.hashCode;
  }
}

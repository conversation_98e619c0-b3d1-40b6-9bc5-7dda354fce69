class TariffService {
  final String serviceId;
  final String serviceName;
  final int exemptQuantity;
  final String pricePerExceedingOp;

  TariffService({
    required this.serviceId,
    required this.serviceName,
    required this.exemptQuantity,
    required this.pricePerExceedingOp,
  });

  factory TariffService.fromMap(Map<String, dynamic> map) {
    return TariffService(
      serviceId: map['service_id'] as String,
      serviceName: map['serviceName'] as String,
      exemptQuantity: map['exemptQuantity'] as int,
      pricePerExceedingOp: map['pricePerExceedingOp'].toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'service_id': serviceId,
      'serviceName': serviceName,
      'exemptQuantity': exemptQuantity,
      'pricePerExceedingOp': pricePerExceedingOp,
    };
  }

  double get priceAsDouble {
    return double.tryParse(pricePerExceedingOp) ?? 0.0;
  }

  bool get isPixTariff {
    return serviceName.toLowerCase().contains('pix');
  }

  @override
  String toString() {
    return 'TariffService(serviceId: $serviceId, serviceName: $serviceName, exemptQuantity: $exemptQuantity, pricePerExceedingOp: $pricePerExceedingOp)';
  }
}

class TariffServiceResponse {
  final List<TariffService> services;

  TariffServiceResponse({required this.services});

  factory TariffServiceResponse.fromMap(Map<String, dynamic> map) {
    final servicesList = map['services'] as List? ?? [];
    return TariffServiceResponse(
      services: servicesList
          .map(
            (service) => TariffService.fromMap(service as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  factory TariffServiceResponse.fromList(List<dynamic> servicesList) {
    return TariffServiceResponse(
      services: servicesList
          .map(
            (service) => TariffService.fromMap(service as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'services': services.map((service) => service.toMap()).toList(),
    };
  }

  List<TariffService> get pixTariffs {
    return services.where((service) => service.isPixTariff).toList();
  }

  TariffService? getTariffByServiceName(String serviceName) {
    try {
      return services.firstWhere(
        (service) => service.serviceName.toLowerCase().contains(
          serviceName.toLowerCase(),
        ),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'TariffServiceResponse(services: $services)';
  }
}

class TariffModel {
  final String serviceId;
  final String serviceName;
  final int exemptQuantity;
  final String pricePerExceedingOp;

  TariffModel({
    required this.serviceId,
    required this.serviceName,
    required this.exemptQuantity,
    required this.pricePerExceedingOp,
  });

  factory TariffModel.fromMap(Map<String, dynamic> json) {
    return TariffModel(
      serviceId: json['service_id'] as String,
      serviceName: json['serviceName'] as String,
      exemptQuantity: json['exemptQuantity'] as int,
      pricePerExceedingOp: json['pricePerExceedingOp'].toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'service_id': serviceId,
      'serviceName': serviceName,
      'exemptQuantity': exemptQuantity,
      'pricePerExceedingOp': pricePerExceedingOp,
    };
  }

  // Getter para facilitar o uso
  double get priceAsDouble {
    return double.tryParse(pricePerExceedingOp) ?? 0.0;
  }

  // Verificar se é tarifa PIX
  bool get isPixTariff {
    return serviceName.toLowerCase().contains('pix');
  }

  @override
  String toString() {
    return 'TariffModel(serviceId: $serviceId, serviceName: $serviceName, exemptQuantity: $exemptQuantity, pricePerExceedingOp: $pricePerExceedingOp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TariffModel &&
        other.serviceId == serviceId &&
        other.serviceName == serviceName &&
        other.exemptQuantity == exemptQuantity &&
        other.pricePerExceedingOp == pricePerExceedingOp;
  }

  @override
  int get hashCode {
    return serviceId.hashCode ^
        serviceName.hashCode ^
        exemptQuantity.hashCode ^
        pricePerExceedingOp.hashCode;
  }
}

import 'dart:convert';

// ignore_for_file: public_member_api_docs, sort_constructors_first
class RefundPixModel {
  /// transactionId
  final String? id;
  final double amount;
  final String? reversaldescription;

  /// <PERSON><PERSON><PERSON> da Devolução. Podendo ser:
  /// BE08: Devolvido como resultado de um erro bancário.
  /// FR01: Devolução por suspeita de fraude.
  /// MD06: Devolução solicitada pelo cliente final.
  /// SL02: Devolução do valor em dinheiro devido a um erro relacionado ao Pix Saque ou Pix Troco.
  final String reason;
  final String? endToEndId;

  /// É necessário informar pelo menos um dos campos: transactionId ou endToEndId.
  RefundPixModel({
    required this.id,
    required this.amount,
    this.reversaldescription,
    this.reason = 'MD06',
    required this.endToEndId,
  }) {
    assert(id != null || endToEndId != null);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'amount': amount,
      'reversaldescription': reversaldescription,
      'reason': reason,
      'endToEndId': endToEndId,
    };
  }

  factory RefundPixModel.fromMap(Map<String, dynamic> map) {
    return RefundPixModel(
      id: map['id'] != null ? map['id'] as String : null,
      amount: map['amount'] as double,
      reversaldescription: map['reversaldescription'] as String,
      reason: map['reason'] as String,
      endToEndId:
          map['endToEndId'] != null ? map['endToEndId'] as String : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory RefundPixModel.fromJson(String source) =>
      RefundPixModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/notifications/models/notification_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../data/repository/i_notifications_repository.dart';

part 'notifications_event.dart';
part 'notifications_state.dart';

class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  final INotificationsRepository repo;
  int page = 1;
  List<NotificationResponse> notifications = [];

  NotificationsBloc(this.repo) : super(NotificationsInitial()) {
    on<NotificationsEvent>((event, emit) async {
      if (event is FetchNotifications) {
        emit(NotificationsLoading());

        try {
          page = 1;
          notifications = await repo.notifications(
            pageSize: 10,
            page: page,
            // read: read,
          );
          emit(NotificationsSuccess(notifications: notifications));
          await Future.delayed(const Duration(milliseconds: 500), () {
            markNotificationAsRead(notifications);
          });
        } catch (e) {
          emit(NotificationsError(error: e as ErrorResponse));
        }
      }

      if (event is PaginationNotifications) {
        try {
          emit(NotificationsLoading());
          page++;
          var list = await repo.notifications(
            pageSize: 10,
            page: page,
            // read: read,
          );
          notifications.addAll(list);
          emit(NotificationsSuccess(
              notifications: notifications, isMore: list.isNotEmpty));

          await Future.delayed(const Duration(milliseconds: 500), () {
            markNotificationAsRead(list);
          });
        } catch (e) {
          emit(NotificationsError(error: e as ErrorResponse));
        }
      }
    });
  }

  markNotificationAsRead(List<NotificationResponse> list) {
    for (NotificationResponse notification in list) {
      if (!notification.read) {
        repo.markAsRead(notification.id);
      }
    }
  }
}

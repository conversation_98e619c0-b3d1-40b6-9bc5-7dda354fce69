part of 'notifications_bloc.dart';

sealed class NotificationsState extends Equatable {
  const NotificationsState({this.isMore = true});

  final bool isMore;
  @override
  List<Object> get props => [];
}

final class NotificationsInitial extends NotificationsState {}

final class NotificationsLoading extends NotificationsState {}

final class NotificationsSuccess extends NotificationsState {
  final List<NotificationResponse> notifications;

  const NotificationsSuccess({
    required this.notifications,
    super.isMore,
  });
}

final class NotificationsError extends NotificationsState {
  const NotificationsError({required this.error});

  final ErrorResponse error;
}

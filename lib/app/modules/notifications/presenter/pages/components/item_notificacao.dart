import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/notifications/models/notification_response.dart';

import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/utils/utils.dart';

class ItemNotificacao extends StatelessWidget {
  final NotificationResponse notification;

  const ItemNotificacao({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return ContainerResponsive(
      height: 106,
      color: !notification.read
          ? ColorsApp.verde[100]
          : const Color.fromARGB(255, 243, 243, 243),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Text(
                  Utils.formatDataWithDay(
                          dataApi: notification.createdAt.toString())
                      .toUpperCase(),
                  style: textTheme.labelSmall!.copyWith(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                  ),
                )
              ],
            ),
            SizedBoxResponsive(height: 8),
            Text(
              notification.body,
              style: textTheme.bodyMedium!.copyWith(
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

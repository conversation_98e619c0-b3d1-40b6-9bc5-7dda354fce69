import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/custom_footer_pagination.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../bloc/notifications_bloc.dart';
import 'components/item_notificacao.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<NotificationsBloc>(),
      child: const _NotificationsView(),
    );
  }
}

class _NotificationsView extends StatefulWidget {
  const _NotificationsView({super.key});

  @override
  State<_NotificationsView> createState() => __NotificationsViewState();
}

class __NotificationsViewState extends State<_NotificationsView> {
  final _refreshController = RefreshController(initialRefresh: true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: const I18n().notificacoes.toUpperCase(),
      ),
      body: BlocConsumer<NotificationsBloc, NotificationsState>(
        listener: (context, state) {
          if (state is NotificationsLoading) {
            _refreshController.refreshCompleted();
          }
          // if (state.progressLoading) _refreshController.loadComplete();
        },
        builder: (context, state) {
          return SmartRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: state.isMore,
            onRefresh: _refresh,
            footer: const CustomFooterPagination(),
            onLoading: _loading,
            child: state is NotificationsSuccess
                ? _buildList(context, state)
                : const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  void _refresh() {
    BlocProvider.of<NotificationsBloc>(context)
        .add(const FetchNotifications(1));
  }

  void _loading() {
    BlocProvider.of<NotificationsBloc>(context).add(PaginationNotifications());
  }

  _buildList(BuildContext context, NotificationsSuccess state) {
    if (state.notifications.isEmpty) {
      return _buildEmpty(context);
    } else {
      return ListView.separated(
        itemCount: state.notifications.length,
        itemBuilder: (context, index) {
          final notification = state.notifications[index];

          return ItemNotificacao(
            notification: notification,
          );
        },
        separatorBuilder: (context, index) => const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Divider(
            height: 0,
            // thickness: 1,
            color: Colors.black26,
          ),
        ),
      );
    }
  }

  // _buildItens(BuildContext context, int index) {
  //   List<Widget> list = [];
  //   for (Notificacao notificacao in widget.state.listNotificacao) {
  //     list.add(
  //       ItemNotificacao(
  //         notificacao: notificacao,
  //       ),
  //     );
  //   }
  //   return list;
  // }

  _buildEmpty(BuildContext context) {
    return Center(
      child: Text(
        I18n.of(context)!.notificacao_empty,
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
            // color: ColorsApp.dark[400],
            ),
      ),
    );
  }
}

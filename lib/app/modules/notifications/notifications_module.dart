import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/notifications/data/datasources/notifications_datasource.dart';
import 'package:siclosbank/app/modules/notifications/data/repository/i_notifications_repository.dart';
import 'package:siclosbank/app/modules/notifications/data/repository/notifications_repositoy_impl.dart';
import 'package:siclosbank/app/modules/notifications/presenter/bloc/notifications_bloc.dart';
import 'package:siclosbank/app/app_module.dart';
import 'presenter/pages/notifications_page.dart';

class NotificationsModule extends Module {
  @override
  void binds(i) {
    i.addLazySingleton<INotificationsDatasource>(NotificationsDatasource.new);
    i.addLazySingleton<INotificationsRepository>(
        NotificationsRepositoyImpl.new);

// blocs
    i.add(NotificationsBloc.new);
  }

  @override
  List<Module> get imports => [
        AppModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (context) => const NotificationsPage());
  }
}

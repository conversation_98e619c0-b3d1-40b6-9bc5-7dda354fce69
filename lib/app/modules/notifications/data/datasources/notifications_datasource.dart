import 'package:siclosbank/app/modules/notifications/data/datasources/notifications_endpoints.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

abstract class INotificationsDatasource {
  Future<ApiResponse> notifications(
      {required int pageSize, required int page, bool? read});
  Future<ApiResponse> checkNotifications();
  Future<ApiResponse> markAsRead(String notificationId);
}

class NotificationsDatasource implements INotificationsDatasource {
  final IClient _client;

  NotificationsDatasource(this._client);

  @override
  Future<ApiResponse> checkNotifications() {
    // TODO: implement checkNotifications
    throw UnimplementedError();
  }

  @override
  Future<ApiResponse> notifications({
    required int pageSize,
    required int page,
    bool? read,
  }) async {
    final result = await _client.fetch(
      method: 'GET',
      path: NotificationsEndpoints.notifications(
        page: page,
        pageSize: pageSize,
        read: read,
      ),
    );

    return result;
  }

  @override
  Future<ApiResponse> markAsRead(String notificationId) async {
    final result = await _client.fetch(
      method: 'PUT',
      path: NotificationsEndpoints.readNotification(notificationId),
    );

    return result;
  }
}

import 'package:siclosbank/app/modules/notifications/models/notification_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../datasources/notifications_datasource.dart';
import 'i_notifications_repository.dart';

class NotificationsRepositoyImpl implements INotificationsRepository {
  final INotificationsDatasource datasource;

  NotificationsRepositoyImpl(this.datasource);

  @override
  Future checkNotifications() {
    // TODO: implement checkNotifications
    throw UnimplementedError();
  }

  @override
  Future<List<NotificationResponse>> notifications({
    required int pageSize,
    required int page,
    bool? read,
  }) async {
    try {
      final result = await datasource.notifications(
        pageSize: pageSize,
        page: page,
        read: read,
      );

      final list = result.data as List;
      final newList = list.map(
        (e) {
          return NotificationResponse.fromMap(e);
        },
      ).toList();

      return newList;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future markAsRead(String notificationId) async {
    try {
      final result = await datasource.markAsRead(notificationId);
      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class NotificationResponse {
  NotificationResponse({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.status,
    required this.createdAt,
    required this.read,
  });

  final String id;
  final String userId;
  final String title;
  final String body;
  final String status;
  final bool read;
  final DateTime? createdAt;

  @override
  List<Object?> get props => [id, userId, title, body, status, read];

  NotificationResponse copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    String? status,
    bool? read,
    DateTime? createdAt,
  }) {
    return NotificationResponse(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      status: status ?? this.status,
      read: read ?? this.read,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'user_id': userId,
      'title': title,
      'body': body,
      'status': status,
      'read': read,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  factory NotificationResponse.fromMap(Map<String, dynamic> map) {
    return NotificationResponse(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      title: map['title'] as String,
      body: map['body'] as String,
      status: map['status'] as String,
      read: map['read'] as bool,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory NotificationResponse.fromJson(String source) =>
      NotificationResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'NotificationResponse(id: $id, userId: $userId, title: $title, body: $body, status: $status, read: $read)';
  }

  @override
  bool operator ==(covariant NotificationResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.userId == userId &&
        other.title == title &&
        other.body == body &&
        other.status == status &&
        other.read == read;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        title.hashCode ^
        body.hashCode ^
        status.hashCode ^
        read.hashCode;
  }
}

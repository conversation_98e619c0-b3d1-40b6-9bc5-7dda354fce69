import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/home/<USER>/blocs/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/blocs/fast_credit/fast_credit_simulation_bloc.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/errors/user_under_3_months_view.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/errors/user_under_6_months_view.dart';

import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/keep_alive_page.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/navigation/named_routes.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/bloc/fast_credit/fast_credit_bloc.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'pages/simulation_confirmation_page.dart';
import 'pages/simulation_address_page.dart';

class FastCreditLoanSimulationPage extends StatelessWidget {
  const FastCreditLoanSimulationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<FastCreditSimulationBloc>(
      create: (context) => Modular.get<FastCreditSimulationBloc>(),
      child: const _ConventionalLoanSimulationView(),
    );
  }
}

class _ConventionalLoanSimulationView extends StatefulWidget {
  const _ConventionalLoanSimulationView({super.key});

  @override
  State<_ConventionalLoanSimulationView> createState() =>
      __ConventionalLoanSimulationViewState();
}

class __ConventionalLoanSimulationViewState
    extends State<_ConventionalLoanSimulationView> {
  List<Widget> pages = [];

  late PageController pageController;
  int currentPage = 0;

  @override
  void initState() {
    pages = [
      const KeepAlivePage(child: FastCreditSimulationConfirmationPage()),
      const KeepAlivePage(child: FastCreditSimulationAddressPage()),
    ];
    super.initState();
    pageController = PageController(
      initialPage: currentPage,
      keepPage: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final userUnder3Month =
        AppSession.getInstance().collaborator!.experiencePeriod == true;

    return Scaffold(
      appBar: AppBarApp(
        title: _getTituloAppBar(currentPage),
        clickBack: _onClickBack,
      ),
      body: userUnder3Month
          ? const UserUnder3MonthsView()
          : BlocConsumer<FastCreditSimulationBloc, FastCreditSimulationState>(
              listener: (context, state) async {
                if (state is SimulationConfirmSuccess) {
                  await push(Routes.loanSendToAnalysis).then((value) {
                    BlocProvider.of<FastCreditBloc>(context)
                        .add(InitFetchEvent());

                    pop(true);
                  });
                }

                if (state.changePage != null) {
                  if (state.changePage == ChangePage.next) {
                    _nextPage();
                  } else {
                    _backPage();
                  }
                }

                if (state is FastCreditSimulationError) {
                  if (currentPage != 0) {
                    DialogUtils.showSnackError(context, state.error);
                  }
                }
              },
              builder: (context, state) {
                return PageView(
                  controller: pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: pages,
                );
              },
            ),
    );
  }

  String _getTituloAppBar(int currentPage) {
    switch (currentPage) {
      case 1:
        return const I18n().endereco.toUpperCase();
      default:
        return const I18n().simulacao.toUpperCase();
    }
  }

  _onClickBack() async {
    pop();
    // _backPage();
  }

  void _nextPage() {
    setState(() {
      currentPage++;
      _movePage();
    });
  }

  void _movePage({bool cancel = false}) {
    pageController.animateToPage(currentPage,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
    if (cancel) {
      pop(cancel);
    }
  }

  void _backPage() {
    setState(() {
      currentPage--;
      _movePage();
    });
  }

  // Future<void> _showDialogCancelarEmprestimo(BuildContext contextApp) async {
  //   await showModalBottomSheet(
  //     elevation: 0,
  //     context: context,
  //     isScrollControlled: true,
  //     builder: (context) {
  //       return SheetAlertConfirm(
  //         title: I18n.of(context)!.deseja_cancelar,
  //         message: I18n.of(context)!.ao_voltar_cancelar,
  //         textPositiveButton: I18n.of(context)!.sim_cancelar,
  //         onClickPositive: () {
  //           _backPage();
  //         },
  //       );
  //     },
  //   );
  // }
}

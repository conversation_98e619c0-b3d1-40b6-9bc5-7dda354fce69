import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/errors/error_in_simulation_view.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../../../../blocs/fast_credit/fast_credit_simulation_bloc.dart';
import '../components/item_horizontal_text.dart';

class FastCreditSimulationConfirmationPage extends StatelessWidget {
  const FastCreditSimulationConfirmationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _SimulationConfirmationView();
  }
}

class _SimulationConfirmationView extends StatefulWidget {
  const _SimulationConfirmationView({super.key});

  @override
  State<_SimulationConfirmationView> createState() =>
      __SimulationConfirmationViewState();
}

class __SimulationConfirmationViewState
    extends State<_SimulationConfirmationView> {
  // SimulationResponse? simulation;
  @override
  void initState() {
    BlocProvider.of<FastCreditSimulationBloc>(context).add(
      const RunSimulationEvent(),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<FastCreditSimulationBloc, FastCreditSimulationState>(
        listener: (context, state) {
          // if (state is LoanSimulationSuccess) {
          //   final fastCredit = AppSession.getInstance().politicalFastCredit;

          //   setState(() {
          //     simulation = BlocProvider.of<FastCreditSimulationBloc>(context)
          //             .simulationResponse ??
          //         SimulationResponse(
          //           valueCredit: fastCredit.valueCredit,
          //           valueTotal: fastCredit.valueCredit,
          //           interestRates: fastCredit.interestRates,
          //           installmentsMany: fastCredit.installmentMany!,
          //           installmentValue: (140.0),
          //           firstPaymentDate:
          //               DateTime.now().add(const Duration(days: 30)).toString(),
          //         );
          //   });
          // }
        },
        builder: (context, state) {
          if (state is FastCreditSimulationCenterLoading) {
            return Center(
              child: Utils.circularProgressButton(),
            );
          }

          if (state is FastCreditSimulationError) {
            return const ErrorInSimulationView();
          }
          if (state is LoanSimulationSuccess) {
            // final fastCredit = AppSession.getInstance().politicalFastCredit;

            // simulation = BlocProvider.of<FastCreditSimulationBloc>(context)
            //         .simulationResponse ??
            //     SimulationResponse(
            //       valueCredit: fastCredit.valueCredit,
            //       valueTotal: fastCredit.valueCredit,
            //       interestRates: fastCredit.interestRates,
            //       installmentsMany: fastCredit.installmentMany!,
            //       installmentValue: (140.0),
            //       firstPaymentDate:
            //           DateTime.now().add(const Duration(days: 30)).toString(),
            //     );

            final simulation = state.simulationResponse;
            return CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 16, top: 20, right: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          const I18n().pedido,
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                        const SizedBox(
                          height: 40,
                        ),
                        ItemHorizontalText(
                          title: const I18n().valor_liberado,
                          info: Utils.formatBalance(
                            simulation?.valueCredit,
                          ),
                        ),
                        ItemHorizontalText(
                          title: const I18n().parcelas,
                          info: '${simulation?.installmentsMany}x',
                        ),
                        ItemHorizontalText(
                          title: const I18n().valor_parcelas,
                          info: Utils.formatBalance(
                            simulation?.installmentValue,
                          ),
                        ),
                        ItemHorizontalText(
                          title: const I18n().taxa_juros_estimada,
                          info: I18n.of(context)!.taxaJuros(Utils.formatDecimal(
                              (simulation?.interestRates ?? 0) * 100)),
                        ),
                        ItemHorizontalText(
                          title: const I18n().primeiro_vencimento,
                          info: Utils.formatDateFirstPayment(
                            dataApi: simulation?.firstPaymentDate,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  fillOverscroll: false,
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                    child: Container(
                      alignment: Alignment.bottomCenter,
                      child: ButtonApp(
                        enabled: true,
                        width: MediaQuery.of(context).size.width,
                        progress: state is FastCreditSimulationLoading
                            ? Utils.circularProgressButton(size: 20)
                            : null,
                        text: const I18n().confirmar_pedido,
                        onPress: () {
                          BlocProvider.of<FastCreditSimulationBloc>(context)
                              .add(const ChangePageEvent());
                        },
                      ),
                    ),
                  ),
                )
              ],
            );
          }
          return Container();
        },
      ),
    );
  }
}

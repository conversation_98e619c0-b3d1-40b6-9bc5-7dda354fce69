import 'package:flutter/material.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/utils/image_utils.dart';

class ErrorInSimulationView extends StatelessWidget {
  const ErrorInSimulationView({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 38),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  I18n.of(context)!.simulacao_nao_pode_ser_iniciada,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  I18n.of(context)!.simulacao_nao_pode_ser_iniciada_explicacao,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: false,
          child: Container(
            alignment: Alignment.bottomCenter,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ImageUtils.imgBackgroundErroSimulacao(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

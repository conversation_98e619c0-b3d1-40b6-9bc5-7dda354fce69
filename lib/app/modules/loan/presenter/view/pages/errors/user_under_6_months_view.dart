import 'package:flutter/material.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/utils/image_utils.dart';

class UserUnder6MonthsView extends StatelessWidget {
  const UserUnder6MonthsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 38),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    I18n.of(context)!.usuario_6_meses_empresa,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: false,
            child: Container(
              alignment: Alignment.bottomRight,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ImageUtils.imgUsuario6MesesEmpresa(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

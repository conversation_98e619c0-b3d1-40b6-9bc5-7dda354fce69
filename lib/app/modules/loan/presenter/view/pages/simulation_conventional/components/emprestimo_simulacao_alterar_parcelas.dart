import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_xlider/flutter_xlider.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../../shared/utils/utils.dart';

class SimulationChangeInstallmentWidget extends StatelessWidget {
  const SimulationChangeInstallmentWidget({
    required this.valueTotalOwed,
    required this.installmentsMin,
    required this.installmentsMax,
    required this.sliderValue,
    required this.onDragCompleted,
    super.key,
  });
  final double valueTotalOwed;
  final int installmentsMin;
  final int installmentsMax;
  final double sliderValue;
  final Function(int, dynamic, dynamic) onDragCompleted;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Utils.setScreeenResponsive(
      context: context,
      height: size.height,
      width: size.width,
    );

    return ContainerResponsive(
      height: 270,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(width: 0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(
          left: 16,
          top: 30,
          right: 16,
          bottom: 16,
        ),
        child: Column(
          children: <Widget>[
            _CabecalhoAlterarParcelas(
              valorEmprestimo: valueTotalOwed,
            ),
            Expanded(
              child: _ConteudoAlteraParcelas(
                valorEmprestimo: valueTotalOwed,
                minParcelas: installmentsMin,
                maxParcelas: installmentsMax,
                sliderValue: sliderValue,
                onDragCompleted: onDragCompleted,
              ),
            ),
            Align(
                alignment: Alignment.bottomLeft,
                child: _RodapeAlterarParcelas())
          ],
        ),
      ),
    );
  }
}

class _CabecalhoAlterarParcelas extends StatelessWidget {
  const _CabecalhoAlterarParcelas({
    required this.valorEmprestimo,
    super.key,
  });
  final double valorEmprestimo;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Text(const I18n().total_a_ser_pago),
        const SizedBox(
          height: 12,
        ),
        Text(
          Utils.formatBalance(valorEmprestimo),
          style: bodyText2Bold(context),
        ),
        SizedBoxResponsive(
          height: 30,
        ),
      ],
    );
  }

  TextStyle bodyText2Bold(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.bold,
        );
  }
}

class _ConteudoAlteraParcelas extends StatelessWidget {
  const _ConteudoAlteraParcelas({
    required this.sliderValue,
    required this.minParcelas,
    required this.maxParcelas,
    required this.valorEmprestimo,
    required this.onDragCompleted,
    super.key,
  });
  final double sliderValue;
  final int minParcelas;
  final int maxParcelas;
  final double valorEmprestimo;
  final Function(int, dynamic, dynamic) onDragCompleted;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.bold,
        );

    return Column(
      children: <Widget>[
        FlutterSlider(
          values: [sliderValue],
          min: minParcelas.toDouble(), // ?? 3.0,
          max: maxParcelas.toDouble(), // ?? 4.0,
          onDragCompleted: onDragCompleted,
          tooltip: FlutterSliderTooltip(
            disabled: minParcelas == maxParcelas,
            alwaysShowTooltip: true,
            format: (value) {
              final number = double.tryParse(value);
              final result = (number?.toInt().toString()) ?? value;
              return result;
            },
            rightSuffix: Text(
              "X",
              style: textTheme,
            ),
            textStyle: bodyText2Bold(context),
            boxStyle: const FlutterSliderTooltipBox(
              decoration: BoxDecoration(
                color: Colors.transparent,
              ),
            ),
            // numberFormat: intl.NumberFormat(),
          ),
          trackBar: FlutterSliderTrackBar(
            activeTrackBarHeight: 4,
            inactiveTrackBarHeight: 4,
            activeTrackBar: BoxDecoration(
              color: ColorsApp.verde[400],
              borderRadius: const BorderRadius.all(Radius.circular(100)),
            ),
            inactiveTrackBar: BoxDecoration(
              color: ColorsApp.verde[200],
              borderRadius: const BorderRadius.all(Radius.circular(4)),
            ),
          ),
          handler: FlutterSliderHandler(
            decoration: const BoxDecoration(),
            disabled: minParcelas == maxParcelas,
            opacity: minParcelas == maxParcelas ? 0 : 1,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: ColorsApp.verde[500],
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                '${minParcelas}X', // ?? 3
                style: bodyText2Bold(context),
              ),
              Text(
                '${maxParcelas}X', // ?? 4
                style: bodyText2Bold(context),
              )
            ],
          ),
        )
      ],
    );
  }

  TextStyle bodyText2Bold(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.bold,
        );
  }
}

class _RodapeAlterarParcelas extends StatelessWidget {
  const _RodapeAlterarParcelas({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Text(
        const I18n().valor_maximo_de_parcela_msg,
        style: Theme.of(context).textTheme.labelSmall,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../../../shared/navigation/named_routes.dart';
import '../../../blocs/conv_simulation/conv_simulation_bloc.dart';
import '../errors/user_under_6_months_view.dart';
import 'pages/simulation_confirmation_page.dart';
import 'pages/simulation_address_page.dart';
import 'pages/simulation_installment_page.dart';
import 'pages/simulation_value_page.dart';
import '../../../../../../app_controller.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/keep_alive_page.dart';

import '../../../../../../../localization/generated/i18n.dart';

import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';

class ConventionalLoanSimulationPage extends StatelessWidget {
  const ConventionalLoanSimulationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ConvSimulationBloc>(
      create: (context) => Modular.get<ConvSimulationBloc>(),
      child: const _ConventionalLoanSimulationView(),
    );
  }
}

class _ConventionalLoanSimulationView extends StatefulWidget {
  const _ConventionalLoanSimulationView({super.key});

  @override
  State<_ConventionalLoanSimulationView> createState() =>
      __ConventionalLoanSimulationViewState();
}

class __ConventionalLoanSimulationViewState
    extends State<_ConventionalLoanSimulationView> {
  List<Widget> pages = [];

  late PageController pageController;
  int currentPage = 0;
  @override
  void initState() {
    pages = [
      // const KeepAlivePage(child: ConvSimulationTermsPage()),
      const KeepAlivePage(child: SimulationValuePage()),
      const KeepAlivePage(child: SimulationInstallmentPage()),
      const KeepAlivePage(child: SimulationConfirmationPage()),
      const KeepAlivePage(child: SimulationAddressPage()),
      // const KeepAlivePage(child: ConvSimulationTermsPage()),
    ];
    super.initState();
    pageController = PageController(
      initialPage: currentPage,
      keepPage: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final userUnder6Month =
        AppSession.getInstance().collaborator!.under6Months == true;

    return Scaffold(
      appBar: AppBarApp(
        title: _getTituloAppBar(currentPage),
        clickBack: _onClickBack,
      ),
      body: userUnder6Month
          ? const UserUnder6MonthsView()
          : BlocConsumer<ConvSimulationBloc, ConvSimulationState>(
              listener: (context, state) async {
                if (state is SimulationConfirmSuccess) {
                  await push(Routes.loanSendToAnalysis).then((value) {
                    pop(true);
                  });
                }

                if (state.changePage != null) {
                  if (state.changePage == ChangePage.next) {
                    _nextPage();
                  } else {
                    _backPage();
                  }
                }

                if (state is ConvSimulationError) {
                  DialogUtils.showSnackError(context, state.error);
                }
              },
              builder: (context, state) {
                return SafeArea(
                  child: PageView(
                    controller: pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: pages,
                  ),
                );
              },
            ),
    );
  }

  String _getTituloAppBar(int currentPage) {
    switch (currentPage) {
      case 2:
        return const I18n().confirmacao.toUpperCase();
      case 3:
        return const I18n().endereco.toUpperCase();
      // case 4:
      //   return const I18n().termos.toUpperCase();
      default:
        return const I18n().simulacao.toUpperCase();
    }
  }

  _onClickBack() async {
    if (currentPage == 0) {
      pop();
    } else if (currentPage == 3) {
      _showDialogCancelarEmprestimo(context);
      // } else if (currentPage == 4) {
      //   _showDialogBackPage(context);
    } else {
      _backPage();
    }
  }

  void _nextPage() {
    setState(() {
      currentPage++;
      _movePage();
    });
  }

  void _movePage({bool cancel = false}) {
    pageController.animateToPage(currentPage,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
    if (cancel) {
      pop(cancel);
    }
  }

  void _backPage() {
    setState(() {
      currentPage--;
      _movePage();
    });
  }

  Future<void> _showDialogBackPage(BuildContext contextApp) async {
    await showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return SheetAlertConfirm(
          title: I18n.of(context)!.deseja_voltar,
          message: I18n.of(context)!.ao_voltar_terms,
          textPositiveButton: I18n.of(context)!.voltar,
          textNegativeButton: I18n.of(context)!.nao,
          onClickPositive: () {
            pop(true);
            pop(true);
          },
        );
      },
    );
  }

  Future<void> _showDialogCancelarEmprestimo(BuildContext contextApp) async {
    await showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return SheetAlertConfirm(
          title: I18n.of(context)!.deseja_cancelar,
          message: I18n.of(context)!.ao_voltar_cancelar,
          textPositiveButton: I18n.of(context)!.sim_cancelar,
          onClickPositive: () {
            _backPage();
          },
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../../../../blocs/conv_simulation/conv_simulation_bloc.dart';
import '../components/item_horizontal_text.dart';

class SimulationConfirmationPage extends StatelessWidget {
  const SimulationConfirmationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _SimulationConfirmationView();
  }
}

class _SimulationConfirmationView extends StatefulWidget {
  const _SimulationConfirmationView({super.key});

  @override
  State<_SimulationConfirmationView> createState() =>
      __SimulationConfirmationViewState();
}

class __SimulationConfirmationViewState
    extends State<_SimulationConfirmationView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<ConvSimulationBloc, ConvSimulationState>(
        listener: (context, state) {
          // TODO: implement listener
        },
        builder: (context, state) {
          if (state is ConvSimulationCenterLoading) {
            return Center(
              child: Utils.circularProgressButton(),
            );
          }

          final simulation =
              BlocProvider.of<ConvSimulationBloc>(context).simulationResponse!;
          return CustomScrollView(
            slivers: <Widget>[
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.only(left: 16, top: 20, right: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        I18n().pedido,
                        style: Theme.of(context).textTheme.labelLarge,
                      ),
                      SizedBox(
                        height: 40,
                      ),
                      ItemHorizontalText(
                        title: I18n().valor_liberado,
                        info: Utils.formatBalance(
                          simulation.valueCredit,
                        ),
                      ),
                      ItemHorizontalText(
                        title: I18n().parcelas,
                        info: '${simulation.installmentsMany}x',
                      ),
                      ItemHorizontalText(
                        title: I18n().valor_parcelas,
                        info: Utils.formatBalance(
                          simulation.installmentValue,
                        ),
                      ),
                      ItemHorizontalText(
                        title: I18n().taxa_juros_estimada,
                        info: I18n.of(context)!.taxaJuros(Utils.formatDecimal(
                            simulation.interestRates * 100)),
                      ),
                      ItemHorizontalText(
                        title: I18n().primeiro_vencimento,
                        info: Utils.formatDateFirstPayment(
                          dataApi: simulation.firstPaymentDate,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverFillRemaining(
                hasScrollBody: false,
                fillOverscroll: false,
                child: Padding(
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  child: Container(
                    alignment: Alignment.bottomCenter,
                    child: ButtonApp(
                      enabled: true,
                      width: MediaQuery.of(context).size.width,
                      progress: state is ConvSimulationLoading
                          ? Utils.circularProgressButton(size: 20)
                          : null,
                      text: I18n().confirmar_pedido,
                      onPress: () {
                        BlocProvider.of<ConvSimulationBloc>(context)
                            .add(const ChangePageEvent());
                      },
                    ),
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }
}

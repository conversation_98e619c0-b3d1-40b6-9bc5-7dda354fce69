import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/conv_simulation/conv_simulation_bloc.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';

import '../../../../../../../app_controller.dart';
import '../../../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../components/emprestimo_simulacao_alterar_parcelas.dart';
import '../components/emprestimo_simulacao_taxa.dart';
import '../components/emprestimo_simulacao_valor_parcela.dart';

class SimulationInstallmentPage extends StatelessWidget {
  const SimulationInstallmentPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<ConvSimulationBloc>(context),
      child: const _SimulationInstallment(),
    );
  }
}

class _SimulationInstallment extends StatefulWidget {
  const _SimulationInstallment({super.key});

  @override
  State<_SimulationInstallment> createState() => __SimulationInstallmentState();
}

class __SimulationInstallmentState extends State<_SimulationInstallment> {
  late double _sliderValue;

  @override
  void initState() {
    _sliderValue = 3;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Utils.setScreeenResponsive(
        context: context, height: size.height, width: size.width);

    final political = AppSession.getInstance().conventionalCreditPolitical;
    final marginLimitUser = AppSession.getInstance().marginLimitResponse;
    final installmentsMin = political!.timeMin!;
    final installmentsMax = marginLimitUser?.installmentsMax ?? installmentsMin;

    var bloc = BlocProvider.of<ConvSimulationBloc>(context);

    final instalmentMaxUser =
        bloc.simulationResponse!.valueCredit / (political.installmentValueMin);

    return Scaffold(
      body: BlocConsumer<ConvSimulationBloc, ConvSimulationState>(
        listener: (context, state) {
          _sliderValue = bloc.simulationResponse!.installmentsMany.toDouble();
        },
        builder: (context, state) {
          final margin = AppSession.getInstance().marginLimitResponse!;
          final valueInstallmentExceded =
              bloc.simulationResponse!.installmentValue > margin.margin;
          return CustomScrollView(
            slivers: <Widget>[
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.only(left: 16, top: 30, right: 16),
                  child: Column(
                    children: <Widget>[
                      Text(const I18n().emprestimo_simulacao_parcelas_mensagem),
                      SizedBoxResponsive(
                        height: 20,
                      ),
                      Container(
                        decoration: Utils.dropShadowNewCircle(radius: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: <Widget>[
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: SimulationInterestRatesWidget(
                                    isLoadingInstallment: state
                                        is ConvSimulationInstallmentsLoading,
                                    interestRates:
                                        bloc.simulationResponse!.interestRates *
                                            100,
                                  ),
                                ),
                                Expanded(
                                  child: SimulationInstallmentValueWidget(
                                      isLoadingInstallment: state
                                          is ConvSimulationInstallmentsLoading,
                                      valueInstallment: bloc.simulationResponse!
                                          .installmentValue),
                                ),
                              ],
                            ),
                            IgnorePointer(
                              ignoring:
                                  state is ConvSimulationInstallmentsLoading,
                              child: SimulationChangeInstallmentWidget(
                                installmentsMax:
                                    (instalmentMaxUser > installmentsMax)
                                        ? installmentsMax
                                        : instalmentMaxUser.toInt(),
                                installmentsMin: installmentsMin,
                                sliderValue: _sliderValue,
                                valueTotalOwed:
                                    bloc.simulationResponse!.valueTotal,
                                onDragCompleted:
                                    (handlerIndex, lowerValue, upperValue) {
                                  log('onDragCompleted');
                                  BlocProvider.of<ConvSimulationBloc>(context)
                                      .add(
                                    RunSimulationEvent(
                                      installment: lowerValue.toInt(),
                                      // value: bloc.value!,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      AlertBanner(
                        isShow: valueInstallmentExceded,
                        typeAlertBanner: TypeAlertBanner.error,
                        error:
                            (state is ConvSimulationError) ? state.error : null,
                        message: valueInstallmentExceded
                            ? I18n.of(context)!.valorParcelaMaximo(
                                Utils.formatBalance(
                                  margin.margin,
                                ),
                              )
                            : '',
                      ),
                    ],
                  ),
                ),
              ),
              SliverFillRemaining(
                hasScrollBody: false,
                fillOverscroll: false,
                child: Padding(
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  child: Container(
                    alignment: Alignment.bottomCenter,
                    child: ButtonApp(
                      enabled: true,
                      width: MediaQuery.of(context).size.width,
                      progress: state is ConvSimulationLoading
                          ? Utils.circularProgressButton(size: 20)
                          : null,
                      text: const I18n().continuar,
                      onPress: () {
                        _clickContinuar(
                          valorLiberado: bloc.simulationResponse!.valueCredit,
                          prazoLiberado:
                              bloc.simulationResponse!.installmentsMany,
                          // widget.state.simulacaoResponse!.valorLiberado ?? 0,
                          // widget.state.simulacaoResponse!.prazoLiberado ?? 0,
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  _clickContinuar({required double valorLiberado, required int prazoLiberado}) {
    BlocProvider.of<ConvSimulationBloc>(context).add(const ChangePageEvent());
  }
}

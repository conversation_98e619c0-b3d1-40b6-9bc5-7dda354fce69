import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/conv_simulation/conv_simulation_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:string_validator/string_validator.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/data/models/address_response.dart';
import '../../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../../../../../../pin/presenter/view/check_pin_page.dart';

class SimulationAddressPage extends StatelessWidget {
  const SimulationAddressPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _SimulationAddressView();
  }
}

class _SimulationAddressView extends StatefulWidget {
  const _SimulationAddressView({super.key});

  @override
  State<_SimulationAddressView> createState() => __SimulationAddressViewState();
}

class __SimulationAddressViewState extends State<_SimulationAddressView> {
  final formKey = GlobalKey<FormState>();
  final _cepController = MaskedTextController(mask: '00000-000');
  final _streetController = TextEditingController();
  final _numberController = TextEditingController();
  final _complementController = TextEditingController();
  final _cityController = TextEditingController();
  final _ufController = TextEditingController();
  final _districtController = TextEditingController();
  bool enableContinueButton = false;

  @override
  initState() {
    BlocProvider.of<ConvSimulationBloc>(context)
        .add(const GetAddressUserEvent());
    super.initState();
  }

  _initTexts(AddressResponse address) {
    setState(() {
      _cepController.text = address.cep ?? '';
      _streetController.text = address.street ?? '';
      _numberController.text = address.number ?? '';
      _cityController.text = address.city ?? '';
      _ufController.text = address.state ?? '';
      _districtController.text = address.district ?? '';
      _complementController.text = address.complement ?? '';

      formKey.currentState?.validate();

      if (_cepController.text.isNotEmpty &&
          _streetController.text.isNotEmpty &&
          _numberController.text.isNotEmpty &&
          _cityController.text.isNotEmpty &&
          _ufController.text.isNotEmpty &&
          _districtController.text.isNotEmpty) {
        enableContinueButton = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<ConvSimulationBloc, ConvSimulationState>(
        listener: (context, state) {
          if (state is AddressSuccess) {
            _initTexts(state.addressResponse);
          }
          if (state is CheckAddressSuccess) {
            CheckPinPage.showSheet(
              context,
              () {
                BlocProvider.of<ConvSimulationBloc>(context)
                    .add(const ConfirmSimulationEvent());
              },
            );
          }
        },
        builder: (context, state) {
          if (state is ConvSimulationLoading) {
            return _buildProgress();
          }

          return _buildBody(state);
        },
      ),
    );
  }

  Container _buildProgress() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  _buildBody(state) {
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Form(
              key: formKey,
              autovalidateMode: AutovalidateMode.always,
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, top: 32, right: 16, bottom: 32),
                child: Column(
                  children: <Widget>[
                    Visibility(
                      visible: state is ConvSimulationLoading ||
                          enableContinueButton,
                      child: const SizedBox(
                        height: 39,
                      ),
                    ),
                    Visibility(
                      visible: state is ConvSimulationLoading ||
                          enableContinueButton,
                      child: Text(state is ConvSimulationLoading
                          ? I18n.of(context)!.loading
                          : (enableContinueButton
                              ? I18n.of(context)!.verifique_endereco
                              : '')),
                    ),
                    const SizedBox(
                      height: 28,
                    ),
                    TextFormFieldApp(
                      label: const I18n().cep,
                      controller: _cepController,
                      enable: false,
                      validator: (value) {
                        if (!isLength(value!, 8, 9)) {
                          // adcionar verificacao de cep
                          return const I18n().campo_obrigatorio;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: <Widget>[
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.6,
                          child: TextFormFieldApp(
                            label: const I18n().rua_avenida,
                            controller: _streetController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 6) ||
                                  !value.contains(' ')) {
                                return const I18n().campo_obrigatorio;
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: TextFormFieldApp(
                            label: const I18n().numero_abreviado,
                            textInputType: TextInputType.number,
                            controller: _numberController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 1)) {
                                return const I18n().campo_obrigatorio;
                              }

                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    TextFormFieldApp(
                      label: const I18n().bairro,
                      controller: _districtController,
                      enable: false,
                      validator: (value) {
                        if (!isLength(value!, 3)) {
                          return const I18n().campo_obrigatorio;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    TextFormFieldApp(
                      label: const I18n().complemento,
                      controller: _complementController,
                      enable: false,
                      // validator: (value) {
                      //   if (!isLength(value!, 3)) {
                      //     return const I18n().campo_obrigatorio;
                      //   }

                      //   return null;
                      // },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: <Widget>[
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.5,
                          child: TextFormFieldApp(
                            label: const I18n().cidade,
                            controller: _cityController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 3)) {
                                return const I18n().campo_obrigatorio;
                              }

                              return null;
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: TextFormFieldApp(
                            label: const I18n().estado,
                            controller: _ufController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 1)) {
                                return const I18n().campo_obrigatorio_rh;
                              }

                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    AlertBanner(
                      isShow: !enableContinueButton,
                      typeAlertBanner: TypeAlertBanner.error,
                      message: I18n.of(context)!.campo_obrigatorio_rh,
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: false,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                alignment: Alignment.bottomCenter,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    AlertBanner(
                      isShow: true,
                      typeAlertBanner: TypeAlertBanner.info,
                      message: I18n.of(context)!.verifique_endereco_msg2,
                    ),
                    const SizedBox(height: 32),
                    ButtonApp(
                      enabled: enableContinueButton,
                      width: MediaQuery.of(context).size.width,
                      height: 50,
                      border: 0,
                      text: const I18n().confirmar,
                      progress: state is ConvSimulationLoading
                          ? Utils.circularProgressButton(size: 20)
                          : null,
                      onPress: enableContinueButton
                          ? () {
                              FocusScope.of(context).unfocus();
                              if (formKey.currentState!.validate()) {
                                BlocProvider.of<ConvSimulationBloc>(context)
                                    .add(const CheckAddressEvent());

                                // CheckPinPage.showSheet(
                                //   context,
                                //   (String pin) {
                                //     BlocProvider.of<ConvSimulationBloc>(context)
                                //         .add(const ConfirmSimulationEvent());
                                //   },
                                // );
                              }
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/utils/utils.dart';

class SimulationInstallmentValueWidget extends StatelessWidget {
  const SimulationInstallmentValueWidget({
    required this.isLoadingInstallment,
    required this.valueInstallment,
    Key? key,
  }) : super(key: key);
  final bool isLoadingInstallment;
  final double valueInstallment;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Utils.setScreeenResponsive(
      context: context,
      height: size.height,
      width: size.width,
    );

    return SizedBoxResponsive(
      height: 106,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Text(
            I18n().valor_parcela,
            style: bodyText2(context),
          ),
          Visibility(
            visible: !isLoadingInstallment,
            replacement: Utils.circularProgressButton(size: 20),
            child: Text(
              Utils.formatBalance(valueInstallment),
              style: bodyText2Bold(context),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle bodyText2(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium!;
  }

  TextStyle bodyText2Bold(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.bold,
        );
  }
}

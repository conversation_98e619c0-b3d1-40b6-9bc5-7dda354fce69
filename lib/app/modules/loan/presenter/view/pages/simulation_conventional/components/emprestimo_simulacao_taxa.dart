import 'package:flutter/material.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/utils/utils.dart';

class SimulationInterestRatesWidget extends StatelessWidget {
  const SimulationInterestRatesWidget({
    this.interestRates,
    this.isLoadingInstallment,
    super.key,
  });
  final bool? isLoadingInstallment;
  final double? interestRates;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Utils.setScreeenResponsive(
      context: context,
      height: size.height,
      width: size.width,
    );

    return ContainerResponsive(
      height: 106,
      decoration: const BoxDecoration(
        border: Border(
          right: BorderSide(width: 0.2),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Text(
            const I18n().taxa,
            style: bodyText2(context),
          ),
          Visibility(
            visible: !(isLoadingInstallment ?? false),
            replacement: Utils.circularProgressButton(size: 20),
            child: Text(
              '${Utils.formatDecimal(interestRates)}%',
              style: bodyText2Bold(context),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle bodyText2(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium!;
  }

  TextStyle bodyText2Bold(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.bold,
        );
  }
}

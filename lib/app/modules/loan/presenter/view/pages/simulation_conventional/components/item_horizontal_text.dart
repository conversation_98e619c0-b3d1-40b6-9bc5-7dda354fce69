import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/themes/styles/colors_app.dart';

class ItemHorizontalText extends StatelessWidget {
  final String? title;
  final String? info;
  final bool showCopy;
  final bool compact;

  ItemHorizontalText({
    Key? key,
    this.title,
    this.info,
    this.showCopy = false,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsetsResponsive.only(
        top: compact ? 2 : 12,
        bottom: compact ? 4 : 12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          TextResponsive(
            title ?? '',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: ColorsApp.cinza[700],
                  fontSize: compact ? 12 : null,
                ),
          ),
          Expanded(
            child: TextResponsive(
              info ?? '',
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    color: ColorsApp.cinza[900],
                    fontSize: compact ? 12 : null,
                  ),
            ),
          ),
          // Visibility(
          //   visible: showCopy,
          //   child: InkWell(
          //     onTap: () {
          //       Utils.copyText(
          //           context: context,
          //           text: info ?? '',
          //           mensagemSucesso: I18n.of(context)!.texto_copiado);
          //     },
          //     child: ContainerResponsive(
          //       margin: EdgeInsetsResponsive.only(left: 16),
          //       child: ImageUtils.icCopyData(),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}

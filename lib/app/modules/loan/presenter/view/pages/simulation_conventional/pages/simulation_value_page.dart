import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/conv_simulation/conv_simulation_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../../shared/utils/utils.dart';

class SimulationValuePage extends StatelessWidget {
  const SimulationValuePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
        value: BlocProvider.of<ConvSimulationBloc>(context),
        child: const _SimulationValueView());
  }
}

class _SimulationValueView extends StatefulWidget {
  const _SimulationValueView({super.key});

  @override
  State<_SimulationValueView> createState() => _SimulationValueViewState();
}

class _SimulationValueViewState extends State<_SimulationValueView> {
  final _formKey = GlobalKey<FormState>();
  late MoneyMaskedTextController _valorController;

  bool _habilitarBotaoContinuar = false;
  late double limitCredit;

  @override
  void initState() {
    limitCredit = AppSession.getInstance().marginLimitResponse?.limit ?? 0;
    super.initState();
    _valorController = MoneyMaskedTextController(
      decimalSeparator: ',',
      thousandSeparator: '.',
      leftSymbol: r'R$',
      // initialValue: widget.state.valorLiberado ?? 0,
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        _habilitarBotaoContinuar = _valorController.numberValue > 0.0;
      });
    });
  }

  @override
  void dispose() {
    _valorController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<ConvSimulationBloc, ConvSimulationState>(
        listener: (context, state) {
          if (state is GetLimitCreditConvSuccess) {
            setState(() {
              limitCredit = state.limitCredit;
            });
          }
          if (state is ConvSimulationError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is ConvSimulationCenterLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          final political =
              AppSession.getInstance().conventionalCreditPolitical!;
          return CustomScrollView(
            slivers: <Widget>[
              SliverToBoxAdapter(
                child: Form(
                  key: _formKey,
                  // autovalidate: true,
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 16, top: 30, right: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        Text(I18n.of(context)!
                            .emprestimo_simulacao_valor_mensagem),
                        SizedBox(height: 16),
                        RichText(
                          text: TextSpan(
                            text: I18n.of(context)!
                                .seu_limite_emprestimo_disponivel,
                            style: Theme.of(context)
                                .textTheme
                                .bodyLarge!
                                .copyWith(fontWeight: FontWeight.normal),
                            children: <TextSpan>[
                              TextSpan(
                                text: Utils.formatBalance(limitCredit),
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 24),
                        TextFormFieldApp(
                          controller: _valorController,
                          label: I18n().valor,
                          textInputType: TextInputType.number,
                          onChanged: (text) {
                            setState(() {
                              _habilitarBotaoContinuar =
                                  _valorController.numberValue > 0.0;
                            });
                          },
                          validator: (value) {
                            if (_valorController.numberValue <= 0.0) {
                              return I18n.of(context)!.campo_obrigatorio;
                            }
                            // colocar variavel com valor minimo de emprestimo
                            // double valMin = 300; // .valorLiberadoMinimo!;

                            final valMin = political.installmentValueMin! *
                                political.timeMin!;
                            if (_valorController.numberValue < valMin) {
                              return I18n.of(context)!
                                  .errorValorMinimoEmprestimo(
                                      Utils.formatBalance(valMin.toDouble()));
                            }

                            if (_valorController.numberValue > limitCredit) {
                              return I18n.of(context)!
                                  .errorValorMaximoEmprestimo(
                                      Utils.formatBalance(limitCredit));
                            }

                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SliverFillRemaining(
                hasScrollBody: false,
                fillOverscroll: false,
                child: Padding(
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  child: Container(
                    alignment: Alignment.bottomCenter,
                    child: ButtonApp(
                      enabled: _habilitarBotaoContinuar,
                      width: MediaQuery.of(context).size.width,
                      progress: state is ConvSimulationLoading
                          ? Utils.circularProgressButton(size: 20)
                          : null,
                      text: I18n.of(context)!.continuar,
                      onPress: () {
                        _clickContinuar(_valorController.numberValue);
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _clickContinuar(double valor) {
    final valido = _formKey.currentState?.validate() ?? false;
    if (valido) {
      FocusScope.of(context).unfocus();
      BlocProvider.of<ConvSimulationBloc>(context).add(
        RunSimulationEvent(value: valor, changePage: ChangePage.next),
      );
    }
  }
}

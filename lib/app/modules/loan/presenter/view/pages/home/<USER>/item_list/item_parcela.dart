import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../../shared/navigation/named_routes.dart';
import '../../../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../../../shared/utils/image_utils.dart';
import '../../../../../../../../shared/utils/utils.dart';
import '../../../../../../data/models/installment_response.dart';

class ItemInstallmentWidget extends StatefulWidget {
  final InstallmentResponse parcela;
  final int quantidadeTotalParcelas;
  final String? numeroContrato;
  final Function atualizaEmprestimo;
  final bool isPrimeira;
  final bool isUltimo;
  final Function(InstallmentResponse)? onCheck;

  const ItemInstallmentWidget({
    super.key,
    required this.parcela,
    this.numeroContrato,
    required this.quantidadeTotalParcelas,
    required this.atualizaEmprestimo,
    this.isPrimeira = false,
    this.isUltimo = false,
    this.onCheck,
  });

  @override
  _ItemInstallmentWidgetState createState() => _ItemInstallmentWidgetState();
}

class _ItemInstallmentWidgetState extends State<ItemInstallmentWidget> {
  bool _isCheck = false;

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    var textTheme = Theme.of(context).textTheme;
    return InkWell(
      onTap: () {
        if (widget.onCheck != null) {
          widget.onCheck!(widget.parcela);
          setState(() {
            _isCheck = !_isCheck;
          });
        } else {
          openDetalheParcela(context);
        }
      },
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(widget.isPrimeira ? 6 : 0),
              topRight: Radius.circular(widget.isPrimeira ? 6 : 0),
              bottomLeft: Radius.circular(widget.isUltimo ? 6 : 0),
              bottomRight: Radius.circular(widget.isUltimo ? 6 : 0),
            )),
        margin: EdgeInsetsResponsive.only(bottom: 1),
        child: Column(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.only(
                  top: 16, bottom: 16, left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  Visibility(
                    visible: widget.onCheck != null,
                    child: SizedBoxResponsive(
                      height: 20,
                      width: 20,
                      child: Checkbox(
                        activeColor: ColorsApp.cinza[800],
                        value: _isCheck,
                        onChanged: (value) {
                          if (value != null) {
                            widget.onCheck!(widget.parcela);
                          }

                          setState(() {
                            _isCheck = value!;
                          });
                        },
                      ),
                    ),
                  ),
                  SizedBoxResponsive(
                    width: 8,
                  ),
                  TextResponsive(
                    widget.parcela.numeroParcela.toString().padLeft(2, "0"),
                    style: textTheme.bodyMedium!.copyWith(
                      color: ColorsApp.cinza[600],
                      height: 1.0,
                      // fontFeatures: [FontFeature.tabularFigures()],
                    ),
                  ),
                  const SizedBox(width: 16),
                  TextResponsive(
                    Utils.formatDateTimeToBr(widget.parcela.dataVencimento),
                    style: textTheme.bodyMedium!.copyWith(
                      color: ColorsApp.cinza[700],
                      height: 1.0,
                      // fontFeatures: [FontFeature.tabularFigures()],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        TextResponsive(
                          Utils.formatBalance(widget.parcela.valorTotal),
                          style: textTheme.bodyMedium!.copyWith(
                            color: widget.parcela.isLiquidated
                                ? ColorsApp.correto[300]
                                : widget.parcela.isDefeated
                                    ? ColorsApp.error[300]
                                    : widget.parcela.isOpen
                                        ? ColorsApp.info[300]
                                        : ColorsApp.cinza[700],
                            height: 1.0,
                          ),
                        ),
                        SizedBoxResponsive(width: 16),
                        Visibility(
                          visible: widget.onCheck == null,
                          child: ImageUtils.icArrowRight(),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDialogPagoAdiantado(BuildContext context) {
    DialogUtils.showDialogTitleMensagem(
      context: context,
      title: I18n.of(context)!.pago_com_adiantamento,
      mensagem: I18n.of(context)!.pago_com_adiantamento_msg,
    );
  }

  void openDetalheParcela(BuildContext context) async {
    widget.parcela.quantidadeParcelas = widget.quantidadeTotalParcelas;

    push(Routes.loanDetailsInstallment, args: widget.parcela);
  }
}

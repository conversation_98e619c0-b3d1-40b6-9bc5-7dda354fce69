import 'package:flutter/material.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../shared/themes/styles/colors_app.dart';

class CardSelectEmprestimoWidget extends StatelessWidget {
  const CardSelectEmprestimoWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.textMsg,
    required this.clickButton,
    // required this.loading,
  });

  final String title;
  final String subtitle;
  final String textMsg;
  final VoidCallback clickButton;
  // final bool loading;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 28),
      // margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: ColorsApp.drop1,
            blurRadius: 36,
            offset: Offset(0.0, 16.0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            title,
            textAlign: TextAlign.left,
            style: Theme.of(context)
                .textTheme
                .titleLarge!
                .copyWith(color: ColorsApp.cinza[900]),
          ),
          Text(
            subtitle,
            textAlign: TextAlign.left,
            style:
                Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11),
          ),
          const SizedBox(height: 32),
          Text(
            textMsg,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 32),
          ButtonApp(
            text: I18n.of(context)!.continuar,
            onPress: clickButton,
            // progress:
            // loading
            //     ? Container(
            //         // padding: EdgeInsets.all(16),
            //         height: 22,
            //         width: 22,
            //         child: const CircularProgressIndicator(
            //           color: Colors.white,
            //         ),
            //       )
            //     : null,
            // enabled: !loading,
          ),
        ],
      ),
    );
  }
}

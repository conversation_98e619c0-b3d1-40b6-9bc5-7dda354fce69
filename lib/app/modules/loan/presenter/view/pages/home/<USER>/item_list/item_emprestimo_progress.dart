import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import '../../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../../shared/data/models/loan/request_loan_home_response.dart';
import '../../../../../../../../shared/data/models/loan/request_loan_response.dart';
import '../../../../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../../../shared/presenter/view/components/others/snack_bar_app.dart';
import '../../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../../../shared/utils/image_utils.dart';
import '../../../../../../../../shared/utils/utils.dart';
import 'item_vertical_centralizado.dart';

enum EmprestimoStatus {
  EM_ANALISE,
  DOCUMENTOS_PENDENTES,
  NOVA_PROPOSTA,
  PROPOSTA_APROVADA,
  AGUARDANDO_INVESTIDOR,
  EMPRESTIMO_AUTORIZADO,
  EMPRESTIMO_CONCEDIDO,
  PEDIDO_EXPIRADO,
  PEDIDO_NAO_AUTORIZADO,
  FINALIZADO,
  CANCELADO,
}

class ItemEmprestimoProgress extends StatefulWidget {
  RequestLoanHomeResponse emprestimo;
  Function() updateApi;
  Function? changeCarteira;
  LoansHomeBloc bloc;

  ItemEmprestimoProgress({
    super.key,
    required this.emprestimo,
    required this.updateApi,
    this.changeCarteira,
    required this.bloc,
  });

  @override
  _ItemEmprestimoProgressState createState() => _ItemEmprestimoProgressState();
}

class _ItemEmprestimoProgressState extends State<ItemEmprestimoProgress> {
  RequestLoanHomeResponse get emprestimo => widget.emprestimo;
  LoansHomeBloc get bloc => widget.bloc;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsetsResponsive.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        children: <Widget>[
          InkWell(
            onTap: _clickButtonDetails,
            child: Container(
              decoration: Utils.dropShadowNewCircle(radius: 10),
              margin: EdgeInsetsResponsive.only(bottom: 8, top: 16),
              child: Column(
                children: <Widget>[
                  Padding(
                    padding: EdgeInsetsResponsive.only(
                        top: 12, bottom: 16, left: 12, right: 16),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Column(
                              children: <Widget>[
                                TextResponsive(
                                  I18n.of(context)!.valor_solicitado,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(color: ColorsApp.cinza[700]),
                                ),
                                SizedBoxResponsive(height: 4),
                                TextResponsive(
                                  Utils.formatBalance(emprestimo.valueCredit),
                                  style: Theme.of(context).textTheme.bodyLarge,
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: <Widget>[
                                Container(
                                  decoration: BoxDecoration(
                                      color: emprestimo.isCanceled() ||
                                              emprestimo.isFinalized()
                                          ? ColorsApp.cinza[400]
                                          : ColorsApp.info[200],
                                      borderRadius: BorderRadius.circular(5)),
                                  padding: EdgeInsetsResponsive.only(
                                      left: 22, right: 22, top: 6, bottom: 6),
                                  child: TextResponsive(
                                    emprestimo.isCanceled()
                                        ? I18n.of(context)!.cancelado
                                        : emprestimo.isFinalized()
                                            ? I18n.of(context)!.finalizado
                                            : emprestimo.isIssued()
                                                ? I18n.of(context)!.contratado
                                                : I18n.of(context)!.em_analise,
                                    // emprestimo.creditStatus,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall!
                                        .copyWith(
                                            color: emprestimo.isCanceled() ||
                                                    emprestimo.isFinalized()
                                                ? ColorsApp.cinza[700]
                                                : ColorsApp.info[300],
                                            height: 1.0),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        Container(
                          alignment: Alignment.centerRight,
                          child: TextResponsive(
                            "ID: ${emprestimo.creditIdCelcoin}",
                            // "ID: ${emprestimo.creditIdCelcoin}",
                            textAlign: TextAlign.right,
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall!
                                .copyWith(color: ColorsApp.cinza[700]),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(
                    height: 0.5,
                    color: ColorsApp.cinza[400],
                  ),
                  SizedBoxResponsive(height: 32),
                  IntrinsicHeight(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: <Widget>[
                        ItemVerticalStart(
                          title: I18n.of(context)!.data,
                          info: Utils.formatDateTimeToBr(
                              emprestimo.getDateTime()),
                        ),
                        VerticalDivider(
                          color: ColorsApp.cinza[400],
                        ),
                        ItemVerticalStart(
                          title: I18n.of(context)!.parcelas,
                          info: emprestimo.installmentMany.toString(),
                        ),
                        VerticalDivider(
                          color: ColorsApp.cinza[400],
                        ),
                        ItemVerticalStart(
                          title: I18n.of(context)!.valor_mensal,
                          info:
                              Utils.formatBalance(emprestimo.installmentValue),
                          contemAsterisco: true,
                        ),
                      ],
                    ),
                  ),
                  SizedBoxResponsive(height: 36),
                  Container(
                    decoration: BoxDecoration(
                        color: ColorsApp.cinza[200],
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        )),
                    padding: EdgeInsetsResponsive.only(
                        top: 5, bottom: 5, left: 16, right: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        TextResponsive(
                          I18n.of(context)!.detalhes,
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(height: 1.0),
                        ),
                        IconsApp.icArrowRight2(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Row(
            children: <Widget>[
              TextResponsive(
                "* ",
                style: Theme.of(context)
                    .textTheme
                    .labelSmall!
                    .copyWith(color: ColorsApp.error[300]),
              ),
              TextResponsive(
                I18n.of(context)!.valor_original_parcela,
                style: Theme.of(context)
                    .textTheme
                    .labelSmall!
                    .copyWith(color: ColorsApp.cinza[700]),
              )
            ],
          ),
          SizedBoxResponsive(height: 8),
          _buildInfo(),
          Divider(
            height: 10,
            color: ColorsApp.cinza[400],
          ),
        ],
      ),
    );
  }

  _buildInfo() {
    if (emprestimo.creditStatus == Status.PENDING_SIGNATURE.name) {
      return _buildPendingSignature();
    }
    // else if (emprestimo.statusEnum == EmprestimoStatus.NOVA_PROPOSTA) {
    //   return _buildNovaProposta();
    // } else if (emprestimo.statusEnum == EmprestimoStatus.FINALIZADO) {
    //   return Container();
    // } else if (emprestimo.statusEnum == EmprestimoStatus.PROPOSTA_APROVADA) {
    //   return _buildPropostaProvada();
    // }
    else if (emprestimo.creditStatus == Status.PENDING_DISBURSEMENT.name) {
      return _buildAguardandoInvestidor();
    } else if (emprestimo.creditStatus == Status.CANCELED.name) {
      return _buildPedidoNaoAprovado();
    }
    //else if (emprestimo.statusEnum == EmprestimoStatus.PEDIDO_EXPIRADO) {
    //   return _buildPedidoExpirado();
    // } else if (emprestimo.statusEnum == EmprestimoStatus.DOCUMENTOS_PENDENTES) {
    //   return _buildDocumentacaoPendente();
    // } else {
    //   return Container();
    // }

    return Container();
  }

  _buildPendingSignature() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AlertBanner(
          isShow: true,
          typeAlertBanner: TypeAlertBanner.info,
          error: null,
          message: I18n.of(context)!.pendent_signature,
        ),
        SizedBoxResponsive(height: 16),
        ButtonApp(
          text: I18n.of(context)!.sign_term,
          onPress: _clickButtonSignTerms,
        ),
        SizedBoxResponsive(height: 24),
      ],
    );
  }

  // _buildNovaProposta() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: <Widget>[
  //       AlertBanner(
  //         isShow: true,
  //         typeAlertBanner: TypeAlertBanner.info,
  //         error: null,
  //         message: I18n.of(context)!.temos_contra_proposta,
  //       ),
  //       SizedBoxResponsive(height: 16),
  //       ButtonApp(
  //         text: I18n.of(context)!.ver_nova_proposta,
  //         onPress: _clickVerDetalhes,
  //       ),
  //       SizedBoxResponsive(height: 24),
  //     ],
  //   );
  // }

  _buildAguardandoInvestidor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AlertBanner(
          isShow: true,
          typeAlertBanner: TypeAlertBanner.info,
          error: null,
          message: I18n.of(context)!.aguardando_fundo,
        ),
        SizedBoxResponsive(height: 24),
      ],
    );
  }

  // _buildPropostaProvada() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: <Widget>[
  //       AlertBanner(
  //         isShow: true,
  //         typeAlertBanner: TypeAlertBanner.success,
  //         error: null,
  //         message: I18n.of(context)!.proposta_pre_aprovada,
  //       ),
  //       SizedBoxResponsive(height: 16),
  //       ButtonApp(
  //         text: I18n.of(context)!.continuar,
  //         onPress: _clickVerDetalhes,
  //       ),
  //       SizedBoxResponsive(height: 24),
  //     ],
  //   );
  // }

  _buildPedidoNaoAprovado() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AlertBanner(
          isShow: true,
          typeAlertBanner: TypeAlertBanner.error,
          error: null,
          message: I18n.of(context)!.pedido_nao_autorizado,
        ),
        SizedBoxResponsive(height: 16),
        ButtonApp(
          text: I18n.of(context)!.continuar,
          onPress: _clickButtonCanceled,
        ),
        SizedBoxResponsive(height: 24),
      ],
    );
  }

  // _buildPedidoExpirado() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: <Widget>[
  //       AlertBanner(
  //         isShow: true,
  //         typeAlertBanner: TypeAlertBanner.error,
  //         error: null,
  //         message: I18n.of(context)!.pedido_expirado,
  //       ),
  //       SizedBoxResponsive(height: 24),
  //     ],
  //   );
  // }

  // _buildDocumentacaoPendente() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: <Widget>[
  //       AlertBanner(
  //         isShow: true,
  //         typeAlertBanner: TypeAlertBanner.error,
  //         error: null,
  //         message: I18n.of(context)!.documentos_pendentes,
  //       ),
  //       SizedBoxResponsive(height: 16),
  //       ButtonApp(
  //         text: I18n.of(context)!.adicionar_documentos,
  //         onPress: _clickVerDetalhes,
  //       ),
  //       SizedBoxResponsive(height: 24),
  //     ],
  //   );
  // }

  _clickButtonDetails() async {
    final result = await push(Routes.loanDetails, args: widget.emprestimo);

    if (result == true) {
      widget.updateApi();
    }
  }

  _clickButtonCanceled() async {
    await DialogUtils.showDialogTitleMensagem(
        context: context,
        title: I18n.of(context)!.nao_foi_possivel_prosseguir,
        mensagem: I18n.of(context)!.nao_foi_possivel_prosseguir_msg,
        clickContinuar: () {
          bloc.add(
              HideCreditCanceled(applicationId: emprestimo.creditIdCelcoin));

          bloc.add(GetCreditsList());
          // widget.updateApi();
        });
  }

  _clickButtonSignTerms() async {
    await push(Routes.loanSignature, args: emprestimo.creditIdCelcoin)
        .then((result) {
      // bloc.add(GetLimitCreditEvent());
      bloc.add(GetCreditsList());
      if (result == true) {
        SnackBarApp.showSnack(
          context: context,
          message: I18n.of(context)!.assinatura_efetuada_sucesso,
          success: true,
        );
        pop(true);
      }
    });
  }

  // _clickVerDetalhes() async {
  //   if (widget.emprestimo.statusEnum == EmprestimoStatus.EM_ANALISE ||
  //       widget.emprestimo.statusEnum == EmprestimoStatus.NOVA_PROPOSTA ||
  //       widget.emprestimo.statusEnum == EmprestimoStatus.PROPOSTA_APROVADA ||
  //       widget.emprestimo.statusEnum ==
  //           EmprestimoStatus.EMPRESTIMO_AUTORIZADO ||
  //       widget.emprestimo.statusEnum ==
  //           EmprestimoStatus.AGUARDANDO_INVESTIDOR) {
  //     if (widget.emprestimo.statusEnum == EmprestimoStatus.NOVA_PROPOSTA &&
  //         (widget.emprestimo.obsNovaPropostaUsuario != null &&
  //             widget.emprestimo.obsNovaPropostaUsuario!.isNotEmpty)) {
  //       // DialogCheckSucesso.showDialogSucesso(
  //       //     context: context,
  //       //     titulo: I18n.of(context)!.temos_nova_proposta,
  //       //     mensagem: widget.emprestimo.obsNovaPropostaUsuario,
  //       //     textBtn: I18n.of(context)!.ver_proposta,
  //       //     clickOk: () {
  //       //       _openDetalheStatusEmprestimo();
  //       //     });
  //       return;
  //     } else {
  //       _openDetalheStatusEmprestimo();
  //     }
  //   } else if (widget.emprestimo.statusEnum == EmprestimoStatus.FINALIZADO) {
  //     pushSlide(
  //         context,
  //         DetalheEmprestimoView(
  //           emprestimo: widget.emprestimo,
  //         ));
  //   } else if (widget.emprestimo.statusEnum ==
  //       EmprestimoStatus.PEDIDO_NAO_AUTORIZADO) {
  //     DialogAlertEmprestimoProvider.showDialogSucesso(
  //       context: context,
  //       tipo: DialogAlertEmprestimoTipo.NAO_AUTORIZADO,
  //       updateApi: widget.updateApi,
  //       changeCarteira: widget.changeCarteira,
  //       idEmprestimo: widget.emprestimo.idEmprestimo,
  //       tipoEmprestimo: getTipoEmprestimoEnum(widget.emprestimo.tipo),
  //     );
  //   } else if (widget.emprestimo.statusEnum ==
  //       EmprestimoStatus.DOCUMENTOS_PENDENTES) {
  //     var result = await pushSlideResult(
  //         context,
  //         DocumentacaoPendenteEmprestimoProvider(
  //           tipoEmprestimo: getTipoEmprestimoEnum(widget.emprestimo.tipo)!,
  //         ));
  //     if (result != null) {
  //       widget.updateApi!();
  //     }
  //   } else if (widget.emprestimo.statusEnum ==
  //       EmprestimoStatus.PEDIDO_EXPIRADO) {
  //     DialogAlertEmprestimoProvider.showDialogSucesso(
  //       context: context,
  //       tipo: DialogAlertEmprestimoTipo.EXPIRADO,
  //       updateApi: widget.updateApi,
  //       changeCarteira: widget.changeCarteira,
  //       idEmprestimo: widget.emprestimo.idEmprestimo,
  //       tipoEmprestimo: getTipoEmprestimoEnum(widget.emprestimo.tipo),
  //     );
  //   }
  // }

  // _openDetalheStatusEmprestimo() async {
  //   var result = await pushSlideResult(
  //       context,
  //       EmpresitmoStatusDetalheProvider(
  //         emprestimo: widget.emprestimo,
  //       ));
  //   if (result != null && result) {
  //     widget.updateApi!();
  //   }
  // }
}

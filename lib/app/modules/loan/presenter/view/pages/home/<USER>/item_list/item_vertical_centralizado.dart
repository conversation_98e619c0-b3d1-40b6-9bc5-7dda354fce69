import 'package:flutter/material.dart';

import '../../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../../shared/themes/styles/colors_app.dart';

class ItemVerticalStart extends StatelessWidget {
  final String? title;
  final String? info;
  final bool contemAsterisco;

  ItemVerticalStart({
    Key? key,
    this.info,
    this.title,
    this.contemAsterisco = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextResponsive(
            title ?? '',
            style: Theme.of(context)
                .textTheme
                .bodySmall!
                .copyWith(color: ColorsApp.cinza[700]),
          ),
          SizedBoxResponsive(height: 4),
          Row(
            children: <Widget>[
              Visibility(
                visible: contem<PERSON><PERSON><PERSON>,
                child: TextResponsive(
                  "* ",
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: ColorsApp.error[300]),
                ),
              ),
              TextResponsive(
                info ?? '',
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge!
                    .copyWith(color: ColorsApp.cinza[900]),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../app_controller.dart';
import '../../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../../shared/presenter/view/components/others/snack_bar_app.dart';
import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'card_select_emprestimo_widget.dart';

class HomeListCardsWidget extends StatelessWidget {
  const HomeListCardsWidget({
    super.key,
    required this.state,
    required this.loanHomeBloc,
    required this.refreshLoanHomePage,
  });
  final LoansHomeState state;
  final LoansHomeBloc loanHomeBloc;
  final VoidCallback refreshLoanHomePage;

  @override
  Widget build(BuildContext context) {
    // final user = AppSession.getInstance().user!;
    final collaborator = AppSession.getInstance().collaborator!;
    bool? showConvCredit = AppSession.getInstance().elegibleConvencionalCredit;

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: EdgeInsetsResponsive.only(
                left: 16, right: 16, top: 26, bottom: 32),
            child: Column(
              children: [
                Visibility(
                  visible: collaborator.experiencePeriod != true,
                  child: CardSelectEmprestimoWidget(
                      title: const I18n().credito_rapido,
                      subtitle: const I18n().consignado,
                      textMsg: const I18n().msg_info_credito_rapido,
                      // loading: false,
                      clickButton: () async {
                        _clickSimulationFastCredit(context, state);
                        // var result = await pushSlideResult(
                        //     context,
                        //     CreditoRapidoSimulacaoProvider(
                        //       // emprestimoResponse:
                        //       //     state.emprestimoResponseCredRapido,
                        //       // currentPage: state.pageOpenSimuladorCredRapido,
                        //       // listDocumentoStatus:
                        //       //     state.listDocumentosPendentesCredRapido ?? [],

                        //       emprestimoResponse: state.emprestimoResponse,
                        //       currentPage: state.pageOpenSimuladorCredRapido,
                        //       listDocumentoStatus:
                        //           state.listDocumentosPendentes ?? [],
                        //       statusConsignado: state.statusConsignado,
                        //     ));

                        // if (result != null && result) {
                        //   state.statusConsignado = null;
                        //   refreshController.requestRefresh();
                        // }
                      }),
                ),
                const SizedBox(
                  height: 24,
                ),
                Visibility(
                  visible: showConvCredit ?? false,
                  // visible: !state.usuario6MesesEmpregado,
                  child: CardSelectEmprestimoWidget(
                      title: const I18n().emprestimo_convencional,
                      subtitle: const I18n().consignado,
                      textMsg: const I18n().como_funciona_emprestimo_consignado,
                      // loading: state.showProgressBotaoSimular,
                      clickButton: () {
                        _clickSimularEmpConsignado(context, state);
                      }),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _clickSimularEmpConsignado(context, LoansHomeState state) async {
    final politicalConventional =
        AppSession.getInstance().conventionalCreditPolitical;

    if (politicalConventional == null) {
      SnackBarApp.showSnack(
          context: context,
          message: I18n.of(context)!.msg_error_limite,
          success: false);
    } else {
      var result = await push(
        Routes.loanConventional,
      );

      if (result == true) {
        _refresh();
        final currentRoute = Modular.to.path;
        if (currentRoute.contains(Routes.loanHomeCards)) {
          log('HomelistCards true');
          pop(true);
        }
      }
    }
  }

  _clickSimulationFastCredit(context, LoansHomeState state) async {
    final political = AppSession.getInstance().politicalFastCredit;

    if (political?.installmentMany == null || political?.installmentMany == 0) {
      SnackBarApp.showSnack(
          context: context,
          message: I18n.of(context)!.msg_error_limite,
          success: false);
    } else {
      var result = await push(
        Routes.loanFastCredit,
      );

      if (result == true) {
        _refresh();
        final currentRoute = Modular.to.path;
        if (currentRoute.contains(Routes.loanHomeCards)) {
          log('HomelistCards true');
          pop(true);
        }
      }
    }
  }

  _refresh() {
    loanHomeBloc.add(GetCreditsList());
    loanHomeBloc.add(GetLimitCreditEvent());
  }
}

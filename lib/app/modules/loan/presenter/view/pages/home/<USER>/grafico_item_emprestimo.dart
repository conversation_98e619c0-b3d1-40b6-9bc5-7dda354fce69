/* import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../../../../../res/generated/i18n.dart';
import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../../shared/utils/utils.dart';

class GraficoItemEmprestimo extends StatefulWidget {
  final List<Parcelas>? parcelas;
  final bool isInveimento;

  const GraficoItemEmprestimo({
    super.key,
    this.parcelas,
    this.isInveimento = false,
  });

  @override
  State<GraficoItemEmprestimo> createState() => _GraficoItemEmprestimoState();
}

class _GraficoItemEmprestimoState extends State<GraficoItemEmprestimo> {
  List<Parcelas>? get parcelas => widget.parcelas;
  bool get isInveimento => widget.isInveimento;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: isInveimento
          ? EdgeInsetsResponsive.zero
          : EdgeInsetsResponsive.only(top: 16, bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Expanded(
            flex: 70,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  margin: EdgeInsetsResponsive.only(left: 32),
                  width: 110,
                  height: 110,
                  child: SfCircularChart(
                      borderWidth: 0.0,
                      palette: listCoresParcelas(),
                      margin: const EdgeInsets.all(0),
                      enableMultiSelection: false,
                      series: <DoughnutSeries<_DataGraficoDetalhes, String>>[
                        DoughnutSeries<_DataGraficoDetalhes, String>(
                            explode: false,
                            explodeIndex: 0,
                            startAngle: 270,
                            endAngle: 270,
                            dataSource: listaData(),
                            xValueMapper: (_DataGraficoDetalhes data, _) =>
                                data.numeroParcela,
                            yValueMapper: (_DataGraficoDetalhes data, _) =>
                                data.valor,
                            dataLabelMapper: (_DataGraficoDetalhes data, _) =>
                                data.numeroParcela,
                            // enableSmartLabels: false,
                            enableTooltip: false,
                            explodeAll: false,
                            dataLabelSettings:
                                const DataLabelSettings(isVisible: false)),
                      ]),
                ),
              ],
            ),
          ),
          SizedBoxResponsive(height: 8),
          Expanded(
            flex: 30,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _buildLegenda(
                    cor: ColorsApp.verde[500]!, title: I18n.of(context)!.pago),
                _buildLegenda(
                    cor: ColorsApp.error[300]!,
                    title: I18n.of(context)!.atrasado),
                _buildLegenda(
                    cor: ColorsApp.info[300]!,
                    title: I18n.of(context)!.a_vencer),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Color> listCoresParcelas() {
    List<Color> list = [];
    // for (Parcelas parcela in parcelas ?? []) {
    //   list.add(Utils.getColorProgressParcela(parcela));
    // }
    list.add(ColorsApp.verde[500]!);
    list.add(ColorsApp.error[300]!);
    list.add(ColorsApp.info[300]!);
    return list;
  }

  List<_DataGraficoDetalhes> listaData() {
    var valorParcela = 100 / (parcelas?.length ?? 0);
    List<_DataGraficoDetalhes> lista = [];
    for (Parcelas parcela in parcelas ?? []) {
      lista.add(_DataGraficoDetalhes("a", valorParcela));
    }
    return lista;
  }

  _buildLegenda({required Color cor, required String title}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Container(
          width: 9,
          height: 9,
          decoration: BoxDecoration(
            color: cor,
            borderRadius: const BorderRadius.all(Radius.circular(2)),
          ),
        ),
        SizedBoxResponsive(width: 8),
        TextResponsive(
          title,
          style: Theme.of(context).textTheme.labelSmall!.copyWith(height: 1.2),
        ),
      ],
    );
  }
}

class _DataGraficoDetalhes {
  String numeroParcela;
  double valor;
  _DataGraficoDetalhes(this.numeroParcela, this.valor);
}
 */

import 'package:flutter/material.dart';

import '../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../shared/utils/utils.dart';
import '../../../../../data/models/installment_response.dart';
import 'item_list/item_parcela.dart';
import 'item_list/item_recisao.dart';

class ListInstallmentsWidget extends StatelessWidget {
  const ListInstallmentsWidget({
    super.key,
    required this.listParcelas,
    // required this.atualizaEmprestimo,
    // this.rescisaoResponse,
    this.numeroContrato,
    this.onCheck,
  });

  final List<InstallmentResponse>? listParcelas;
  final bool _selected = false;
  final String? numeroContrato;
  // final Function atualizaEmprestimo;
  // final RescisaoResponse? rescisaoResponse;
  final bool _isShowRescisao = false;
  final Function(InstallmentResponse installment)? onCheck;

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    Utils.setScreeenResponsive(context: context);
    if (listParcelas == null || listParcelas?.length == 0) {
      return Container();
    }
    return Container(
      // margin: EdgeInsetsResponsive.only(left: 16, right: 16),
      padding: const EdgeInsets.only(top: 0, bottom: 6),
      child: ListView(
        children: _buildItens(),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
      ),
    );
  }

  _buildItens() {
    List<Widget> list = [];
    for (int i = 0; i < listParcelas!.length; i++) {
      InstallmentResponse parcela = listParcelas![i];
      list.add(ItemInstallmentWidget(
        isPrimeira: i == 0,
        isUltimo: i == (listParcelas!.length - 1),
        parcela: parcela,
        quantidadeTotalParcelas: listParcelas!.length,
        numeroContrato: numeroContrato ?? '',
        atualizaEmprestimo: () {},
        onCheck: onCheck != null
            ? (parcela) {
                onCheck!(parcela);
              }
            : null,
      ));

      // try {
      //   InstallmentResponse proximaParcela = listParcelas![i + 1];
      //   if ((proximaParcela.valor> 0 ||
      //           proximaParcela.valorInvestimentoAntecipacao > 0) &&
      //       (rescisaoResponse != null && rescisaoResponse!.valor! > 0) &&
      //       !_isShowRescisao) {
      //     _isShowRescisao = true;
      //     list.add(ItemRecisao(
      //       rescisaoResponse: rescisaoResponse!,
      //     ));
      //   }
      // } catch (error) {}
    }

    return list;
  }
}

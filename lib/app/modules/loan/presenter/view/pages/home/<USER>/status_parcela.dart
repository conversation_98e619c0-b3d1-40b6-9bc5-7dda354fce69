import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:siclosbank/app/modules/loan/data/models/installment_response.dart';

import '../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../shared/themes/styles/colors_app.dart';

class StatusParcela extends StatelessWidget {
  final InstallmentResponse installment;

  StatusParcela({
    Key? key,
    required this.installment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Container(
      padding: EdgeInsets.only(left: 8, right: 8, top: 6, bottom: 6),
      decoration: BoxDecoration(
        color: installment.isLiquidated
            ? ColorsApp.correto[200]
            : installment.isOpen
                ? ColorsApp.info[200]
                : installment.isPendent
                    ? ColorsApp.atencao[100]
                    : ColorsApp.error[200],
        borderRadius: BorderRadius.all(
          Radius.circular(4.0),
        ),
      ),
      child: Text(
        // installment.isPagoAdiantado
        //     ? I18n.of(context)!.pago_adiantado
        //     : installment.isPagoAtraso
        //         ? I18n.of(context)!.pago_com_atraso
        //         :
        installment.isLiquidated
            ? I18n.of(context)!.pago
            // : installment.isOpen()
            //     ? I18n.of(context)!.processado
            //     :
            : installment.isDefeated
                ? I18n.of(context)!.vencido
                : I18n.of(context)!.a_vencer,
        style: textTheme.bodySmall!.copyWith(
            color: installment.isLiquidated
                // installment.isPagoAdiantado ||
                // installment.isPagoAtraso
                ? ColorsApp.correto[300]
                : installment.isOpen
                    ? ColorsApp.info[300]
                    : installment.isPendent
                        ? ColorsApp.atencao[300]
                        : ColorsApp.error[300],
            height: 1.0),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../blocs/home/<USER>';
import 'components/home_list_cards.dart';

class HomeListCardsPage extends StatefulWidget {
  const HomeListCardsPage({super.key});

  @override
  State<HomeListCardsPage> createState() => _HomeListCardsPageState();
}

class _HomeListCardsPageState extends State<HomeListCardsPage> {
  late LoansHomeState state;
  late LoansHomeBloc loanHomeBloc;
  late void Function() refresh;

  @override
  void initState() {
    final args = Modular.args.data['args'] as Map<String, dynamic>;
    state = args['state'] as LoansHomeState;
    loanHomeBloc = args['loanHomeBloc'] as LoansHomeBloc;
    refresh = args['refreshPage'] as void Function();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBarApp(
          title: I18n.of(context)!.emprestimo.toUpperCase(),
          showBack: true,
        ),
        body: HomeListCardsWidget(
          state: state,
          refreshLoanHomePage: refresh,
          loanHomeBloc: loanHomeBloc,
        ));
  }
}

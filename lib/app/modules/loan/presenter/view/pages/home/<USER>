import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/details_loan/details_loan_bloc.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import '../../../../../../shared/data/models/loan/request_loan_home_response.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/snack_bar_app.dart';
import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/utils/utils.dart';
import 'components/item_horizontal_text.dart';
import 'components/lista_installments_widget.dart';

class LoanDetailsPage extends StatelessWidget {
  const LoanDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<DetailsLoanBloc>(),
      child: const _LoanDetailsView(),
    );
  }
}

class _LoanDetailsView extends StatefulWidget {
  const _LoanDetailsView({super.key});

  @override
  State<_LoanDetailsView> createState() => _LoanDetailsViewState();
}

class _LoanDetailsViewState extends State<_LoanDetailsView> {
  late RequestLoanHomeResponse loanResponse;

  @override
  void initState() {
    loanResponse = Modular.args.data['args'] as RequestLoanHomeResponse;

    // if (loanResponse.isIssued() || loanResponse.isFinalized()) {
    BlocProvider.of<DetailsLoanBloc>(context).add(GetDetailsLoanEvent(
      requestLoanId: loanResponse.creditIdCelcoin,
    ));
    // }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: loanResponse.creditStatus == Status.PENDING_DISBURSEMENT.name
            ? I18n.of(context)!.aguardando_fundo.toUpperCase()
            : I18n.of(context)!.detalhe_emprestimo.toUpperCase(),
      ),
      body: BlocConsumer<DetailsLoanBloc, DetailsLoanState>(
        listener: (context, state) {},
        builder: (context, state) {
          return SafeArea(
            child: CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 16, top: 20, right: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Visibility(
                            visible: loanResponse.creditStatus ==
                                Status.PENDING_DISBURSEMENT.name,
                            child: Column(
                              children: [
                                const SizedBox(height: 14),
                                Text(
                                  I18n.of(context)!.aguardando_fundo_msg,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                        color: ColorsApp.cinza[700],
                                        fontSize: 16,
                                      ),
                                ),
                                const SizedBox(height: 18)
                              ],
                            )),
                        // const SizedBox(height: 16),
                        ItemHorizontalText(
                          title: I18n.of(context)!.valor_liberado,
                          info: Utils.formatBalance(
                            loanResponse.valueCredit,
                          ),
                        ),
                        ItemHorizontalText(
                          title: I18n.of(context)!.parcelas,
                          info: '${loanResponse.installmentMany}x',
                        ),

                        Visibility(
                          visible: loanResponse.creditStatus ==
                              Status.PENDING_DISBURSEMENT.name,
                          child: ItemHorizontalText(
                            title: I18n.of(context)!.valor_parcelas,
                            info: Utils.formatBalance(
                              loanResponse.installmentValue,
                            ),
                          ),
                        ),
                        ItemHorizontalText(
                          title: loanResponse.creditStatus ==
                                  Status.PENDING_DISBURSEMENT.name
                              ? I18n.of(context)!.taxa_juros_estimada
                              : I18n.of(context)!.taxas_de_juros,
                          info: I18n.of(context)!.taxaJuros(
                              Utils.formatDecimal(loanResponse.interestRates)),
                        ),

                        ItemHorizontalText(
                          title: I18n.of(context)!.tipo_emprestimo,
                          info:
                              'Consignado - ${loanResponse.creditType == 'RAPIDO' ? I18n.of(context)!.rapido : I18n.of(context)!.consignado}',
                        ),
                        _buildAlertBanner(
                            context: context,
                            status: loanResponse.creditStatus),

                        const SizedBox(height: 40),
                        buildListInstallments(state),
                      ],
                    ),
                  ),
                ),
                // SliverFillRemaining(
                //   hasScrollBody: false,
                //   fillOverscroll: false,
                //   child: Padding(
                //     padding:
                //         const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                //     child: Container(
                //         alignment: Alignment.bottomCenter,
                //         child: _buildButton(
                //             context: context,
                //             status: loanResponse.creditStatus,
                //             loanding: state is DetailsLoanLoading)),
                //   ),
                // ),
                if (loanResponse.numContract != null &&
                    loanResponse.numContract!.isNotEmpty)
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Container(
                      padding: EdgeInsetsResponsive.only(
                          left: 16, top: 32, bottom: 80),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: <Widget>[
                          Text(
                            I18n.of(context)!
                                .numeroDeContrato(loanResponse.numContract!),
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall!
                                .copyWith(color: ColorsApp.cinza[700]),
                          ),
                        ],
                      ),
                    ),
                  )
              ],
            ),
          );
        },
      ),
    );
  }

  buildListInstallments(DetailsLoanState state) {
    if (state is DetailsLoanSuccess) {
      // if (state.listInstallments.isEmpty) {
      //   return Container(
      //     padding: const EdgeInsets.all(16),
      //     alignment: Alignment.center,
      //     decoration: BoxDecoration(
      //       color: ColorsApp.cinza[300],
      //       borderRadius: BorderRadius.circular(6),
      //     ),
      //     child: Text(
      //       I18n.of(context)!.parcelas_nao_encontradas_ou_nao_disponiveis,
      //       style: Theme.of(context).textTheme.bodyMedium,
      //     ),
      //   );
      // }
      return ListInstallmentsWidget(
        listParcelas: state.listInstallments,
      );
    }
    if (state is DetailsLoanLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Container();
  }

  _buildAlertBanner({required BuildContext context, required String status}) {
    bool isShow = false;
    String message = '';
    if (status == Status.PENDING_SIGNATURE.name) {
      isShow = true;
      message = I18n.of(context)!.pendent_signature;
    }
    // if (status == Status.PENDING_DISBURSEMENT.name) {
    //   isShow = true;
    //   message = I18n.of(context)!.aguardando_fundo;
    // }

    return AlertBanner(
      isShow: isShow,
      typeAlertBanner: TypeAlertBanner.info,
      error: null,
      message: message,
    );
  }

  // _buildButton({
  //   required BuildContext context,
  //   required String status,
  //   required bool loanding,
  // }) {
  //   bool isShow = false;
  //   String message = '';
  //   Function()? onPress;
  //   if (status == Status.PENDING_SIGNATURE.name) {
  //     isShow = true;
  //     message = I18n.of(context)!.sign_term;
  //     onPress = () async {
  //       await push(Routes.loanSignature, args: loanResponse.creditIdCelcoin)
  //           .then((result) {
  //         if (result == true) {
  //           SnackBarApp.showSnack(
  //             context: context,
  //             message: I18n.of(context)!.assinatura_efetuada_sucesso,
  //             success: true,
  //           );
  //           pop(true);
  //         }
  //       });
  //     };
  //   }

  //   return Visibility(
  //     visible: isShow,
  //     child: ButtonApp(
  //       enabled: true,
  //       width: MediaQuery.of(context).size.width,
  //       progress: loanding ? Utils.circularProgressButton(size: 20) : null,
  //       text: message,
  //       onPress: onPress,
  //     ),
  //   );
  // }
}

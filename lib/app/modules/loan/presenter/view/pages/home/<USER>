import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/signature/signature_bloc.dart';

class SignatureTermsPage extends StatelessWidget {
  const SignatureTermsPage({
    super.key,
  });
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<SignatureBloc>(),
      child: const _SimulationAddressView(),
    );
  }
}

class _SimulationAddressView extends StatefulWidget {
  const _SimulationAddressView({
    super.key,
  });

  @override
  State<_SimulationAddressView> createState() => __SimulationAddressViewState();
}

class __SimulationAddressViewState extends State<_SimulationAddressView> {
  var htmlPage = 'assets/html/clicksign_page.html';
  WebViewController controller = WebViewController();

  @override
  void initState() {
    final id = Modular.args.data['args'] as String;

    BlocProvider.of<SignatureBloc>(context)
        .add(GetSignatureEvent(requestLoanId: id));

    controller.addJavaScriptChannel('ON_SIGNED_SUCCESS',
        onMessageReceived: (message) {
      log(message.message, name: 'ON_SIGNED_SUCCESS');
      pop(true);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isIOS = Platform.isIOS;
    return Scaffold(
        appBar: AppBarApp(
          title: I18n.of(context)!.sign_term.toUpperCase(),
        ),
        body: BlocConsumer<SignatureBloc, SignatureState>(
          listener: (context, state) async {
            if (state is SignatureSuccess) {
              if (isIOS) {
                var result = await launchUrlString(state.url,
                    mode: LaunchMode.inAppBrowserView,
                    webViewConfiguration: const WebViewConfiguration(
                      enableJavaScript: true,
                      enableDomStorage: true,
                    ),
                    browserConfiguration: const BrowserConfiguration(
                      showTitle: true,
                    ));
                pop();
                log(result.toString());
              }
            }
          },
          builder: (context, state) {
            if (state is SignatureLoading) {
              return _buildProgress();
            }
            if (state is SignatureSuccess && !isIOS) {
              return _buildTermo(context,
                  signatureId: state.signatureId, url: state.url);
            }
            if (state is SignatureError) {
              return Column(
                children: [
                  AlertBanner(
                    isShow: true,
                    typeAlertBanner: TypeAlertBanner.error,
                    message: state.error.message,
                  ),
                ],
              );
            }
            return Container();
          },
        ));
  }

  Container _buildProgress() {
    return Container(
      color: ColorsApp.cinzaDetalhesContatoConta,
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  Widget _buildTermo(context,
      {required String signatureId, required String url}) {
    return WebViewWidget(
        controller: controller
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setOnConsoleMessage((context) {
            log(
              context.message,
              name: context.level.toString(),
            );
          })
          ..loadRequest(Uri.parse(url)));

    // return FutureBuilder<String>(
    //     future: DefaultAssetBundle.of(context).loadString(htmlPage),
    //     builder: (context, snap) {
    //       if (snap.connectionState == ConnectionState.waiting) {
    //         return _buildProgress();
    //       }
    //       if (!snap.hasData) {
    //         return Container(
    //           child: const Center(child: Text('Error')),
    //         );
    //       }

    //       return WebViewWidget(
    //           controller: controller
    //             ..setJavaScriptMode(JavaScriptMode.unrestricted)
    //             ..setOnConsoleMessage((context) {
    //               log(
    //                 context.message,
    //                 name: context.level.toString(),
    //               );
    //             })
    //             ..loadHtmlString(snap.data ?? '',
    //                 baseUrl:
    //                     'https://siclosbank.com.br?signature_id=$signatureId&prod=${Flavor.isProduction}'));
    //     });
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/errors/user_not_collaborator_view.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/data/models/loan/request_loan_home_response.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/utils/utils.dart';
import '../errors/user_under_3_months_view.dart';
import 'components/home_list_cards.dart';
import 'components/item_list/item_emprestimo_progress.dart';

class LoansHomePage extends StatelessWidget {
  const LoansHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<LoansHomeBloc>(),
      child: const _LoansHomeView(),
    );
  }
}

class _LoansHomeView extends StatefulWidget {
  const _LoansHomeView({super.key});

  @override
  State<_LoansHomeView> createState() => __LoansHomeViewState();
}

class __LoansHomeViewState extends State<_LoansHomeView> {
  List<RequestLoanHomeResponse>? credits;

  @override
  void initState() {
    BlocProvider.of<LoansHomeBloc>(context).add(GetLimitCreditEvent());
    BlocProvider.of<LoansHomeBloc>(context).add(GetElegibleConvCreditEvent());
    BlocProvider.of<LoansHomeBloc>(context).add(GetConventionalPolitical());
    BlocProvider.of<LoansHomeBloc>(context).add(GetCreditsList());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.emprestimo.toUpperCase(),
        showBack: false,
      ),
      body: RefreshIndicator(
        onRefresh: () {
          _refresh();
          return Future.value();
        },
        child: BlocConsumer<LoansHomeBloc, LoansHomeState>(
          listener: (context, state) {
            if (state is LoansHomeError) {
              DialogUtils.showSnackError(context, state.error);
            }
          },
          builder: (context, state) {
            final collaborator = AppSession.getInstance().collaborator;
            // se o colaborador for null, não é colaborador
            // e se o colaborador não estiver disponível para emprestimo
            bool notCollaborator = collaborator == null ||
                collaborator.isAvailableToLoan() == false;
            if (state is LoansHomeLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (notCollaborator) {
              return const UserNotCollaboratorView();
            }
            if (collaborator.experiencePeriod == true) {
              return const UserUnder3MonthsView();
            }

            if (state is ListCreditSuccess) {
              if (state.listCreditIsEmpty) {
                return HomeListCardsWidget(
                  state: state,
                  refreshLoanHomePage: _refresh,
                  loanHomeBloc: BlocProvider.of<LoansHomeBloc>(context),
                );
              } else {
                return _buildList(state);
              }
            }

            return Container();
            // return _body(state});
          },
        ),
      ),
    );
  }

  void _refresh() {
    log('REFRESH LOANS HOME PAGE');
    BlocProvider.of<LoansHomeBloc>(context).add(GetCreditsList());
    BlocProvider.of<LoansHomeBloc>(context).add(GetLimitCreditEvent());
  }

  _buildList(ListCreditSuccess state) {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: <Widget>[
        _buildItensAnalise(state),
        _buildItensConcedidos(state),
        _buildItensFinalizados(state),
        _buildBtnSolicitarEmprestimo(state),
      ],
    );
  }

  _buildItensAnalise(ListCreditSuccess state) {
    if (state.listaEmAnalise.isEmpty) return const SliverToBoxAdapter();
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              RequestLoanHomeResponse emprestimo = state.listaEmAnalise[index];
              return ItemEmprestimoProgress(
                emprestimo: emprestimo,
                updateApi: _refresh,
                bloc: BlocProvider.of<LoansHomeBloc>(context),
              );
            },
            itemCount: state.listaEmAnalise.length,
          ),
        ],
      ),
    );
  }

  _buildItensConcedidos(ListCreditSuccess state) {
    if (state.listaConcedido.isEmpty) return const SliverToBoxAdapter();
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          ListView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (context, index) {
              RequestLoanHomeResponse emprestimo = state.listaConcedido[index];
              // return ItemEmprestimoConcedido(
              //   emprestimo: emprestimo,
              //   refreshPage: _refresh,
              // );

              return ItemEmprestimoProgress(
                emprestimo: emprestimo,
                updateApi: _refresh,
                bloc: BlocProvider.of<LoansHomeBloc>(context),
              );
            },
            itemCount: state.listaConcedido.length,
          ),
        ],
      ),
    );
  }

  _buildItensFinalizados(ListCreditSuccess state) {
    if (state.listaFinalizados.isEmpty) return const SliverToBoxAdapter();
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          ListView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (context, index) {
              RequestLoanHomeResponse emprestimo =
                  state.listaFinalizados[index];
              return ItemEmprestimoProgress(
                emprestimo: emprestimo,
                updateApi: _refresh,
                bloc: BlocProvider.of<LoansHomeBloc>(context),
              );
            },
            itemCount: state.listaFinalizados.length,
          ),
        ],
      ),
    );
  }

  Widget _buildBtnSolicitarEmprestimo(ListCreditSuccess state) {
    final collaborator = AppSession.getInstance().collaborator;

    final political = AppSession.getInstance().conventionalCreditPolitical;
    final margin = AppSession.getInstance().marginLimitResponse;
    if (collaborator == null || margin == null || political == null) {
      return const SliverToBoxAdapter();
    }

    bool byMargin = political.creditsMany;

    bool hasMarginAvailable = margin.margin >= political.installmentValueMin;

    bool showButton = byMargin
        ? hasMarginAvailable
        : state.listaConcedido.isEmpty && state.listaEmAnalise.isEmpty;

    if (showButton) {
      // if (showButton || Flavor.isDevelopment) {
      return SliverToBoxAdapter(
        child: Container(
          padding: EdgeInsetsResponsive.all(16),
          child: ButtonApp(
            progress: state is LoansHomeLoading
                ? Utils.circularProgressButton(size: 20)
                : null,
            text: I18n.of(context)!.solicitar_novo_emprestimo,
            onPress: () async {
              var result = await push(Routes.loanHomeCards, args: {
                'state': state,
                'refreshPage': _refresh,
                'loanHomeBloc': BlocProvider.of<LoansHomeBloc>(context),
              });
              if (result != null && result) {
                _refresh();
              }
            },
          ),
        ),
      );
    } else {
      return const SliverToBoxAdapter();
    }
  }
}

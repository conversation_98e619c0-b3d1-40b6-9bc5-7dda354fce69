import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/loan/data/models/installment_response.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/home/<USER>/status_parcela.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';

import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../shared/utils/utils.dart';
import 'components/item_horizontal_text.dart';

class InstallmentDetailsPage extends StatelessWidget {
  const InstallmentDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final installment = Modular.args.data['args'] as InstallmentResponse;
    final juros = installment.valorMulta + installment.valorMora;
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.detalhe_parcela.toUpperCase(),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Row(
                    children: [
                      Expanded(
                        child: TextResponsive(
                          I18n.of(context)!.parcelaItemTotal(
                              installment.numeroParcela,
                              installment.quantidadeParcelas.toString()),
                          // textAlign: TextAlign.right,
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: ColorsApp.cinza[900],
                                    // fontSize: 12,
                                  ),
                        ),
                      ),
                      StatusParcela(installment: installment)
                    ],
                  ),
                  const SizedBox(height: 24),
                  ItemHorizontalText(
                    title: I18n.of(context)!.vencimento,
                    info: Utils.formatDateTimeToBr(installment.dataVencimento),
                  ),
                  Visibility(
                    visible: (installment.dataPagamento != null) &&
                        installment.isLiquidated,
                    child: ItemHorizontalText(
                      title: I18n.of(context)!.pagamento,
                      info:
                          Utils.formatDateTimeToBr(installment.dataPagamento!),
                    ),
                  ),
                  ItemHorizontalText(
                    title: I18n.of(context)!.valor_parcela,
                    info: Utils.formatBalance(installment.valorTotal),
                  ),
                  Visibility(
                    visible: installment.isDefeated,
                    child: ItemHorizontalText(
                      title: I18n.of(context)!.multa_mora,
                      info: Utils.formatBalance(juros),
                    ),
                  ),
                  Divider(
                    color: Colors.grey.shade300,
                    height: 32,
                  ),
                  ItemHorizontalText(
                    title: installment.isLiquidated
                        ? I18n.of(context)!.total_recebido
                        : I18n.of(context)!.total_parcela,
                    info: Utils.formatBalance(installment.isDefeated
                        ? (installment.saldoAtual)
                        : installment.valorTotal),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../../../../../localization/generated/i18n.dart';
import '../../../../../../../../shared/data/models/loan/request_loan_home_response.dart';
import '../../../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../../../shared/utils/utils.dart';
import 'item_vertical_centralizado.dart';

class ItemEmprestimoConcedido extends StatefulWidget {
  RequestLoanHomeResponse emprestimo;
  Function? changeCarteira;
  VoidCallback refreshPage;
  bool showValores;

  ItemEmprestimoConcedido({
    Key? key,
    required this.emprestimo,
    this.changeCarteira,
    required this.refreshPage,
    this.showValores = true,
  }) : super(key: key);

  @override
  _ItemEmprestimoConcedidoState createState() =>
      _ItemEmprestimoConcedidoState();
}

class _ItemEmprestimoConcedidoState extends State<ItemEmprestimoConcedido> {
  bool get showValores => widget.showValores;
  VoidCallback get refreshPage => widget.refreshPage;

  @override
  Widget build(BuildContext context) {
    var emprestimo = widget.emprestimo;
    Utils.setScreeenResponsive(context: context);
    return ContainerResponsive(
      margin: EdgeInsetsResponsive.only(left: 16, right: 16, bottom: 16),
      child: InkWell(
        onTap: _openDetalheEmpresitmo,
        child: Column(
          children: <Widget>[
            Container(
              decoration: Utils.dropShadowNewCircle(radius: 10),
              margin: EdgeInsetsResponsive.only(bottom: 16, top: 16),
              child: Column(
                children: <Widget>[
                  Container(
                    padding:
                        EdgeInsetsResponsive.only(left: 12, right: 12, top: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Column(
                          children: <Widget>[
                            TextResponsive(
                              I18n.of(context)!.valor_solicitado,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(color: ColorsApp.cinza[700]),
                            ),
                            SizedBoxResponsive(height: 4),
                            TextResponsive(
                              Utils.formatBalance(emprestimo.valueCredit),
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            Container(
                              decoration: BoxDecoration(
                                  color: ColorsApp.info[200],
                                  borderRadius: BorderRadius.circular(5)),
                              padding: EdgeInsetsResponsive.only(
                                  left: 22, right: 22, top: 6, bottom: 6),
                              child: TextResponsive(
                                I18n.of(context)!.contratado,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                        color: ColorsApp.info[300],
                                        height: 1.0),
                              ),
                            ),
                            TextResponsive(
                              "ID: ${emprestimo.id}",
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: ColorsApp.cinza[700]),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsResponsive.only(left: 12, right: 12, top: 8),
                    child: Divider(
                      height: 0.5,
                      color: ColorsApp.cinza[400],
                    ),
                  ),
                  SizedBoxResponsive(height: 19),
                  IntrinsicHeight(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: <Widget>[
                        ItemVerticalStart(
                          title: I18n.of(context)!.data,
                          info: Utils.formatDateToBr(emprestimo.createdAt),
                        ),
                        VerticalDivider(
                          color: ColorsApp.cinza[400],
                        ),
                        ItemVerticalStart(
                          title: I18n.of(context)!.parcelas,
                          info: emprestimo.installmentMany.toString(),
                        ),
                        VerticalDivider(
                          color: ColorsApp.cinza[400],
                        ),
                        ItemVerticalStart(
                          title: I18n.of(context)!.valor_mensal,
                          info:
                              Utils.formatBalance(emprestimo.installmentValue),
                          contemAsterisco: true,
                        ),
                      ],
                    ),
                  ),
                  SizedBoxResponsive(height: 19),
                  Padding(
                    padding:
                        EdgeInsetsResponsive.only(left: 12, right: 12, top: 8),
                    child: Divider(
                      height: 0.5,
                      color: ColorsApp.cinza[400],
                    ),
                  ),
                  // GraficoItemEmprestimo(
                  //   parcelas: emprestimo.installmentMany,
                  // ),
                  Container(
                    decoration: BoxDecoration(
                        color: ColorsApp.cinza[200],
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        )),
                    padding: EdgeInsetsResponsive.only(
                        top: 8, bottom: 8, left: 16, right: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        TextResponsive(
                          I18n.of(context)!.detalhes,
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(height: 1.0),
                        ),
                        IconsApp.icArrowRight2(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: <Widget>[
                TextResponsive(
                  "* ",
                  style: Theme.of(context)
                      .textTheme
                      .labelSmall!
                      .copyWith(color: ColorsApp.error[300]),
                ),
                TextResponsive(
                  I18n.of(context)!.valor_original_parcela,
                  style: Theme.of(context)
                      .textTheme
                      .labelSmall!
                      .copyWith(color: ColorsApp.cinza[700]),
                )
              ],
            ),
            SizedBoxResponsive(height: 8),
            Divider(
              height: 10,
              color: ColorsApp.cinza[400],
            ),
          ],
        ),
      ),
    );
  }

  _openDetalheEmpresitmo() async {
    var result = await push(Routes.loanDetails, args: widget.emprestimo);
    // if (result != null && result) {
    //     context,
    //     DetalheEmprestimoView(
    //       emprestimo: widget.emprestimo,
    //     ));
    if (result != null && result) {
      log('Retornando da pagina de detalhes, deve atualizar pagina principal');
      refreshPage();
    }
  }
}

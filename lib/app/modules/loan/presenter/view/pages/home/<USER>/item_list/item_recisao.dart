/* import 'dart:ui';

import 'package:responsive_widgets2/responsive_widgets.dart';
import '../../../../../../../../../generated/i18n.dart';
import '../../../../../../../../../res/style/theme.dart';
import '../../../../../../../layers/services/pojos/response/emprestimo/rescisao_response.dart';
import '../../../../../../../layers/utils/image_utils.dart';
import '../../../../../../../layers/utils/nav.dart';
import '../../../../../../../layers/utils/utils.dart';
import 'package:flutter/material.dart';
import '../../../pagamento_recisao_view.dart';

class ItemRecisao extends StatelessWidget {
  RescisaoResponse rescisaoResponse;

  ItemRecisao({
    Key? key,
    required this.rescisaoResponse,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return InkWell(
      onTap: () {
        pushSlide(context, PagamentoRecisaoView());
      },
      child: Container(
        color: Colors.white,
        margin: EdgeInsetsResponsive.only(top: 6, bottom: 6),
        child: Column(
          children: <Widget>[
            Container(
              padding:
                  EdgeInsets.only(top: 16, bottom: 16, left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  Text(
                    I18n.of(context)!.desconto_recisao,
                    style: textTheme.bodyText2!.copyWith(
                      color: ColorsApp.cinza[900],
                      height: 1.0,
                    ),
                  ),
                  Expanded(
                    child: Container(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Text(
                            Utils.formataSaldo(rescisaoResponse.valor),
                            style: textTheme.bodyText2!.copyWith(
                                height: 1.0,
                                fontFeatures: [FontFeature.tabularFigures()],
                                color: rescisaoResponse.pago!
                                    ? ColorsApp.correto[300]
                                    : ColorsApp.cinza[800]),
                          ),
                          SizedBox(width: 16),
                          ImageUtils.icArrowRight(),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
 */

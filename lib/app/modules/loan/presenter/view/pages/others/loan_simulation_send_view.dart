import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/navigation/named_routes.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/utils/image_utils.dart';
import '../../../../../../shared/utils/my_behavior.dart';

class LoanSimulationSendView extends StatelessWidget {
  const LoanSimulationSendView({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBarApp(
        showLine: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: ScrollConfiguration(
            behavior: MyBehavior(),
            child: CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Text(
                        const I18n().pedido_enviado,
                        style: textTheme.displayLarge,
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      Text(
                        const I18n().emprestimo_simulacao_enviada_mensagem,
                        style: textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  fillOverscroll: false,
                  child: ContainerResponsive(
                    padding: const EdgeInsets.only(bottom: 16),
                    alignment: Alignment.bottomCenter,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        ImageUtils.imgEmprestimoSimulacaoEnviada(),
                        ButtonApp(
                          enabled: true,
                          width: size.width,
                          height: 50,
                          border: 0,
                          text: const I18n().ok,
                          onPress: () {
                            pop(true);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

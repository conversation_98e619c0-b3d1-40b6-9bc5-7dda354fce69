part of 'loans_home_bloc.dart';

sealed class LoansHomeState extends Equatable {
  const LoansHomeState();

  @override
  List<Object> get props => [];
}

final class LoansHomeInitial extends LoansHomeState {}

final class LoansHomeLoading extends LoansHomeState {}

final class LoansHomeError extends LoansHomeState {
  final ErrorResponse error;

  const LoansHomeError(this.error);
}

final class GetConventionalPoliticalSuccess implements LoansHomeState {
  final PoliticalCreditResponse political;

  const GetConventionalPoliticalSuccess(this.political);

  @override
  List<Object> get props => [political];

  @override
  bool get stringify => true;
}

final class ListCreditSuccess implements LoansHomeState {
  final List<RequestLoanHomeResponse> _credits;

  const ListCreditSuccess(this._credits);

  List<RequestLoanHomeResponse> get listaEmAnalise =>
      _lista(isConcedido: false);
  List<RequestLoanHomeResponse> get listaConcedido => _lista(isConcedido: true);
  List<RequestLoanHomeResponse> get listaFinalizados =>
      _lista(isConcedido: false, isFinalizado: true);

  List<RequestLoanHomeResponse> get getCredits {
    _credits.sort(
      (a, b) {
        return a
            .getDateTime()
            .millisecond
            .compareTo(b.getDateTime().millisecond);
      },
    );
    return _credits;
  }

  bool get listCreditIsEmpty => _credits.isEmpty;
  _lista({required bool isConcedido, bool isFinalizado = false}) {
    List<RequestLoanHomeResponse> list = [];

    for (RequestLoanHomeResponse emprestimo in getCredits) {
      //list concedidos
      if (isConcedido &&
          (emprestimo.isIssued() && (!emprestimo.isFinalized()))) {
        list.add(emprestimo);
      }
      // list finalizados
      else if (isFinalizado &&
          (emprestimo.isCanceled() || emprestimo.isFinalized())) {
        list.add(emprestimo);
      }
      // list em analise
      else if (!isConcedido &&
          !isFinalizado &&
          (!emprestimo.isIssued()) &&
          (!emprestimo.isCanceled()) &&
          (!emprestimo.isFinalized())) {
        list.add(emprestimo);
      }
    }
    return list;
  }

  @override
  List<Object> get props => [_credits];

  @override
  bool get stringify => true;

  @override
  String toString() {
    return 'ListCreditSuccess{credits=$getCredits}';
  }
}

final class HideCreditCanceledSuccess extends LoansHomeState {}

final class GetElegibleConvCreditSuccess extends LoansHomeState {}

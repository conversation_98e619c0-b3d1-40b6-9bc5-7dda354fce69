part of 'loans_home_bloc.dart';

sealed class LoansHomeEvent extends Equatable {
  const LoansHomeEvent();

  @override
  List<Object> get props => [];
}

final class GetLimitCreditEvent extends LoansHomeEvent {}

final class GetElegibleConvCreditEvent extends LoansHomeEvent {}

final class GetConventionalPolitical extends LoansHomeEvent {}

final class GetCreditsList extends LoansHomeEvent {}

final class HideCreditCanceled extends LoansHomeEvent {
  final String applicationId;
  const HideCreditCanceled({required this.applicationId});
}

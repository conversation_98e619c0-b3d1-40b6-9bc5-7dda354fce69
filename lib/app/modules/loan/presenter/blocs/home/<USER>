import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/loan/domain/usecases/loan_usecase.dart';
import 'package:siclosbank/app/shared/constants/credit_type_enum.dart';
import 'package:siclosbank/app/shared/data/models/wallet/political_credit_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../app_controller.dart';
import '../../../../../shared/data/models/loan/margin_limit_credit_response.dart';
import '../../../../../shared/data/models/loan/request_loan_home_response.dart';
import '../../../../../shared/domain/usecase/app_usecase.dart';

part 'loans_home_event.dart';
part 'loans_home_state.dart';

class LoansHomeBloc extends Bloc<LoansHomeEvent, LoansHomeState> {
  final ILoanUsecase _usecase;
  final IAppUseCase _appUsecase;

  MarginLimitCreditResponse? marginLimitResponse;
  LoansHomeBloc(this._usecase, this._appUsecase) : super(LoansHomeInitial()) {
    on<LoansHomeEvent>((event, emit) async {
      if (event is GetLimitCreditEvent) {
        final experiencePeriod =
            AppSession.getInstance().collaborator!.experiencePeriod == true;

        if (!experiencePeriod) {
          try {
            final result = await _usecase.getMarginLimitCredit();
            marginLimitResponse = result;
          } on ErrorResponse catch (e) {
            emit(LoansHomeError(e));
          }
        }
      }
      if (event is GetConventionalPolitical) {
        try {
          final political =
              AppSession.getInstance().conventionalCreditPolitical;
          if (political == null) {
            await _appUsecase.getPoliticalCredit(CreditType.conventional);
          }
        } on ErrorResponse catch (e) {
          emit(LoansHomeError(e));
        }
      }
      if (event is GetCreditsList) {
        emit(LoansHomeLoading());
        try {
          final result = await _usecase.getCredits();

          emit(ListCreditSuccess(result));
        } on ErrorResponse catch (e) {
          emit(LoansHomeError(e));
        }
      }
      if (event is HideCreditCanceled) {
        emit(LoansHomeLoading());
        try {
          await _usecase.hideCredit(applicationId: event.applicationId);

          emit(HideCreditCanceledSuccess());
        } on ErrorResponse catch (e) {
          emit(LoansHomeError(e));
        }
      }
      if (event is GetElegibleConvCreditEvent) {
        try {
          await _usecase.getElegibleConventionalCreditUser();
          // emit(GetElegibleFastCreditSuccess(result));
        } on ErrorResponse catch (e) {
          // emit(LoansHomeError(e));
        }
      }
    });
  }
}

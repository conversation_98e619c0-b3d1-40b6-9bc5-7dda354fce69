part of 'fast_credit_simulation_bloc.dart';

sealed class FastCreditSimulationEvent extends Equatable {
  const FastCreditSimulationEvent();

  @override
  List<Object> get props => [];
}

final class RunSimulationEvent extends FastCreditSimulationEvent {
  final ChangePage? changePage;

  const RunSimulationEvent({this.changePage});
}

final class ChangePageEvent extends FastCreditSimulationEvent {
  final double? value;

  const ChangePageEvent({
    this.value,
  });
}

final class ConfirmSimulationEvent extends FastCreditSimulationEvent {
  const ConfirmSimulationEvent();
}

final class GetAddressUserEvent extends FastCreditSimulationEvent {
  const GetAddressUserEvent();
}

final class CheckAddressUserEvent extends FastCreditSimulationEvent {
  const CheckAddressUserEvent();
}

// final class SuccessSignedEvent extends FastCreditSimulationEvent {
//   const SuccessSignedEvent();
// }

part of 'fast_credit_simulation_bloc.dart';

enum ChangePage { next, back }

sealed class FastCreditSimulationState extends Equatable {
  const FastCreditSimulationState({this.changePage});
  final ChangePage? changePage;

  @override
  List<Object> get props => [];
}

final class FastCreditSimulationInitial extends FastCreditSimulationState {}

final class FastCreditSimulationCenterLoading
    extends FastCreditSimulationState {}

final class FastCreditSimulationLoading extends FastCreditSimulationState {}

final class FastCreditSimulationInstallmentsLoading
    extends FastCreditSimulationLoading {}

final class GetLimitCreditConvSuccess extends FastCreditSimulationState {
  final double limitCredit;

  const GetLimitCreditConvSuccess({
    required this.limitCredit,
    super.changePage,
  });
}

final class FastCreditSimulationError extends FastCreditSimulationState {
  final ErrorResponse error;

  const FastCreditSimulationError(this.error);
}

final class LoanSimulationSuccess extends FastCreditSimulationState {
  final SimulationResponse simulationResponse;

  const LoanSimulationSuccess({
    required this.simulationResponse,
    super.changePage,
  });
}

final class AddressSuccess extends FastCreditSimulationState {
  final AddressResponse addressResponse;

  const AddressSuccess({
    required this.addressResponse,
    super.changePage,
  });
}

final class CheckAddressSuccess extends FastCreditSimulationState {
  const CheckAddressSuccess();
}

final class ChangePageState extends FastCreditSimulationState {
  const ChangePageState({
    super.changePage,
  });
}

final class SimulationConfirmSuccess extends FastCreditSimulationState {
  // final String signatureId;

  const SimulationConfirmSuccess(
      // this.signatureId,
      {
    super.changePage,
  });
}

// final class SuccessSignedState extends FastCreditSimulationState {
//   const SuccessSignedState();
// }

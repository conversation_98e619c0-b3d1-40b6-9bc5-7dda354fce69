import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/loan/domain/usecases/loan_usecase.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../app_controller.dart';

part 'fast_credit_simulation_event.dart';
part 'fast_credit_simulation_state.dart';

class FastCreditSimulationBloc
    extends Bloc<FastCreditSimulationEvent, FastCreditSimulationState> {
  final ILoanUsecase _usecase;
  // MarginLimitCreditResponse? marginLimitResponse;

  FastCreditSimulationBloc(this._usecase)
      : super(FastCreditSimulationInitial()) {
    on<FastCreditSimulationEvent>((event, emit) async {
      if (event is ChangePageEvent) {
        try {
          emit(FastCreditSimulationLoading());

          emit(const ChangePageState(
            changePage: ChangePage.next,
          ));
        } catch (e) {
          _handleError(emit, e);
        }
      }

      if (event is RunSimulationEvent) {
        final userUnder3Month =
            AppSession.getInstance().collaborator!.experiencePeriod == true;
        if (userUnder3Month) {
          emit(FastCreditSimulationError(
              ErrorResponse(message: const I18n().erro_simulacao)));
        } else {
          try {
            emit(FastCreditSimulationCenterLoading());

            final simulation = AppSession.getInstance().simulationFastCredit;
            if (simulation != null) {
              emit(LoanSimulationSuccess(
                simulationResponse: simulation,
                changePage: event.changePage,
              ));
              return;
            } else {
              final result = await _usecase.simulationFastCredit();

              emit(LoanSimulationSuccess(
                simulationResponse: result,
                changePage: event.changePage,
              ));
            }
          } catch (e) {
            _handleError(emit, e);
          }
        }
      }

      if (event is GetAddressUserEvent) {
        try {
          emit(FastCreditSimulationLoading());

          final result = await _usecase.getAddressUser();
          emit(AddressSuccess(
            addressResponse: result,
          ));
        } catch (e) {
          _handleError(emit, e);
        }
      }
      if (event is CheckAddressUserEvent) {
        try {
          emit(FastCreditSimulationLoading());

          final result = await _usecase.checkAddress();
          if (result == true) {
            emit(const CheckAddressSuccess());
          } else {
            emit(FastCreditSimulationError(
              ErrorResponse(
                  message:
                      'Ocorreu um erro ao verificar o endereço. Entre em contato com o suporte.'),
            ));
          }
          emit(AddressSuccess(
            addressResponse: result,
          ));
        } catch (e) {
          _handleError(emit, e);
        }
      }

      if (event is ConfirmSimulationEvent) {
        try {
          emit(FastCreditSimulationLoading());
          // envia solicitacao de emprestimo
          await _usecase.requestFastLoan();

          emit(const SimulationConfirmSuccess());
        } catch (e) {
          _handleError(emit, e);
        }
      }
    });
  }

  void _handleError(Emitter<FastCreditSimulationState> emit, Object e) {
    emit(FastCreditSimulationError(e as ErrorResponse));
  }
}

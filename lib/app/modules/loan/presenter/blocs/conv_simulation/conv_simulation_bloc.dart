import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/loan/domain/usecases/loan_usecase.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../app_controller.dart';

part 'conv_simulation_event.dart';
part 'conv_simulation_state.dart';

class ConvSimulationBloc
    extends Bloc<ConvSimulationEvent, ConvSimulationState> {
  final ILoanUsecase _usecase;
  SimulationResponse? simulationResponse;
  // MarginLimitCreditResponse? marginLimitResponse;

  ConvSimulationBloc(this._usecase) : super(ConvSimulationInitial()) {
    on<ConvSimulationEvent>((event, emit) async {
      if (event is ChangePageEvent) {
        try {
          emit(ConvSimulationLoading());

          emit(const ChangePageState(
            changePage: ChangePage.next,
          ));
        } catch (e) {
          _handleError(emit, e);
        }
      }

      if (event is RunSimulationEvent) {
        try {
          emit(ConvSimulationInstallmentsLoading());

          final installments = event.installment ??
              AppSession.getInstance().conventionalCreditPolitical!.timeMin!;

          final result = await _usecase.simulationConventionalLoan(
              value: event.value ?? simulationResponse!.valueCredit,
              installments: installments);
          simulationResponse = result;

          emit(LoanSimulationSuccess(
            simulationResponse: result,
            changePage: event.changePage,
          ));
        } catch (e) {
          _handleError(emit, e);
        }
      }

      if (event is GetAddressUserEvent) {
        try {
          emit(ConvSimulationLoading());

          final result = await _usecase.getAddressUser();
          emit(AddressSuccess(
            addressResponse: result,
          ));
        } catch (e) {
          _handleError(emit, e);
        }
      }
      if (event is CheckAddressEvent) {
        try {
          emit(ConvSimulationLoading());

          final result = await _usecase.checkAddress();
          if (result == true) {
            emit(const CheckAddressSuccess());
          } else {
            emit(ConvSimulationError(
              ErrorResponse(
                  message:
                      'Ocorreu um erro ao verificar o endereço. Entre em contato com o suporte.'),
            ));
          }
        } catch (e) {
          _handleError(emit, e);
        }
      }

      if (event is ConfirmSimulationEvent) {
        try {
          emit(ConvSimulationLoading());
          // envia solicitacao de emprestimo
          await _usecase.requestConventionalLoan(
              value: simulationResponse!.valueCredit,
              installments: simulationResponse!.installmentsMany);
          // await Future.delayed(const Duration(seconds: 1));
          // var signature = await _usecase.signatureCredit(
          //     applicationId: result.idRequestLoan);

          // // assert(signature.externalId != null);
          // if (signature.externalId == null) {
          //   for (var i = 0; i < 4; i++) {
          //     print('trying more one time...');
          //     signature = await _usecase.signatureCredit(
          //         applicationId: result.idRequestLoan);

          //     if (signature.externalId != null) {
          //       break;
          //     }
          //   }
          // }

          emit(const SimulationConfirmSuccess(
              // signature.externalId!,
              ));
        } catch (e) {
          _handleError(emit, e);
        }
      }

      // if (event is SuccessSignedEvent) {
      //   emit(const SuccessSignedState());
      // }
    });
  }

  void _handleError(Emitter<ConvSimulationState> emit, Object e) {
    emit(ConvSimulationError(e as ErrorResponse));
  }
}

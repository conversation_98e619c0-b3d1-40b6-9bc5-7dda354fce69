part of 'conv_simulation_bloc.dart';

sealed class ConvSimulationEvent extends Equatable {
  const ConvSimulationEvent();

  @override
  List<Object> get props => [];
}

final class RunSimulationEvent extends ConvSimulationEvent {
  final double? value;
  final int? installment;
  final ChangePage? changePage;

  const RunSimulationEvent({this.value, this.installment, this.changePage});
}

final class ChangePageEvent extends ConvSimulationEvent {
  final double? value;

  const ChangePageEvent({
    this.value,
  });
}

final class CheckAddressEvent extends ConvSimulationEvent {
  const CheckAddressEvent();
}

final class ConfirmSimulationEvent extends ConvSimulationEvent {
  const ConfirmSimulationEvent();
}

// final class SuccessSignedEvent extends ConvSimulationEvent {
//   const SuccessSignedEvent();
// }

final class GetAddressUserEvent extends ConvSimulationEvent {
  const GetAddressUserEvent();
}

part of 'conv_simulation_bloc.dart';

enum ChangePage { next, back }

sealed class ConvSimulationState extends Equatable {
  const ConvSimulationState({this.changePage});
  final ChangePage? changePage;

  @override
  List<Object> get props => [];
}

final class ConvSimulationInitial extends ConvSimulationState {}

final class ConvSimulationCenterLoading extends ConvSimulationState {}

final class ConvSimulationLoading extends ConvSimulationState {}

final class ConvSimulationInstallmentsLoading extends ConvSimulationLoading {}

final class GetLimitCreditConvSuccess extends ConvSimulationState {
  final double limitCredit;

  const GetLimitCreditConvSuccess({
    required this.limitCredit,
    super.changePage,
  });
}

final class ConvSimulationError extends ConvSimulationState {
  final ErrorResponse error;

  const ConvSimulationError(this.error);
}

final class LoanSimulationSuccess extends ConvSimulationState {
  final SimulationResponse simulationResponse;

  const LoanSimulationSuccess({
    required this.simulationResponse,
    super.changePage,
  });
}

final class AddressSuccess extends ConvSimulationState {
  final AddressResponse addressResponse;

  const AddressSuccess({
    required this.addressResponse,
    super.changePage,
  });
}

final class CheckAddressSuccess extends ConvSimulationState {
  const CheckAddressSuccess();
}

final class ChangePageState extends ConvSimulationState {
  const ChangePageState({
    super.changePage,
  });
}

final class SimulationConfirmSuccess extends ConvSimulationState {
  // final String signatureId;
  const SimulationConfirmSuccess(
      // this.signatureId,
      {
    super.changePage,
  });
}

// final class SuccessSignedState extends ConvSimulationState {
//   const SuccessSignedState();
// }

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../shared/errors/error_response.dart';
import '../../../data/models/installment_response.dart';
import '../../../domain/usecases/loan_usecase.dart';

part 'details_loan_event.dart';
part 'details_loan_state.dart';

class DetailsLoanBloc extends Bloc<DetailsLoanEvent, DetailsLoanState> {
  final ILoanUsecase _usecase;
  DetailsLoanBloc(this._usecase) : super(DetailsLoanInitial()) {
    on<DetailsLoanEvent>((event, emit) async {
      if (event is GetDetailsLoanEvent) {
        emit(DetailsLoanLoading());
        try {
          final result =
              await _usecase.getDetailsLoan(applicationId: event.requestLoanId);

          emit(DetailsLoanSuccess(result));
        } catch (e) {
          emit(DetailsLoanError(e as ErrorResponse));
        }
      }
    });
  }
}

part of 'details_loan_bloc.dart';

sealed class DetailsLoanState extends Equatable {
  const DetailsLoanState();

  @override
  List<Object> get props => [];
}

final class DetailsLoanInitial extends DetailsLoanState {}

final class DetailsLoanLoading extends DetailsLoanState {}

final class DetailsLoanSuccess extends DetailsLoanState {
  final List<InstallmentResponse> listInstallments;

  const DetailsLoanSuccess(this.listInstallments);
}

final class DetailsLoanError extends DetailsLoanState {
  final ErrorResponse error;

  const DetailsLoanError(this.error);
}

part of 'signature_bloc.dart';

sealed class SignatureState extends Equatable {
  const SignatureState();

  @override
  List<Object> get props => [];
}

final class SignatureInitial extends SignatureState {}

final class SignatureLoading extends SignatureState {}

final class SignatureError extends SignatureState {
  final ErrorResponse error;

  const SignatureError(this.error);
}

final class SignatureSuccess extends SignatureState {
  final String signatureId;
  final String url;

  const SignatureSuccess({
    required this.signatureId,
    required this.url,
  });
}

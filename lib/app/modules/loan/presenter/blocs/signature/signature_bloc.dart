import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/loan/domain/usecases/loan_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'signature_event.dart';
part 'signature_state.dart';

class SignatureBloc extends Bloc<SignatureEvent, SignatureState> {
  final ILoanUsecase _usecase;
  SignatureBloc(this._usecase) : super(SignatureInitial()) {
    on<SignatureEvent>((event, emit) async {
      if (event is GetSignatureEvent) {
        emit(SignatureLoading());
        try {
          var signature = await _usecase.signatureCredit(
            applicationId: event.requestLoanId,
          );

          // assert(signature.externalId != null);
          if (signature.externalId == null) {
            for (var i = 0; i < 3; i++) {
              print('trying more one time...');
              signature = await _usecase.signatureCredit(
                applicationId: event.requestLoanId,
              );

              if (signature.externalId != null) {
                break;
              }
            }
          }

          emit(
            signature.externalId != null
                ? SignatureSuccess(
                    signatureId: signature.externalId!,
                    url: signature.collectSignLink!,
                  )
                : SignatureError(
                    ErrorResponse(
                      message: 'Erro ao tentar carregar o contrato',
                    ),
                  ),
          );
        } on ErrorResponse catch (e) {
          emit(SignatureError(e));
        }
      }
    });
  }
}

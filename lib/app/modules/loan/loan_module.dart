import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/loan/data/datasource/loan_datasource.dart';
import 'package:siclosbank/app/modules/loan/data/repository/loan_repository_impl.dart';
import 'package:siclosbank/app/modules/loan/domain/i_loan_repository.dart';
import 'package:siclosbank/app/modules/loan/domain/usecases/loan_usecase.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/details_loan/details_loan_bloc.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/conv_simulation/conv_simulation_bloc.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/fast_credit/fast_credit_simulation_bloc.dart';
import 'package:siclosbank/app/modules/loan/presenter/blocs/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/fast_credit/fast_credit_simulation_navigation_pageview.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/home/<USER>';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/others/loan_simulation_send_view.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/simulation_conventional/conventional_simulation_navigation_pageview.dart';
import 'package:siclosbank/app/modules/pin/pin_module.dart';
import 'package:siclosbank/app/app_module.dart';

import 'presenter/blocs/signature/signature_bloc.dart';

class LoanModule extends Module {
  @override
  void binds(i) {
    i.addSingleton<ILoanDatasource>(LoanDatasourceImplement.new);
    i.addSingleton<ILoanRepository>(LoanRepositoryImpl.new);
    i.addSingleton<ILoanUsecase>(LoanUsecaseImpl.new);

    // blocs
    i.add(LoansHomeBloc.new);
    i.add(DetailsLoanBloc.new);
    i.add(SignatureBloc.new);
    i.add(ConvSimulationBloc.new);
    i.add(FastCreditSimulationBloc.new);
  }

  @override
  List<Module> get imports => [
        PinModule(),
        AppModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (context) => const LoansHomePage());
    r.child('/conventional-simulation',
        child: (_) => const ConventionalLoanSimulationPage());
    r.child('/fast-credit-simulation',
        child: (_) => const FastCreditLoanSimulationPage());
    r.child('/send-to-analysis', child: (_) => const LoanSimulationSendView());
    r.child('/signature-terms', child: (_) => const SignatureTermsPage());
    r.child('/details', child: (_) => const LoanDetailsPage());
    r.child('/details-installment',
        child: (_) => const InstallmentDetailsPage());
    r.child('/home-cards', child: (_) => const HomeListCardsPage());
  }
}

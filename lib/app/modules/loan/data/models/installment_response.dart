class InstallmentResponse {
  final String id;
  final String idExterno;
  final String contrato;
  final String numeroContrato;
  final String numeroParcela;
  final DateTime dataVencimento;
  final DateTime? dataPagamento;
  final int diasAtraso;
  final double saldoPrincipal;
  final double saldoTotal;
  final double saldoAtual;
  final double saldoContabil;
  final double valorPrincipal;
  final double valorTotal;
  final double valorMulta;
  final double valorPermanencia;
  final double valorMora;
  final double valorOutros;
  final double valorDesconto;
  final double? valorDespesa;
  final double? valorBoleto;
  final double? valorBaseTributo;
  final double valorPrincipalAberto;
  int quantidadeParcelas = 0;

  /// Situação - [BLOQUEADO, CANCELADO, ABERTO, PARCIAL, PROPOSTA, REMOVIDO, LIQUIDADO, NAO_CUMPRIDO, PENDENTE, INTEGRADO, CONCLUIDO, RENEGOCIADO]
  final String situacao;
  final String? agencia;
  final String? banco;
  final String? conta;
  final String? digito;
  final String? numeroNossoNumero;
  final String? nossoNumero;
  final String? digitoNossoNumero;
  final String? numeroDocumento;
  final String? notaFiscal;
  final String? cobrador;
  final String? cliente;
  final bool acordo;
  final bool bloqueio;
  final bool promessa;

  /// Tipo - [ACORDO, FATURAMENTO, PAGAMENTO_AVULSO, PROMESSA, RENEGOCIACAO]
  final String? tipoAcordo;

  bool get isOpen => diasAtraso <= 0 && situacao == 'ABERTO';

  bool get isPendent => situacao == 'PENDENTE';

  bool get isLiquidated => situacao == 'LIQUIDADO';

  bool get isDefeated => diasAtraso > 0 && situacao == 'ABERTO';
  //  => situacao == 'NAO_CUMPRIDO';

  InstallmentResponse({
    required this.id,
    required this.idExterno,
    required this.contrato,
    required this.numeroContrato,
    required this.numeroParcela,
    required this.dataVencimento,
    required this.dataPagamento,
    required this.diasAtraso,
    required this.saldoPrincipal,
    required this.saldoTotal,
    required this.saldoAtual,
    required this.saldoContabil,
    required this.valorPrincipal,
    required this.valorTotal,
    required this.valorMulta,
    required this.valorPermanencia,
    required this.valorMora,
    required this.valorOutros,
    required this.valorDesconto,
    this.valorDespesa,
    this.valorBoleto,
    this.valorBaseTributo,
    required this.valorPrincipalAberto,
    required this.situacao,
    this.agencia,
    this.banco,
    this.conta,
    this.digito,
    this.numeroNossoNumero,
    this.nossoNumero,
    this.digitoNossoNumero,
    this.numeroDocumento,
    this.notaFiscal,
    this.cobrador,
    this.cliente,
    required this.acordo,
    required this.bloqueio,
    required this.promessa,
    this.tipoAcordo,
  });

  factory InstallmentResponse.fromMap(Map<String, dynamic> map) {
    double? toDouble(dynamic value) =>
        value != null ? double.tryParse(value.toString()) : null;
    int? toInt(dynamic value) =>
        value != null ? int.tryParse(value.toString()) : null;

    return InstallmentResponse(
      id: map['id'],
      idExterno: map['idExterno'],
      contrato: map['contrato'],
      numeroContrato: map['numeroContrato'],
      numeroParcela: map['numeroParcela'],
      dataVencimento: DateTime.parse(map['dataVencimento']),
      dataPagamento: DateTime.tryParse(map['liquidacoes'][0]['data']),
      diasAtraso: toInt(map['diasAtraso']) ?? 0,
      saldoPrincipal: toDouble(map['saldoPrincipal']) ?? 0.0,
      saldoTotal: toDouble(map['saldoTotal']) ?? 0.0,
      saldoAtual: toDouble(map['saldoAtual']) ?? 0.0,
      saldoContabil: toDouble(map['saldoContabil']) ?? 0.0,
      valorPrincipal: toDouble(map['valorPrincipal']) ?? 0.0,
      valorTotal: toDouble(map['valorTotal']) ?? 0.0,
      valorMulta: toDouble(map['valorMulta']) ?? 0.0,
      valorPermanencia: toDouble(map['valorPermanencia']) ?? 0.0,
      valorMora: toDouble(map['valorMora']) ?? 0.0,
      valorOutros: toDouble(map['valorOutros']) ?? 0.0,
      valorDesconto: toDouble(map['valorDesconto']) ?? 0.0,
      valorDespesa: toDouble(map['valorDespesa']),
      valorBoleto: toDouble(map['valorBoleto']),
      valorBaseTributo: toDouble(map['valorBaseTributo']),
      valorPrincipalAberto: toDouble(map['valorPrincipalAberto']) ?? 0.0,
      situacao: map['situacao'],
      agencia: map['agencia'],
      banco: map['banco'],
      conta: map['conta'],
      digito: map['digito'],
      numeroNossoNumero: map['numeroNossoNumero'],
      nossoNumero: map['nossoNumero'],
      digitoNossoNumero: map['digitoNossoNumero'],
      numeroDocumento: map['numeroDocumento'],
      notaFiscal: map['notaFiscal'],
      cobrador: map['cobrador'],
      cliente: map['cliente'],
      acordo: map['acordo'] ?? false,
      bloqueio: map['bloqueio'] ?? false,
      promessa: map['promessa'] ?? false,
      tipoAcordo: map['tipoAcordo'],
    );
  }
}

import 'dart:convert';

// ignore_for_file: public_member_api_docs, sort_constructors_first
class SignatureResponse {
  String id;
  String? userAgent;
  String? externalId;
  String? collectSignLink;
  SignatureResponse({
    required this.id,
    this.userAgent,
    this.externalId,
    this.collectSignLink,
  });

  SignatureResponse copyWith({
    String? id,
    String? userAgent,
    String? externalId,
    String? collectSignLink,
  }) {
    return SignatureResponse(
      id: id ?? this.id,
      userAgent: userAgent ?? this.userAgent,
      externalId: externalId ?? this.externalId,
      collectSignLink: collectSignLink ?? this.collectSignLink,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'user_agent': userAgent,
      'external_id': externalId,
      'collect_sign_link': collectSignLink,
    };
  }

  factory SignatureResponse.fromMap(Map<String, dynamic> map) {
    return SignatureResponse(
      id: map['id'] as String,
      userAgent: map['user_agent'] as String?,
      externalId: map['external_id'] as String?,
      collectSignLink: map['collect_sign_link'] as String?,
    );
  }

  String toJson() => json.encode(toMap());

  factory SignatureResponse.fromJson(String source) =>
      SignatureResponse.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'SignatureResponse(id: $id, userAgent: $userAgent, signedAt: $externalId, collectSignLink: $collectSignLink)';
  }

  @override
  bool operator ==(covariant SignatureResponse other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.userAgent == userAgent &&
        other.externalId == externalId &&
        other.collectSignLink == collectSignLink;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userAgent.hashCode ^
        externalId.hashCode ^
        collectSignLink.hashCode;
  }
}

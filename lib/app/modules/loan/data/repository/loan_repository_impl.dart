import 'dart:convert';
import 'dart:developer';

import 'package:siclosbank/app/modules/loan/data/datasource/loan_datasource.dart';
import 'package:siclosbank/app/modules/loan/data/models/signature_response.dart';
import 'package:siclosbank/app/modules/loan/domain/i_loan_repository.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/request_loan_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/margin_limit_credit_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../../../../shared/data/models/loan/request_loan_home_response.dart';
import '../models/installment_response.dart';

class LoanRepositoryImpl implements ILoanRepository {
  final ILoanDatasource _datasource;

  LoanRepositoryImpl(this._datasource);

  @override
  Future<bool> getElegibleConventionalCreditUser(String cpf) async {
    try {
      final result = await _datasource.getElegibleConventionalCreditUser(cpf);

      return result.data['success'] as bool;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<MarginLimitCreditResponse> getMarginLimitUser(String id) async {
    try {
      final result = await _datasource.getMarginLimitUser(id);

      return MarginLimitCreditResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<SimulationResponse> simulateConventionalCreditUser({
    required String id,
    required double value,
    required int installments,
  }) async {
    try {
      final result = await _datasource.simulateConventionalCreditUser(
          id: id, value: value, installments: installments);
      log(result.data.toString());
      return SimulationResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<AddressResponse> getAddressUser(String id) async {
    try {
      final result = await _datasource.getAddressUser(id);

      return AddressResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<SimulationResponse> simulateFastCreditUser(
      {required String userId}) async {
    try {
      final result = await _datasource.simulateFastCreditUser(userId: userId);
      log(result.data.toString());
      return SimulationResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<RequestLoanResponse> requestConventionalCreditUser(
      {required String id,
      required double value,
      required int installments}) async {
    try {
      final result = await _datasource.requestConventionalCredit(
          id: id, value: value, installments: installments);
      log(result.data.toString());
      return RequestLoanResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<SignatureResponse> signatureCredit(
      {required String applicationId}) async {
    try {
      final result =
          await _datasource.signatureCredit(applicationId: applicationId);
      log(result.data.toString());
      return SignatureResponse.fromMap(result.data[0]);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<RequestLoanResponse> requestFastCreditUser(
      {required String id}) async {
    try {
      final result = await _datasource.requestFastCredit(id: id);
      return RequestLoanResponse.fromMap(result.data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<RequestLoanHomeResponse>> getCredits(
      {required String userId}) async {
    try {
      final result = await _datasource.getCredits(userId: userId);
      return List.from(result.data)
          .map((e) => RequestLoanHomeResponse.fromMap(e))
          .toList();
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<InstallmentResponse>> getDetailsLoan(
      {required String applicationId}) async {
    try {
      final result =
          await _datasource.getDetailsLoan(applicationId: applicationId);
      log(json.encode(result.data), name: 'getDetailsLoan');

      return List.from(result.data)
          .map((e) => InstallmentResponse.fromMap(e))
          .toList();
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future hideCredit({required String applicationId}) async {
    try {
      final result = await _datasource.hideCredit(applicationId: applicationId);
      return result.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future checkAddress({required String userId}) async {
    try {
      final result = await _datasource.checkAddress(userId: userId);
      return result.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

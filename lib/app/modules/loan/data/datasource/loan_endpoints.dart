abstract class LoanEnpoints {
  static String elegibleConventionalCredit(String cpf) =>
      '/credit/elegible_credit_conv/$cpf';

  static String politicalCredit(String creditType) =>
      '/credit/political/$creditType';

  static String simulationConventionalCredit(String id) =>
      '/credit/simulation_credit_conv/$id';

  static String simulationFastCredit(String id) =>
      '/credit/simulation_credit_speed/$id';

  static String getMarginLimitCredit(String id) =>
      '/credit/get_margin_limit/$id';

  static String getAddressUser(String id) => '/user/address/$id';

  static String requestConventionalCredit(String userId) =>
      '/credit/request_credit/$userId';

  static String requestFastCredit(String userId) =>
      '/credit/request_credit_speed/$userId';
  static String signatureCredit(String applicationId) =>
      '/credit/link_signature/$applicationId';

  static String listCredits(String userId) => '/credit/list_credits/$userId';

  static String getDetailsLoan(String applicationId) =>
      '/credit/credit_by_id/$applicationId';
  static String hideCredit(String applicationId) =>
      '/credit/disable_credit/$applicationId';

  static String checkAddress(String userId) => '/user/verify-address/$userId';
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:siclosbank/app/modules/loan/data/datasource/loan_endpoints.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

import '../../../../shared/data/client/api_response.dart';

abstract class ILoanDatasource {
  Future<ApiResponse> getElegibleConventionalCreditUser(String cpf);
  Future<ApiResponse> simulateConventionalCreditUser(
      {required String id, required double value, required int installments});
  Future<ApiResponse> simulateFastCreditUser({required String userId});

  Future<ApiResponse> requestConventionalCredit(
      {required String id, required double value, required int installments});

  Future<ApiResponse> requestFastCredit({required String id});

  Future<ApiResponse> getMarginLimitUser(String cpf);
  Future<ApiResponse> getAddressUser(String id);
  Future<ApiResponse> signatureCredit({required String applicationId});
  Future<ApiResponse> getCredits({required String userId});
  Future<ApiResponse> getDetailsLoan({required String applicationId});

  Future<ApiResponse> hideCredit({required String applicationId});
  Future<ApiResponse> checkAddress({required String userId});
}

class LoanDatasourceImplement implements ILoanDatasource {
  final IClient _client;

  LoanDatasourceImplement(
    this._client,
  );

  @override
  Future<ApiResponse> getElegibleConventionalCreditUser(String cpf) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.elegibleConventionalCredit(cpf),
    );

    return result;
  }

  @override
  Future<ApiResponse> getMarginLimitUser(String cpf) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.getMarginLimitCredit(cpf),
    );

    return result;
  }

  @override
  Future<ApiResponse> simulateConventionalCreditUser(
      {required String id,
      required double value,
      required int installments}) async {
    final body = {
      "value_credit": value,
      'installment_many': installments,
    };
    final result = await _client.fetch(
      method: 'POST',
      path: LoanEnpoints.simulationConventionalCredit(id),
      data: body,
    );

    return result;
  }

  @override
  Future<ApiResponse> getAddressUser(String id) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.getAddressUser(id),
    );

    return result;
  }

  @override
  Future<ApiResponse> simulateFastCreditUser({
    required String userId,
  }) async {
    final result = await _client.fetch(
      method: 'POST',
      path: LoanEnpoints.simulationFastCredit(userId),
    );

    return result;
  }

  @override
  Future<ApiResponse> requestConventionalCredit({
    required String id,
    required double value,
    required int installments,
  }) async {
    final body = {
      "value_credit": value,
      'installment_many': installments,
    };
    final result = await _client.fetch(
      method: 'POST',
      path: LoanEnpoints.requestConventionalCredit(id),
      data: body,
    );

    return result;
  }

  @override
  Future<ApiResponse> signatureCredit({
    required String applicationId,
  }) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.signatureCredit(applicationId),
    );

    return result;
  }

  @override
  Future<ApiResponse> requestFastCredit({required String id}) async {
    final result = await _client.fetch(
      method: 'POST',
      path: LoanEnpoints.requestFastCredit(id),
    );
    return result;
  }

  @override
  Future<ApiResponse> getCredits({required String userId}) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.listCredits(userId),
    );

    return result;
  }

  @override
  Future<ApiResponse> getDetailsLoan({required String applicationId}) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.getDetailsLoan(applicationId),
    );

    return result;
  }

  @override
  Future<ApiResponse> hideCredit({required String applicationId}) async {
    final result = await _client.fetch(
      method: 'PATCH',
      path: LoanEnpoints.hideCredit(applicationId),
    );
    return result;
  }

  @override
  Future<ApiResponse> checkAddress({required String userId}) async {
    final result = await _client.fetch(
      method: 'GET',
      path: LoanEnpoints.checkAddress(userId),
    );
    return result;
  }
}

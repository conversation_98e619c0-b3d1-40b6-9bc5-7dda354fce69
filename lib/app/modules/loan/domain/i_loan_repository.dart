import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/margin_limit_credit_response.dart';

import '../../../shared/data/models/loan/request_loan_home_response.dart';
import '../../../shared/data/models/loan/request_loan_response.dart';
import '../data/models/installment_response.dart';
import '../data/models/signature_response.dart';

abstract class ILoanRepository {
  Future<bool> getElegibleConventionalCreditUser(String cpf);
  Future<SimulationResponse> simulateConventionalCreditUser({
    required String id,
    required double value,
    required int installments,
  });

  Future<SimulationResponse> simulateFastCreditUser({required String userId});
  Future<MarginLimitCreditResponse> getMarginLimitUser(String id);
  Future<AddressResponse> getAddressUser(String id);

  Future<RequestLoanResponse> requestConventionalCreditUser({
    required String id,
    required double value,
    required int installments,
  });
  Future<SignatureResponse> signatureCredit({required String applicationId});

  Future<RequestLoanResponse> requestFastCreditUser({required String id});

  Future<List<RequestLoanHomeResponse>> getCredits({required String userId});

  Future<List<InstallmentResponse>> getDetailsLoan(
      {required String applicationId});

  Future hideCredit({required String applicationId});

  Future checkAddress({required String userId});
}

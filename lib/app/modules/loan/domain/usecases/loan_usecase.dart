import 'package:siclosbank/app/modules/loan/data/models/installment_response.dart';
import 'package:siclosbank/app/modules/loan/data/models/signature_response.dart';
import 'package:siclosbank/app/modules/loan/domain/i_loan_repository.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/request_loan_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/simulation_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/loan_status_response.dart';
import 'package:siclosbank/app/shared/data/models/loan/margin_limit_credit_response.dart';

import '../../../../shared/data/models/loan/request_loan_home_response.dart';

abstract class ILoanUsecase {
  Future<bool> getElegibleConventionalCreditUser();
  Future<MarginLimitCreditResponse> getMarginLimitCredit();
  Future<SimulationResponse> simulationConventionalLoan({
    required double value,
    required int installments,
  });
  Future<SimulationResponse> simulationFastCredit();
  Future<AddressResponse> getAddressUser();

  Future<RequestLoanResponse> requestConventionalLoan({
    required double value,
    required int installments,
  });

  Future<RequestLoanResponse> requestFastLoan();

  Future<SignatureResponse> signatureCredit({required String applicationId});
  Future<List<RequestLoanHomeResponse>> getCredits();

  Future<List<InstallmentResponse>> getDetailsLoan(
      {required String applicationId});

  Future hideCredit({required String applicationId});

  Future checkAddress();
}

class LoanUsecaseImpl implements ILoanUsecase {
  final ILoanRepository _repository;

  LoanUsecaseImpl(this._repository);

  @override
  Future<bool> getElegibleConventionalCreditUser() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final cpf = user!.cpf!;
    try {
      final result = await _repository.getElegibleConventionalCreditUser(cpf);

      AppSession.getInstance().elegibleConvencionalCredit = result;

      // final loan = AppSession.getInstance().elegibleConvencionalCredit;
      // if (loan == null) {
      //   AppSession.getInstance().user!.loanStatusResponse =
      //       LoanStatusResponse(elegibleConventionalCredit: result);
      // } else {
      //   AppSession.getInstance().user!.loanStatusResponse =
      //       loan.copyWith(elegibleConventionalCredit: result);
      // }

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<MarginLimitCreditResponse> getMarginLimitCredit() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.getMarginLimitUser(id);
      AppSession.getInstance().marginLimitResponse = result;
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<SimulationResponse> simulationConventionalLoan({
    required double value,
    required int installments,
  }) async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.simulateConventionalCreditUser(
        id: id,
        value: value,
        installments: installments,
      );
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AddressResponse> getAddressUser() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.getAddressUser(id);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<SimulationResponse> simulationFastCredit() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final userId = user!.id!;

    try {
      final result = await _repository.simulateFastCreditUser(
        userId: userId,
      );
      AppSession.getInstance().saveSimulationFastCredit = result;
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RequestLoanResponse> requestConventionalLoan(
      {required double value, required int installments}) async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.requestConventionalCreditUser(
        id: id,
        value: value,
        installments: installments,
      );
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<SignatureResponse> signatureCredit(
      {required String applicationId}) async {
    try {
      final result =
          await _repository.signatureCredit(applicationId: applicationId);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RequestLoanResponse> requestFastLoan() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.requestFastCreditUser(id: id);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<RequestLoanHomeResponse>> getCredits() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.getCredits(userId: id);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<InstallmentResponse>> getDetailsLoan(
      {required String applicationId}) async {
    try {
      final result =
          await _repository.getDetailsLoan(applicationId: applicationId);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future hideCredit({required String applicationId}) async {
    try {
      final result = await _repository.hideCredit(applicationId: applicationId);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future checkAddress() async {
    final user = AppSession.getInstance().user;
    assert(user != null);
    final id = user!.id!;

    try {
      final result = await _repository.checkAddress(userId: id);

      return result;
    } catch (e) {
      rethrow;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/themes/styles/images_app.dart';

import 'investment_controller.dart';

class InvestmentPage extends StatefulWidget {
  const InvestmentPage({
    super.key,
  });

  @override
  State<InvestmentPage> createState() => _InvestmentPageState();
}

class _InvestmentPageState extends State<InvestmentPage> {
  final controller = Modular.get<InvestmentController>();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBarApp(
        title: I18n().investimentos.toUpperCase(),
      ),
      body: SizedBox(
        width: size.width,
        height: size.height,
        child: Column(
          children: [
            const Expanded(child: SizedBox()),
            IconsApp.icInvestimentNaoDisponivelText(),
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                const I18n().em_breve_vamos_disponibilizar,
                textAlign: TextAlign.center,
              ),
            ),
            const Expanded(child: SizedBox()),
            ImagesApp.imgBackgroundInvestimentoNaoDisponivel(),
          ],
        ),
      ),
    );
  }
}

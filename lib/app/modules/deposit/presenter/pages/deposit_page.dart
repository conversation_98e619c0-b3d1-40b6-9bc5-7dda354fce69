import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/deposit/presenter/bloc/deposit_bloc.dart';
import 'package:siclosbank/app/modules/loan/presenter/view/pages/fast_credit/components/item_horizontal_text.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/data/models/wallet/bank_account_user_response.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class DepositPageProvider extends StatelessWidget {
  const DepositPageProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DepositBloc>(
      create: (context) {
        return Modular.get();
      },
      child: BlocBuilder<DepositBloc, DepositState>(
        builder: (context, state) {
          return _DepositView(state: state);
        },
      ),
    );
  }
}

class _DepositView extends StatefulWidget {
  const _DepositView({super.key, required this.state});

  final DepositState state;

  @override
  __DepositViewState createState() => __DepositViewState();
}

class __DepositViewState extends State<_DepositView> {
  var key = GlobalKey();
  bool takeScreenShot = false;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<DepositBloc>(context).add(const GetBankAccountEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        showBack: true,
        title: const I18n().depositar.toUpperCase(),
        clickBack: () {
          Navigator.pop(context);
        },
        actions: <Widget>[
          IconButton(
            icon: ImageUtils.icShare(),
            onPressed: () async {
              setState(() {
                takeScreenShot = true;
              });

              await Future.delayed(const Duration(milliseconds: 300));

              Utils.takeScreenShot(
                key: key,
                fileName: "dados-bancarios-siclosbank${DateTime.now()}",
              );

              setState(() {
                takeScreenShot = false;
              });
            },
          ),
        ],
      ),
      body: _bodyCurrentAccount(context),
    );
  }

  CustomScrollView _bodyCurrentAccount(BuildContext context) {
    User? user = AppSession.getInstance().user;
    BankAccountResponse? account = AppSession.getInstance().bankAccount;

    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.only(top: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(const I18n().depositar_mensagem),
                ),
                const SizedBox(height: 65),
                BlocBuilder<DepositBloc, DepositState>(
                  builder: (context, state) {
                    if (state is DepositSuccess) {
                      if (user != null && account != null) {
                        return RepaintBoundary(
                          key: key,
                          child: Material(
                            color: takeScreenShot ? Colors.grey[100] : null,
                            child: _buildBankDetails(user, account),
                          ),
                        );
                      } else {
                        return const Text('Dados bancários não encontrados.');
                      }
                    }
                    return _bodyProgress();
                  },
                ),
                const SizedBox(height: 50),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

_bodyProgress() {
  return Center(child: Utils.circularProgressButton());
}

String _formatBankName(String? bankName) {
  if (bankName == null) return '-';
  if (bankName == "Celcoin Instituição de Pagamento S.A. 509") {
    return "509 - Celcoin IP S.A";
  }
  return bankName;
}

RepaintBoundary _buildBankDetails(User user, BankAccountResponse account) {
  final name = user.socialName != null && user.socialName!.isNotEmpty
      ? user.socialName!
      : user.name ?? '_';

  return RepaintBoundary(
    child: Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: <Widget>[
          ItemHorizontalText(
            title: const I18n().titular,
            info: name,
            // showCopy: true,
          ),
          ItemHorizontalText(
            title: const I18n().cpf.toUpperCase(),
            info: FieldsUtils.obterCpf(user.cpf!),
            showCopy: true,
          ),
          ItemHorizontalText(
            title: const I18n().agencia,
            info: account.accountBranchCode ?? '-',
            showCopy: true,
          ),
          ItemHorizontalText(
            title: const I18n().conta_corrente,
            info: account.accountNumber ?? '-',
            showCopy: true,
          ),
          ItemHorizontalText(
            title: const I18n().banco,
            info: _formatBankName(account.bankName),
            showCopy: true,
          ),
        ],
      ),
    ),
  );
}

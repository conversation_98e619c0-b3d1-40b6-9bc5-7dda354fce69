import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/deposit/presenter/bloc/deposit_bloc.dart';
import 'package:siclosbank/app/modules/deposit/presenter/pages/deposit_page.dart';
import 'package:siclosbank/app/app_module.dart';

class DepositModule extends Module {
  @override
  List<Module> get imports => [
        AppModule(),
      ];

  @override
  void binds(i) {
    // i.addLazySingleton<IDepositRepository>(DepositRepository.new);
    // i.addLazySingleton<IDepositUsecase>(DepositUsecase.new);

    i.add(DepositBloc.new);
    super.binds(i);
  }

  @override
  void routes(RouteManager r) {
    r.child('/', child: (context) => const DepositPageProvider());
  }
}

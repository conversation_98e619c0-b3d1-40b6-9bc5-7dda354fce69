part of 'deposit_bloc.dart';

sealed class DepositState extends Equatable {
  const DepositState();

  @override
  List<Object> get props => [];
}

final class DepositInitial extends DepositState {}

final class DepositLoading extends DepositState {}

final class DepositSuccess extends DepositState {
  final CollaboratorResponse collaborator;
  const DepositSuccess(this.collaborator);

  @override
  List<Object> get props => [collaborator];

  @override
  bool? get stringify => false;
}

final class DepositError extends DepositState {
  final ErrorResponse error;
  const DepositError(this.error);

  @override
  List<Object> get props => [error];

  @override
  bool? get stringify => false;
}

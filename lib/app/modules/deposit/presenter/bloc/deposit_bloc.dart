import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/collaborator_response.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'deposit_event.dart';
part 'deposit_state.dart';

class DepositBloc extends Bloc<DepositEvent, DepositState> {
  final IAppUseCase _usecase;
  DepositBloc(this._usecase) : super(DepositInitial()) {
    on<DepositEvent>((event, emit) async {
      if (event is GetBankAccountEvent) {
        emit(DepositLoading());
        final user = AppSession.getInstance().user;
        await _usecase.getCollaborator(user!.cpf!).then((value) {
          emit(DepositSuccess(value));
        }).catchError((e) {
          emit(DepositError((e as ErrorResponse)));
        });
      }
    });
  }
}

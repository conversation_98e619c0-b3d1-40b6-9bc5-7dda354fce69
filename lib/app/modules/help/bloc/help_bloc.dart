import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:url_launcher/url_launcher_string.dart';

part 'help_event.dart';
part 'help_state.dart';

class HelpBloc extends Bloc<HelpEvent, HelpState> {
  HelpBloc() : super(HelpInitial()) {
    on<HelpEvent>((event, emit) async {
      if (event is OpenCallPhoneSuport) {
        final number = AppSession.getInstance().numberPhone;
        await launchUrlString("tel:$number");
      }

      if (event is OpenWhatsAppSuport) {
        final whatsapp = AppSession.getInstance().numberWhatsApp;
        var whatsappUrl = "whatsapp://send?phone=55$whatsapp";
        try {
          bool isRun = await launchUrlString(whatsappUrl);
          if (!isRun) {
            emit(HelpOpenWhatsappError());
          }
        } catch (error) {
          if (error is PlatformException &&
              error.message!.startsWith("No Activity found")) {
            emit(HelpOpenWhatsappError());
          }
        }
      }

      if (event is OpenEmailSuport) {
        final email = AppSession.getInstance().emailHomeSupport;
        final emailCc = AppSession.getInstance().emailCcSuporte;

        if (emailCc != null && emailCc.isNotEmpty) {
          await launchUrlString("mailto:$email" "?cc=$emailCc");
        } else {
          await launchUrlString("mailto:$email");
        }
      }
    });
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import '../../../../localization/generated/i18n.dart';
import '../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../shared/presenter/view/components/others/card_buttons_support_widget.dart';
import '../../../shared/presenter/view/components/others/snack_bar_app.dart';
import '../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../shared/utils/calls_and_messages_service.dart';
import '../bloc/help_bloc.dart';

class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<HelpBloc>(),
      child: _HelpPage(
        key: key,
      ),
    );
  }
}

class _HelpPage extends StatefulWidget {
  const _HelpPage({
    super.key,
  });

  @override
  State<_HelpPage> createState() => _HelpPageState();
}

class _HelpPageState extends State<_HelpPage> {
  // var _service = locator.get<CallsAndMessagesService>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.ajuda.toUpperCase(),
      ),
      body: BlocConsumer<HelpBloc, HelpState>(
        listener: (context, state) {
          if (state is HelpOpenWhatsappError) {
            SnackBarApp.showSnack(
              context: context,
              message: I18n.of(context)!.whatsapp_nao_instalado,
              success: false,
            );
          }
        },
        builder: (context, state) {
          return _buildBody();
        },
      ),
    );
  }

  _buildBody() {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 24),
            child: Column(
              children: <Widget>[
                TextResponsive(
                  I18n.of(context)!.msg_ajuda,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 44),
                CardButtonsSupport(
                  clickEmail: () {
                    CallsAndMessagesService.sendEmailSuporte();
                    // BlocProvider.of<HelpBloc>(context).add(OpenEmailSuport());
                  },
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}

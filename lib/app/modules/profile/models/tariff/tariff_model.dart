// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class TariffModel extends Equatable {
  final String title;
  final String value;
  final String observation;
  final String observationValue;

  TariffModel({
    required this.title,
    required this.value,
    required this.observation,
    required this.observationValue,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'title': title,
      'value': value,
      'observation': observation,
      'observationValue': observationValue,
    };
  }

  factory TariffModel.fromMap(Map<String, dynamic> map, String titleSection) {
    String observationValue = '';
    String observation = '';
    if (map['exemptQuantity'] != null && map['exemptQuantity'] >= 0) {
      if (titleSection == "Transferências") {
        observation = "*Isenção em ${map['exemptQuantity']} envios mensais";
        observationValue = '/envio';
      } else {
        observation = "*Isenção em ${map['exemptQuantity']} depósitos mensais";
        observationValue = '/emissão';
      }
    }

    return TariffModel(
      title: map['serviceName'] ?? '',
      value: map['pricePerExceedingOp'] ?? '',
      observation: observation,
      observationValue: observationValue,
    );
  }

  String toJson() => json.encode(toMap());

  @override
  List<Object> get props => [title, value, observation, observationValue];
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class Devices extends Equatable {
  final String id;
  final String imei;
  final String os;
  final String user_id;
  final String delete_code;
  final String validate_device_code;
  final String token_fcm;
  final String created_at;
  final String updated_at;

  const Devices({
    required this.id,
    required this.os,
    required this.imei,
    required this.user_id,
    required this.delete_code,
    required this.validate_device_code,
    required this.token_fcm,
    required this.created_at,
    required this.updated_at,
  });

  factory Devices.fromJson(Map<String, dynamic> json) => Devices(
        id: json['id'] ?? '',
        imei: json['imei'] ?? '',
        os: json['os'] ?? '',
        user_id: json['user_id'] ?? '',
        delete_code: json['delete_code'] ?? '',
        validate_device_code: json['validate_device_code'] ?? '',
        token_fcm: json['token_fcm'] ?? '',
        created_at: json['created_at'] ?? '',
        updated_at: json['updated_at'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'imei': imei,
        'os': os,
        'user_id': user_id,
        'delete_code': delete_code,
        'validate_device_code': validate_device_code,
        'token_fcm': token_fcm,
        'created_at': created_at,
        'updated_at': updated_at,
      };

  @override
  List<Object> get props => [
        id,
        imei,
        os,
        user_id,
        delete_code,
        validate_device_code,
        token_fcm,
        created_at,
        updated_at
      ];
}

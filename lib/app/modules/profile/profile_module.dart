import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pin/pin_module.dart';
import 'package:siclosbank/app/modules/profile/data/datasource/profile_datasource.dart';
import 'package:siclosbank/app/modules/profile/data/datasource/profile_datasource_impl.dart';
// import 'package:siclosbank/app/modules/profile/domain/repositories/i_profile_repository.dart';
// import 'package:siclosbank/app/modules/profile/domain/usecases/manage_devices_usecase.dart';
import 'package:siclosbank/app/modules/profile/data/repository/profile_repository_impl.dart';
// import 'package:siclosbank/app/modules/profile/domain/usecases/user/%20change_password_usecase.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/user/change_display_name_view.dart';
// import 'package:siclosbank/app/modules/profile/domain/usecases/user/change_email_usecase.dart';
// import 'package:siclosbank/app/modules/profile/domain/usecases/user/change_profile_picture_usecase.dart';
// import 'package:siclosbank/app/modules/profile/domain/usecases/user/get_user_usecase.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/displayName/change_display_name_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/profile_digital/profile_bloc.dart';

import 'package:siclosbank/app/modules/profile/presenter/bloc/manage_devices/manage_devices_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/tariff/tariff_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/address/change_address_bloc.dart';

import 'package:siclosbank/app/modules/profile/presenter/bloc/user/email/change_email_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/password/change_password_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/picture/change_profile_picture_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/adjustments/adjustments_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/user/change_address_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/user/change_profile_picture_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/devices/manage_devices_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/profile_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/tariff/tariff_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/user/change_email_view.dart';
import 'package:siclosbank/app/modules/profile/presenter/pages/user/change_password_view.dart';
import 'package:siclosbank/app/app_module.dart';
// import 'package:siclosbank/app/modules/profile/domain/usecases/user/change_social_name_usecase.dart';

import 'data/repository/i_profile_repository.dart';

class ProfileModule extends Module {
  @override
  void binds(Injector i) {
    i.addLazySingleton<IProfileDatasource>(ProfileDatasourceImpl.new);
    i.addLazySingleton<IProfileRepository>(ProfileRepositoryImpl.new);
    // i.addLazySingleton<IManageDevicesUsecase>(ManageDevicesUsecase.new);
    // i.addLazySingleton<ITariffUsecase>(TariffUsecase.new);
    // i.addLazySingleton<IChangePasswordUsecase>(ChangePasswordUsecase.new);
    // i.addLazySingleton<ILogoutUsecase>(LogoutUsecase.new);
    // i.addLazySingleton<IChangeEmailUsecase>(ChangeEmailUsecase.new);
    // i.addLazySingleton<IChangeProfilePictureUsecase>(
    //     ChangeProfilePictureUsecase.new);
    // i.addLazySingleton<IGetUserUsecase>(GetUserUsecase.new);
    // i.addLazySingleton<IChangeSocialNameUsecase>(ChangeSocialNameUsecase.new);

    i.add(TariffBloc.new);
    i.add(ManageDevicesBloc.new);
    i.add(ChangePasswordBloc.new);
    i.add(ChangeEmailBloc.new);
    i.add(ChangeProfilePictureBloc.new);
    i.add(ProfileBloc.new);
    i.add(ChangeAddressBloc.new);
    i.add(ChangeDisplayNameBloc.new);
  }

  @override
  List<Module> get imports => [
        AppModule(),
        PinModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (context) => const PerfilHomeProvider());
    r.child('/manage-devices',
        child: (context) => const ManageDevicesProvider());
    r.child('/adjustments', child: (context) => const AdjustmentsProvider());
    r.child('/tariffs', child: (context) => const TariffViewProvider());

    r.child('/change-password',
        child: (context) => const ChangePasswordViewProvider());

    r.child('/change-email',
        child: (context) => const ChangeEmailViewProvider());

    r.child('/change-profile-picture',
        child: (context) => const ChangeProfilePictureProvider());

    r.child('/change-address',
        child: (context) => const ChangeAddressViewProvider());

    r.child('/change-display-name',
        child: (context) => const ChangeDisplayNameViewProvider());
  }
}

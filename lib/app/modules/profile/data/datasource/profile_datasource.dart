import 'dart:io';

import 'package:siclosbank/app/shared/data/client/api_response.dart';

abstract class IProfileDatasource {
  Future<ApiResponse> getUser();
  Future<ApiResponse> changeSocialName(
      {required String displayName, required String id});
  Future<ApiResponse> getDevices(String id);
  Future<ApiResponse> sendCodeToDeleteDevice(String deviceId);
  Future<ApiResponse> deleteDevice(String deviceId, String deleteCode);
  Future<ApiResponse> getTariffService();

  Future<ApiResponse> changePassword(
      {required String password, required String oldPassword});

  Future<void> logout();
  Future<ApiResponse> changeEmail(
      {required String newEmail, required String password});
  Future<ApiResponse> verifyPassword({required Map<String, dynamic> request});

  Future<ApiResponse> changeProfilePicture(File pathFile);
}

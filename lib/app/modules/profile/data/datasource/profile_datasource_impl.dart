import 'dart:convert';
import 'dart:io';

import 'package:siclosbank/app/modules/profile/data/datasource/profile_datasource.dart';
import 'package:siclosbank/app/modules/profile/data/datasource/profile_endpoints.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

class ProfileDatasourceImpl implements IProfileDatasource {
  final IClient _client;
  ProfileDatasourceImpl(this._client);

  @override
  Future<ApiResponse> getUser() async {
    var result = _client.fetch(
      method: 'GET',
      path: ProfileEndpoints.getUser,
    );

    return result;
  }

  @override
  Future<ApiResponse> changeSocialName(
      {required String displayName, required String id}) async {
    var result = await _client.fetch(
      method: 'PUT',
      path: ProfileEndpoints.getUser,
      data: {
        "id": id,
        "social_name": displayName,
      },
    );

    return result;
  }

  @override
  Future<ApiResponse> getDevices(String id) async {
    var result = _client.fetch(
      method: 'GET',
      path: ProfileEndpoints.getDevices(id),
    );

    return result;
  }

  @override
  Future<ApiResponse> sendCodeToDeleteDevice(String deviceId) async {
    var result = _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.sendCodeToDeleteDevice,
      data: {
        "device_id": deviceId,
      },
    );

    return result;
  }

  @override
  Future<ApiResponse> deleteDevice(
    String deviceId,
    String deleteCode,
  ) async {
    var result = _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.deleteDevice,
      data: {"device_id": deviceId, "delete_code": deleteCode},
    );

    return result;
  }

  @override
  Future<ApiResponse> getTariffService() {
    var result = _client.fetch(
      method: 'GET',
      path: ProfileEndpoints.serviceTariff,
    );

    return result;
  }

  @override
  Future<ApiResponse> changePassword(
      {required String password, required String oldPassword}) async {
    var result = await _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.getChangePassword(),
      data: {
        "newPassword": password,
        "currentPassword": oldPassword,
      },
    );

    return result;
  }

  @override
  Future<void> logout() async {
    await _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.logout,
    );
    return;
  }

  @override
  Future<ApiResponse> changeEmail(
      {required String newEmail, required String password}) async {
    var result = await _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.getChangeEmail(),
      data: {
        "email": newEmail,
        "password": password,
      },
    );

    return result;
  }

  @override
  Future<ApiResponse> verifyPassword(
      {required Map<String, dynamic> request}) async {
    var result = await _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.verifyPassword(),
      data: request,
    );

    return result;
  }

  @override
  Future<ApiResponse> changeProfilePicture(File pathFile) async {
    File imageFile = File(pathFile.path.toString());
    List<int> imageBytes = imageFile.readAsBytesSync();
    String base64Image = base64Encode(imageBytes);

    var result = await _client.fetch(
      method: 'POST',
      path: ProfileEndpoints.changeProfilePicture(),
      data: {"photo": "data:image/png;base64, $base64Image"},
    );

    return result;
  }
}

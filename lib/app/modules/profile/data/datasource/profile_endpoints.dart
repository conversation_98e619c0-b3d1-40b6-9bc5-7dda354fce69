abstract class ProfileEndpoints {
  static String getUser = "/user";

  static String getDevices(String id) =>
      "/user_device/find-device-by-user-id/$id";

  static String sendCodeToDeleteDevice =
      "/user_device/send-code-to-delete-device";
  static String deleteDevice = "/user_device/delete-device";
  static String serviceTariff = "/service/get-all-service-quantity";
  static String getChangePassword() => "/user/change-password";
  static const String logout = "/user/logout";
  static String getChangeEmail() => "/user/update-email-user";
  static String verifyPassword() => "/user/auth";
  static String changeProfilePicture() => "/user/upload-profile-photo";
}

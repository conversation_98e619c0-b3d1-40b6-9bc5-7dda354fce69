import 'dart:io';

import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/modules/profile/models/tariff/tariff_section_model.dart';

abstract class IProfileRepository {
  Future<dynamic> getUser();
  Future<bool> changeSocialName(String displayName, String id);
  Future<List<Devices>> getDevices(String id);
  Future<dynamic> sendCodeToDeleteDevice(String deviceId);
  Future<dynamic> deleteDevice(String deviceId, String deleteCode);
  Future<List<TariffSectionModel>> getTariffService();
  Future<bool> createNewPassword(String oldPassword, String newPassword);
  Future<void> logout();
  Future<bool> createEmail(
      {required String newEmail, required String password});
  Future<bool> verifyPassword(String password);
  Future<String> changeProfilePicture(File pathFile);
}

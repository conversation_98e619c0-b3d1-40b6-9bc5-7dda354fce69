import 'dart:io';

import 'package:siclosbank/app/modules/profile/data/datasource/profile_datasource.dart';
import 'package:siclosbank/app/modules/profile/models/tariff/tariff_section_model.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import 'i_profile_repository.dart';

class ProfileRepositoryImpl implements IProfileRepository {
  final IProfileDatasource _datasource;

  ProfileRepositoryImpl(this._datasource);

  @override
  Future<dynamic> getUser() async {
    try {
      ApiResponse result = await _datasource.getUser();
      return result.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> changeSocialName(String displayName, String id) async {
    try {
      ApiResponse response = await _datasource.changeSocialName(
        displayName: displayName,
        id: id,
      );
      return response.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<Devices>> getDevices(String id) async {
    try {
      ApiResponse result = await _datasource.getDevices(id);
      List<Devices> devices = (result.data as List)
          .map((deviceJson) => Devices.fromJson(deviceJson))
          .toList();
      return devices;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> sendCodeToDeleteDevice(String deviceId) async {
    try {
      ApiResponse response = await _datasource.sendCodeToDeleteDevice(deviceId);
      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> deleteDevice(String deviceId, String deleteCode) async {
    try {
      ApiResponse response = await _datasource.deleteDevice(
        deviceId,
        deleteCode,
      );
      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<TariffSectionModel>> getTariffService() async {
    try {
      ApiResponse response = await _datasource.getTariffService();

      final data = response.data as List;
      return TariffSectionModel.generetadTariffList(data);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> createNewPassword(String oldPassword, String newPassword) async {
    try {
      ApiResponse response = await _datasource.changePassword(
        password: newPassword,
        oldPassword: oldPassword,
      );
      if (response.statusCode != 200) {
        throw response;
      }

      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> logout() async {
    try {
      return await _datasource.logout();
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<bool> createEmail({
    required String newEmail,
    required String password,
  }) async {
    try {
      ApiResponse response = await _datasource.changeEmail(
        newEmail: newEmail,
        password: password,
      );

      return response.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<bool> verifyPassword(String password) async {
    final user = AppSession.getInstance().user!;

    try {
      final request = {"login": user.cpf, "password": password};
      ApiResponse response = await _datasource.verifyPassword(request: request);
      return response.statusCode == 200;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> changeProfilePicture(File pathFile) async {
    try {
      ApiResponse response = await _datasource.changeProfilePicture(pathFile);
      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import 'package:siclosbank/app/shared/utils/storage_utils.dart';

import '../../../data/repository/i_profile_repository.dart';

part 'login_digital_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<LoginDigitalEvent, IProfileState> {
  final IAppUseCase _usecase;
  final IProfileRepository _repo;

  ProfileBloc(this._usecase, this._repo)
      : super(
          InitalLoginDigitalState(),
        ) {
    on((LoginDigitalEvent event, emit) async {
      if (event is LogoutEvent) {
        try {
          emit(LoadingState());
          await _repo.logout();
          StorageUtils.clearDataLogin();
          emit(SuccessLogoutState());
        } on ErrorResponse catch (error) {
          emit(ErrorLogoutState(error));
        }
      }
      if (event is CleanLoginDigitalEvent) {
        try {
          emit(LoadingState());
          AppSession.getInstance().lastLogin = null;
          AppSession.getInstance().lastSenha = null;
          await _usecase.cleanEnableIdDigitalLogin();
          emit(InitalLoginDigitalState(isEnabledLoginDigital: false));
        } on Exception catch (error) {
          emit(InitalLoginDigitalState());
        }
      }
      if (event is GetLoginDigitalEvent) {
        try {
          emit(LoadingState());

          final result = await _usecase.getEnableIdDigitalLogin();
          emit(InitalLoginDigitalState(isEnabledLoginDigital: result));
        } on Exception catch (error) {
          emit(InitalLoginDigitalState());
        }
      }
      if (event is SetLoginDigitalEvent) {
        try {
          emit(LoadingState());
          final curerntState = await _usecase.getEnableIdDigitalLogin();
          await _usecase.setEnableIdDigitalLogin(!curerntState);
          emit(InitalLoginDigitalState(isEnabledLoginDigital: !curerntState));
        } on Exception catch (error) {
          emit(InitalLoginDigitalState());
        }
      }
    });
  }
}

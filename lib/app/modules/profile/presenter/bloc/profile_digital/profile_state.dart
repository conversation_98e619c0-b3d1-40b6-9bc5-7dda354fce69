part of 'profile_bloc.dart';

sealed class IProfileState extends Equatable {}

class InitalLoginDigitalState extends IProfileState {
  final bool isEnabledLoginDigital;

  InitalLoginDigitalState({
    this.isEnabledLoginDigital = false,
  });

  @override
  List get props => [
        isEnabledLoginDigital,
      ];
}

class LoadingState extends IProfileState {
  LoadingState();

  @override
  List get props => [];
}

class ErrorLogoutState extends IProfileState {
  final ErrorResponse error;

  ErrorLogoutState(this.error);

  @override
  List get props => [error];
}

class SuccessLogoutState extends IProfileState {
  @override
  List get props => [];
}

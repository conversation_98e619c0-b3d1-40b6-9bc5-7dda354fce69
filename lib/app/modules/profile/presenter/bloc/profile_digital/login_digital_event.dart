// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'profile_bloc.dart';

abstract class LoginDigitalEvent implements Equatable {}

class LogoutEvent extends LoginDigitalEvent {
  @override
  List get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class CleanLoginDigitalEvent extends LoginDigitalEvent {
  @override
  List get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class GetLoginDigitalEvent extends LoginDigitalEvent {
  @override
  List get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class SetLoginDigitalEvent extends LoginDigitalEvent {
  @override
  List get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

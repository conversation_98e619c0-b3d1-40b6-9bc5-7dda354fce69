part of 'tariff_bloc.dart';

sealed class ITariffState {
  const ITariffState();
}

class TariffInitialState extends ITariffState {}

class TariffLoadingState extends ITariffState {}

class TariffErrorState extends ITariffState {
  final ErrorResponse error;

  TariffErrorState(this.error);
}

class TariffSuccessState extends ITariffState {
  final List<TariffSectionModel> tariffs;

  TariffSuccessState(
    this.tariffs,
  );
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/modules/profile/models/tariff/tariff_section_model.dart';

import '../../../../../shared/errors/error_response.dart';
import '../../../data/repository/i_profile_repository.dart';

part 'tariff_event.dart';
part 'tariff_state.dart';

class TariffBloc extends Bloc<TariffEvent, ITariffState> {
  final IProfileRepository _usecase;

  TariffBloc(this._usecase) : super(TariffInitialState()) {
    on((TariffEvent event, emit) async {
      if (event is GetTariffEvent) {
        emit(TariffLoadingState());
        try {
          final result = await _usecase.getTariffService();
          emit(TariffSuccessState(result));
        } on ErrorResponse catch (e) {
          emit(TariffErrorState(e));
        }
      }
    });
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/profile/data/repository/i_profile_repository.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'change_password_event.dart';
part 'change_password_state.dart';

class ChangePasswordBloc
    extends Bloc<IChangePasswordEvent, IChangePasswordState> {
  final IProfileRepository _repo;
  ChangePasswordBloc(this._repo) : super(ChangePasswordInitialState()) {
    on(
      (IChangePasswordEvent event, emit) async {
        if (event is CreateChangePasswordEvent) {
          emit(ChangePasswordLoadingState());
          try {
            final result = await _repo.createNewPassword(
                event.oldPassword, event.newPassword);

            if (result) {
              emit(ChangePasswordSucessState());
            }
            throw result;
          } on ErrorResponse catch (e) {
            if (e.statusCode == 406) {
              emit(ChangePasswordErrorState(
                  error: e.copyWith(message: I18n().senha_atual_incorreta)));
            } else {
              emit(ChangePasswordErrorState(error: e));
            }
          }
        }
      },
    );
  }
}
// A234$6

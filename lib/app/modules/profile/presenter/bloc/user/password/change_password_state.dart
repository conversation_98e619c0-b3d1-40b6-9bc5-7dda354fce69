part of 'change_password_bloc.dart';

sealed class IChangePasswordState {}

class ChangePasswordInitialState extends IChangePasswordState {}

class ChangePasswordLoadingState extends IChangePasswordState {}

class ChangePasswordSucessState extends IChangePasswordState {}

class ChangePasswordErrorState extends IChangePasswordState {
  final ErrorResponse error;
  ChangePasswordErrorState({required this.error});
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'change_password_bloc.dart';

abstract class IChangePasswordEvent implements Equatable {}

class CreateChangePasswordEvent extends IChangePasswordEvent {
  final String newPassword;
  final String oldPassword;

  CreateChangePasswordEvent({
    required this.oldPassword,
    required this.newPassword,
  });

  @override
  List<Object> get props => [newPassword];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

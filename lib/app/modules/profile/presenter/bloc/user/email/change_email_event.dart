// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'change_email_bloc.dart';

abstract class IChangeEmailEvent implements Equatable {}

class SetEmailEvent extends IChangeEmailEvent {
  final String newEmail;

  SetEmailEvent({
    required this.newEmail,
  });

  @override
  List<Object> get props => [newEmail];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class CreateChangeEmailEvent extends IChangeEmailEvent {
  final String newEmail;
  final String password;

  CreateChangeEmailEvent({
    required this.newEmail,
    required this.password,
  });

  @override
  List<Object> get props => [newEmail];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

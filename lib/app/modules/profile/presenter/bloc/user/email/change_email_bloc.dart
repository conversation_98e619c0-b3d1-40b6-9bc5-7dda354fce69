import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/profile/data/repository/i_profile_repository.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'change_email_event.dart';
part 'change_email_state.dart';

class ChangeEmailBloc extends Bloc<IChangeEmailEvent, IChangeEmailState> {
  final IProfileRepository _repo;
  ChangeEmailBloc(this._repo) : super(ChangeEmailInitialState()) {
    on(
      (IChangeEmailEvent event, emit) async {
        if (event is SetEmailEvent) {
          emit(ChangeEmailVerifyPasswordState(newEmail: event.newEmail));
        }
        if (event is CreateChangeEmailEvent) {
          emit(ChangeEmailLoadingState());
          try {
            final result = await _repo.createEmail(
              newEmail: event.newEmail,
              password: event.password,
            );

            if (result) {
              emit(const ChangeEmailSucessState());
              return;
            } else {
              emit(
                ChangeEmailErrorState(
                    error: ErrorResponse(
                  message: "Não foi possivel alterar sua senha",
                )),
              );
            }
            return;
          } on ErrorResponse catch (e) {
            if (e.statusCode == 406) {
              emit(ChangeEmailErrorState(
                  error:
                      e.copyWith(message: const I18n().senha_atual_incorreta)));
            } else {
              emit(ChangeEmailErrorState(error: e));
            }
          }
        }
      },
    );
  }
}

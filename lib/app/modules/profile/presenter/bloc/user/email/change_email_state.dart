// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'change_email_bloc.dart';

sealed class IChangeEmailState extends Equatable {
  const IChangeEmailState();

  @override
  List<Object> get props => [];
}

class ChangeEmailInitialState extends IChangeEmailState {
  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class ChangeEmailVerifyPasswordState extends IChangeEmailState {
  final String newEmail;
  const ChangeEmailVerifyPasswordState({
    required this.newEmail,
  });

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class ChangeEmailLoadingState extends IChangeEmailState {
  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class ChangeEmailState extends IChangeEmailState {
  const ChangeEmailState();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class ChangeEmailSucessState extends IChangeEmailState {
  const ChangeEmailSucessState();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class ChangeEmailErrorState extends IChangeEmailState {
  final ErrorResponse error;
  const ChangeEmailErrorState({
    required this.error,
  });

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

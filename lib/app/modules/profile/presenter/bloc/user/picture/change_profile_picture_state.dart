import 'dart:io';
import 'package:equatable/equatable.dart';

sealed class IChangeProfilePictureState extends Equatable {
  const IChangeProfilePictureState();

  @override
  List<Object> get props => [];
}

class ChangeProfilePictureInitialState extends IChangeProfilePictureState {
  @override
  bool? get stringify => throw UnimplementedError();
}

class ChangeProfilePictureLoadingState extends IChangeProfilePictureState {}

class ChangeProfilePictureState extends IChangeProfilePictureState {
  const ChangeProfilePictureState();

  @override
  List<Object> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class ChangeProfilePictureSucessState extends IChangeProfilePictureState {
  const ChangeProfilePictureSucessState();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class ChangeProfilePictureErrorState extends IChangeProfilePictureState {
  final String message;
  const ChangeProfilePictureErrorState({required this.message});

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class ChangeProfilePictureBinaryDataState extends IChangeProfilePictureState {
  final File imageData;

  const ChangeProfilePictureBinaryDataState({required this.imageData});

  @override
  List<Object> get props => [imageData];

  @override
  bool? get stringify => throw UnimplementedError();
}

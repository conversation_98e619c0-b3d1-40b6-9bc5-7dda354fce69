import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/profile/data/repository/i_profile_repository.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/picture/change_profile_picture_event.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/picture/change_profile_picture_state.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';

class ChangeProfilePictureBloc
    extends Bloc<IChangeProfilePictureEvent, IChangeProfilePictureState> {
  final IProfileRepository _repo;
  final IAppUseCase _appUseCase;

  ChangeProfilePictureBloc(this._repo, this._appUseCase)
      : super(ChangeProfilePictureInitialState()) {
    on<CreateChangeProfilePictureEvent>((event, emit) async {
      try {
        final instance = AppSession.getInstance();
        final user = instance.user;

        emit(ChangeProfilePictureLoadingState());
        final result = await _repo
            .changeProfilePicture(event.newPicture); // pedir para retornar
        if (result.isNotEmpty) {
          // update user url profile image
          // final resultUser = await _appUseCase.getUserByCpf();
          user!.profileImage = result; // resultUser.profileImage;
          instance.user = user;
          emit(const ChangeProfilePictureSucessState());
        } else {
          emit(const ChangeProfilePictureErrorState(
              message: "Erro ao tentar mudar imagem de perfil"));
          throw result;
        }
      } catch (error) {
        emit(const ChangeProfilePictureErrorState(
            message: "Não foi possivel alterar sua foto de perfil"));
      }
    });

    on<GetImageProfileInitialEvent>((event, emit) async {
      try {
        final response = await _appUseCase.getImageProfile();

        emit(ChangeProfilePictureBinaryDataState(
          imageData: response,
        ));
      } catch (error) {
        // emit(ChangeProfilePictureErrorState(
        //     message: "Falha ao carregar imagem de perfil: $error"));
      }
    });
  }
}

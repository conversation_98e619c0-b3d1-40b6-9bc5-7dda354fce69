import 'dart:io';
import 'dart:typed_data';

import 'package:equatable/equatable.dart';

abstract class IChangeProfilePictureEvent implements Equatable {}

class CreateChangeProfilePictureEvent extends IChangeProfilePictureEvent {
  final File newPicture;

  CreateChangeProfilePictureEvent({
    required this.newPicture,
  });

  @override
  List<Object> get props => [newPicture];

  @override
  bool? get stringify => throw UnimplementedError();
}

class GetImageProfileInitialEvent extends IChangeProfilePictureEvent {
  @override
  List<Object> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class SetProfilePictureBinaryDataEvent extends IChangeProfilePictureEvent {
  final Uint8List imageData;

  SetProfilePictureBinaryDataEvent({required this.imageData});

  @override
  List<Object> get props => [imageData];

  @override
  bool? get stringify => throw UnimplementedError();
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';

part 'change_address_event.dart';
part 'change_address_state.dart';

class ChangeAddressBloc extends Bloc<ChangeAddressEvent, ChangeAddressState> {
  final IAppUseCase appUseCase;
  ChangeAddressBloc(this.appUseCase) : super(ChangeAddressInitial()) {
    on<ChangeAddressEvent>((event, emit) async {
      if (event is GetAddressEvent) {
        emit(const ChangeAddressLoading(message: 'Carregando endereço...'));

        final address = await appUseCase.getAddress(event.userId);

        emit(ChangeAddressLoaded(address: address));
      }
    });
  }
}

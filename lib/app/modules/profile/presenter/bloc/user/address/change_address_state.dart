part of 'change_address_bloc.dart';

sealed class ChangeAddressState extends Equatable {
  const ChangeAddressState();

  @override
  List<Object> get props => [];
}

final class ChangeAddressInitial extends ChangeAddressState {}

final class ChangeAddressLoading extends ChangeAddressState {
  final String message;

  const ChangeAddressLoading({required this.message});

  @override
  List<Object> get props => [message];
}

final class ChangeAddressLoaded extends ChangeAddressState {
  final AddressResponse address;

  const ChangeAddressLoaded({required this.address});

  @override
  List<Object> get props => [address];
}

final class ChangeAddressError extends ChangeAddressState {
  final String message;

  const ChangeAddressError({required this.message});

  @override
  List<Object> get props => [message];
}

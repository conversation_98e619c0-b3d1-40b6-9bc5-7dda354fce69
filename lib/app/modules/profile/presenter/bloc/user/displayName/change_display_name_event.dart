part of 'change_display_name_bloc.dart';

sealed class ChangeDisplayNameEvent extends Equatable {
  const ChangeDisplayNameEvent();

  @override
  List<Object> get props => [];
}

class ChangeDisplayNameSubmittedEvent extends ChangeDisplayNameEvent {
  final String displayName;
  final String id;
  const ChangeDisplayNameSubmittedEvent({
    required this.displayName,
    required this.id,
  });

  @override
  List<Object> get props => [displayName, id];
}

class ChangeDisplayNameInitialEvent extends ChangeDisplayNameEvent {
  final String displayNameInitial;
  const ChangeDisplayNameInitialEvent({
    required this.displayNameInitial,
  });

  @override
  List<Object> get props => [displayNameInitial];
}

class ChangeDisplayNameLoadingEvent extends ChangeDisplayNameEvent {
  const ChangeDisplayNameLoadingEvent();

  @override
  List<Object> get props => [];
}

class ChangeDisplayNameSuccessEvent extends ChangeDisplayNameEvent {
  const ChangeDisplayNameSuccessEvent();

  @override
  List<Object> get props => [];
}

class ChangeDisplayNameErrorEvent extends ChangeDisplayNameEvent {
  const ChangeDisplayNameErrorEvent();

  @override
  List<Object> get props => [];
}

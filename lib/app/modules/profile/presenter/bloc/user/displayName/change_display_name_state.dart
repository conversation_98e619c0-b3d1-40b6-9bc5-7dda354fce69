import 'package:equatable/equatable.dart';

sealed class ChangeDisplayNameState extends Equatable {
  const ChangeDisplayNameState();

  @override
  List<Object> get props => [];
}

final class ChangeDisplayNameInitial extends ChangeDisplayNameState {
  const ChangeDisplayNameInitial();

  @override
  List<Object> get props => [];
}

final class ChangeDisplayNameLoading extends ChangeDisplayNameState {
  const ChangeDisplayNameLoading();

  @override
  List<Object> get props => [];
}

final class ChangeDisplayNameSuccess extends ChangeDisplayNameState {
  final String message;

  const ChangeDisplayNameSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

final class ChangeDisplayNameError extends ChangeDisplayNameState {
  final String message;

  const ChangeDisplayNameError({required this.message});

  @override
  List<Object> get props => [message];
}

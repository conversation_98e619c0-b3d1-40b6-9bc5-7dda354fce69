import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/profile/data/repository/i_profile_repository.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/displayName/change_display_name_state.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';
import 'package:siclosbank/app/shared/data/models/token_response.dart';

part 'change_display_name_event.dart';

class ChangeDisplayNameBloc
    extends Bloc<ChangeDisplayNameEvent, ChangeDisplayNameState> {
  final IProfileRepository _repo;
  ChangeDisplayNameBloc(this._repo) : super(const ChangeDisplayNameInitial()) {
    on<ChangeDisplayNameSubmittedEvent>((event, emit) async {
      emit(const ChangeDisplayNameLoading());
      try {
        final result =
            await _repo.changeSocialName(event.displayName, event.id);

        if (result) {
          final user = AppSession.getInstance().user;
          user?.socialName = event.displayName;

          final token = TokenResponse(
            token: AppSession.getInstance().getAuth().replaceAll('Bearer ', ''),
            user: user,
          );

          const successState = ChangeDisplayNameSuccess(
              message: 'Nome social alterado com sucesso');
          emit(successState);

          StorageUtils.saveTokenResponse(token: token);
          StorageUtils.saveUser(user: user);
        } else {
          emit(const ChangeDisplayNameError(
              message: 'Erro ao alterar nome social'));
        }
      } catch (e) {
        emit(const ChangeDisplayNameError(
            message: 'Erro ao alterar nome social'));
      }
    });
  }
}

import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/modules/profile/data/repository/i_profile_repository.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/app_controller.dart';

import 'manage_devices_event.dart';
import 'manage_devices_state.dart';

class ManageDevicesBloc extends Bloc<ManageDevicesEvent, ManageDevicesState> {
  final IProfileRepository _repo;

  ManageDevicesBloc(this._repo) : super(ManageDevicesState.initState()) {
    on((ManageDevicesEvent event, emit) async {
      if (event is GetDevicesEvent) {
        try {
          String? userId;
          emit(state.copy(isProgress: true, deviceIdToRemove: ''));

          userId = AppSession.getInstance().user?.id;

          List<Devices> list = await _repo.getDevices(userId!);

          emit(state.copy(isProgress: false, listDevices: list));
        } on Exception catch (error) {
          emit(state.copy(isProgress: false, error: error));
        }
      } else if (event is RemoveDeviceEvent) {
        try {
          emit(state.copy(isProgress: true, success: false));

          await _repo.sendCodeToDeleteDevice(event.device.id);

          emit(state.copy(
            isProgress: false,
            deviceIdToRemove: event.device.id,
            success: true,
          ));
        } on Exception catch (error) {
          emit(state.copy(isProgress: false, error: error));
        }
      } else if (event is RefreshDevicesEvent) {
        try {
          String? userId;
          emit(state.copy(isProgress: true));

          userId = AppSession.getInstance().user?.id;

          List<Devices> list = await _repo.getDevices(userId!);

          emit(state.copy(isProgress: false, listDevices: list));
        } on Exception catch (error) {
          emit(state.copy(isProgress: false, error: error));
        }
      }
    });
  }
}

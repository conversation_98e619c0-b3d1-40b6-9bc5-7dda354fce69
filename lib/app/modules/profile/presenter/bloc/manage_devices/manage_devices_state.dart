import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';

class ManageDevicesState implements Equatable {
  bool isProgress;
  Exception? error;
  List<Devices> listDevices;
  bool? success;

  String? deviceIdToRemove;
  CodeSentAction action;

  ManageDevicesState({
    this.isProgress = false,
    this.error,
    this.listDevices = const [],
    this.deviceIdToRemove = "",
    this.success,
    this.action = CodeSentAction.REMOVE_DEVICE,
  });

  static ManageDevicesState initState() => ManageDevicesState();

  ManageDevicesState copy({
    bool? isProgress,
    Exception? error,
    List<Devices>? listDevices,
    String? deviceIdToRemove,
    bool? success,
    CodeSentAction? action,
  }) =>
      ManageDevicesState(
        isProgress: isProgress ?? this.isProgress,
        error: error,
        listDevices: listDevices ?? this.listDevices,
        deviceIdToRemove: deviceIdToRemove ?? this.deviceIdToRemove,
        success: success ?? this.success,
        action: action ?? this.action,
      );

  @override
  List<dynamic> get props => [
        isProgress,
        error,
        listDevices,
        deviceIdToRemove,
        success,
        action,
      ];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

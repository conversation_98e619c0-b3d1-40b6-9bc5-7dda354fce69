import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';

abstract class ManageDevicesEvent implements Equatable {}

class GetDevicesEvent extends ManageDevicesEvent {
  @override
  List<Devices> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class RemoveDeviceEvent extends ManageDevicesEvent {
  Devices device;

  RemoveDeviceEvent({required this.device});

  @override
  List<dynamic> get props => [device];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class RefreshDevicesEvent extends ManageDevicesEvent {
  @override
  List<Object> get props => [];

  @override
  bool? get stringify => true;
}

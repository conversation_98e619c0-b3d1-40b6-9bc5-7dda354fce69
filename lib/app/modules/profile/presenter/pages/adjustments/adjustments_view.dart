import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/pin/presenter/view/check_pin_page.dart';
import 'package:siclosbank/app/modules/pin/presenter/view/pin_registration_page.dart';
import 'package:siclosbank/app/modules/profile/presenter/components/item_menu_round.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';

class AdjustmentsProvider extends StatelessWidget {
  const AdjustmentsProvider({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const _AdjustmentsView();
  }
}

class _AdjustmentsView extends StatefulWidget {
  const _AdjustmentsView({super.key});

  @override
  State<_AdjustmentsView> createState() => __AdjustmentsViewState();
}

class __AdjustmentsViewState extends State<_AdjustmentsView> {
  User user = AppSession.getInstance().user!;
  bool isUpdatePin = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        clickBack: () => {
          Navigator.pop(context),
        },
        title: I18n.of(context)!.ajustes.toUpperCase(),
      ),
      body: ScrollConfiguration(
        behavior: MyBehavior(),
        child: CustomScrollView(
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 27),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    ItemMenuRound(
                      title: I18n.of(context)!.alterar_imagem_perfil,
                      onClick: () {
                        push(Routes.changeProfilePicturePage);
                      },
                    ),
                    ItemMenuRound(
                      title: I18n.of(context)!.alterar_display_name,
                      onClick: () {
                        push(Routes.changeDisplayNamePage);
                      },
                    ),
                    ItemMenuRound(
                      title: I18n.of(context)!.alterar_email,
                      onClick: () {
                        push(Routes.changeEmailPage);
                      },
                    ),
                    ItemMenuRound(
                      title: I18n.of(context)!.alterar_senha,
                      onClick: () {
                        push(Routes.changePasswordPage);
                      },
                    ),
                    Visibility(
                        visible: user.pin != null && user.pin!.isNotEmpty,
                        child: ItemMenuRound(
                          title: I18n.of(context)!.altear_pin,
                          onClick: _checkPin,
                        )),
                    Visibility(
                      visible: user.pin == null || user.pin!.isEmpty,
                      child: ItemMenuRound(
                        title: I18n.of(context)!.cadastrar_pin,
                        onClick: _createPin,
                      ),
                    ),
                    ItemMenuRound(
                      title: I18n.of(context)!.alterar_endereco,
                      isBottom: true,
                      onClick: () {
                        push(Routes.changeAddressPage);
                      },
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _checkPin() {
    // if (user.numberWrongPinAttempt! >= 3) {
    //   Navigator.of(context).push(
    //     MaterialPageRoute(
    //       builder: (_) => const AccountBlockedPinView(),
    //     ),
    //   );
    //   return;
    // }

    CheckPinPage.showSheet(context, () {
      // BlocProvider.of<CheckPinBloc>(context).add(CheckPinEventSend(pin: pin));
      SnackBarApp.showSnack(
        context: context,
        success: true,
        message: I18n.of(context)!.pin_alterado_sucesso,
      );
    }, isUpdatePin: true);
  }

  _createPin({ErrorResponse? errorResponse}) {
    PinRegistrationPage.showSheet(context, onSuccess: (context, isSuccess) {
      if (isSuccess == true) {
        _refresh();
      }
      //<EMAIL>
      //A234$6

      SnackBarApp.showSnack(
        context: context,
        message: I18n.of(context)!.pin_alterado_sucesso,
        success: true,
      );
    });
  }

  void _refresh() {
    setState(() {
      user.pin = AppSession.getInstance().user!.pin;
    });
  }
}

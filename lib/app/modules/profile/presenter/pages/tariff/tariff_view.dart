import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/tariff/tariff_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/money_format.dart';

class TariffViewProvider extends StatelessWidget {
  const TariffViewProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TariffBloc>(
      create: (context) => Modular.get(),
      child: const TariffView(),
    );
  }
}

class TariffView extends StatefulWidget {
  const TariffView({super.key});

  @override
  State<TariffView> createState() => _TariffViewState();
}

class _TariffViewState extends State<TariffView> {
  @override
  void initState() {
    BlocProvider.of<TariffBloc>(context).add(GetTariffEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.tarifas.toUpperCase(),
        clickBack: () {
          Navigator.maybePop(context);
        },
      ),
      body: BlocConsumer<TariffBloc, ITariffState>(
        listener: (context, state) {
          if (state is TariffErrorState) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is TariffLoadingState) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (state is TariffSuccessState) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 32,
                  ),
                  Text(
                    const I18n().valores_cobrados_servicos,
                    style: theme.textTheme.bodyMedium,
                  ),
                  const SizedBox(
                    height: 43,
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    itemCount: state.tariffs.length,
                    itemBuilder: (context, index) {
                      final tariffSection = state.tariffs[index];

                      if (tariffSection.tariffs.isEmpty) {
                        return const SizedBox();
                      }
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tariffSection.title,
                            style: theme.textTheme.bodyLarge,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          ListView.separated(
                            shrinkWrap: true,
                            itemCount: tariffSection.tariffs.length,
                            itemBuilder: (context, index) {
                              final tariff = tariffSection.tariffs[index];
                              return Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Text(
                                          tariff.title,
                                          style: theme.textTheme.bodyMedium,
                                          maxLines: 2,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 24,
                                      ),
                                      Text(
                                        formatStringMoney(tariff.value),
                                        style: theme.textTheme.bodyLarge,
                                      ),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        tariff.observation,
                                        style: theme.textTheme.labelSmall,
                                      ),
                                      Text(
                                        tariff.observationValue,
                                        style: theme.textTheme.labelSmall,
                                      ),
                                    ],
                                  )
                                ],
                              );
                            },
                            separatorBuilder: (context, index) {
                              return const SizedBox(
                                height: 8,
                              );
                            },
                          ),
                          const SizedBox(
                            height: 32,
                          ),
                          Divider(
                            color: ColorsApp.cinza[400],
                            thickness: .7,
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }
}

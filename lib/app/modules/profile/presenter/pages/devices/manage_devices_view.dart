import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import 'package:siclosbank/app/modules/pin/presenter/view/check_pin_page.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/manage_devices/manage_devices_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/manage_devices/manage_devices_event.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/manage_devices/manage_devices_state.dart';
import 'package:siclosbank/app/modules/profile/presenter/components/devices/item_device.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../../shared/presenter/view/components/others/snack_bar_app.dart';

class ManageDevicesProvider extends StatelessWidget {
  const ManageDevicesProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ManageDevicesBloc>(
      create: (context) => Modular.get<ManageDevicesBloc>(),
      child: const ManageDevicesView(),
    );
  }
}

class ManageDevicesView extends StatefulWidget {
  const ManageDevicesView({super.key});

  @override
  createState() => _ManageDevicesViewState();
}

class _ManageDevicesViewState extends State<ManageDevicesView> {
  String? pin;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<ManageDevicesBloc>(context).add(GetDevicesEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.dispositivos.toUpperCase(),
        clickBack: () {
          Navigator.maybePop(context);
        },
      ),
      body: BlocListener<ManageDevicesBloc, ManageDevicesState>(
        listener: (context, state) async {
          if (state.error != null) {
            DialogUtils.showSnackError(context, state.error!);
          }

          if (state.deviceIdToRemove!.isNotEmpty) {
            final result = await push(Routes.codeSendEmail, args: state);

            if (result == true) {
              SnackBarApp.showSnack(
                // ignore: use_build_context_synchronously
                context: context,
                success: true,
                message: I18n.of(context)!.dispositivo_removido_sucesso,
              );
              BlocProvider.of<ManageDevicesBloc>(context)
                  .add(GetDevicesEvent());
            }
          }
        },
        child: BlocBuilder<ManageDevicesBloc, ManageDevicesState>(
          builder: (context, state) {
            if (state.isProgress) {
              return _buildProgress();
            } else {
              return _buildBody(list: state.listDevices);
            }
          },
        ),
      ),
    );
  }

  _buildBody({required List<Devices> list}) {
    var textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: EdgeInsetsResponsive.only(top: 32, left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Text(
            I18n.of(context)!.dispositivos_autorizados,
            style: textTheme.bodyMedium,
          ),
          const SizedBox(height: 32),
          Text(
            I18n.of(context)!.dispositivos,
            style: textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: list.length,
              itemBuilder: (BuildContext context, int index) {
                final device = list[index];
                return ItemDevice(
                  devices: device,
                  clickDelete: _clickDeleteItem,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _buildProgress() {
    return Center(
      child: Utils.circularProgressButton(),
    );
  }

  _clickDeleteItem(Devices device) async {
    await SheetAlertConfirm.showSheet(
      context,
      title: I18n.of(context)!.deseja_remover_dispositivo,
      message: I18n.of(context)!.deseja_remover_dispositivo_msg,
      textNegative: I18n.of(context)!.cancelar,
      textPositive: I18n.of(context)!.remover,
      clickPositive: () async {
        Navigator.pop(context);
        await CheckPinPage.showSheet(context, () {
          BlocProvider.of<ManageDevicesBloc>(context).add(RemoveDeviceEvent(
            device: device,
          ));
        });
      },
    );
  }
}

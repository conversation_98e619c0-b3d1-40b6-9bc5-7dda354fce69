import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/profile_digital/profile_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/components/item_menu_round.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/config/flavor.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/bloc/terms/enums/type_terms.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../shared/utils/auth/local_auth.dart';

class PerfilHomeProvider extends StatelessWidget {
  const PerfilHomeProvider(
      {super.key, this.showSheetInformeRendimentos, this.changeFingerprint});

  final bool? showSheetInformeRendimentos;
  final bool? changeFingerprint;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ProfileBloc>(
      create: (context) {
        return Modular.get<ProfileBloc>();
      },
      child: _PerfilHomeView(
        showSheetInformeRendimentos: showSheetInformeRendimentos,
      ),
    );
  }
}

class _PerfilHomeView extends StatefulWidget {
  const _PerfilHomeView(
      {super.key, this.showSheetInformeRendimentos, this.changeFingerprint});
  final bool? showSheetInformeRendimentos;
  final bool? changeFingerprint;

  @override
  State<_PerfilHomeView> createState() => __PerfilHomeViewState();
}

class __PerfilHomeViewState extends State<_PerfilHomeView> {
  bool showButtonLoginDigital = true;
  bool isHabilitadoLoginDigital = true;
  final localAuthUtils = LocalAuthUtils();
  bool? get showSheetInformeRendimentos => widget.showSheetInformeRendimentos;
  bool? get changeFingerprint => widget.changeFingerprint;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _checkLoginDigital(context);
      if (showSheetInformeRendimentos == true) {
        _showSheetInformeRendimentos();
      }

      if (changeFingerprint == true && showButtonLoginDigital) {
        _clickLoginDigital(context);
      }
    });
  }

  @override
  void did() {
    _checkLoginDigital(context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        showBack: false,
        title: I18n.of(context)!.perfil.toUpperCase(),
      ),
      body: _buildBody(),
    );
  }

  _buildProgress() {
    return ContainerResponsive(
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  _buildBody() {
    return BlocConsumer<ProfileBloc, IProfileState>(
      listener: (context, state) {
        // if (state != null) {
        //   DialogUtils.showSnackError(context, state.error!);
        // }

        // if (state.envioInfRendSuccess) {
        //   DialogUtils.showSnackSuccess(
        //       context, 'Informe de rendimentos enviado');
        // }

        if (state is SuccessLogoutState) {
          navigate(Routes.intro);
        }

        // if (state.checkLoginDigital) {
        //   _checkLoginDigital();
        // }
      },
      builder: (context, state) {
        if (state is InitalLoginDigitalState) {
          final current = state;
          isHabilitadoLoginDigital = current.isEnabledLoginDigital;
          return ScrollConfiguration(
            behavior: MyBehavior(),
            child: CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        const SizedBox(height: 27),
                        ItemMenuRound(
                          title: I18n.of(context)!.ajustes,
                          onClick: () {
                            push(Routes.adjustmentsPage);
                          },
                        ),
                        ItemMenuRound(
                          title: I18n.of(context)!.tarifas,
                          onClick: () {
                            //AppSession.getInstance().tarifas == null
                            push(Routes.tariffsPage);
                          },
                        ),
                        ItemMenuRound(
                          title: I18n.of(context)!.dispositivos,
                          onClick: () {
                            push(Routes.devicesPage);
                          },
                        ),
                        Visibility(
                          visible: showButtonLoginDigital,
                          child: Builder(
                            builder: (contextApp) {
                              return ContainerResponsive(
                                child: ItemMenuRound(
                                  title: isHabilitadoLoginDigital
                                      ? AppSession.getInstance().useFaceID
                                          ? I18n.of(context)!
                                              .desabilitar_login_digital_menu_face_id
                                          : I18n.of(context)!
                                              .desabilitar_login_digital_menu
                                      : AppSession.getInstance().useFaceID
                                          ? I18n.of(context)!
                                              .habilitar_login_com_digital_menu_face_id
                                          : I18n.of(context)!
                                              .habilitar_login_com_digital_menu,
                                  onClick: () {
                                    _clickLoginDigital(contextApp);
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                        // Visibility(
                        //   // AppSession.getInstance().usuario?.isContaCorrente
                        //   visible: true ?? false,
                        //   child: ItemMenuRound(
                        //     //I18n.of(context)!.open_finance
                        //     title: '',
                        //     isBottom: true,
                        //     onClick: () {
                        //       // pushSlide(context, OpenFinancePage());
                        //     },
                        //   ),
                        // ),
                        Visibility(
                          visible: isHomologOrDev,
                          child: ItemMenuRound(
                            title: I18n.of(context)!.informe_rendimentos,
                            onClick: _showSheetInformeRendimentos,
                          ),
                        ),
                        ItemMenuRound(
                          title: I18n.of(context)!.termos_uso,
                          onClick: () {
                            push('/terms', args: TyperTerms.TERMOS_DE_USO);
                          },
                        ),
                        ItemMenuRound(
                          title: I18n.of(context)!.politica_privacidade,
                          onClick: () {
                            push('/terms',
                                args: TyperTerms.POLITICA_PRIVACIDADE);
                          },
                        ),
                        ItemMenuRound(
                          title: I18n.of(context)!.ajuda,
                          isBottom: true,
                          onClick: () {
                            push(Routes.help);
                          },
                        ),
                        InkWell(
                          onTap: _clickSair,
                          child: Container(
                            margin: const EdgeInsets.only(left: 16, top: 16),
                            padding: const EdgeInsets.only(top: 16, bottom: 16),
                            child: Text(
                              I18n.of(context)!.sair_conta,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(color: ColorsApp.error[300]),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  fillOverscroll: false,
                  child: Container(
                    padding: EdgeInsetsResponsive.all(16),
                    alignment: Alignment.bottomCenter,
                    child: Text(
                      '${AppSession.getInstance().versaoApp} ${AppSession.getInstance().numBuildApp}',
                      style: TextStyle(color: ColorsApp.cinza[500]),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }

  _clickSair() {
    SheetAlertConfirm.showSheet(
      context,
      title: I18n.of(context)!.sair,
      message: I18n.of(context)!.certeza_sair,
      textPositive: I18n.of(context)!.sim,
      textNegative: I18n.of(context)!.cancelar,
      clickPositive: () {
        BlocProvider.of<ProfileBloc>(context).add(LogoutEvent());
      },
    );
  }

// TDDO: Melhorar essa funcao e preparar integracao
  _showSheetInformeRendimentos() {
    SheetAlertConfirm.showSheet(
      context,
      title: I18n.of(context)!.informe_rendimentos,
      // I18n.of(context)!
      //     .msgInformeRendimentos(AppSession.getInstance().usuario!.email),
      message: "",
      textPositive: I18n.of(context)!.confirmar,
      textNegative: I18n.of(context)!.cancelar,
      clickPositive: () {
        // BlocProvider.of<PerfilHomeBloc>(context)
        //     .add(EnviarInformeRendimentos());
      },
    );
  }

  _checkLoginDigital(context) async {
    if (await LocalAuthUtils.canCheck()) {
      BlocProvider.of<ProfileBloc>(context).add(GetLoginDigitalEvent());
      final state = BlocProvider.of<ProfileBloc>(context).state
          as InitalLoginDigitalState;

      bool isHabilitado = state.isEnabledLoginDigital;
      // setState(() {
      //   isHabilitadoLoginDigital = isHabilitado;
      // });
    } else {
      setState(() {
        showButtonLoginDigital = false;
      });
    }
  }

  _clickLoginDigital(BuildContext context) {
    if (isHabilitadoLoginDigital) {
      _showSheetDesabilitarDigital();
    } else {
      SheetAlertConfirm.showSheet(
        context,
        title: AppSession.getInstance().useFaceID
            ? I18n.of(context)!.habilitar_login_face_id
            : I18n.of(context)!.habilitar_login_digital,
        message: AppSession.getInstance().useFaceID
            ? I18n.of(context)!.login_face_id_seguro
            : I18n.of(context)!.login_digital_seguro,
        textPositive: AppSession.getInstance().useFaceID
            ? I18n.of(context)!.habilitar_login_face_id_btn
            : I18n.of(context)!.habilitar_login_digital_btn,
        textNegative: I18n.of(context)!.cancelar,
        clickPositive: () async {
          var result = await localAuthUtils.auth(
              isCheck: false,
              showAlert: (term) {
                SnackBarApp.showSnack(
                  context: context,
                  message: term,
                  info: true,
                  success: false,
                );
              });
          if (result != null && result) {
            BlocProvider.of<ProfileBloc>(context).add(SetLoginDigitalEvent());
            // setState(() {
            //   isHabilitadoLoginDigital = !isHabilitadoLoginDigital;
            // });
            // _checkLoginDigital();
            // SnackBarApp.showSnack(
            //   context: context,
            //   message: AppSession.getInstance().useFaceID
            //       ? I18n.of(context)!.msg_habilitar_sucesso_face_id
            //       : I18n.of(context)!.msg_habilitar_sucesso,
            //   success: true,
            // );
          }
        },
      );
    }
  }

  _showSheetDesabilitarDigital() {
    SheetAlertConfirm.showSheet(
      context,
      title: AppSession.getInstance().useFaceID
          ? I18n.of(context)!.desabilitar_login_digital_face_id
          : I18n.of(context)!.desabilitar_login_digital,
      message: AppSession.getInstance().useFaceID
          ? I18n.of(context)!.desabilitar_login_digital_msg_face_id
          : I18n.of(context)!.desabilitar_login_digital_msg,
      textPositive: I18n.of(context)!.desabilitar_digital,
      clickPositive: () {
        BlocProvider.of<ProfileBloc>(context).add(CleanLoginDigitalEvent());
        // setState(() {
        //   isHabilitadoLoginDigital = !isHabilitadoLoginDigital;
        // });
      },
      textNegative: I18n.of(context)!.cancelar,
    );
  }
}

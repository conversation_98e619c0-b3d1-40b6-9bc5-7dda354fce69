import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/password/change_password_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/validator_password.dart';
import 'package:siclosbank/app/shared/utils/validator_utils.dart';

import '../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../shared/utils/utils.dart';
import '../../../../pin/presenter/view/check_pin_page.dart';

class ChangePasswordViewProvider extends StatelessWidget {
  const ChangePasswordViewProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ChangePasswordBloc>(
      create: (context) => Modular.get<ChangePasswordBloc>(),
      child: const ChangePasswordView(),
    );
  }
}

class ChangePasswordView extends StatefulWidget {
  const ChangePasswordView({super.key});

  @override
  State<ChangePasswordView> createState() => _ChangePasswordViewState();
}

class _ChangePasswordViewState extends State<ChangePasswordView> {
  final _formKey = GlobalKey<FormState>();
  bool isBtnEnabled = false;

  final TextEditingController _passwordCurrentController =
      TextEditingController(text: '');
  final TextEditingController _passwordNewController =
      TextEditingController(text: '');
  final TextEditingController _passwordConfirmController =
      TextEditingController(text: '');

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.alterar_senha.toUpperCase(),
        clickBack: () {
          Navigator.maybePop(context);
        },
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
          ),
          child: BlocConsumer<ChangePasswordBloc, IChangePasswordState>(
            listener: (context, state) {
              if (state is ChangePasswordErrorState) {
                SnackBarApp.showSnack(
                  context: context,
                  message: state.error.message ?? '',
                  success: false,
                );
              }
              if (state is ChangePasswordSucessState) {
                Navigator.pop(context);

                SnackBarApp.showSnack(
                    context: context,
                    message: I18n().senha_alterada_sucesso,
                    success: true);
              }
            },
            builder: (context, state) {
              final isLoading = state is ChangePasswordLoadingState;
              return CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          const SizedBox(
                            height: 38,
                          ),
                          Text(
                            const I18n().confirme_sua_senha_atual,
                            style: theme.textTheme.bodyMedium,
                          ),
                          const SizedBox(
                            height: 54,
                          ),
                          TextFormFieldApp(
                            controller: _passwordCurrentController,
                            label: I18n.of(context)!.senha_atual,
                            enable: !isLoading,
                            validator: (text) {
                              return ValidatorUtils.validateSenha(senha: text);
                            },
                            typePassword: true,
                            highLight: true,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          TextFormFieldApp(
                            controller: _passwordNewController,
                            label: I18n.of(context)!.nova_senha,
                            enable: !isLoading,
                            typePassword: true,
                            highLight: true,
                            validator: (text) {
                              final validatorPassword =
                                  ValidatorUtils.validateSenha(senha: text);
                              return validatorPassword;
                            },
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          TextFormFieldApp(
                            controller: _passwordConfirmController,
                            label: I18n.of(context)!.confirme_nova_senha,
                            enable: !isLoading,
                            validator: (text) {
                              final validatorPassword =
                                  ValidatorUtils.validateSenha(senha: text);
                              if (text != _passwordNewController.text) {
                                isBtnEnabled = false;
                                return "As senhas precisam ser iguais";
                              }
                              return validatorPassword;
                            },
                            typePassword: true,
                            highLight: true,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          ValidatorPasswordWidget(
                            passwordController: _passwordNewController,
                            onPasswordOk: (bool status) {
                              isBtnEnabled = status;
                              setState(() {});
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  SliverFillRemaining(
                    hasScrollBody: false,
                    fillOverscroll: false,
                    child: Container(
                        padding: EdgeInsets.only(top: 32, bottom: 32),
                        alignment: Alignment.bottomCenter,
                        width: size.width,
                        child: ButtonApp(
                          progress: isLoading
                              ? Utils.circularProgressButton(size: 20)
                              : null,
                          border: 1,
                          width: size.width,
                          text: I18n.of(context)!.alterar_senha,
                          enabled: isBtnEnabled && !isLoading,
                          onPress: () async {
                            if (_formKey.currentState!.validate()) {
                              await CheckPinPage.showSheet(
                                context,
                                () {
                                  BlocProvider.of<ChangePasswordBloc>(context)
                                      .add(
                                    CreateChangePasswordEvent(
                                      oldPassword:
                                          _passwordCurrentController.text,
                                      newPassword:
                                          _passwordConfirmController.text,
                                    ),
                                  );
                                },
                              );
                            }
                          },
                        )),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

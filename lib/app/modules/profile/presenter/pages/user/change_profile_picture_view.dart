import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/picture/change_profile_picture_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/picture/change_profile_picture_event.dart';

import 'package:siclosbank/app/modules/profile/presenter/bloc/user/picture/change_profile_picture_state.dart';
import 'package:siclosbank/app/modules/profile/presenter/components/camera_app_view.dart';
import 'package:siclosbank/app/modules/transaction/presenter/widget/image_avatar.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/button_negative_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/sheet_camera_gallery.dart';

import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class ChangeProfilePictureProvider extends StatelessWidget {
  const ChangeProfilePictureProvider({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ChangeProfilePictureBloc>(
      create: (context) => Modular.get(),
      child: const _ChangeProfilePictureView(),
    );
  }
}

class _ChangeProfilePictureView extends StatefulWidget {
  const _ChangeProfilePictureView();

  @override
  State<_ChangeProfilePictureView> createState() =>
      __ChangeProfilePictureState();
}

class __ChangeProfilePictureState extends State<_ChangeProfilePictureView> {
  final user = AppSession.getInstance().user;
  File? _imagePath;
  final imageCropper = ImageCropper();
  bool _isProcessingImage = false;
  bool get isProcessingImage => _isProcessingImage;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<ChangeProfilePictureBloc>(context)
        .add(GetImageProfileInitialEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.alterar_imagem_perfil.toUpperCase(),
        clickBack: () {
          Navigator.maybePop(context);
        },
      ),
      body: BlocConsumer<ChangeProfilePictureBloc, IChangeProfilePictureState>(
        listener: (context, state) async {
          if (state is ChangeProfilePictureBinaryDataState) {
            setState(() {
              _imagePath = state.imageData;
            });
          }
          if (state is ChangeProfilePictureErrorState) {
            final err = state;
            DialogUtils.showSnackError(context, Exception(err.message));
          }
          if (state is ChangeProfilePictureSucessState) {
            Navigator.pop(context);

            SnackBarApp.showSnack(
                context: context,
                message: const I18n().imagem_alterada_sucesso,
                success: true);
          }
        },
        builder: (context, state) {
          return _body(state);
        },
      ),
    );
  }

  _body(IChangeProfilePictureState state) {
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 24,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      I18n.of(context)!.selecione_imagem,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ImageAvatar(
                    size: 200,
                    isQuadrada: true,
                    showEditar: false,
                    nomeUsuario: user?.name,
                    urlImage: _imagePath?.path,
                    fileImage: _imagePath,
                  ),
                  const SizedBox(height: 20),
                  if (_isProcessingImage)
                    const SizedBox(
                      height: 48,
                      width: 200,
                      child: Center(child: CircularProgressIndicator()),
                    )
                  else
                    ButtonNegativeApp(
                      width: 200,
                      text: I18n.of(context)!.editar_imagem,
                      onPress: _clickEditImage,
                    ),
                ],
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: false,
            child: Container(
              padding: const EdgeInsets.only(
                  left: 12, right: 12, bottom: 16, top: 16),
              alignment: Alignment.bottomCenter,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  ButtonApp(
                    text: I18n.of(context)!.finalizar,
                    progress: state is ChangeProfilePictureLoadingState
                        ? Utils.circularProgressButton(size: 20)
                        : null,
                    enabled: _imagePath != null && !_isProcessingImage,
                    onPress: () {
                      if (_isProcessingImage) return;
                      BlocProvider.of<ChangeProfilePictureBloc>(context).add(
                          CreateChangeProfilePictureEvent(
                              newPicture: _imagePath!));
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _clickEditImage() {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            Navigator.pop(context);
          },
          child: SheetCameraGallery(
            onClickGallery: _openGallery,
            onClickCamera: _openCamera,
          ),
        );
      },
    );
  }

  _openCamera() async {
    setState(() {
      _isProcessingImage = true;
    });
    try {
      String? imagePath = await pushSlideResult(
          context,
          const CameraAppView(
            typeCamera: TypeCameraApp.SELFIE,
          ));
      if (imagePath != null) {
        await _openCrop(imagePath);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingImage = false;
        });
      }
    }
  }

  _openGallery() async {
    // Navigator.pop(context);
    setState(() {
      _isProcessingImage = true;
    });
    try {
      var file = await Utils.getImageGallery();
      if (file != null) {
        await _openCrop(file.path);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingImage = false;
        });
      }
    }
  }

  _openCrop(String? imgUrl) async {
    try {
      if (imgUrl == null || imgUrl.isEmpty) return;

      final androidSettings = AndroidUiSettings(
        toolbarTitle: I18n.of(context)!.ajuste_foto,
        toolbarColor: Colors.white,
        toolbarWidgetColor: ColorsApp.cinza[900],
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: true,
      );

      final iosUiSettings = IOSUiSettings(
        title: I18n.of(context)!.ajuste_foto,
        aspectRatioLockEnabled: true,
        resetAspectRatioEnabled: false,
        rotateButtonsHidden: true,
        rotateClockwiseButtonHidden: true,
        doneButtonTitle: I18n.of(context)!.finalizar,
        cancelButtonTitle: I18n.of(context)!.cancelar,
        aspectRatioPresets: [
          CropAspectRatioPreset.square,
        ],
        resetButtonHidden: true,
        minimumAspectRatio: 1.0,
      );

      final croppedFile = await imageCropper.cropImage(
        sourcePath: imgUrl,
        uiSettings: [androidSettings, iosUiSettings],
        compressQuality: 70,
        maxWidth: 500,
        maxHeight: 500,
        compressFormat: ImageCompressFormat.jpg,
      );

      if (!mounted) return;

      if (croppedFile != null) {
        final compressedFile = await _compressImage(File(croppedFile.path));
        setState(() {
          _imagePath = compressedFile;
        });
      }
    } catch (e) {
      print("Error cropping image: $e");
      if (mounted) {
        DialogUtils.showSnackError(
            context, Exception("Erro ao recortar imagem. Tente novamente."));
      }
    }
  }

  Future<File> _compressImage(File file) async {
    final bytes = await file.readAsBytes();
    final compressedBytes = await FlutterImageCompress.compressWithList(
      bytes,
      minHeight: 500,
      minWidth: 500,
      quality: 70,
    );

    final tempDir = await getTemporaryDirectory();
    final tempPath = tempDir.path;
    final targetPath = '$tempPath/${DateTime.now().millisecondsSinceEpoch}.jpg';

    final compressedFile = File(targetPath);
    await compressedFile.writeAsBytes(compressedBytes);

    return compressedFile;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/address/change_address_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:flutter_modular/flutter_modular.dart';

class ChangeAddressViewProvider extends StatelessWidget {
  const ChangeAddressViewProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ChangeAddressBloc>(
      create: (context) => Modular.get<ChangeAddressBloc>(),
      child: const _ChangeAddressView(),
    );
  }
}

class _ChangeAddressView extends StatefulWidget {
  const _ChangeAddressView();

  @override
  State<_ChangeAddressView> createState() => _ChangeAddressViewState();
}

class _ChangeAddressViewState extends State<_ChangeAddressView> {
  User? get user => AppSession.getInstance().user;

  final _zipCodeController = MaskedTextController(mask: '00000-000');
  final _streetController = TextEditingController();
  final _numberController = TextEditingController();
  final _complementController = TextEditingController();
  final _districtController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();

  bool showAlert = false;

  bool _isFieldEmpty(String? value) {
    return value == null || value.isEmpty;
  }

  @override
  void initState() {
    super.initState();
    BlocProvider.of<ChangeAddressBloc>(context)
        .add(GetAddressEvent(userId: user!.id!));

    _zipCodeController.text = '';
    _streetController.text = '';
    _numberController.text = '';
    _complementController.text = '';
    _districtController.text = '';
    _cityController.text = '';
    _stateController.text = '';
  }

  @override
  void dispose() {
    _zipCodeController.dispose();
    _streetController.dispose();
    _numberController.dispose();
    _complementController.dispose();
    _districtController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        setState(() {
          showAlert = false;
        });
      },
      child: Scaffold(
        appBar: AppBarApp(
          title: const I18n().alterar_endereco.toUpperCase(),
          clickBack: () {
            Navigator.maybePop(context);
          },
        ),
        body: BlocListener<ChangeAddressBloc, ChangeAddressState>(
          listener: (context, state) {
            if (state is ChangeAddressLoaded) {
              _initTexts(state);
            }
          },
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Text(const I18n().endereco,
                      style: Theme.of(context).textTheme.bodyMedium),
                  const SizedBox(height: 28),
                  // ZIP CODE
                  TextFormFieldApp(
                    label: const I18n().cep,
                    controller: _zipCodeController,
                    enable: false,
                    onTap: () {
                      if (_isFieldEmpty(_zipCodeController.text)) {
                        setState(() {
                          showAlert = true;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: TextFormFieldApp(
                          label: const I18n().rua_avenida,
                          controller: _streetController,
                          enable: false,
                          onTap: () {
                            if (_isFieldEmpty(_streetController.text)) {
                              setState(() {
                                showAlert = true;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: TextFormFieldApp(
                          label: const I18n().numero_abreviado,
                          controller: _numberController,
                          enable: false,
                          onTap: () {
                            if (_isFieldEmpty(_numberController.text)) {
                              setState(() {
                                showAlert = true;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // DISTRICT
                  TextFormFieldApp(
                    label: const I18n().bairro,
                    controller: _districtController,
                    enable: false,
                    onTap: () {
                      if (_isFieldEmpty(_districtController.text)) {
                        setState(() {
                          showAlert = true;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  // COMPLEMENT
                  GestureDetector(
                    onTap: () {
                      if (_isFieldEmpty(_complementController.text)) {
                        setState(() {
                          showAlert = true;
                        });
                      }
                    },
                    child: AbsorbPointer(
                      child: TextFormFieldApp(
                        label: const I18n().complemento,
                        controller: _complementController,
                        enable: false,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // STATE AND CITY
                  Row(
                    children: [
                      Expanded(
                        child: TextFormFieldApp(
                          label: const I18n().estado,
                          controller: _stateController,
                          enable: false,
                          onTap: () {
                            if (_isFieldEmpty(_stateController.text)) {
                              setState(() {
                                showAlert = true;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormFieldApp(
                          label: const I18n().cidade,
                          controller: _cityController,
                          enable: false,
                          onTap: () {
                            if (_isFieldEmpty(_cityController.text)) {
                              setState(() {
                                showAlert = true;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),
                  // INFO BANNER
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.info_outline, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            I18n.of(context)!.warning_change_address,
                            style: TextStyle(color: Colors.blue.shade900),
                          ),
                        ),
                      ],
                    ),
                  ),
                  AlertBanner(
                    isShow: showAlert,
                    typeAlertBanner: TypeAlertBanner.error,
                    message: I18n.of(context)!.alert_change_address,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _initTexts(ChangeAddressState state) {
    if (state is ChangeAddressLoaded) {
      _zipCodeController.text = state.address.cep ?? '';
      _streetController.text = state.address.street ?? '';
      _numberController.text = state.address.number ?? '';
      _complementController.text = state.address.complement ?? '';
      _districtController.text = state.address.district ?? '';
      _cityController.text = state.address.city ?? '';
      _stateController.text = state.address.state ?? '';
    }
  }
}

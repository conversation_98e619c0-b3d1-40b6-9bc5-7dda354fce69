import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/displayName/change_display_name_bloc.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/displayName/change_display_name_state.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class ChangeDisplayNameViewProvider extends StatelessWidget {
  const ChangeDisplayNameViewProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ChangeDisplayNameBloc>(
        create: (context) => Modular.get<ChangeDisplayNameBloc>(),
        child: const ChangeDisplayNameView());
  }
}

class ChangeDisplayNameView extends StatefulWidget {
  const ChangeDisplayNameView({super.key});

  @override
  State<ChangeDisplayNameView> createState() => _ChangeDisplayNameViewState();
}

class _ChangeDisplayNameViewState extends State<ChangeDisplayNameView> {
  final _formKey = GlobalKey<FormState>();
  bool _enableButton = false;

  late TextEditingController _newSocialNameCurrentController;

  @override
  void initState() {
    super.initState();
    final user = AppSession.getInstance().user;

    final name = user?.socialName != null && user!.socialName!.isNotEmpty
        ? user.socialName!
        : user?.name ?? '_';

    _newSocialNameCurrentController = TextEditingController(text: name);
    _newSocialNameCurrentController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _newSocialNameCurrentController.removeListener(_validateForm);
    _newSocialNameCurrentController.dispose();
    super.dispose();
  }

  void _validateForm() {
    if (!mounted) return;
    setState(() {
      final newName = _newSocialNameCurrentController.text.trim();
      final currentName = AppSession.getInstance().user?.socialName ??
          AppSession.getInstance().user?.name ??
          '_';
      _enableButton = newName.isNotEmpty && newName != currentName;
    });
  }

  String _capitalizeWords(String text) {
    if (text.isEmpty) return text;

    final prepositions = ['de', 'da', 'do', 'das', 'dos', 'e'];

    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      final wordLower = word.toLowerCase();
      if (prepositions.contains(wordLower)) {
        return wordLower;
      }
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.alterar_display_name.toUpperCase(),
        clickBack: () {
          Navigator.maybePop(context);
        },
      ),
      body: BlocConsumer<ChangeDisplayNameBloc, ChangeDisplayNameState>(
        listener: (context, state) {
          if (state is ChangeDisplayNameError) {
            SnackBarApp.showSnack(
              context: context,
              message: state.message,
              success: false,
            );
          } else if (state is ChangeDisplayNameSuccess) {
            SnackBarApp.showSnack(
              context: context,
              message: state.message,
              success: true,
            );
          }
        },
        builder: (context, state) {
          final loading = state is ChangeDisplayNameLoading;
          final enableButton = state is! ChangeDisplayNameLoading;
          return _buildBody(loading, enableButton);
        },
      ),
    );
  }

  Widget _buildBody(bool loading, bool enableButton) {
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  TextResponsive(
                    I18n.of(context)!.digite_nome_exibido_alterar,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  SizedBoxResponsive(height: 52),
                  Form(
                    key: _formKey,
                    child: TextFormFieldApp(
                      label: I18n.of(context)!.nome_exibido,
                      controller: _newSocialNameCurrentController,
                      textInputType: TextInputType.text,
                      textInputAction: TextInputAction.go,
                      onFieldSubmitted: (String text) {},
                      formatter:
                          TextInputFormatter.withFunction((oldValue, newValue) {
                        return TextEditingValue(
                          text: _capitalizeWords(newValue.text),
                          selection: newValue.selection,
                        );
                      }),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return I18n.of(context)!.campo_obrigatorio;
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: false,
            child: Container(
              alignment: Alignment.bottomCenter,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ButtonApp(
                    text: I18n.of(context)!.continuar,
                    enabled: !loading && _enableButton,
                    onPress: _clickChangeSocialName,
                    progress:
                        loading ? Utils.circularProgressButton(size: 20) : null,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _clickChangeSocialName() async {
    if (_formKey.currentState!.validate()) {
      BlocProvider.of<ChangeDisplayNameBloc>(context).add(
        ChangeDisplayNameSubmittedEvent(
          displayName: _newSocialNameCurrentController.text,
          id: AppSession.getInstance().user!.id!,
        ),
      );
    }
  }
}

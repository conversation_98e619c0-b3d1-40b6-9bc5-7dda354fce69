import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/profile/presenter/bloc/user/email/change_email_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';

import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/utils/validator_utils.dart';

import '../../../../pin/presenter/view/check_pin_page.dart';

class ChangeEmailViewProvider extends StatelessWidget {
  const ChangeEmailViewProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ChangeEmailBloc>(
      create: (context) => Modular.get<ChangeEmailBloc>(),
      child: const ChangeEmailView(),
    );
  }
}

class ChangeEmailView extends StatefulWidget {
  const ChangeEmailView({super.key});

  @override
  State<ChangeEmailView> createState() => _ChangeEmailViewState();
}

class _ChangeEmailViewState extends State<ChangeEmailView> {
  final _formKey = GlobalKey<FormState>();
  final _formKeyPassword = GlobalKey<FormState>();
  bool isBtnEnabled = false;

  final TextEditingController _newEmailCurrentController =
      TextEditingController(text: AppSession.getInstance().user!.email);
  final TextEditingController _passwordCurrentController =
      TextEditingController(text: '');

  Widget getEmailFrom(
    Size size,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Expanded(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 38,
                ),
                Text(
                  const I18n().digite_email_alterar,
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(
                  height: 54,
                ),
                TextFormFieldApp(
                  controller: _newEmailCurrentController,
                  label: I18n.of(context)!.email,
                  validator: (text) {
                    return ValidatorUtils.validateEmail(context, text);
                  },
                  typePassword: false,
                  highLight: true,
                ),
                const SizedBox(
                  height: 16,
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          width: size.width,
          child: ButtonApp(
            text: I18n.of(context)!.continuar,
            enabled: true,
            onPress: () {
              if (_formKey.currentState!.validate()) {
                BlocProvider.of<ChangeEmailBloc>(context).add(
                    SetEmailEvent(newEmail: _newEmailCurrentController.text));
              }
            },
          ),
        ),
      ],
    );
  }

  Widget getPasswordFrom(
    Size size,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Expanded(
          child: Form(
            key: _formKeyPassword,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 38,
                ),
                Text(
                  const I18n().digite_sua_senha_confirmar_alteracao_email +
                      _newEmailCurrentController.text,
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(
                  height: 54,
                ),
                TextFormFieldApp(
                  controller: _passwordCurrentController,
                  label: I18n.of(context)!.senha_atual,
                  validator: (text) {
                    if (text == null || text.isEmpty) {
                      return I18n.of(context)!.senha_incorreta;
                    }
                    return null;
                    // return ValidatorUtils.validateSenha(senha: text);
                  },
                  typePassword: true,
                  highLight: true,
                ),
                const SizedBox(
                  height: 16,
                ),
              ],
            ),
          ),
        ),
        SizedBox(
            width: size.width,
            child: ButtonApp(
              border: 1,
              text: I18n.of(context)!.finalizar,
              enabled: true,
              onPress: () async {
                if (_formKeyPassword.currentState!.validate()) {
                  CheckPinPage.showSheet(
                    context,
                    () {
                      BlocProvider.of<ChangeEmailBloc>(context).add(
                        CreateChangeEmailEvent(
                          newEmail: _newEmailCurrentController.text,
                          password: _passwordCurrentController.text,
                        ),
                      );
                    },
                  );
                }
              },
            )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.alterar_email.toUpperCase(),
        clickBack: () {
          Navigator.maybePop(context);
        },
      ),
      body: SizedBox(
        height: size.height,
        child: Padding(
          padding:
              const EdgeInsets.only(left: 16, right: 16, bottom: 24, top: 0),
          child: BlocConsumer<ChangeEmailBloc, IChangeEmailState>(
            listener: (context, state) {
              if (state is ChangeEmailErrorState) {
                SnackBarApp.showSnack(
                  context: context,
                  message: state.error.message ?? '',
                  success: false,
                );
              }
              if (state is ChangeEmailSucessState) {
                Navigator.pop(context);
                AppSession.getInstance().user!.email =
                    _newEmailCurrentController.text;
                SnackBarApp.showSnack(
                    context: context,
                    message: const I18n().email_alterado_com_sucesso,
                    success: true);
              }
            },
            builder: (context, state) {
              if (state is ChangeEmailInitialState) {
                return getEmailFrom(
                  size,
                  theme,
                );
              }
              if (state is ChangeEmailLoadingState) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (state is ChangeEmailVerifyPasswordState) {
                return getPasswordFrom(size, theme);
              }

              if (state is ChangeEmailErrorState) {
                return getPasswordFrom(size, theme);
              }

              return const SizedBox();
            },
          ),
        ),
      ),
    );
  }
}

import 'dart:developer';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';

import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

enum TypeCameraApp { SELFIE, DOCUMENTO }

enum TypeDocumentoApp { CNH, RG, COMPROVANTE }

class CameraAppView extends StatefulWidget {
  const CameraAppView(
      {required this.typeCamera, this.tipoDocumento, this.lado, super.key});
  final TypeCameraApp typeCamera;
  final TypeDocumentoApp? tipoDocumento;
  final String? lado;

  @override
  _CameraAppViewState createState() => _CameraAppViewState();
}

class _CameraAppViewState extends State<CameraAppView>
    with WidgetsBindingObserver {
  CameraController? controller;
  List<CameraDescription>? cameras;
  final GlobalKey<ScaffoldMessengerState> _scaffoldKey =
      GlobalKey<ScaffoldMessengerState>();

  String _filePath = '';
  late bool cameraFrontal;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
      ),
    );

    cameraFrontal = (widget.typeCamera == TypeCameraApp.SELFIE);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initCamera();
    });
  }

  Future<void> _initCamera() async {
    try {
      cameras = await availableCameras();
      if (cameras!.isNotEmpty) {
        if (cameras!.length == 1) {
          controller = CameraController(
            cameras![0],
            ResolutionPreset.high,
            enableAudio: false,
          );
        } else {
          controller = CameraController(
            cameraFrontal ? cameras![1] : cameras![0],
            ResolutionPreset.high,
            enableAudio: false,
          );
        }
      }
      // controller.initialize().then((_) {
      //   if (!mounted) {
      //     return;
      //   }
      //   setState(() {});
      // });

      await controller?.initialize();
      setState(() {});
    } on CameraException catch (e) {
      // Se o usuário não der permissão irá gerar um exceção
      pop(context);
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Utils.setScreeenResponsive(
      context: context,
      height: size.height,
      width: size.width,
    );

    if (controller == null || !controller!.value.isInitialized) {
      return ContainerResponsive();
    }

    return Scaffold(
      body: _filePath.isEmpty
          ? _buildTirarFoto(size, context)
          : _buildMostrarFoto(size, context),
    );
  }

  SizedBox _buildTirarFoto(Size size, BuildContext context) {
    return SizedBoxResponsive(
      height: size.height,
      width: size.width,
      child: AspectRatio(
        aspectRatio: controller!.value.aspectRatio,
        child: Stack(children: <Widget>[
          CameraPreview(controller!),
          // if (widget.typeCamera == TypeCameraApp.SELFIE)
          _buildImagemBackground(size),
          _buildTopoCamera(context),
          _buildRodapeFoto(size, context)
        ]),
      ),
    );
  }

  SafeArea _buildTopoCamera(BuildContext context) {
    return SafeArea(
      child: Align(
        alignment: Alignment.topCenter,
        child: Column(
          children: <Widget>[
            Align(
              alignment: Alignment.topLeft,
              child: IconButton(
                padding: EdgeInsets.zero,
                icon: ImageUtils.icVoltarView(color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
            Visibility(
              visible: _filePath.isEmpty &&
                  widget.typeCamera == TypeCameraApp.DOCUMENTO,
              child: TextResponsive(
                _textoDicaTopoCamera(widget.tipoDocumento),
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.white,
                    ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _textoDicaTopoCamera(TypeDocumentoApp? tipoDocumento) {
    if (tipoDocumento == TypeDocumentoApp.CNH) {
      return const I18n().dica_posicionamento_cnh_topo(widget.lado ?? '');
    }

    if (tipoDocumento == TypeDocumentoApp.RG) {
      return const I18n().dica_posicionamento_rg_topo(widget.lado ?? '');
    }

    if (tipoDocumento == TypeDocumentoApp.COMPROVANTE) {
      return const I18n()
          .dica_posicionamento_comprovante_topo(widget.lado ?? '');
    }

    return '';
  }

  Align _buildRodapeFoto(Size size, BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: ContainerResponsive(
        padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
        height: widget.tipoDocumento == TypeDocumentoApp.RG ? 140 : 177
        // 215
        , //size.width * 0.5,
        alignment: Alignment.bottomCenter,
        child: Column(
          children: <Widget>[
            _buildDicaFotoRodape(
              size,
              context,
              _textoDicaRodapeCamera(widget.tipoDocumento, widget.typeCamera),
            ),
            const Spacer(),
            _buildBotaoTirarFoto()
          ],
        ),
      ),
    );
  }

  String _textoDicaRodapeCamera(
      TypeDocumentoApp? tipoDocumento, TypeCameraApp? tipoCamera) {
    if (tipoCamera == TypeCameraApp.SELFIE) {
      return const I18n().dica_posicionamento_auto_retrato;
    }

    if (tipoDocumento == TypeDocumentoApp.CNH) {
      return const I18n().dica_posicionamento_cnh(widget.lado ?? '');
    }

    if (tipoDocumento == TypeDocumentoApp.RG) {
      return const I18n().dica_posicionamento_rg(widget.lado ?? '');
    }

    if (tipoDocumento == TypeDocumentoApp.COMPROVANTE) {
      return const I18n().dica_posicionamento_comprovante;
    }

    return const I18n().dica_posicionamento_comprovante;
  }

  SizedBox _buildBotaoTirarFoto() {
    return SizedBoxResponsive(
      height: 65,
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          GestureDetector(
            onTap: () async {
              final filePath = await takePicture('auto_retrato');

              if (filePath != null) {
                setState(() {
                  _filePath = filePath;
                });
              }
            },
            child: ImageUtils.icTirarFoto(),
          ),
          GestureDetector(
            onTap: () {
              alternarCamera();
            },
            child: Align(
              alignment: Alignment.centerRight,
              child: ImageUtils.icAlternarCamera(),
            ),
          )
        ],
      ),
    );
  }

  Container _buildDicaFotoRodape(Size size, BuildContext context, String dica) {
    return ContainerResponsive(
      padding: const EdgeInsets.only(left: 8, right: 8),
      height: widget.typeCamera == TypeCameraApp.SELFIE ? 80 : 56,
      width: size.width,
      decoration: BoxDecoration(
        color: widget.typeCamera == TypeCameraApp.SELFIE
            ? Colors.black.withOpacity(0.8)
            : ColorsApp.cinza[100],
        borderRadius: const BorderRadius.all(
          Radius.circular(9),
        ),
      ),
      child: Center(
        child: TextResponsive(
          dica,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: widget.typeCamera == TypeCameraApp.SELFIE
                    ? Colors.white
                    : ColorsApp.cinza[800],
              ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Image _buildImagemBackground(Size size) {
    if (widget.typeCamera == TypeCameraApp.SELFIE) {
      return ImageUtils.imgBackgroundSelfie(
        width: size.width,
        height: size.height,
      );
    } else if (widget.tipoDocumento == TypeDocumentoApp.CNH) {
      return ImageUtils.imgBackgroundCnh(
        width: size.width,
        height: size.height,
      );
    } else {
      return ImageUtils.imgBackgroundRg(
        width: size.width,
        height: size.height,
      );
    }
  }

  Widget _buildMostrarFoto(Size size, BuildContext context) {
    return Stack(
      children: <Widget>[
        Image.file(
          File(_filePath),
          fit: BoxFit.cover,
        ),
        _buildTopoCamera(context),
        Align(
          alignment: Alignment.bottomCenter,
          child: ContainerResponsive(
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            height: 220,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                TextResponsive(
                  const I18n().titulo_validacao_auto_retrato,
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                SizedBoxResponsive(height: 16),
                ButtonApp(
                  height: 48,
                  width: size.width,
                  text: const I18n().utilizar_foto,
                  onPress: () {
                    Navigator.pop(context, _filePath);
                  },
                ),
                SizedBoxResponsive(
                  height: 8,
                ),
                ButtonApp(
                  height: 48,
                  width: size.width,
                  buttonColor: Colors.white,
                  border: 0,
                  text: const I18n().nao_utilizar_foto,
                  onPress: () {
                    File(_filePath).deleteSync();
                    setState(() {
                      _filePath = '';
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<String?> takePicture(String documento) async {
    if (!controller!.value.isInitialized) {
      showInSnackBar('Error: select a camera first.');
      return null;
    }
    final Directory extDir = await getApplicationDocumentsDirectory();
    final String dirPath = '${extDir.path}/Pictures/siclos';
    await Directory(dirPath).create(recursive: true);
    final String filePath = '$dirPath/${timestamp()}.jpg';

    if (controller!.value.isTakingPicture) {
      // A capture is already pending, do nothing.
      return null;
    }

    try {
      var xfile = await controller!.takePicture(); //(filePath);

      // log(json.encode(xfile));
      // xfile.
      log('filePath: $filePath');

      log('xfile.path: ${xfile.path}');
      await xfile.saveTo(filePath);

      return filePath;
    } on CameraException catch (e) {
      print(e);
      return null;
    }
    return filePath;
  }

  void showInSnackBar(String message) {
    _scaffoldKey.currentState!
        .showSnackBar(SnackBar(content: TextResponsive(message)));
  }

  String timestamp() => DateTime.now().millisecondsSinceEpoch.toString();

  Future<void> alternarCamera() async {
    // if (widget.typeCamera == TypeCameraApp.SELFIE) {
    //   widget.typeCamera = TypeCameraApp.DOCUMENTO;
    // } else {
    //   widget.typeCamera = TypeCameraApp.SELFIE;
    // }

    cameraFrontal = !cameraFrontal;

    _initCamera();
  }
}

import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/profile/models/devices.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class ItemDevice extends StatelessWidget {
  final Devices devices;
  final Function clickDelete;

  const ItemDevice({
    super.key,
    required this.clickDelete,
    required this.devices,
  });

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Container(
      // padding: EdgeInsetsResponsive.symmetric(vertical: 24),
      child: Column(
        children: <Widget>[
          Padding(
            padding: EdgeInsetsResponsive.symmetric(vertical: 18),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        devices.os,
                        style: textTheme.bodyMedium!
                            .copyWith(fontWeight: FontWeight.w400),
                      ),
                      Text(
                        Utils.getLastAccessDate(dataString: devices.updated_at),
                        style: textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, top: 16, bottom: 16),
                  onPressed: () {
                    clickDelete(devices);
                  },
                  icon: ImageUtils.icDelete(),
                ),
              ],
            ),
          ),
          Divider(
            height: 0.7,
            color: ColorsApp.cinza[400],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class ItemMenuRound extends StatelessWidget {
  final String title;
  final bool isBottom;
  final Function onClick;

  ItemMenuRound({
    Key? key,
    this.title = '',
    this.isBottom = false,
    required this.onClick,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return ContainerResponsive(
      height: 48,
      margin: const EdgeInsets.only(bottom: 2),
      child: TextButton(
        style: ButtonStyle(
          splashFactory: InkSplash.splashFactory,
          overlayColor: const WidgetStatePropertyAll(ColorsApp.verde),
          backgroundColor: const WidgetStatePropertyAll(Colors.white),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5),
            ),
          ),
        ),
        onPressed: () {
          if (onClick != null) onClick();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            ImageUtils.icArrowRight(),
          ],
        ),
        // color: Colors.white,
        // shape: isTop || isBottom
        //     ? RoundedRectangleBorder(
        //         borderRadius: isTop
        //             ? BorderRadius.only(
        //                 topLeft:
        //                     isBig ? Radius.circular(10) : Radius.circular(5),
        //                 topRight:
        //                     isBig ? Radius.circular(10) : Radius.circular(5),
        //               )
        //             : isBottom
        //                 ? BorderRadius.only(
        //                     bottomLeft: isBig
        //                         ? Radius.circular(10)
        //                         : Radius.circular(5),
        //                     bottomRight: isBig
        //                         ? Radius.circular(10)
        //                         : Radius.circular(5),
        //                   )
        //                 : BorderRadius.circular(5),
        //       )
        //     : null,
      ),
    );

    // return Container(
    //   margin: EdgeInsets.only(bottom: 2),
    //   child: Column(
    //     crossAxisAlignment: CrossAxisAlignment.stretch,
    //     children: <Widget>[
    //       InkWell(
    //         onTap: () {
    //           if (onClick != null) onClick();
    //         },
    //         child: Container(
    //           decoration: BoxDecoration(
    //             color: Colors.white,
    //             borderRadius: isTop
    //                 ? BorderRadius.only(
    //                     topLeft:
    //                         isBig ? Radius.circular(10) : Radius.circular(5),
    //                     topRight:
    //                         isBig ? Radius.circular(10) : Radius.circular(5),
    //                   )
    //                 : isBottom
    //                     ? BorderRadius.only(
    //                         bottomLeft: isBig
    //                             ? Radius.circular(10)
    //                             : Radius.circular(5),
    //                         bottomRight: isBig
    //                             ? Radius.circular(10)
    //                             : Radius.circular(5),
    //                       )
    //                     : null,
    //           ),
    //           padding: EdgeInsets.only(
    //               top: isBig ? 28 : 16,
    //               bottom: isBig ? 28 : 16,
    //               left: 16,
    //               right: 16),
    //           child: Row(
    //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //             children: <Widget>[
    //               Text(
    //                 title,
    //                 style: Theme.of(context).textTheme.bodyText2,
    //               ),
    //               ImageUtils.icArrowRight(),
    //             ],
    //           ),
    //         ),
    //       ),
    //     ],
    //   ),
    // );
  }
}

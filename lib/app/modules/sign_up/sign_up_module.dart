import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/address_usecase.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/documents_usecase.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/terms_usecase.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/basic_data/basic_data_bloc.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/check_collaborator/check_collaborator_bloc.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/register/register_bloc.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/terms/terms_bloc.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/register_pageview.dart';
import 'data/datasource/i_sign_up_datasource.dart';
import 'data/datasource/sign_up_datasource_impl.dart';
import 'data/repositories/sign_up_repository_impl.dart';
import 'domain/repositories/i_sign_up_repository.dart';
import 'domain/usecases/basic_data_usecase.dart';
import 'domain/usecases/check_is_collaborator_usecase.dart';
import 'domain/usecases/create_password_usecase.dart';
import 'domain/usecases/send_sms_usecase.dart';
import 'presenter/blocs/address_register/address_register_bloc.dart';
import 'presenter/blocs/create_password/create_password_bloc.dart';
import 'presenter/blocs/phone_check/phone_check_bloc.dart';
import 'presenter/pages/register/6-documents/webview_send_documents.dart';
import 'presenter/pages/errors/already_registered_page.dart';
import 'presenter/pages/errors/registration_collaborators_only_page.dart';
import '../../app_module.dart';
import '../../shared/navigation/named_routes.dart';

import 'presenter/blocs/register_documents/register_documents_bloc.dart';
import 'presenter/pages/check_cpf_page.dart';

class SignUpModule extends Module {
  @override
  void binds(Injector i) {
    // datasources
    i.addLazySingleton<ISignUpDatasource>(SignUpDatasourceImpl.new);

    // repositories
    i.addLazySingleton<ISignUpRepository>(SignUpRepositoryImpl.new);

    // usecases
    i.addLazySingleton<ICheckIsCollaboratorUsecase>(
        CheckIsCollaboratorUsecase.new);
    i.addLazySingleton<IBasicDataUsecase>(BasicDataUsecaseImpl.new);
    i.addLazySingleton<IAddressUsecase>(AddressUsecaseImpl.new);
    i.addLazySingleton<ISendSmsUsecase>(SendSmsUsecaseImpl.new);
    i.addLazySingleton<CreatePasswordUsecase>(CreatePasswordUsecaseImpl.new);
    i.addLazySingleton<ITermsRegisterUsecase>(TermsRegisterUsecaseImpl.new);
    i.addLazySingleton<IDocumentsUsecase>(DocumentsUsecase.new);

    // blocs
    i.add(RegisterBloc.new);
    i.add(CheckCollaboratorBloc.new);
    i.add(BasicDataBloc.new);
    i.add(AddressRegisterBloc.new);
    i.add(PhoneCheckBloc.new);
    i.add(CreatePasswordBloc.new);
    i.add(TermsBloc.new);
    i.add(RegisterDocumentsBloc.new);

    super.binds(i);
  }

  @override
  List<Module> get imports => [
        AppModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (_) => const CheckCpfPage());
    r.child('/register', child: (_) => const RegisterPageView());
    r.child(Routes.errorCollaboratorOnly,
        child: (_) => const RegistrationCollaboratorsOnlyPage());
    r.child(Routes.errorAlreadyRegistered,
        child: (_) => const AlreadyRegisteredPage());

    r.child(Routes.webviewDocuments,
        child: (_) => const WebviewSendDocuments());
  }
}

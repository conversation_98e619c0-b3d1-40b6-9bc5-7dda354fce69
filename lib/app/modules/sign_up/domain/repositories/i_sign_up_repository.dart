import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';

import '../../../../shared/data/models/register_user_response.dart';

abstract class ISignUpRepository {
  Future<RegisterUserResponse> checkIsCollaborator(String cpf);

  Future<RegisterUserResponse> createBasicData(
      BasicDataRequest basicDataRequest);
  Future<Map<String, dynamic>> getPublicyExposedPersonOptions();
  Future<List<dynamic>> getOccupationsOptions();
  Future<void> sendSms(String userId);
  Future checkSmsCode({required String userId, required String code});
  Future<void> createPassword(
      {required String userId, required String password});

  Future<AddressResponse> getAddress(String userId);

  Future<RegisterUserResponse> confirmAddress(String userId);

  Future<RegisterUserResponse> getTerms();
  Future<void> acceptTerms(String userId);
  Future<void> rejectTerms(String userId);

  Future<String?> getUrlSendDocuments(String userId);
}

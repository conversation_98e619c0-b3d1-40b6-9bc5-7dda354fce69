import '../repositories/i_sign_up_repository.dart';

abstract class IDocumentsUsecase {
  Future<String?> getUrlSendDocuments(String userId);
}

class DocumentsUsecase implements IDocumentsUsecase {
  final ISignUpRepository _repository;

  DocumentsUsecase(this._repository);

  @override
  Future<String?> getUrlSendDocuments(String userId) async {
    try {
      return await _repository.getUrlSendDocuments(userId);
    } catch (e) {
      rethrow;
    }
  }
}

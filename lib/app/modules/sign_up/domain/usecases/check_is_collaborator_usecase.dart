import '../../../../shared/data/models/register_user_response.dart';
import '../repositories/i_sign_up_repository.dart';

abstract class ICheckIsCollaboratorUsecase {
  Future<RegisterUserResponse> call(String cpf);
}

class CheckIsCollaboratorUsecase implements ICheckIsCollaboratorUsecase {
  final ISignUpRepository _repository;

  CheckIsCollaboratorUsecase(this._repository);

  @override
  Future<RegisterUserResponse> call(String cpf) async {
    try {
      final result = await _repository.checkIsCollaborator(cpf);
      return result;
    } catch (erro) {
      rethrow;
    }
  }
}

import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';

import '../../data/models/basic_data_request.dart';
import '../../../../shared/data/models/register_user_response.dart';
import '../models/occupation_response.dart';
import '../models/politically_exposed_person_response.dart';

abstract class IBasicDataUsecase {
  Future<RegisterUserResponse> createUser(BasicDataRequest basicDataRequest);

  Future<List<OccupationResponse>> getOccupationsOptions();

  Future<List<PoliticallyExposedPersonResponse>> getPepOptions();
}

class BasicDataUsecaseImpl implements IBasicDataUsecase {
  final ISignUpRepository _repository;

  BasicDataUsecaseImpl(this._repository);

  // Future create({required String userId, required String password}) async {
  //   try {
  //     await _repository.createPassword(userId: userId, password: password);
  //   } catch (e) {
  //     rethrow;
  //   }
  // }

  @override
  Future<List<OccupationResponse>> getOccupationsOptions() async {
    try {
      final result = await _repository.getOccupationsOptions();
      var list = result
          .map(
            (map) => OccupationResponse(
              key: map['id'],
              value: map['description'],
            ),
          )
          .toList();
      return list;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<PoliticallyExposedPersonResponse>> getPepOptions() async {
    try {
      final result = await _repository.getPublicyExposedPersonOptions();
      return result.entries
          .map(
            (e) => PoliticallyExposedPersonResponse(
              key: e.key,
              value: e.value,
            ),
          )
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RegisterUserResponse> createUser(
      BasicDataRequest basicDataRequest) async {
    try {
      final result = await _repository.createBasicData(basicDataRequest);
      return result;
    } catch (e) {
      rethrow;
    }
  }
}

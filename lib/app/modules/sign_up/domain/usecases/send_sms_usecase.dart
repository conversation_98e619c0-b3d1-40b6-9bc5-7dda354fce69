import '../../../../app_controller.dart';
import '../repositories/i_sign_up_repository.dart';

abstract class ISendSmsUsecase {
  Future call(String userId);

  Future<bool> checkSmsCode({required String userId, required String code});
}

class SendSmsUsecaseImpl implements ISendSmsUsecase {
  final ISignUpRepository _repository;

  SendSmsUsecaseImpl(this._repository);

  @override
  Future call(String userId) async {
    try {
      await _repository.sendSms(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> checkSmsCode(
      {required String userId, required String code}) async {
    try {
      final result = await _repository.checkSmsCode(userId: userId, code: code);
      if (result != null) {
        AppSession.getInstance().setNotLoggedToken(result);
      }
      return result != null;
    } catch (e) {
      rethrow;
    }
  }
}

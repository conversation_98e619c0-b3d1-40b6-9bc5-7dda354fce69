import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';

import '../../../../shared/data/models/register_user_response.dart';

abstract class ITermsRegisterUsecase {
  Future<RegisterUserResponse> getTerms();
  Future<void> acceptTerms(String userId);
  Future<void> rejectTerms(String userId);
}

class TermsRegisterUsecaseImpl implements ITermsRegisterUsecase {
  final ISignUpRepository _repository;

  TermsRegisterUsecaseImpl(this._repository);

  @override
  Future<void> acceptTerms(String userId) async {
    if (userId.isEmpty) {
      throw Exception('Erro ao identificar usuario.');
    }

    try {
      return await _repository.acceptTerms(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> rejectTerms(String userId) async {
    if (userId.isEmpty) {
      throw Exception('Erro ao identificar usuario.');
    }
    try {
      await _repository.rejectTerms(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RegisterUserResponse> getTerms() async {
    try {
      final response = await _repository.getTerms();
      return response;
    } catch (e) {
      rethrow;
    }
  }
}

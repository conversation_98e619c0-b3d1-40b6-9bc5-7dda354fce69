import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';

import '../../../../shared/data/models/register_user_response.dart';
import '../../../../shared/data/models/address_response.dart';

abstract class IAddressUsecase {
  Future<AddressResponse> getAddress(String userId);

  Future<RegisterUserResponse> confirmAddress(String userId);
}

class AddressUsecaseImpl implements IAddressUsecase {
  final ISignUpRepository _repository;

  AddressUsecaseImpl(this._repository);

  @override
  Future<RegisterUserResponse> confirmAddress(String userId) async {
    try {
      return await _repository.confirmAddress(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AddressResponse> getAddress(String userId) async {
    try {
      return await _repository.getAddress(userId);
    } catch (e) {
      rethrow;
    }
  }
}

import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';

abstract class CreatePasswordUsecase {
  Future call({required String userId, required String password});
}

class CreatePasswordUsecaseImpl implements CreatePasswordUsecase {
  final ISignUpRepository _repository;

  CreatePasswordUsecaseImpl(this._repository);

  @override
  Future call({required String userId, required String password}) async {
    try {
      await _repository.createPassword(userId: userId, password: password);
    } catch (e) {
      rethrow;
    }
  }
}

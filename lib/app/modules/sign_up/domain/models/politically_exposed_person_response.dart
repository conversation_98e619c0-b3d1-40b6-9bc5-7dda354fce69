// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class PoliticallyExposedPersonResponse implements Equatable {
  final String? key;
  final dynamic value;

  PoliticallyExposedPersonResponse({required this.key, required this.value});

  @override
  List<Object?> get props => [key, value];

  @override
  bool operator ==(covariant PoliticallyExposedPersonResponse other) {
    if (identical(this, other)) return true;

    return other.key == key && other.value == value;
  }

  @override
  int get hashCode => key.hashCode ^ value.hashCode;

  @override
  bool? get stringify => true;
}

// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:equatable/equatable.dart';

class OccupationResponse implements Equatable {
  OccupationResponse({
    this.key,
    this.value,
  });

  final String? key;
  final dynamic value;

  @override
  List<Object?> get props => [key, value];

  @override
  bool? get stringify => true;

  @override
  bool operator ==(covariant OccupationResponse other) {
    if (identical(this, other)) return true;

    return other.key == key && other.value == value;
  }

  @override
  int get hashCode => key.hashCode ^ value.hashCode;
}

import 'package:siclosbank/app/modules/sign_up/data/datasource/endpoints/sign_up_endpoints.dart';
import 'package:siclosbank/app/modules/sign_up/data/datasource/i_sign_up_datasource.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

import '../../../../app_controller.dart';
import '../../../../shared/constants/constants.dart';

class SignUpDatasourceImpl implements ISignUpDatasource {
  final IClient _client;

  SignUpDatasourceImpl(this._client);

  @override
  Future<ApiResponse> checkIsCollaborator(String cpf) async {
    final path = SignUpEndpoints.checkIsCollaborator(cpf);
    final result = await _client.fetch<dynamic>(
      method: 'GET',
      path: path,
    );
    return result;
  }

  @override
  Future<ApiResponse> getPublicyExposedPersonOptions() async {
    final result = await _client.fetch(
        method: 'GET', path: SignUpEndpoints.publiclyExposedPersonOptions);
    return result;
  }

  @override
  Future<ApiResponse> getOccupationsOptions() async {
    final result = await _client.fetch(
        method: 'GET', path: SignUpEndpoints.occupationsOptions);
    return result;
  }

  @override
  Future<ApiResponse> createBasicDataUser(
      BasicDataRequest basicDataRequest) async {
    final result = await _client.fetch(
      method: 'POST',
      path: SignUpEndpoints.createUser,
      data: basicDataRequest.toMap(),
    );
    return result;
  }

  @override
  Future<ApiResponse> sendSms(String userId) async {
    final result = await _client.fetch(
      method: 'POST',
      path: SignUpEndpoints.sendSms(userId),
      // data: {},
    );
    return result;
  }

  @override
  Future<ApiResponse> checkSmsCode(
      {required String userId, required String code}) async {
    final result = await _client.fetch(
        method: 'POST',
        path: SignUpEndpoints.checkSmsCode(userId),
        data: {
          'code': code,
        });
    return result;
  }

  @override
  Future<ApiResponse> createPassword(
      {required String userId, required String password}) async {
    final header = {
      Headers.AUTH: AppSession.getInstance().getAuth(isLogged: false)
    };
    final result = await _client.fetch(
        method: 'PATCH',
        path: SignUpEndpoints.createPassword(userId),
        headers: header,
        data: {
          'password': password,
        });
    return result;
  }

  @override
  Future<ApiResponse> confirmAddress(String userId) async {
    final result = await _client.fetch(
      method: 'PATCH',
      path: SignUpEndpoints.addressConfirm(userId),
    );
    return result;
  }

  @override
  Future<ApiResponse> getAddress(String userId) async {
    final result = await _client.fetch(
      method: 'GET',
      path: SignUpEndpoints.address(userId),
    );
    return result;
  }

  @override
  Future<ApiResponse> acceptTerms(String userId) async {
    final result = await _client.fetch(
      method: 'POST',
      path: SignUpEndpoints.acceptTerms(userId),
    );
    return result;
  }

  @override
  Future<ApiResponse> rejectTerms(String userId) async {
    final result = await _client.fetch(
      method: 'POST',
      path: SignUpEndpoints.rejectTerms(userId),
    );
    return result;
  }

  @override
  Future<ApiResponse> getUrlSendDocuments(String userId) async {
    final result = await _client.fetch(
      method: 'GET',
      path: SignUpEndpoints.getUrlSendDocuments(userId),
    );
    return result;
  }

  @override
  Future<ApiResponse> getUrlTerms() async {
    final result = await _client.fetch(
      method: 'GET',
      path: SignUpEndpoints.getUrlTerms,
    );
    return result;
  }
}

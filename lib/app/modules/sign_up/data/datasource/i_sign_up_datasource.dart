import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';

abstract class ISignUpDatasource {
  Future<ApiResponse> checkIsCollaborator(String cpf);

  Future<ApiResponse> getPublicyExposedPersonOptions();

  Future<ApiResponse> getOccupationsOptions();

  Future<ApiResponse> createBasicDataUser(BasicDataRequest basicDataRequest);

  Future<ApiResponse> sendSms(String userId);

  Future<ApiResponse> checkSmsCode(
      {required String userId, required String code});

  Future<ApiResponse> createPassword(
      {required String userId, required String password});

  Future<ApiResponse> getAddress(String userId);

  Future<ApiResponse> confirmAddress(String userId);

  Future<ApiResponse> getUrlTerms();
  Future<ApiResponse> acceptTerms(String userId);

  Future<ApiResponse> rejectTerms(String userId);
  Future<ApiResponse> getUrlSendDocuments(String userId);
}

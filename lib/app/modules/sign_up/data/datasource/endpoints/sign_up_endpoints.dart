abstract class SignUpEndpoints {
  // check cpf
  static checkIsCollaborator(String cpf) => '/user/verify-cpf/$cpf';
  // basic data
  static const createUser = '/user/';
  static const publiclyExposedPersonOptions = '/user/pep'; // not implemented
  static const occupationsOptions = '/occupation_user';
  // address
  static address(String userId) => '/user/address/$userId';
  static addressConfirm(String userId) =>
      '/user/confirm-address-register/$userId';
  // check phone
  static sendSms(String userId) => '/user/send-code-confirm-phone/$userId';
  static checkSmsCode(String userId) =>
      '/user/verify-code-confirm-phone/$userId';
  // create password
  static createPassword(String userId) => '/user/password/$userId';
  // terms
  static const getUrlTerms = '/user/term/TERMOS_DE_USO';
  static acceptTerms(String userId) => '/user/terms_of_use/$userId';
  static rejectTerms(String userId) => '/user/reject_terms/$userId';
  // send documents
  static getUrlSendDocuments(String userId) => '/user/get-document-url/$userId';
}

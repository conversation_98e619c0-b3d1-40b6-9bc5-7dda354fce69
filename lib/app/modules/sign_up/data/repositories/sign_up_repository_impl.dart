import 'package:siclosbank/app/modules/sign_up/data/datasource/i_sign_up_datasource.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/repositories/i_sign_up_repository.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../../../../shared/data/models/register_user_response.dart';

class SignUpRepositoryImpl implements ISignUpRepository {
  final ISignUpDatasource _datasource;

  SignUpRepositoryImpl(this._datasource);

  @override
  Future<Map<String, dynamic>> getPublicyExposedPersonOptions() async {
    try {
      final response = await _datasource.getPublicyExposedPersonOptions();
      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<List<dynamic>> getOccupationsOptions() async {
    try {
      final response = await _datasource.getOccupationsOptions();
      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> sendSms(String userId) async {
    try {
      await _datasource.sendSms(userId);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future checkSmsCode({required String userId, required String code}) async {
    try {
      final response =
          await _datasource.checkSmsCode(userId: userId, code: code);

      return response.data;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> createPassword(
      {required String userId, required String password}) async {
    try {
      await _datasource.createPassword(userId: userId, password: password);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<RegisterUserResponse> checkIsCollaborator(String cpf) async {
    try {
      final response = await _datasource.checkIsCollaborator(cpf);

      final result = RegisterUserResponse.fromMap(response.data);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<RegisterUserResponse> createBasicData(
      BasicDataRequest basicDataRequest) async {
    try {
      final response = await _datasource.createBasicDataUser(basicDataRequest);

      final result = RegisterUserResponse.fromMap(response.data);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<RegisterUserResponse> confirmAddress(String userId) async {
    try {
      final response = await _datasource.confirmAddress(userId);

      final result = RegisterUserResponse.fromMap(response.data);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<AddressResponse> getAddress(String userId) async {
    try {
      final response = await _datasource.getAddress(userId);

      final result = AddressResponse.fromMap(response.data);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> acceptTerms(String userId) async {
    try {
      await _datasource.acceptTerms(userId);
      // final result = RegisterUserResponse.fromMap(response.data);
      // return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<void> rejectTerms(String userId) async {
    try {
      await _datasource.rejectTerms(userId);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String?> getUrlSendDocuments(String userId) async {
    try {
      final response = await _datasource.getUrlSendDocuments(userId);
      final result = (response.data);
      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<RegisterUserResponse> getTerms() async {
    try {
      final response = await _datasource.getUrlTerms();
      final result = RegisterUserResponse.fromMap(response.data);
      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

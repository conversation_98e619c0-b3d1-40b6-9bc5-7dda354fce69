// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class BasicDataRequest extends Equatable {
  String cpf;
  String mobilePhone;
  String email;
  String fullName;
  String birthdate;
  String maritalStatus;
  String motherName;
  String? socialName;
  String occupation;
  String politicallyExposedPerson;
  // fields to create credit account
  String documentType;
  String documentNUmber;
  String expeditionDate;
  String issuingInstitution;
  String nationality;

  BasicDataRequest({
    required this.cpf,
    required this.mobilePhone,
    required this.email,
    required this.fullName,
    required this.birthdate,
    required this.maritalStatus,
    required this.motherName,
    this.socialName,
    required this.occupation,
    required this.politicallyExposedPerson,
    // fields to create credit account
    required this.documentType,
    required this.documentNUmber,
    required this.expeditionDate,
    required this.issuingInstitution,
    required this.nationality,
  });

  @override
  List<Object?> get props => [
        cpf,
        mobilePhone,
        email,
        fullName,
        birthdate,
        maritalStatus,
        motherName,
        socialName,
        occupation,
        politicallyExposedPerson,
        documentType,
        documentNUmber,
        expeditionDate,
        issuingInstitution,
        nationality,
      ];

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'cpf': cpf,
      'phone': mobilePhone,
      'email': email,
      'name': fullName,
      'date_of_birth': birthdate,
      'civil_status': maritalStatus,
      'mother_name': motherName,
      'social_name': socialName,
      'occupation': occupation,
      'political_exposure': politicallyExposedPerson,
      'document_type': documentType,
      'document_number': documentNUmber,
      'document_issue_date': expeditionDate,
      'issuing_institution': issuingInstitution,
      'nationality': nationality,
    };
  }

  factory BasicDataRequest.fromMap(Map<String, dynamic> map) {
    return BasicDataRequest(
      cpf: map['cpf'] as String,
      mobilePhone: map['mobilePhone'] as String,
      email: map['email'] as String,
      fullName: map['fullName'] as String,
      birthdate: map['birthdate'] as String,
      maritalStatus: map['maritalStatus'] as String,
      motherName: map['motherName'] as String,
      socialName: map['socialName'] as String?,
      occupation: map['occupation'] as String,
      politicallyExposedPerson: map['politicallyExposedPerson'] as String,
      documentType: map['document_type'] as String,
      documentNUmber: map['document_number'] as String,
      expeditionDate: map['document_issue_date'] as String,
      issuingInstitution: map['issuing_institution'] as String,
      nationality: map['nationality'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory BasicDataRequest.fromJson(String source) =>
      BasicDataRequest.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool? get stringify => true;
}

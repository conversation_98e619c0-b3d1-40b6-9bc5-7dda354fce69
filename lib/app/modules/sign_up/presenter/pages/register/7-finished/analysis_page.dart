import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../../../shared/utils/utils.dart';

class AnalysisPage extends StatelessWidget {
  const AnalysisPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      // canPop: ,
      child: Scaffold(
        appBar: AppBarApp(
          showBack: false,
          showLine: false,
          actions: <Widget>[
            Utils.buttonIcHelp(context),
          ],
        ),
        body: SafeArea(
          child: _bodyContainer(context),
        ),
      ),
    );
  }

  // _bodyProgress() {
  //   return Center(
  //     child: Utils.circularProgressButton(),
  //   );
  // }

  Widget _bodyContainer(context) {
    var textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  I18n.of(context)!.pronto,
                  style: textTheme.displayLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  I18n.of(context)!.recebemos_informacoes_cadastro,
                  style: textTheme.bodyMedium,
                ),
                const SizedBox(height: 40),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: <Widget>[
                //     ImageUtils.icCadastroAguardando(
                //         size: MediaQuery.of(context).size.width - 32),
                //   ],
                // ),
              ],
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              alignment: Alignment.bottomCenter,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ImageUtils.imgEmprestimoSimulacaoEnviada(),
                  ButtonApp(
                    text: I18n.of(context)!.sair,
                    onPress: _clickSair,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _clickSair() async {
    await StorageUtils.clearUserRegister();
    navigate(Routes.intro);
    // await SheetAlertConfirm.showSheet(
    //   context,
    //   title: I18n.of(context)!.sair_conta,
    //   message: I18n.of(context)!.certeza_sair,
    //   textPositive: I18n.of(context)!.sim,
    //   textNegative: I18n.of(context)!.cancelar,
    //   clickPositive: () async {
    //   },
    // );
  }
}

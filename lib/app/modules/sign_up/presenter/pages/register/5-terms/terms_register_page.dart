import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';

import 'package:siclosbank/app/modules/sign_up/presenter/blocs/register/register_bloc.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../../../../../localization/generated/i18n.dart';

import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/sheet_alert_confirm.dart';
import '../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/terms/terms_bloc.dart';

class TermsRegisterPage extends StatelessWidget {
  const TermsRegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<TermsBloc>(),
      child: const _TermsView(),
    );
  }
}

class _TermsView extends StatefulWidget {
  const _TermsView({super.key});

  @override
  State<_TermsView> createState() =>
      // ignore: no_logic_in_create_state
      _WebviewSendDocumentsState();
}

class _WebviewSendDocumentsState extends State<_TermsView> {
  bool isCheckTermos = false;
  var loadingView = true;
  late String htmlTerms;

  @override
  void initState() {
    htmlTerms = '';
    BlocProvider.of<TermsBloc>(context).add(GetTermosEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TermsBloc, TermsState>(
      listener: (context, state) {
        if (state is GetTermsHtmlSuccessState) {
          setState(() {
            htmlTerms = state.url;
          });
        }
        if (state is AcceptTermsSuccessState) {
          BlocProvider.of<RegisterBloc>(context).add(ChangeStageEvent(
            stage: RegisterStage.DOCUMENTS,
          ));
        }

        if (state is DeniedTermsSuccessState) {
          pop();
        }

        if (state is ErrorTermsState) {
          DialogUtils.showSnackError(context, state.error);
        }
      },
      builder: (context, state) {
        return Scaffold(
          body: SafeArea(
            child: Builder(builder: (context) {
              if (state is LoadingCenterTerms) {
                return _buildProgress();
              }
              if (htmlTerms.isNotEmpty) {
                return _buildTerm2(htmlTerms);
              }
              return Container();
            }),
          ),
          bottomNavigationBar:
              htmlTerms.isEmpty ? null : _buildButtonAceite(state),
        );
      },
    );
  }

  Container _buildProgress() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  Widget _buildTerm2(String url) {
    return SfPdfViewer.network(
      url,
      enableTextSelection: false,
    );
  }

  _textTituloNegar() {
    return I18n.of(context)!.recusar_termo_de_compromisso;
  }

  _textMensagemNegar() {
    return I18n.of(context)!.msg_recusar_termo_de_comprimisso;
  }

  _textBtnPositivoNegar() {
    return I18n.of(context)!.cancelar_cadastro;
  }

  _textBtnNegativoNegar() {
    return I18n.of(context)!.cancelar;
  }

  _buildButtonAceite(TermsState state) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, -0.9),
            blurRadius: 3,
            color: Colors.black12,
            // blurStyle: BlurStyle.outer,
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Checkbox(
                onChanged: (bool? value) {
                  setState(() {
                    isCheckTermos = !isCheckTermos;
                  });
                },
                value: isCheckTermos,
              ),
              Text(
                I18n.of(context)!.confirmo_que_li,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(height: 1.0),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ButtonApp(
            text: I18n.of(context)!.aceitar,
            onPress: _callBlocAceitar,
            enabled: isCheckTermos && state is! LoadDeniedTerms,
            progress: state is LoadAcceptTerms
                ? Utils.circularProgressButton(size: 20)
                : null,
          ),
          const SizedBox(height: 8),
          ButtonApp(
            text: I18n.of(context)!.recusar,
            buttonColor: Colors.white,
            enabled: state is! LoadAcceptTerms,
            onPress: _clickNegar,
            progress: state is LoadDeniedTerms
                ? Utils.circularProgressButton(
                    size: 20, color: ColorsApp.verde[500])
                : null,
          ),
        ],
      ),
    );
  }

  _clickNegar() {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return SheetAlertConfirm(
          title: _textTituloNegar(),
          message: _textMensagemNegar(),
          textPositiveButton: _textBtnPositivoNegar(),
          textNegativeButton: _textBtnNegativoNegar(),
          onClickPositive: () {
            _callProviderNegar();
          },
        );
      },
    );
  }

  _callBlocAceitar({String? pin}) {
    var user = BlocProvider.of<RegisterBloc>(context).registerUser!;
    BlocProvider.of<TermsBloc>(context).add(AcceptTermsEvent(
      userID: user.id!,
    ));
  }

  _callProviderNegar() {
    var user = BlocProvider.of<RegisterBloc>(context).registerUser!;
    BlocProvider.of<TermsBloc>(context).add(DeniedTermsEvent(userID: user.id!));
  }
}

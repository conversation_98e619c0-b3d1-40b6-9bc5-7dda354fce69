import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/phone_check/phone_check_bloc.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../../../../../shared/data/models/register_user_response.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_reenviar_codigo.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/pin_code_custom.dart';
import '../../../../../../shared/themes/styles/colors_app.dart';
// import '../../../../shared/utils/constants.dart' show ErrorAnimationType;
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/register/register_bloc.dart';

class PhoneCheckPage extends StatelessWidget {
  const PhoneCheckPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<PhoneCheckBloc>(
      create: (context) => Modular.get<PhoneCheckBloc>(),
      child: const _PhoneCheckView(),
    );
  }
}

class _PhoneCheckView extends StatefulWidget {
  const _PhoneCheckView({
    super.key,
  });

  @override
  State<_PhoneCheckView> createState() => __PhoneCheckViewState();
}

class __PhoneCheckViewState extends State<_PhoneCheckView> {
  bool enableButtomContinue = false;
  final codePinController = TextEditingController();
  late StreamController<ErrorAnimationType> errorController;
  late CountdownController controller;
  bool enableButtomResend = false;
  bool hasError = false;
  late RegisterUserResponse registerUser;

  @override
  void initState() {
    super.initState();
    final bloc = BlocProvider.of<RegisterBloc>(context);
    registerUser = bloc.registerUser!;

    if (bloc.registerStage == RegisterStage.PHONE_CHECK) {
      BlocProvider.of<PhoneCheckBloc>(context)
          .add(SendSmsCodeEvent(registerUser.id!));
    }
    _iniciaContador();
  }

  void _iniciaContador() {
    errorController = StreamController<ErrorAnimationType>();
    controller = CountdownController(autoStart: true);
  }

  @override
  void dispose() {
    errorController.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<PhoneCheckBloc, PhoneCheckState>(
        listener: (context, state) {
          if (state is ErrorPhoneCheck) {
            DialogUtils.showSnackError(context, state.error);
            errorController.add(
                ErrorAnimationType.shake); // Triggering error shake animation
          }

          if (state is CodeVerifiedSuccessfully) {
            BlocProvider.of<RegisterBloc>(context).add(ChangeStageEvent(
              stage: RegisterStage.PASSWORD,
            ));
          }
        },
        builder: (context, state) {
          var isPhoneStage =
              BlocProvider.of<RegisterBloc>(context).registerStage ==
                  RegisterStage.PHONE_CHECK;
          return isPhoneStage ? _buildBody(state) : Container();
        },
      ),
    );
  }

  _buildBody(PhoneCheckState state) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 32),
        child: CustomScrollView(
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: Column(
                children: <Widget>[
                  Text(
                    const I18n().descricao_confirmacao_sms,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Container(
                    margin: const EdgeInsets.only(
                        left: 32, right: 32, top: 64, bottom: 32),
                    child: PinCodeAppCustom(
                      autoFocus: true,
                      autoDisposeControllers: false,
                      controller: codePinController,
                      onChanged: (value) {
                        setState(() {
                          enableButtomContinue = (value.length == 6);
                          hasError = false;
                        });
                      },
                      length: 6,
                      obsecureText: false,
                      animationType: AnimationType.fade,
                      textStyle: Theme.of(context)
                          .textTheme
                          .bodyLarge!
                          .copyWith(fontSize: 24),
                      animationDuration: const Duration(milliseconds: 300),
                      pinTheme: PinTheme(
                        activeColor:
                            hasError ? Colors.red : ColorsApp.cinza[500],
                        shape: PinCodeFieldShape.underline,
                        inactiveColor: ColorsApp.cinza[500],
                        selectedColor: ColorsApp.cinza[500],
                        fieldHeight: 50,
                        fieldWidth: 32,
                      ),
                      backgroundColor: Colors.transparent,
                      textInputType: TextInputType.number,
                      errorAnimationController: errorController,
                    ),
                  ),
                  state is LoadingResendCodePhoneCheck
                      ? Container(
                          child: Utils.circularProgressButton(size: 20),
                        )
                      : !enableButtomResend
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  const I18n().reenviar_codigo_sms_timer,
                                  style: Theme.of(context).textTheme.labelLarge,
                                ),
                                const SizedBox(width: 4),
                                Countdown(
                                  controller: controller,
                                  seconds: 60,
                                  build: (_, double time) {
                                    return Text(
                                      time == 60
                                          ? '1:00'
                                          : '0:${time.round().toString().padLeft(2, '0')}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelLarge,
                                    );
                                  },
                                  interval: const Duration(seconds: 1),
                                  onFinished: () {
                                    // controller.restart();
                                    setState(() {
                                      enableButtomResend = true;
                                    });
                                  },
                                ),
                              ],
                            )
                          : ButtonResendCode(
                              onTap: () {
                                BlocProvider.of<PhoneCheckBloc>(context)
                                    .add(SendSmsCodeEvent(registerUser.id!));

                                // controller.restart();
                                _iniciaContador();
                                setState(() {
                                  enableButtomResend = false;
                                });
                              },
                            )
                ],
              ),
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              fillOverscroll: false,
              child: Container(
                alignment: Alignment.bottomCenter,
                padding: const EdgeInsets.only(bottom: 16),
                child: ButtonApp(
                  enabled: enableButtomContinue,
                  width: MediaQuery.of(context).size.width,
                  text: const I18n().continuar,
                  progress: state is LoadingCheckCode
                      ? Utils.circularProgressButton(size: 20)
                      : null,
                  onPress: () {
                    BlocProvider.of<PhoneCheckBloc>(context).add(
                        CheckSmsCodeEvent(
                            userId: registerUser.id!,
                            code: codePinController.text));
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

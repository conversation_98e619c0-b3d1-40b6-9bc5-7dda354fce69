// import 'package:flutter/foundation.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/sign_up/data/models/basic_data_request.dart';
import 'package:siclosbank/app/modules/sign_up/domain/models/occupation_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/models/politically_exposed_person_response.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/basic_data/basic_data_bloc.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/widgets/select_marital_status_widget.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/widgets/select_nacionality_widget.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/widgets/select_occupations_widget.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/widgets/select_publicly_exposed_person.dart';
import 'package:siclosbank/app/shared/constants/nacionalities_list_const.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';
import 'package:string_validator/string_validator.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/data/models/register_user_response.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/snack_bar_app.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/register/register_bloc.dart';

const listPEPOptions = {
  'NAO_EXPOSTA':
      'Nao sou e não possuo vínculo com uma pessoa politicamente exposta',
  'EXPOSTA': 'Sou uma pessoa politicamente exposta',
  'VINCULO_POLITICO': 'Tenho vínculo com pessoa(s) exposta(s) politicamente',
};

enum DocumentType { RNE, RG }

class BasicDataPage extends StatelessWidget {
  const BasicDataPage({
    super.key,
  });
  // final RegisterUserResponse? user;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<BasicDataBloc>(),
      child: _BasicDataPage(
          // user: user,
          ),
    );
  }
}

class _BasicDataPage extends StatefulWidget {
  const _BasicDataPage({
    super.key,
    // required this.user,
  });
  // final RegisterUserResponse? user;
  @override
  State<_BasicDataPage> createState() => _BasicDataRegistrationViewState();
}

class _BasicDataRegistrationViewState extends State<_BasicDataPage> {
  final _formKey = GlobalKey<FormState>();
  bool trySaveForm = false;
  final translator = {'#': RegExp(r'[a-z0-9]')};
  late TextEditingController fullNameController;
  late TextEditingController cpfController;
  late TextEditingController numberDocumentController;
  late TextEditingController expDateController;
  late TextEditingController issuingInstitController;
  // late TextEditingController nationalityController;
  late TextEditingController birthDateController;
  late TextEditingController emailController;
  late TextEditingController phoneController;
  late TextEditingController socialNameController;
  late TextEditingController motherNameController;
  bool informSocialName = false;
  bool enableContinueButton = false;

  String? maritalStatus;
  String? nationality;
  DocumentType? documentType;
  final listOccupationsOptions = ValueNotifier(<OccupationResponse>[]);
  final listPolicalExposedPerson = ValueNotifier(listPEPOptions.entries
      .map((map) =>
          PoliticallyExposedPersonResponse(key: map.key, value: map.value))
      .toList());
  OccupationResponse? occupation;
  PoliticallyExposedPersonResponse? selectedPEP;

  @override
  void initState() {
    _initControllers();
    setMock();
    super.initState();
  }

  _initControllers() {
    final user = Modular.args.data?['args'] as RegisterUserResponse?;

    documentType = DocumentType.RG;
    if (user != null && user.cpf != null) {
      var cpf = FieldsUtils.obterCpf(user.cpf!);
      cpfController = TextEditingController(text: cpf);
    } else {
      cpfController = TextEditingController();
    }
    fullNameController = TextEditingController();
    birthDateController = MaskedTextController(mask: '00/00/0000');
    emailController = TextEditingController();
    phoneController = MaskedTextController(mask: '(00)00000-0000');
    socialNameController = TextEditingController();
    motherNameController = TextEditingController();

    numberDocumentController = TextEditingController();
    expDateController = MaskedTextController(mask: '00/00/0000');
    issuingInstitController = TextEditingController();
    nationality = 'Brasileiro (a)';
  }

  @override
  void didChangeDependencies() {
    final bloc = BlocProvider.of<BasicDataBloc>(context);
    if (!bloc.isClosed) {
      BlocProvider.of<BasicDataBloc>(context).add(FecthOptionsEvent());
    }
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<BasicDataBloc, BasicDataState>(
        listener: (context, state) {
          if (state is ErrorBasicDataState) {
            DialogUtils.showSnackError(context, state.error);
          }
          if (state is FetchOptionsSuccess) {
            listOccupationsOptions.value = state.listOccupationsOptions;
          }
          if (state is RegisterBasicDataSuccess) {
            BlocProvider.of<RegisterBloc>(context).add(ChangeStageEvent(
              stage: RegisterStage.ADDRESS,
              registerUser: state.registerUserResponse,
            ));
          }
        },
        builder: (context, state) {
          if (state is LoadingPageState) {
            return _buildProgress();
          }
          if (state is FetchOptionsSuccess) {
            listOccupationsOptions.value = state.listOccupationsOptions;
          }

          var isThisStage = BlocProvider.of<RegisterBloc>(context)
                  .registerUser!
                  .isRegistered ==
              false;
          // BlocProvider.of<RegisterBloc>(context).state.stage ==
          //     RegisterStage.BASIC_DATA;
          return isThisStage ? _buildBody(state) : Container();
        },
      ),
    );
  }

  Widget _buildProgress() {
    return Center(
      child: Utils.circularProgressButton(),
    );
  }

  _buildBody(BasicDataState state) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Form(
            key: _formKey,
            autovalidateMode: trySaveForm
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Padding(
              padding: const EdgeInsets.only(
                  left: 16, top: 16, right: 16, bottom: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    const I18n().sobre_voce,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 26),
                  // fullname
                  TextFormFieldApp(
                    controller: fullNameController,
                    label: const I18n().nome_completo,
                    maxLength: 200,
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    formatter: _formatterFullName(),
                    validator: (value) {
                      if (!isLength(value!, 4)) {
                        return const I18n().campo_obrigatorio;
                      }
                      if (!FieldsUtils.fullNameIsValid(value)) {
                        return I18n.of(context)!.nome_completo_erro;
                      }
                      return null;
                    },
                  ),
                  // const SizedBox(height: 16),
                  // cpf
                  // TextFormFieldApp(
                  //   controller: cpfController,
                  //   enable: false,
                  //   label: const I18n().cpf,
                  // ),

                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      RadioMenuButton<DocumentType?>(
                        value: DocumentType.RG,
                        groupValue: documentType,
                        onChanged: (value) {
                          setState(() {
                            documentType = value;
                          });
                        },
                        child: Text(
                          'RG',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(color: ColorsApp.cinza[600]),
                        ),
                      ),
                      RadioMenuButton<DocumentType?>(
                          value: DocumentType.RNE,
                          groupValue: documentType,
                          onChanged: (value) {
                            setState(() {
                              documentType = value;
                            });
                          },
                          child: Text(
                            'RNE',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(color: ColorsApp.cinza[600]),
                          )),
                    ],
                  ),
                  const SizedBox(height: 16),

                  TextFormFieldApp(
                    controller: numberDocumentController,
                    textInputType: TextInputType.text,
                    maxLength: documentType == DocumentType.RNE ? 11 : 15,
                    label: documentType == DocumentType.RNE
                        ? const I18n().rne
                        : const I18n().rg,
                    formatter:
                        TextInputFormatter.withFunction((oldValue, newValue) {
                      late RegExp valRegex;
                      if (documentType == DocumentType.RG) {
                        valRegex = RegExp(
                            r'^([A-Za-z0-9]{3})([A-Za-z0-9]{3})([A-Za-z0-9]{3})([A-Za-z0-9]{2})$');
                      } else {
                        valRegex =
                            RegExp(r'^([A-Za-z0-9]{8})([A-Za-z0-9]{1})$');
                      }

                      if (valRegex.hasMatch(newValue.text)) {
                        var text = newValue.text;

                        if (documentType == DocumentType.RG) {
                          text = newValue.text.replaceAllMapped(valRegex,
                              (Match m) {
                            return '${m[1]}.${m[2]}.${m[3]}-${m[4]}';
                          });
                        } else {
                          text = newValue.text.replaceAllMapped(valRegex,
                              (Match m) {
                            return '${m[1]}-${m[2]}';
                          });
                        }

                        // var text = newValue.text.toUpperCase().trim();
                        return newValue.copyWith(text: text);
                      }
                      return newValue;
                    }),
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return const I18n().campo_obrigatorio;
                      }

                      if (!isLength(value, 4)) {
                        return const I18n().campo_obrigatorio;
                      }

                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    controller: expDateController,
                    label: const I18n().data_expedicao,
                    textInputType: TextInputType.datetime,
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    validator: (value) {
                      if (Utils.validDate(value!)) {
                        return const I18n().data_invalida;
                      }

                      if (!isLength(value, 10)) {
                        return const I18n().campo_obrigatorio;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    controller: issuingInstitController,
                    textInputType: TextInputType.text,
                    label: const I18n().orgao_expedidor,
                    formatter:
                        TextInputFormatter.withFunction((oldValue, newValue) {
                      var text = newValue.text.toUpperCase().trim();
                      return newValue.copyWith(
                        text: text.replaceAll(' ', ''),
                      );
                    }),
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return const I18n().campo_obrigatorio;
                      }

                      if (!isLength(value, 1)) {
                        return const I18n().campo_obrigatorio;
                      }

                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  SelectNacionalityWidget(
                    selected: nationality,
                    onChanged: (input) {
                      setState(() {
                        nationality = input;
                      });
                      _verificarPreenchimentoCampos();
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    controller: birthDateController,
                    label: const I18n().data_nascimento,
                    textInputType: TextInputType.datetime,
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    validator: (value) {
                      if (Utils.validDate(value!)) {
                        return const I18n().data_invalida;
                      }

                      if (Utils.calculateYearsOld(value) < 18) {
                        return const I18n().idade_inferior_18;
                      }

                      if (!isLength(value, 10)) {
                        return const I18n().campo_obrigatorio;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  SelectMaritalStatusWidget(
                    selected: maritalStatus,
                    onChanged: (input) {
                      setState(() {
                        maritalStatus = input;
                      });
                      _verificarPreenchimentoCampos();
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    controller: motherNameController,
                    label: const I18n().nome_mae,
                    maxLength: 200,
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    formatter: _formatterFullName(),
                    validator: (value) {
                      if (!isLength(value!, 4)) {
                        return I18n.of(context)!.campo_obrigatorio;
                      }
                      if (!FieldsUtils.fullNameIsValid(value)) {
                        return I18n.of(context)!.nome_completo_erro;
                      }

                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  ValueListenableBuilder(
                      valueListenable: listOccupationsOptions,
                      builder: (context, value, _) {
                        return SelectOccupationsWidget(
                          items: value,
                          selected: occupation,
                          loading: state is LoadingOptionsState,
                          onChanged: (value) {
                            setState(() {
                              occupation = value;
                            });

                            _verificarPreenchimentoCampos();
                          },
                        );
                      }),

                  const SizedBox(height: 16),
                  ValueListenableBuilder(
                      valueListenable: listPolicalExposedPerson,
                      builder: (context, value, _) {
                        return SelectPubliclyExposedPerson(
                          items: value,
                          selected: selectedPEP,
                          loading: state is LoadingOptionsState,
                          onChanged: (input) {
                            setState(() {
                              selectedPEP = input;
                            });
                            _verificarPreenchimentoCampos();
                          },
                        );
                      }),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    controller: emailController,
                    textInputType: TextInputType.emailAddress,
                    label: const I18n().email,
                    formatter:
                        TextInputFormatter.withFunction((oldValue, newValue) {
                      var text = newValue.text.toLowerCase().trim();
                      return newValue.copyWith(
                        text: text.replaceAll(' ', ''),
                      );
                    }),
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return const I18n().campo_obrigatorio;
                      }

                      if (!isEmail(value)) {
                        return const I18n().email_invalido;
                      }

                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    controller: phoneController,
                    textInputType: TextInputType.phone,
                    label: const I18n().telefone_celular,
                    onChanged: (text) {
                      _verificarPreenchimentoCampos();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return const I18n().campo_obrigatorio;
                      }

                      if (value.length < 14) {
                        return const I18n().telefone_celular_invalido;
                      }

                      return null;
                    },
                  ),
                  Row(
                    children: <Widget>[
                      Checkbox(
                        checkColor: ColorsApp.cinza[800],
                        // activeColor: Colors.transparent,
                        // fillColor: ,
                        value: informSocialName,
                        onChanged: (value) {
                          setState(() {
                            informSocialName = value!;
                          });
                        },
                      ),
                      Text(
                        const I18n().informar_nome_social,
                        style: Theme.of(context).textTheme.bodyMedium,
                      )
                    ],
                  ),
                  Visibility(
                    visible: informSocialName,
                    child: TextFormFieldApp(
                      controller: socialNameController,
                      label: const I18n().nome_social,
                      suffix: GestureDetector(
                        onTap: () {
                          DialogUtils.showDialogTitleMensagem(
                            context: context,
                            title: I18n.of(context)!.nome_social_dialog,
                            mensagem: I18n.of(context)!.nome_sicial_dialog_msg,
                          );
                        },
                        child: IconsApp.icInfo(
                          color: ColorsApp.azul[100],
                          height: 12,
                          width: 12,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: false,
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
            child: Container(
              alignment: Alignment.bottomCenter,
              child: ButtonApp(
                // enabled: enableContinueButton,
                width: MediaQuery.of(context).size.width,
                progress: state is LoadingOptionsState
                    ? Utils.circularProgressButton(size: 20)
                    : null,
                text: const I18n().continuar,
                onPress: _clickContinuar,
              ),
            ),
          ),
        ),
      ],
    );
  }

  formatDocument() {
    String formatedValue = numberDocumentController.text.replaceAll('.', '');
    formatedValue = formatedValue.replaceAll('-', '');
    return formatedValue;
  }

  _clickContinuar() {
    FocusScope.of(context).unfocus();
    _formKey.currentState!.save();
    setState(() => trySaveForm = true);

    if (_formKey.currentState!.validate()) {
      BlocProvider.of<BasicDataBloc>(context).add(
        RegisterBasicDataEvent(
          basicData: BasicDataRequest(
            fullName: fullNameController.text.trim(),
            cpf: FieldsUtils.removeCharacters(cpfController.text),
            birthdate: Utils.formatDateBrToApi(birthDateController.text),
            email: emailController.text.trim(),
            mobilePhone: Utils.formatPhoneToApi(phoneController.text),
            socialName: socialNameController.text.isNotEmpty
                ? socialNameController.text.trim()
                : null,
            politicallyExposedPerson: selectedPEP!.key!,
            maritalStatus: maritalStatus!,
            motherName: motherNameController.text.trim(),
            occupation: occupation!.key!,
            documentType: documentType == DocumentType.RG ? 'RG' : 'RNE',
            documentNUmber: formatDocument(),
            expeditionDate: Utils.formatDateBrToApi(expDateController.text),
            issuingInstitution: issuingInstitController.text,
            nationality: nationality!,
          ),
        ),
      );
    } else {
      SnackBarApp.showSnack(
        context: context,
        message:
            'Verifique se todos os campos estão preenchidos corretamente e tente novamente.',
        success: false,
        // warning: true,
      );
    }
  }

  TextInputFormatter _formatterFullName() {
    return TextInputFormatter.withFunction(
      (oldValue, newValue) {
        var text = newValue.text;
        return TextEditingValue(
            text: text.replaceAll(FieldsUtils.regexName, ''));
      },
    );
  }

  void _verificarPreenchimentoCampos() {
    if (fullNameController.text.isNotEmpty &&
        birthDateController.text.isNotEmpty &&
        maritalStatus != null &&
        maritalStatus!.isNotEmpty &&
        motherNameController.text.isNotEmpty &&
        emailController.text.isNotEmpty &&
        phoneController.text.isNotEmpty) {
      setState(() {
        enableContinueButton = true;
      });
    } else {
      setState(() {
        enableContinueButton = false;
      });
    }
  }

  setMock() {
    if (!kReleaseMode && Constants.mock) {
      fullNameController.text = 'Nome Teste User';
      birthDateController.text = '01/01/2000';
      maritalStatus = 'CASADO';
      motherNameController.text = 'Mae Nome Teste';
      emailController.text = '<EMAIL>';
      phoneController.text = '(77) 99819-9751';
      selectedPEP = listPolicalExposedPerson.value.first;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/7-finished/analysis_page.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/1-basic_data/basic_data_page.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/2-address/address_page.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/3-check_phone/phone_check_page.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/4-password/create_password_page.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/5-terms/terms_register_page.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/pages/register/6-documents/documents_page.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/errors/errors.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../shared/utils/utils.dart';
import '../../blocs/register/register_bloc.dart';

abstract class RegisterPageIndex {
  static const BASIC_DATA = 0;
  static const ADDRESS = 1;
  static const PHONE_CHECK = 2;
  static const PASSWORD = 3;
  static const TERMS = 4;
  static const DOCUMENTS = 5;
  static const ANALYSIS = 6;
}

class RegisterPageView extends StatelessWidget {
  const RegisterPageView({super.key, this.currentPage = 0});
  final int currentPage;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<RegisterBloc>(),
      child: _RegisterView(currentPage: currentPage),
    );
  }
}

class _RegisterView extends StatefulWidget {
  const _RegisterView({super.key, this.currentPage = 0});
  final int currentPage;

  @override
  State<_RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<_RegisterView> {
  late int _currentPage;
  // int _previousPage = HomePageIndex.HOME;
  late PageController pageController;
  late List<Widget> pages;

  @override
  void initState() {
    final user = Modular.args.data?['args'] as RegisterUserResponse;
    BlocProvider.of<RegisterBloc>(context).registerUser = user;
    _currentPage = widget.currentPage;

    RegisterStage? toStage;
    if (user.isRegistered == true) {
      if (user.addressStage) {
        toStage = RegisterStage.ADDRESS;
      }
      if (user.checkPhoneStage) {
        toStage = RegisterStage.PHONE_CHECK;
      }
      if (user.passwordStage) {
        toStage = RegisterStage.PASSWORD;
      }
      if (user.termoCompromissoEstagio) {
        toStage = RegisterStage.TERMS;
      }
      if (user.documentsStage ||
          user.pendingDocumentsStage ||
          user.documentsDeniedStage) {
        toStage = RegisterStage.DOCUMENTS;
      }
      if (user.analysisDocumentsStage) {
        toStage = RegisterStage.ANALYSIS;
      }
    } else if (user.isCollaborator == true) {
      toStage = RegisterStage.BASIC_DATA;
    }
    _currentPage = toStage?.index ?? widget.currentPage;
    pageController = PageController(
      initialPage: _currentPage, // _currentPage,
      keepPage: true,
    );
    pages = [
      const BasicDataPage(),
      const AddressPage(),
      const PhoneCheckPage(),
      const CreatePasswordPage(),
      const TermsRegisterPage(),
      const SendDocumentsPage(),
      const AnalysisPage(),
    ];

    BlocProvider.of<RegisterBloc>(context).registerStage = toStage;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _currentPage == RegisterPageIndex.ANALYSIS
          ? null
          : AppBarApp(
              title: _getTextTitle(),
              clickBack: _clickBack,
              actions: <Widget>[
                Utils.buttonIcHelp(context),
              ],
            ),
      body: BlocConsumer<RegisterBloc, RegisterState>(
        listener: (context, state) {
          if (state is RegisterError) {
            if (state.error is ErrorForbidden || state.error is TokenInvalid) {
              SnackBarApp.showSnack(
                context: context,
                message:
                    'Solicitação negada. Necessário validacao de código de verificação.',
                success: false,
              );
            } else {
              DialogUtils.showSnackError(context, state.error);
            }
          }
          if (state is RegisterRequestSuccess) {
            if (state.changePage) {
              if (state.stage == RegisterStage.BASIC_DATA) {
                _changePage(state.stage!.index);
              }
              if (state.stage == RegisterStage.ADDRESS) {
                _changePage(state.stage!.index);
              }
              if (state.stage == RegisterStage.PHONE_CHECK) {
                _changePage(state.stage!.index);
              }
              if (state.stage == RegisterStage.PASSWORD) {
                _changePage(state.stage!.index);
              }
              if (state.stage == RegisterStage.TERMS) {
                _changePage(state.stage!.index);
              }
              if (state.stage == RegisterStage.DOCUMENTS) {
                _changePage(state.stage!.index);
              }
              if (state.stage == RegisterStage.ANALYSIS) {
                _changePage(state.stage!.index);
              }
            }
          }
        },
        builder: (context, state) {
          return PageView(
            physics: const NeverScrollableScrollPhysics(),
            controller: pageController,
            children: pages,
          );
        },
      ),
    );
  }

  // _clickPagina(int index) {
  //   _changePage(index);
  // }

  String? _getTextTitle() {
    if (_currentPage == RegisterPageIndex.BASIC_DATA) {
      return I18n.of(context)!.dados_basicos.toUpperCase();
    } else if (_currentPage == RegisterPageIndex.PHONE_CHECK) {
      return I18n.of(context)!.confirmacao_sms.toUpperCase();
    } else if (_currentPage == RegisterPageIndex.PASSWORD) {
      return I18n.of(context)!.seguranca.toUpperCase();
    } else if (_currentPage == RegisterPageIndex.ADDRESS) {
      return I18n.of(context)!.endereco.toUpperCase();
    } else if (_currentPage == RegisterPageIndex.DOCUMENTS) {
      return I18n.of(context)!.documentacao.toUpperCase();
    } else if (_currentPage == RegisterPageIndex.TERMS) {
      return I18n.of(context)!.termos_e_condicoes.toUpperCase();
    } else {
      return I18n.of(context)!.cadastrar.toUpperCase();
    }
  }

  // _backPage() {
  //   setState(() {
  //     _currentPage--;
  //     _movePage();
  //   });
  // }

  _changePage(int page) {
    setState(() {
      _currentPage = page;
      _movePage();
    });
  }

  _movePage() {
    pageController.animateToPage(_currentPage,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
  }

  // _onPageControl(int page) {

  //   // _previousPage = page;
  // }

  _clickBack() {
    FocusScope.of(context).unfocus();

    // sair do cadastro
    pop();

    // SheetAlertConfirm.showSheet(
    //   context,
    //   title: I18n.of(context)!.confirmacao,
    //   message: I18n.of(context)!.certeza_sair,
    //   textPositive: I18n.of(context)!.sim,
    //   textNegative: I18n.of(context)!.nao,
    //   clickPositive: () {
    //     pop();
    //   },
    // );

    // else if (currentPage == EstagioCadastroPF.DADOS_PESSOAIS) {
    //   _backPage();
    // } else {
    //   BlocProvider.of<CadastroPfBloc>(context).add(VoltarEstagioPfEvent());
    // }
  }
}

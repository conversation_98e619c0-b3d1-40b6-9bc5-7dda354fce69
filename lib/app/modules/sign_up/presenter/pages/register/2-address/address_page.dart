import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/data/models/address_response.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/address_register/address_register_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:string_validator/string_validator.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/data/models/register_user_response.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/register/register_bloc.dart';

class AddressPage extends StatelessWidget {
  const AddressPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<AddressRegisterBloc>(),
      child: _AddressPage(),
    );
  }
}

class _AddressPage extends StatefulWidget {
  const _AddressPage({
    super.key,
  });

  @override
  State<_AddressPage> createState() => __AddressPageState();
}

class __AddressPageState extends State<_AddressPage> {
  final formKey = GlobalKey<FormState>();
  final _cepController = TextEditingController();
  final _streetController = TextEditingController();
  final _numberController = TextEditingController();
  final _complementController = TextEditingController();
  final _cityController = TextEditingController();
  final _ufController = TextEditingController();
  final _districtController = TextEditingController();
  bool enableContinueButton = false;

  @override
  initState() {
    // final user = Modular.args.data?['args'] as RegisterUserResponse?;
    var userRegister = BlocProvider.of<RegisterBloc>(context).registerUser;
    final bloc = BlocProvider.of<AddressRegisterBloc>(context);
    if (!bloc.isClosed) {
      bloc.add(GetAddressRegister(userRegister?.id ?? ''));
    }
    super.initState();
  }

  _initTexts(AddressResponse address) {
    setState(() {
      _cepController.text = _hideAddress(address.cep, isCep: true) ?? '';
      _streetController.text = _hideAddress(address.street) ?? '';
      _numberController.text = _hideAddress(address.number) ?? '';
      _cityController.text = (address.city) ?? '';
      _ufController.text = (address.state) ?? '';
      _districtController.text = _hideAddress(address.district) ?? '';
      _complementController.text = _hideAddress(address.complement) ?? '';

      formKey.currentState?.validate();

      if (_cepController.text.isNotEmpty &&
          _streetController.text.isNotEmpty &&
          _numberController.text.isNotEmpty &&
          _cityController.text.isNotEmpty &&
          _ufController.text.isNotEmpty &&
          _districtController.text.isNotEmpty) {
        enableContinueButton = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<AddressRegisterBloc, AddressRegisterState>(
        listener: (context, state) {
          if (state is ErrorAddressState) {
            DialogUtils.showSnackError(context, state.error);
          }
          if (state is GetAddressSuccess) {
            _initTexts(state.addressResponse);
          }

          if (state is ConfirmAddressSuccessState) {
            BlocProvider.of<RegisterBloc>(context).add(ChangeStageEvent(
              registerUser: state.registerUserResponse,
              stage: RegisterStage.PHONE_CHECK,
              changePage: true,
            ));
          }
        },
        builder: (context, state) {
          if (state is GetAddressLoading) {
            return _buildProgress();
          }
          var isThisStage =
              BlocProvider.of<RegisterBloc>(context).registerStage ==
                  RegisterStage.ADDRESS;
          return isThisStage ? _buildBody(state) : Container();
        },
      ),
    );
  }

  String? _hideAddress(String? text, {isCep = false}) {
    if (text == null) {
      return null;
    }
    if (isCep) {
      final cep1 = text.substring(0, 3);
      final cep = '$cep1**-***';
      return cep;
    }

    if (text.length > 3) {
      final firstPart = text.substring(0, 1);
      final result = text.replaceAll(RegExp(r'[a-zA-Z0-9]'), '*');

      final secondPart = result.substring(1);
      return firstPart + secondPart;
    } else {
      final result = text.replaceAll(RegExp(r'[a-zA-Z0-9]'), '*');
      return result;
    }
  }

  Container _buildProgress() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  _buildBody(AddressRegisterState state) {
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Form(
              key: formKey,
              autovalidateMode: AutovalidateMode.always,
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, top: 32, right: 16, bottom: 32),
                child: Column(
                  children: <Widget>[
                    Visibility(
                      visible:
                          state is GetAddressLoading || enableContinueButton,
                      child: const SizedBox(
                        height: 39,
                      ),
                    ),
                    Visibility(
                      visible:
                          state is GetAddressLoading || enableContinueButton,
                      child: Text(state is GetAddressLoading
                          ? I18n.of(context)!.loading
                          : (enableContinueButton
                              ? I18n.of(context)!.verifique_endereco
                              : '')),
                    ),
                    const SizedBox(
                      height: 28,
                    ),
                    TextFormFieldApp(
                      label: const I18n().cep,
                      controller: _cepController,
                      enable: false,
                      validator: (value) {
                        if (!isLength(value!, 8, 9)) {
                          // adcionar verificacao de cep
                          return const I18n().campo_obrigatorio;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: <Widget>[
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.6,
                          child: TextFormFieldApp(
                            label: const I18n().rua_avenida,
                            controller: _streetController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 1)) {
                                return const I18n().campo_obrigatorio;
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: TextFormFieldApp(
                            label: const I18n().numero_abreviado,
                            textInputType: TextInputType.number,
                            controller: _numberController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 1)) {
                                return const I18n().campo_obrigatorio;
                              }

                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    TextFormFieldApp(
                      label: const I18n().bairro,
                      controller: _districtController,
                      enable: false,
                      validator: (value) {
                        if (!isLength(value!, 2)) {
                          return const I18n().campo_obrigatorio;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    TextFormFieldApp(
                      label: const I18n().complemento,
                      controller: _complementController,
                      enable: false,
                      // validator: (value) {
                      //   if (!isLength(value!, 3)) {
                      //     return const I18n().campo_obrigatorio;
                      //   }

                      //   return null;
                      // },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: <Widget>[
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.5,
                          child: TextFormFieldApp(
                            label: const I18n().cidade,
                            controller: _cityController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 2)) {
                                return const I18n().campo_obrigatorio;
                              }

                              return null;
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: TextFormFieldApp(
                            label: const I18n().estado,
                            controller: _ufController,
                            enable: false,
                            validator: (value) {
                              if (!isLength(value!, 1)) {
                                return const I18n().campo_obrigatorio_rh;
                              }

                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    AlertBanner(
                      isShow: !enableContinueButton,
                      typeAlertBanner: TypeAlertBanner.error,
                      message: I18n.of(context)!.campo_obrigatorio_rh,
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: false,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                alignment: Alignment.bottomCenter,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    AlertBanner(
                      isShow: true,
                      typeAlertBanner: TypeAlertBanner.info,
                      message: I18n.of(context)!.verifique_endereco_msg,
                    ),
                    const SizedBox(height: 16),
                    ButtonApp(
                      enabled: enableContinueButton,
                      width: MediaQuery.of(context).size.width,
                      height: 50,
                      border: 0,
                      text: const I18n().continuar,
                      progress: state is ConfirmAddressLoading
                          ? Utils.circularProgressButton(size: 20)
                          : null,
                      onPress: enableContinueButton
                          ? () {
                              FocusScope.of(context).unfocus();
                              if (formKey.currentState!.validate()) {
                                BlocProvider.of<AddressRegisterBloc>(context)
                                    .add(ConfirmAddressRegister(
                                        BlocProvider.of<RegisterBloc>(context)
                                            .registerUser!
                                            .id!));
                                //   // SaveAddressRegister(
                                //   //   cep: _cepController.text,
                                //   //   street: _streetController.text,
                                //   //   number: _numberController.text,
                                //   //   complement: _complementController.text,
                                //   //   city: _idCidade ?? 0,
                                //   //   uf: _idUf ?? 0,
                                //   //   district: _districtController.text,
                                //   // ),
                              }
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

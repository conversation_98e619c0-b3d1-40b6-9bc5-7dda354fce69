import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/sign_up/presenter/blocs/create_password/create_password_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/validator_password.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../shared/constants/constants.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/register/register_bloc.dart';

class CreatePasswordPage extends StatelessWidget {
  const CreatePasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CreatePasswordBloc>(
      create: (_) => Modular.get<CreatePasswordBloc>(),
      child: _CreatePasswordView(),
    );
  }
}

class _CreatePasswordView extends StatefulWidget {
  const _CreatePasswordView({
    super.key,
  });

  @override
  State<_CreatePasswordView> createState() => _CreatePasswordViewState();
}

class _CreatePasswordViewState extends State<_CreatePasswordView> {
  final formKey = GlobalKey<FormState>();
  final passController = TextEditingController();
  bool passIsOK = false;

  @override
  void initState() {
    super.initState();
    _setMock();
    // final user = BlocProvider.of<RegisterBloc>(context).registerUser;
    // final bloc = Modular.get<CheckCollaboratorBloc>();
    // print(bloc.response);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<CreatePasswordBloc, CreatePasswordState>(
        listener: (context, state) {
          if (state is ErrorCreatePassword) {
            DialogUtils.showSnackError(context, state.error);
          }
          if (state is SuccessCreatePassword) {
            BlocProvider.of<RegisterBloc>(context)
                .add(ChangeStageEvent(stage: RegisterStage.TERMS));
          }
        },
        builder: (context, state) {
          var isThisStage =
              BlocProvider.of<RegisterBloc>(context).registerStage ==
                  RegisterStage.PASSWORD;
          return isThisStage ? buildBody(state) : Container();
        },
      ),
    );
  }

  Widget buildBody(CreatePasswordState state) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Form(
            key: formKey,
            child: Padding(
              padding: const EdgeInsets.only(
                  left: 16, top: 32, right: 16, bottom: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    const I18n().crie_uma_senha,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(
                    height: 72,
                  ),
                  TextFormFieldApp(
                    typePassword: true,
                    label: const I18n().senha,
                    controller: passController,
                    validator: (value) {
                      if (!passIsOK) {
                        return const I18n().senha_invalida;
                      }

                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  ValidatorPasswordWidget(
                    passwordController: passController,
                    onPasswordOk: (bool status) {
                      passIsOK = status;
                      setState(() {});
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: false,
          child: Container(
            padding: const EdgeInsets.all(16),
            alignment: Alignment.bottomCenter,
            child: ButtonApp(
              enabled: passIsOK,
              width: MediaQuery.of(context).size.width,
              height: 50,
              border: 0,
              text: const I18n().continuar,
              progress: state is LoadingCreatePassword
                  ? Utils.circularProgressButton(size: 20)
                  : null,
              onPress: () {
                FocusScope.of(context).unfocus();
                if (formKey.currentState!.validate()) {
                  BlocProvider.of<CreatePasswordBloc>(context).add(CreateEvent(
                      password: passController.text,
                      userId: BlocProvider.of<RegisterBloc>(context)
                          .registerUser!
                          .id!));
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  _setMock() {
    if (!kReleaseMode && Constants.mock) {
      passController.text = "A234\$6";
    }
  }
}

import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/navigation/named_routes.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/presenter/view/components/others/send_document_widget.dart';
import '../../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/register/register_bloc.dart';
import '../../../blocs/register_documents/register_documents_bloc.dart';

class SendDocumentsPage extends StatelessWidget {
  const SendDocumentsPage({super.key});
  // final String urlSendDocuments;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<RegisterDocumentsBloc>(),
      child: const _DocumentsView(),
    );
  }
}

class _DocumentsView extends StatefulWidget {
  const _DocumentsView({
    super.key,
  });

  @override
  State<_DocumentsView> createState() => __DocumentsViewState();
}

class __DocumentsViewState extends State<_DocumentsView> {
  @override
  void initState() {
    final user = BlocProvider.of<RegisterBloc>(context).registerUser;

    BlocProvider.of<RegisterDocumentsBloc>(context)
        .add(GetUrlSendDocuments(user!.id!));

    super.initState();
  }

  String url = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocConsumer<RegisterDocumentsBloc, RegisterDocumentsState>(
      listener: (context, state) {
        if (state is RegisterDocumentsError) {
          DialogUtils.showSnackError(context, state.error);
        }
        if (state is RegisterDocumentsSuccess) {
          setState(() {
            url = state.url;
          });
        }
      },
      builder: (context, state) {
        final textTheme = Theme.of(context).textTheme;
        return CustomScrollView(
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 39),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      const I18n().verificao_identidade,
                      style: textTheme.bodyMedium,
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Text(
                      const I18n().voce_sera_redirecionado,
                      style: textTheme.bodyMedium,
                      // textAlign: TextAlign.center,
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              ),
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              fillOverscroll: false,
              child: ContainerResponsive(
                padding: const EdgeInsets.all(16),
                alignment: Alignment.bottomCenter,
                child: ButtonApp(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  border: 0,
                  text: const I18n().enviar_documentos,
                  enabled: url.isNotEmpty,
                  progress: state is RegisterDocumentsLoading
                      ? Utils.circularProgressButton(size: 20)
                      : null,
                  onPress: () async {
                    if (url.isNotEmpty) {
                      if (Platform.isIOS) {
                        await launchUrlString(url,
                            mode: LaunchMode.inAppBrowserView,
                            browserConfiguration: const BrowserConfiguration(
                              showTitle: true,
                            ),
                            webViewConfiguration: const WebViewConfiguration(
                              enableJavaScript: true,
                              enableDomStorage: true,
                            )).whenComplete(() {
                          pop();
                        });
                      } else {
                        await push(
                          Routes.signUpWebviewDocumentos,
                          args: url, // alterar isso
                        ).then(
                          (value) => pop(),
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    ));
  }

  // setLoading(bool value) {
  //   loading.value = value;
  // }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/utils/check_permissions.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
// Import for iOS/macOS features.
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../../shared/utils/utils.dart';

// enum TyperResultDocuments { success, failed }

class WebviewSendDocuments extends StatefulWidget {
  const WebviewSendDocuments({super.key});
  // final String urlSendDocuments;/

  @override
  State<WebviewSendDocuments> createState() => _WebviewSendDocumentsState();
}

class _WebviewSendDocumentsState extends State<WebviewSendDocuments> {
  late WebViewController controller;

  @override
  void initState() {
    final url = Modular.args.data?['args'] as String?;

    if (url != null && url.isNotEmpty) {
      final uri = Uri.parse(url);

      const params = PlatformWebViewControllerCreationParams();

      controller = WebViewController.fromPlatformCreationParams(params)
        ..setJavaScriptMode(JavaScriptMode.unrestricted);
      // ..setMediaPlaybackRequiresUserGesture(false);

      if (controller.platform is AndroidWebViewController) {
        final platform = (controller.platform as AndroidWebViewController);
        platform
          ..setMediaPlaybackRequiresUserGesture(false)
          ..setAllowFileAccess(true)
          ..setOnPlatformPermissionRequest(
            (request) async {
              log(request.types.toString());
              for (var type in request.types) {
                log(type.name, name: 'requestType');
                if (type.name == 'camera') {
                  final result1 = await CheckPermissions.checkCamera();
                  // final result2 = await CheckPermissions.checkPermissionFiles();

                  (result1) ? request.grant() : request.deny();
                }
              }
            },
          );
      } else if (controller.platform is WebKitWebViewController) {
        // final platform = (controller.platform as WebKitWebViewController);
      }

      controller
        ..clearLocalStorage()
        ..loadRequest(uri)
        ..setJavaScriptMode(JavaScriptMode.unrestricted);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: const I18n().documentacao.toUpperCase(),
        actions: [Utils.buttonIcHelp(context)],
        clickBack: () {
          pop();
        },
      ),
      body: SafeArea(child: WebViewWidget(controller: controller)),
    );
  }
}

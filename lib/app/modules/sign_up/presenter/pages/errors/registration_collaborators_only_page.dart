import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class RegistrationCollaboratorsOnlyPage extends StatelessWidget {
  const RegistrationCollaboratorsOnlyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    Utils.setScreeenResponsive(
      context: context,
    );

    return Scaffold(
      // appBar: AppBarApp(
      //   showLine: false,
      //   showBack: false,
      // ),
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.only(
            left: 16,
            top: 30,
            right: 16,
            bottom: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                const I18n().nao_podemos_continuar_com_cadastro,
                style: textTheme.displayLarge,
              ),
              SizedBoxResponsive(
                height: 16,
              ),
              Text(
                const I18n().cadastro_exclusivo_para_funcionarios,
                style: textTheme.bodyMedium,
              ),
              // SizedBoxResponsive(
              //   height: 60,
              // ),
              Expanded(
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: <Widget>[
                    ImageUtils.imgNaoFuncionario(),
                    ButtonApp(
                      enabled: true,
                      width: MediaQuery.of(context).size.width,
                      text: const I18n().sair,
                      onPress: () {
                        pop();
                      },
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

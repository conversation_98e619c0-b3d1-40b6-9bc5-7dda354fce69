import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class AlreadyRegisteredPage extends StatelessWidget {
  const AlreadyRegisteredPage({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    Utils.setScreeenResponsive(
      context: context,
    );

    return Scaffold(
      // appBar: AppBarApp(
      //   actions: <Widget>[
      //     Utils.buildIcHelp(context),
      //   ],
      // ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(
            left: 16,
            top: 16,
            right: 16,
            bottom: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                const I18n().already_registered,
                style: textTheme.displayLarge,
              ),
              SizedBoxResponsive(
                height: 16,
              ),
              Text(
                const I18n().account_created_success_login,
                style: textTheme.bodyMedium,
              ),
              // SizedBoxResponsive(
              //   height: 60,
              // ),
              Expanded(
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: <Widget>[
                    Positioned.fill(
                        bottom: 38, child: ImageUtils.imgTermoPendente()),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ButtonApp(
                          width: MediaQuery.of(context).size.width,
                          text: const I18n().fazer_login,
                          onPress: () {
                            navigate(Routes.intro);
                          },
                        ),
                        const SizedBox(height: 16),
                        ButtonApp(
                          width: MediaQuery.of(context).size.width,
                          text: const I18n().voltar,
                          border: 3,
                          buttonColor: ColorsApp.scaffoldColor,
                          borderColor: ColorsApp.verde,
                          onPress: () {
                            pop();
                          },
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

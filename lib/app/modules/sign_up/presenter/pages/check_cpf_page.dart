import 'package:cpf_cnpj_validator/cpf_validator.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/navigation/named_routes.dart';
import '../../../../shared/navigation/navigator_app.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../shared/presenter/view/components/others/icon_button_help.dart';
import '../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../shared/constants/constants.dart';
import '../../../../shared/utils/utils.dart';
import '../../../../shared/utils/validator_utils.dart';
import '../blocs/check_collaborator/check_collaborator_bloc.dart';

class CheckCpfPage extends StatelessWidget {
  const CheckCpfPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CheckCollaboratorBloc>(
      create: (context) => Modular.get<CheckCollaboratorBloc>(),
      child: const _CheckCpfPage(),
    );
  }
}

class _CheckCpfPage extends StatefulWidget {
  const _CheckCpfPage({super.key});

  @override
  __CheckCpfPageState createState() => __CheckCpfPageState();
}

class __CheckCpfPageState extends State<_CheckCpfPage> {
  bool continueButton = false;
  final cpfController = MaskedTextController(mask: '000.000.000-00');
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _setMock();
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.cadastrar,
        actions: const <Widget>[IconButtonHelp()],
      ),
      body: BlocConsumer<CheckCollaboratorBloc, CheckCollaboratorState>(
        listener: (context, state) {
          if (state is ErrorRegistration) {
            DialogUtils.showSnackError(context, state.error);
          }

          if (state is CheckCollaboratorResult) {
            var user = state.registerUserResponse;
            user.cpf ??= cpfController.text;

            if (user.isCollaborator != null && user.isRegistered != null) {
              if (user.isCollaborator == false) {
                push(Routes.signUpErrorCollaboratorOnly);
              } else {
                if (user.isRegistered == false) {
                  if (user.olderAge!) {
                    push(Routes.signUpRegister, args: user);
                  } else {
                    SnackBarApp.showSnack(
                      context: context,
                      message:
                          "Cadastro permitido apenas para maiores de 18 anos.",
                      success: false,
                    );
                  }
                } else {
                  // se já possuem cadastro iniciado...
                  if (user.finishedStage) {
                    push(Routes.signUpErrorAlreadyRegistered);
                  } else if (user.documentsDeniedStage) {
                    push(Routes.signUpRegister, args: user);
                    // TODO: CRIAR TELA PARA DOCUMENTACAO  NEGADA
                  } else {
                    push(Routes.signUpRegister, args: user);
                  }
                }
              }
            }
          }
        },
        builder: (context, state) => CustomScrollView(
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const SizedBox(height: 32),
                    Text(
                      const I18n().title_what_is_your_cpf,
                      style: textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 72),
                    Form(
                      key: _formKey,
                      child: TextFormFieldApp(
                        label: I18n.of(context)!.cpf,
                        controller: cpfController,
                        textInputType: TextInputType.number,
                        onChanged: (text) {
                          if (text.length == 14) {
                            _formKey.currentState?.validate();
                          }

                          setState(() {
                            continueButton =
                                (cpfController.text.length == 14) &&
                                    (CPFValidator.isValid(cpfController.text));
                          });
                        },
                        validator: (input) => ValidatorUtils.validateCpf(
                            context: context, value: input),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverFillRemaining(
              fillOverscroll: false,
              hasScrollBody: false,
              child: Container(
                alignment: Alignment.bottomCenter,
                padding: const EdgeInsets.only(
                  left: 16,
                  right: 16,
                  bottom: 40,
                ),
                child: ButtonApp(
                  enabled: continueButton,
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  border: 0,
                  text: const I18n().continuar,
                  progress: state is LoadingRegistration
                      ? Utils.circularProgressButton(size: 20)
                      : null,
                  onPress: () {
                    final cpf = Utils.formatOnlyNumbers(cpfController.text);

                    BlocProvider.of<CheckCollaboratorBloc>(context)
                        .add(SearchEmployeeEvent(cpf));
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _setMock() {
    if (!kReleaseMode && Constants.mock) {
//       cpfController.text = "07323918570";
      cpfController.text = "40431873860";
      continueButton = true;
    }
  }
}

import 'package:flutter/material.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/themes/styles/icons_app.dart';

const listMaritalStatus = {
  'SOLTEIRO': 'Solteiro(a)',
  'CASADO': 'Casado(a)',
  'DIVORCIADO': 'Divorciado(a)',
  'VIUVO': 'Viúvo(a)',
  'SEPARADO': 'Separado(a)',
  'UNIAO_ESTAVEL': 'União Estável',
};

class SelectMaritalStatusWidget extends StatefulWidget {
  const SelectMaritalStatusWidget({
    super.key,
    required this.selected,
    this.onChanged,
  });

  final String? selected;
  final void Function(String?)? onChanged;

  @override
  State<SelectMaritalStatusWidget> createState() =>
      _SelectMaritalStatusWidgetState();
}

class _SelectMaritalStatusWidgetState extends State<SelectMaritalStatusWidget> {
  late String? selected;
  late void Function(String?)? onChanged;
  @override
  void initState() {
    selected = widget.selected;
    onChanged = widget.onChanged;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selected,
      isExpanded: true,
      iconSize: 30,
      icon: IconsApp.icDropDown(),
      validator: (value) {
        if (value == null) {
          return const I18n().campo_obrigatorio;
        }

        return null;
      },
      decoration: _inputDecorationDropdown(),
      style: Theme.of(context).textTheme.bodyMedium,
      items: listMaritalStatus.entries
          .map(
            (item) => DropdownMenuItem<String>(
              value: item.key,
              child: Text(item.value),
            ),
          )
          .toList(),
      onChanged: onChanged,
    );
  }

  InputDecoration _inputDecorationDropdown() {
    return InputDecoration(
      fillColor: Colors.white,
      filled: true,
      label: Text(
        const I18n().marital_status,
        style: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: ColorsApp.cinza[600]),
      ),
      hintStyle: Theme.of(context)
          .textTheme
          .bodyMedium!
          .copyWith(color: ColorsApp.cinza[600]),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(
          width: 0.5,
          color: Colors.transparent,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          width: 0.5,
          color: ColorsApp.azul[100]!,
        ),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: BorderSide.none,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
    );
  }
}

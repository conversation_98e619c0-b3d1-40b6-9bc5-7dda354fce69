import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/sign_up/domain/models/occupation_response.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/themes/styles/icons_app.dart';
import '../../../domain/models/politically_exposed_person_response.dart';

class SelectOccupationsWidget extends StatelessWidget {
  const SelectOccupationsWidget({
    super.key,
    required this.items,
    required this.selected,
    this.onChanged,
    required this.loading,
  });

  final bool loading;
  final List<OccupationResponse> items;
  final OccupationResponse? selected;
  final void Function(OccupationResponse?)? onChanged;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<OccupationResponse>(
      value: selected,
      isExpanded: true,
      iconSize: 30,
      icon: loading
          ? Utils.circularProgressButton(size: 20)
          : IconsApp.icDropDown(),
      validator: (value) {
        if (value == null) {
          return const I18n().campo_obrigatorio;
        }

        return null;
      },
      decoration: _inputDecorationDropdown(context),
      style: Theme.of(context).textTheme.bodyMedium,
      items: items
          .map(
            (item) => DropdownMenuItem<OccupationResponse>(
              value: item,
              child: Text(item.value),
            ),
          )
          .toList(),
      onChanged: onChanged,
    );
  }
  //  (value) {
  //             setState(() {
  //   ocupacao = value;
  //   _verificarPreenchimentoCampos();
  // });
  //           },

  InputDecoration _inputDecorationDropdown(context) {
    return InputDecoration(
      fillColor: Colors.white,
      filled: true,
      label: Text(
        const I18n().occupation,
        style: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: ColorsApp.cinza[600]),
      ),
      hintStyle: Theme.of(context)
          .textTheme
          .bodyMedium!
          .copyWith(color: ColorsApp.cinza[600]),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(
          width: 0.5,
          color: Colors.transparent,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          width: 0.5,
          color: ColorsApp.azul[100]!,
        ),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: BorderSide.none,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
    );
  }
}

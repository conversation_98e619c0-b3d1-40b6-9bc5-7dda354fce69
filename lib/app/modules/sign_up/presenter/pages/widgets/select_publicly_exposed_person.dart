import 'package:flutter/material.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/themes/styles/icons_app.dart';
import '../../../../../shared/utils/utils.dart';
import '../../../domain/models/politically_exposed_person_response.dart';

class SelectPubliclyExposedPerson extends StatefulWidget {
  const SelectPubliclyExposedPerson({
    super.key,
    required this.items,
    required this.selected,
    this.onChanged,
    required this.loading,
  });

  final bool loading;
  final List<PoliticallyExposedPersonResponse> items;
  final PoliticallyExposedPersonResponse? selected;
  final void Function(PoliticallyExposedPersonResponse?)? onChanged;

  @override
  State<SelectPubliclyExposedPerson> createState() =>
      _SelectPubliclyExposedPersonState();
}

class _SelectPubliclyExposedPersonState
    extends State<SelectPubliclyExposedPerson> {
  late List<PoliticallyExposedPersonResponse> items;
  late PoliticallyExposedPersonResponse? selected;
  late void Function(PoliticallyExposedPersonResponse?)? onChanged;
  @override
  void initState() {
    items = widget.items;
    selected = widget.selected;
    onChanged = widget.onChanged;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<PoliticallyExposedPersonResponse>(
      value: selected,
      isExpanded: true,
      isDense: false,
      iconSize: 30,
      icon: widget.loading
          ? Utils.circularProgressButton(size: 20)
          : IconsApp.icDropDown(),
      validator: (value) {
        if (value == null) {
          return const I18n().campo_obrigatorio;
        }

        return null;
      },
      decoration: _inputDecorationDropdown(),
      style: Theme.of(context).textTheme.bodyMedium,
      items: items
          .map(
            (item) => DropdownMenuItem<PoliticallyExposedPersonResponse>(
              value: item,
              child: Text(item.value),
            ),
          )
          .toList(),
      onChanged: onChanged,
    );
  }
  //  (value) {
  //             setState(() {
  //               value = value;
  //               _verificarPreenchimentoCampos();
  //             });
  //           },

  InputDecoration _inputDecorationDropdown() {
    return InputDecoration(
      fillColor: Colors.white,
      filled: true,
      label: Text(
        const I18n().nivel_exposicao_politica,
        style: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: ColorsApp.cinza[600]),
      ),
      hintStyle: Theme.of(context)
          .textTheme
          .bodyMedium!
          .copyWith(color: ColorsApp.cinza[600]),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(
          width: 0.5,
          color: Colors.transparent,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          width: 0.5,
          color: ColorsApp.azul[100]!,
        ),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: BorderSide.none,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
    );
  }
}

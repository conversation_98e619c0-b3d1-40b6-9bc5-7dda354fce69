import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/constants/nacionalities_list_const.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/themes/styles/icons_app.dart';

class SelectNacionalityWidget extends StatefulWidget {
  const SelectNacionalityWidget({
    super.key,
    required this.selected,
    this.onChanged,
  });

  final String? selected;
  final void Function(String?)? onChanged;

  @override
  State<SelectNacionalityWidget> createState() => _SelectWidgetState();
}

class _SelectWidgetState extends State<SelectNacionalityWidget> {
  late String? selected;
  late void Function(String?)? onChanged;
  @override
  void initState() {
    selected = widget.selected;
    onChanged = widget.onChanged;
    super.initState();
  }

  String label = const I18n().nacionality;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selected,
      isExpanded: true,
      iconSize: 30,
      icon: IconsApp.icDropDown(),
      validator: (value) {
        if (value == null) {
          return const I18n().campo_obrigatorio;
        }

        return null;
      },
      decoration: _inputDecorationDropdown(),
      style: Theme.of(context).textTheme.bodyMedium,
      items: Nacionalities.list
          .map(
            (item) => DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            ),
          )
          .toList(),
      onChanged: onChanged,
    );
  }

  InputDecoration _inputDecorationDropdown() {
    return InputDecoration(
      fillColor: Colors.white,
      filled: true,
      label: Text(
        label,
        style: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: ColorsApp.cinza[600]),
      ),
      hintStyle: Theme.of(context)
          .textTheme
          .bodyMedium!
          .copyWith(color: ColorsApp.cinza[600]),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(
          width: 0.5,
          color: Colors.transparent,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          width: 0.5,
          color: ColorsApp.azul[100]!,
        ),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: BorderSide.none,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(11),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
    );
  }
}

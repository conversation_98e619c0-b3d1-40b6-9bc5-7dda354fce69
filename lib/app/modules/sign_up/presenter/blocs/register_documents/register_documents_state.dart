part of 'register_documents_bloc.dart';

sealed class RegisterDocumentsState extends Equatable {
  const RegisterDocumentsState();

  @override
  List<Object> get props => [];
}

final class RegisterDocumentsInitial extends RegisterDocumentsState {}

final class RegisterDocumentsSuccess extends RegisterDocumentsState {
  RegisterDocumentsSuccess({required this.url});
  final String url;
}

final class RegisterDocumentsLoading extends RegisterDocumentsState {}

final class RegisterDocumentsError extends RegisterDocumentsState {
  const RegisterDocumentsError(this.error);

  final ErrorResponse error;
}

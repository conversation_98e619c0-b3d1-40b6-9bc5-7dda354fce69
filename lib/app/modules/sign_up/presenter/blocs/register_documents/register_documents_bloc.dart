import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/documents_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'register_documents_event.dart';
part 'register_documents_state.dart';

class RegisterDocumentsBloc
    extends Bloc<RegisterDocumentsEvent, RegisterDocumentsState> {
  final IDocumentsUsecase _documentsUsecase;
  RegisterDocumentsBloc(this._documentsUsecase)
      : super(RegisterDocumentsInitial()) {
    on<RegisterDocumentsEvent>((event, emit) async {
      if (event is GetUrlSendDocuments) {
        emit(RegisterDocumentsLoading());
        try {
          final result =
              await _documentsUsecase.getUrlSendDocuments(event.userId);
          if (result != null) {
            emit(RegisterDocumentsSuccess(url: result));
          } else {
            for (int i = 0; i < 3; i++) {
              await Future.delayed(const Duration(seconds: 1));
              final result =
                  await _documentsUsecase.getUrlSendDocuments(event.userId);
              if (result != null) {
                emit(RegisterDocumentsSuccess(url: result));
                break;
              }
            }
          }
        } on ErrorResponse catch (e) {
          emit(RegisterDocumentsError(e));
        }
      }
    });
  }
}

part of 'register_documents_bloc.dart';

sealed class RegisterDocumentsEvent extends Equatable {
  const RegisterDocumentsEvent();

  @override
  List<Object> get props => [];
}

final class GetUrlSendDocuments implements RegisterDocumentsEvent {
  final String userId;

  GetUrlSendDocuments(this.userId);
  @override
  // TODO: implement props
  List<Object> get props => throw UnimplementedError();

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

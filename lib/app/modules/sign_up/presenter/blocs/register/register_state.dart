// ignore_for_file: public_member_api_docs, sort_constructors_first

part of 'register_bloc.dart';

enum RegisterStage {
  BASIC_DATA,
  ADDRESS,
  PHONE_CHECK,
  PASSWORD,
  TERMS,
  DOCUMENTS,
  ANALYSIS,
  ERROR_COLLABORATOR_ONLY,
  ERROR_ALREADY_REGISTERED,
}

abstract class RegisterState extends Equatable {
  const RegisterState(
      {this.stage, this.changePage = false, this.registerUserResponse});
  final RegisterUserResponse? registerUserResponse;
  final RegisterStage? stage;
  final bool changePage;

  @override
  List<Object?> get props => [registerUserResponse, stage, changePage];

  @override
  bool get stringify => true;
}

final class RegisterInitial extends RegisterState {
  const RegisterInitial({super.registerUserResponse});
}

final class RegisterLoading extends RegisterState {
  const RegisterLoading({super.registerUserResponse});
}

final class RegisterError extends RegisterState {
  final ErrorResponse error;

  const RegisterError(
    this.error, {
    super.registerUserResponse,
  });
}

final class RegisterRequestSuccess extends RegisterState {
  const RegisterRequestSuccess(
      {super.registerUserResponse,
      super.changePage = true,
      required super.stage});
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'register_bloc.dart';

sealed class RegisterEvent {}

class SetRegisterUser implements RegisterEvent {
  final RegisterUserResponse? user;
  final bool changePage;
  SetRegisterUser(this.user, this.changePage);
}

class ChangeStageEvent implements RegisterEvent {
  ChangeStageEvent(
      {this.registerUser, required this.stage, this.changePage = true});

  final RegisterUserResponse? registerUser;
  final RegisterStage stage;
  final bool changePage;
}

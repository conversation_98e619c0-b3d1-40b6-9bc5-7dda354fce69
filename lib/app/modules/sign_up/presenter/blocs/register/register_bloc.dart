import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../shared/data/models/register_user_response.dart';

part 'register_event.dart';
part 'register_state.dart';

class RegisterBloc extends Bloc<RegisterEvent, RegisterState> {
  RegisterUserResponse? registerUser;
  RegisterStage? registerStage;

  RegisterBloc() : super(const RegisterInitial()) {
    on<RegisterEvent>((event, emit) async {
      // if (event is SetRegisterUser) {
      //   registerUser = event.user;
      //   RegisterStage? toStage;
      //   if (registerUser!.isRegistered == true) {
      //     // 2 stage
      //     if (registerUser!.addressStage) {
      //       toStage = RegisterStage.ADDRESS;
      //     }
      //     // 3 stage
      //     if (registerUser!.checkPhoneStage) {
      //       toStage = RegisterStage.PHONE_CHECK;
      //     }
      //     // 4 stage
      //     if (registerUser!.passwordStage) {
      //       toStage = RegisterStage.PASSWORD;
      //     }
      //     // 5 stage
      //     if (registerUser!.termoCompromissoEstagio) {
      //       toStage = RegisterStage.TERMS;
      //     }
      //     // 6 stage
      //     if (registerUser!.documentsStage ||
      //         registerUser!.pendingDocumentsStage) {
      //       toStage = RegisterStage.DOCUMENTS;
      //     }

      //     if (registerUser!.analysisDocumentsStage) {
      //       toStage = RegisterStage.ANALYSIS;
      //       // push(Routes.signUpSendDocuments, args: user);
      //     }
      //   } else if (registerUser!.isCollaborator == true) {
      //     toStage = RegisterStage.BASIC_DATA;
      //   }
      //   emit(RegisterRequestSuccess(
      //     registerUserResponse: registerUser,
      //     changePage: event.changePage,
      //     stage: toStage,
      //   ));
      // }

      if (event is ChangeStageEvent) {
        emit(const RegisterLoading());
        if (event.registerUser != null) {
          registerUser = event.registerUser;
        }
        // registerUser!.stage = event.stage.name;
        registerStage = event.stage;
        emit(RegisterRequestSuccess(
            registerUserResponse: event.registerUser ?? registerUser,
            stage: event.stage,
            changePage: true));
      }
    });
  }
}

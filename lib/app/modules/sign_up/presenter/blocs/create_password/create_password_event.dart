part of 'create_password_bloc.dart';

sealed class CreatePasswordEvent extends Equatable {
  const CreatePasswordEvent();

  @override
  List<Object> get props => [];
}

final class CreateEvent implements CreatePasswordEvent {
  final String password;
  final String userId;
  CreateEvent({required this.password, required this.userId});

  @override
  List<Object> get props => [password, userId];

  @override
  bool? get stringify => true;
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/create_password_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'create_password_event.dart';
part 'create_password_state.dart';

class CreatePasswordBloc
    extends Bloc<CreatePasswordEvent, CreatePasswordState> {
  final CreatePasswordUsecase _createPasswordUsecase;
  CreatePasswordBloc(this._createPasswordUsecase)
      : super(CreatePasswordInitial()) {
    on<CreatePasswordEvent>((event, emit) async {
      if (event is CreateEvent) {
        emit(LoadingCreatePassword());

        try {
          final result = await _createPasswordUsecase(
              userId: event.userId, password: event.password);

          emit(SuccessCreatePassword());
        } on ErrorResponse catch (e) {
          emit(ErrorCreatePassword(e));
        }
      }
    });
  }
}

part of 'create_password_bloc.dart';

sealed class CreatePasswordState extends Equatable {
  const CreatePasswordState();

  @override
  List<Object> get props => [];
}

final class CreatePasswordInitial extends CreatePasswordState {}

final class LoadingCreatePassword extends CreatePasswordState {}

final class ErrorCreatePassword extends CreatePasswordState {
  final ErrorResponse error;

  const ErrorCreatePassword(this.error);
}

final class SuccessCreatePassword extends CreatePasswordState {}

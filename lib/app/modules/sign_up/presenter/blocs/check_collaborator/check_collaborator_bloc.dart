import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../shared/data/models/register_user_response.dart';
import '../../../../../shared/errors/error_response.dart';
import '../../../domain/usecases/check_is_collaborator_usecase.dart';

part 'check_collaborator_event.dart';
part 'check_collaborator_state.dart';

class CheckCollaboratorBloc
    extends Bloc<CheckCollaboratorEvent, CheckCollaboratorState> {
  final ICheckIsCollaboratorUsecase _checkIsCollaborator;

  CheckCollaboratorBloc(this._checkIsCollaborator)
      : super(RegistrationInitial()) {
    on<CheckCollaboratorEvent>((event, emit) async {
      if (event is SearchEmployeeEvent) {
        emit(LoadingRegistration());

        try {
          var result = await _checkIsCollaborator(event.cpf);
          result.cpf ??= event.cpf;
          emit(CheckCollaboratorResult(
            registerUserResponse: result,
          ));
        } on ErrorResponse catch (e) {
          emit(ErrorRegistration(e));
        }
      }
    });
  }
}

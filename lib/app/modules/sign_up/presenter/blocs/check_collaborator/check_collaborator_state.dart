part of 'check_collaborator_bloc.dart';

sealed class CheckCollaboratorState extends Equatable {
  const CheckCollaboratorState();

  @override
  List<Object> get props => [];
}

final class RegistrationInitial extends CheckCollaboratorState {}

final class CheckCollaboratorResult extends CheckCollaboratorState {
  // final bool isCollaborator;
  // final String cpfOnlyNumbers;
  // final bool isRegistered;
  final RegisterUserResponse registerUserResponse;

  const CheckCollaboratorResult({required this.registerUserResponse});
}

final class LoadingRegistration extends CheckCollaboratorState {}

final class ErrorRegistration extends CheckCollaboratorState {
  final ErrorResponse error;

  const ErrorRegistration(this.error);
}

part of 'basic_data_bloc.dart';

sealed class BasicDataState extends Equatable {
  const BasicDataState();

  @override
  List<Object> get props => [];
}

final class BasicDataInitial extends BasicDataState {}

final class LoadingPageState extends BasicDataState {}

final class LoadingOptionsState extends BasicDataState {}

final class FetchOptionsSuc<PERSON> extends BasicDataState {
  const FetchOptionsSuccess(
      {required this.listPEPOptions, required this.listOccupationsOptions});

  final List<PoliticallyExposedPersonResponse> listPEPOptions;
  final List<OccupationResponse> listOccupationsOptions;
}

final class ErrorBasicDataState extends BasicDataState {
  final ErrorResponse error;

  const ErrorBasicDataState(this.error);
}

final class RegisterBasicDataSuccess extends BasicDataState {
  final RegisterUserResponse registerUserResponse;

  const RegisterBasicDataSuccess(this.registerUserResponse);
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'basic_data_bloc.dart';

sealed class BasicDataEvent implements Equatable {}

class FecthOptionsEvent extends BasicDataEvent {
  @override
  List<Object?> get props => [];

  @override
  bool? get stringify => throw UnimplementedError();
}

class RegisterBasicDataEvent extends BasicDataEvent {
  final BasicDataRequest basicData;

  RegisterBasicDataEvent({
    required this.basicData,
  });

  @override
  List<Object?> get props => [
        basicData,
      ];

  @override
  bool? get stringify => true;

  BasicDataRequest get request => basicData;
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/sign_up/domain/models/occupation_response.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/basic_data_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../shared/data/models/register_user_response.dart';
import '../../../data/models/basic_data_request.dart';
import '../../../domain/models/politically_exposed_person_response.dart';

part 'basic_data_event.dart';
part 'basic_data_state.dart';

class BasicDataBloc extends Bloc<BasicDataEvent, BasicDataState>
    implements Disposable {
  final IBasicDataUsecase _basicDataUsecase;
  BasicDataBloc(this._basicDataUsecase) : super(BasicDataInitial()) {
    on<BasicDataEvent>((event, emit) async {
      if (event is FecthOptionsEvent) {
        emit(LoadingOptionsState());
        try {
          // final pepOptions = await _basicDataUsecase.getPepOptions();

          final occupationsOptions =
              await _basicDataUsecase.getOccupationsOptions();

          emit(FetchOptionsSuccess(
            listPEPOptions: [],
            listOccupationsOptions: occupationsOptions,
          ));
        } on ErrorResponse catch (error) {
          emit(ErrorBasicDataState(error));
        }
      }

      if (event is RegisterBasicDataEvent) {
        emit(LoadingPageState());

        try {
          final result = await _basicDataUsecase.createUser(event.basicData);
          emit(RegisterBasicDataSuccess(result));
        } on ErrorResponse catch (error) {
          emit(ErrorBasicDataState(error));
        }
      }
    });
  }

  @override
  void dispose() {
    print("BasicDataBloc dispose() called, about to call close() now...");
    close();
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../domain/usecases/terms_usecase.dart';

part 'terms_event.dart';
part 'terms_state.dart';

class TermsBloc extends Bloc<TermsEvent, TermsState> {
  final ITermsRegisterUsecase _termsUsecase;
  TermsBloc(this._termsUsecase) : super(TermsInitial()) {
    on<TermsEvent>((event, emit) async {
      if (event is GetTermosEvent) {
        emit(LoadingCenterTerms());
        try {
          final result = await _termsUsecase.getTerms();
          emit(GetTermsHtmlSuccessState(
            url: result.content!,
          ));
        } on ErrorResponse catch (e) {
          emit(ErrorTermsState(e));
        }
      }

      if (event is AcceptTermsEvent) {
        emit(LoadAcceptTerms());
        try {
          await _termsUsecase.acceptTerms(event.userID);
          emit(AcceptTermsSuccessState());
        } on ErrorResponse catch (e) {
          emit(ErrorTermsState(e));
        }
      }
      if (event is DeniedTermsEvent) {
        emit(LoadDeniedTerms());
        try {
          await _termsUsecase.rejectTerms(event.userID);
          emit(DeniedTermsSuccessState());
        } on ErrorResponse catch (e) {
          emit(ErrorTermsState(e));
        }
      }
    });
  }
}

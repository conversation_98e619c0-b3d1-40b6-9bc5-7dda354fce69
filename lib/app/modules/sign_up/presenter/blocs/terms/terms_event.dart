// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'terms_bloc.dart';

sealed class TermsEvent extends Equatable {
  const TermsEvent();

  @override
  List<Object> get props => [];
}

class GetTermosEvent implements TermsEvent {
  GetTermosEvent();
  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

class AcceptTermsEvent implements TermsEvent {
  final String userID;

  AcceptTermsEvent({
    required this.userID,
  });

  @override
  List<Object> get props => [userID];

  @override
  bool? get stringify => false;
}

class DeniedTermsEvent implements TermsEvent {
  final String userID;

  DeniedTermsEvent({
    required this.userID,
  });

  @override
  List<Object> get props => [userID];

  @override
  bool? get stringify => false;
}

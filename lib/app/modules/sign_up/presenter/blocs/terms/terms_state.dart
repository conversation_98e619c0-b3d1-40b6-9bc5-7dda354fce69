part of 'terms_bloc.dart';

sealed class TermsState extends Equatable {
  const TermsState();

  @override
  List<Object> get props => [];
}

final class TermsInitial extends TermsState {}

final class LoadingCenterTerms extends TermsState {}

final class LoadAcceptTerms extends TermsState {}

final class LoadDeniedTerms extends TermsState {}

final class GetTermsHtmlSuccessState extends TermsState {
  final String url;

  const GetTermsHtmlSuccessState({required this.url});
}

final class AcceptTermsSuccessState extends TermsState {}

final class DeniedTermsSuccessState extends TermsState {}

final class ErrorTermsState extends TermsState {
  final ErrorResponse error;

  const ErrorTermsState(this.error);
}

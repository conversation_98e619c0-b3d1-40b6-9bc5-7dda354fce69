part of 'phone_check_bloc.dart';

sealed class PhoneCheckState extends Equatable {
  const PhoneCheckState();

  @override
  List<Object> get props => [];
}

final class PhoneCheckInitial extends PhoneCheckState {}

final class ErrorPhoneCheck extends PhoneCheckState {
  final ErrorResponse error;

  const ErrorPhoneCheck(this.error);
}

final class LoadingCheckCode extends PhoneCheckState {}

final class LoadingResendCodePhoneCheck extends PhoneCheckState {}

final class SuccessSendSmsCode extends PhoneCheckState {}

final class CodeVerifiedSuccessfully extends PhoneCheckState {}

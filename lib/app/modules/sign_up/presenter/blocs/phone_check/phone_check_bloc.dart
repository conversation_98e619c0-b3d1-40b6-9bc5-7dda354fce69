import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/send_sms_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'phone_check_event.dart';
part 'phone_check_state.dart';

class PhoneCheckBloc extends Bloc<PhoneCheckEvent, PhoneCheckState> {
  final ISendSmsUsecase sendSmsUsecase;

  PhoneCheckBloc(this.sendSmsUsecase) : super(PhoneCheckInitial()) {
    on<PhoneCheckEvent>((event, emit) async {
      if (event is SendSmsCodeEvent) {
        emit(LoadingResendCodePhoneCheck());

        try {
          await sendSmsUsecase(event.userId);

          emit(SuccessSendSmsCode());
        } on ErrorResponse catch (e) {
          emit(ErrorPhoneCheck(e));
        }
      }

      if (event is CheckSmsCodeEvent) {
        emit(LoadingCheckCode());

        try {
          final result = await sendSmsUsecase.checkSmsCode(
              userId: event.userId, code: event.code);

          if (result == true) {
            emit(CodeVerifiedSuccessfully());
          } else {
            emit(ErrorPhoneCheck(
                ErrorResponse(message: I18n().codigo_invalido)));
          }
        } on ErrorResponse catch (e) {
          emit(ErrorPhoneCheck(e));
        }
      }
    });
  }
}

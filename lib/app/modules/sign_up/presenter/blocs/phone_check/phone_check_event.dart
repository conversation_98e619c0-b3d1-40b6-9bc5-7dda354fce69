part of 'phone_check_bloc.dart';

sealed class PhoneCheckEvent extends Equatable {
  const PhoneCheckEvent();

  @override
  List<Object> get props => [];
}

final class SendSmsCodeEvent extends PhoneCheckEvent {
  final String userId;

  const SendSmsCodeEvent(this.userId);
}

final class CheckSmsCodeEvent extends PhoneCheckEvent {
  final String userId;
  final String code;

  const CheckSmsCodeEvent({required this.userId, required this.code});
}

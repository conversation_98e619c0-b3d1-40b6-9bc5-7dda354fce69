part of 'address_register_bloc.dart';

sealed class AddressRegisterState extends Equatable {
  const AddressRegisterState();

  @override
  List<Object> get props => [];
}

final class AddressRegisterInitial extends AddressRegisterState {}

final class GetAddressLoading extends AddressRegisterState {}

final class GetAddressSuccess extends AddressRegisterState {
  final AddressResponse addressResponse;

  const GetAddressSuccess(this.addressResponse);
}

final class ConfirmAddressSuccessState extends AddressRegisterState {
  final RegisterUserResponse registerUserResponse;

  const ConfirmAddressSuccessState(this.registerUserResponse);
}

final class ConfirmAddressLoading extends AddressRegisterState {}

final class ErrorAddressState extends AddressRegisterState {
  final ErrorResponse error;

  const ErrorAddressState(this.error);
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'address_register_bloc.dart';

sealed class AddressRegisterEvent extends Equatable {
  const AddressRegisterEvent();

  @override
  List<Object> get props => [];
}

class GetAddressRegister implements AddressRegisterEvent {
  final String userId;

  GetAddressRegister(this.userId);
  @override
  bool? get stringify => true;

  @override
  List<Object> get props => [];
}

class ConfirmAddressRegister implements AddressRegisterEvent {
  final String userId;

  ConfirmAddressRegister(this.userId);
  @override
  bool? get stringify => true;

  @override
  List<Object> get props => [];
}

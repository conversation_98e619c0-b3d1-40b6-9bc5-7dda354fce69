import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/sign_up/domain/usecases/address_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../shared/data/models/register_user_response.dart';
import '../../../../../shared/data/models/address_response.dart';

part 'address_register_event.dart';
part 'address_register_state.dart';

class AddressRegisterBloc
    extends Bloc<AddressRegisterEvent, AddressRegisterState> {
  final IAddressUsecase _addressUsecase;
  AddressRegisterBloc(this._addressUsecase) : super(AddressRegisterInitial()) {
    on<AddressRegisterEvent>((event, emit) async {
      if (event is GetAddressRegister) {
        emit(GetAddressLoading());

        try {
          final result = await _addressUsecase.getAddress(event.userId);

          emit(GetAddressSuccess(result));
        } on ErrorResponse catch (e) {
          emit(ErrorAddressState(e));
        }
      }
      if (event is ConfirmAddressRegister) {
        emit(ConfirmAddressLoading());

        try {
          final result = await _addressUsecase.confirmAddress(event.userId);

          emit(ConfirmAddressSuccessState(result));
        } on ErrorResponse catch (e) {
          emit(ErrorAddressState(e));
        }
      }
    });
  }
}

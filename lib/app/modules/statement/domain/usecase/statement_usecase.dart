import 'package:siclosbank/app/modules/statement/data/models/details_statement_model.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/modules/statement/domain/repository/i_repository_statement.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/wallet/statement_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../shared/data/models/wallet/details_pix_model.dart';

abstract class IStatementUsecase {
  Future<StatementResponse> getStatement(
      {required String initialDate, required String finalDate});

  Future<DetailsTransactionModel> getDetailTEC({required String transactionId});

  Future<DetailsTransactionModel> getDetailTED({required String transactionId});
  Future<DetailsPixModel> getDetailPIX(
      {required String movementId, required MovementType type});
  Future<double> getAmountAvailableToReversalPIX({required String endToEndId});
}

class StatementUsecase implements IStatementUsecase {
  final IRepositoryStatement _repository;

  StatementUsecase(this._repository);

  @override
  Future<StatementResponse> getStatement({
    required String initialDate,
    required String finalDate,
  }) async {
    final user = AppSession.getInstance().user;

    assert(user != null);
    assert(user!.cpf != null);
    try {
      final result = await _repository.getStatement(
        cpf: user!.cpf!,
        initialDate: initialDate,
        finalDate: finalDate,
      );

      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<DetailsTransactionModel> getDetailTEC(
      {required String transactionId}) {
    try {
      final result = _repository.getDetailTEC(transactionId: transactionId);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<DetailsTransactionModel> getDetailTED(
      {required String transactionId}) {
    try {
      final result = _repository.getDetailTED(transactionId: transactionId);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<DetailsPixModel> getDetailPIX(
      {required String movementId, required MovementType type}) {
    try {
      final result = switch (type) {
        MovementType.pixPaymentIn =>
          _repository.getDetailReceivedPIX(movementId: movementId),
        MovementType.pixReversalIn =>
          _repository.getDetailReceivedPIX(movementId: movementId),
        MovementType.pixPaymentOut =>
          _repository.getDetailPaymentOutPIX(movementId: movementId),
        MovementType.pixReversalOut =>
          _repository.getDetailPaymentOutPIX(movementId: movementId),
        _ =>
          throw ErrorResponse(message: 'Tipo de transação PIX não suportado.'),
      };
      return result;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<double> getAmountAvailableToReversalPIX(
      {required String endToEndId}) async {
    try {
      final result = await _repository.getAmountAvailableToReversalPIX(
        endToEndId: endToEndId,
      );
      return result;
    } catch (e) {
      rethrow;
    }
  }
}

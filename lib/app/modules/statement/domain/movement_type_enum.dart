enum MovementType {
  tefTransferIn,
  tefTransferOut,

  pixPaymentIn,
  pixPaymentOut,
  pixReversalOut,
  pixReversalIn,

  tedTransferIn,
  tedTransferOut,
  tedReversalOut,
  tedReversalIn,

  chargeIn,

  billPayment,
  billPaymentChargeback,

  recharge,
  rechargeChargeback,

  slc,

  judicialMovementOut,
  judicialMovementIn,

  entryCredit,

  unknown,
}

extension MovementTypeExtension on MovementType {
  String get description {
    switch (this) {
      case MovementType.tefTransferIn:
        return 'Transferência TEC recebida';
      case MovementType.tefTransferOut:
        return 'Transferência TEC enviada';
      case MovementType.pixPaymentIn:
        return 'PIX recebido';
      case MovementType.pixPaymentOut:
        return 'PIX enviado';
      case MovementType.pixReversalOut:
        return 'Devolução de PIX enviada';
      case MovementType.pixReversalIn:
        return 'Devolução de PIX recebida';
      case MovementType.tedTransferIn:
        return 'Transferência TED recebida';
      case MovementType.tedTransferOut:
        return 'Transferência TED enviada';
      case MovementType.tedReversalOut:
        return 'Devolução de TED enviada';
      case MovementType.tedReversalIn:
        return 'Devolução de TED recebida';
      case MovementType.chargeIn:
        return 'Credito referente a uma cobrança recebida';
      case MovementType.billPayment:
        return 'Débito referente a um pagamento realizado';
      case MovementType.billPaymentChargeback:
        return 'Credito referente ao estorno de um pagamento';
      case MovementType.recharge:
        return 'Débito referente a uma recarga realizada';
      case MovementType.rechargeChargeback:
        return 'Crédito referente ao estorno de uma recarga';
      case MovementType.slc:
        return 'Crédito referente a um recebimento de recebíveis do arranjo de pagamentos de cartões por meio do SLC.';
      case MovementType.judicialMovementOut:
        return 'Liberação de valor bloqueado';
      case MovementType.judicialMovementIn:
        return 'Bloqueio de valor por ordem judicial';
      case MovementType.entryCredit:
        return 'Depósito recebido';
      case MovementType.unknown:
        return 'Sem Descrição';
    }
  }

  static MovementType fromString(String value) {
    switch (value.toUpperCase()) {
      case 'TEFTRANSFERIN':
        return MovementType.tefTransferIn;
      case 'TEFTRANSFEROUT':
        return MovementType.tefTransferOut;
      case 'PIXPAYMENTIN':
        return MovementType.pixPaymentIn;
      case 'PIXPAYMENTOUT':
        return MovementType.pixPaymentOut;
      case 'PIXREVERSALOUT':
        return MovementType.pixReversalOut;
      case 'PIXREVERSALIN':
        return MovementType.pixReversalIn;
      case 'TEDTRANSFERIN':
        return MovementType.tedTransferIn;
      case 'TEDTRANSFEROUT':
        return MovementType.tedTransferOut;
      case 'TEDREVERSALOUT':
        return MovementType.tedReversalOut;
      case 'TEDREVERSALIN':
        return MovementType.tedReversalIn;
      case 'CHARGEIN':
        return MovementType.chargeIn;
      case 'BILLPAYMENT':
        return MovementType.billPayment;
      case 'BILLPAYMENTCHARGEBACK':
        return MovementType.billPaymentChargeback;
      case 'RECHARGE':
        return MovementType.recharge;
      case 'RECHARGECHARGEBACK':
        return MovementType.rechargeChargeback;
      case 'SLC':
        return MovementType.slc;
      case 'JUDICIALMOVEMENTOUT':
        return MovementType.judicialMovementOut;
      case 'JUDICIALMOVEMENTIN':
        return MovementType.judicialMovementIn;
      case 'ENTRYCREDIT':
        return MovementType.entryCredit;
      default:
        return MovementType.unknown;
    }
  }

  String get category {
    switch (this) {
      // Transferência entre contas (P2P) e TED
      case MovementType.tefTransferIn:
      case MovementType.tefTransferOut:
      case MovementType.tedTransferIn:
      case MovementType.tedTransferOut:
      case MovementType.tedReversalOut:
      case MovementType.tedReversalIn:
        return 'Transferência';

      // PIX
      case MovementType.pixPaymentIn:
      case MovementType.pixPaymentOut:
      case MovementType.pixReversalOut:
      case MovementType.pixReversalIn:
        return 'PIX';

      // Cobrança
      case MovementType.chargeIn:
        return 'Cobrança';

      // Pagamento
      case MovementType.billPayment:
      case MovementType.billPaymentChargeback:
        return 'Pagamento';

      // Recarga
      case MovementType.recharge:
      case MovementType.rechargeChargeback:
        return 'Recarga';

      // SLC
      case MovementType.slc:
        return 'SLC';

      // Judicial
      case MovementType.judicialMovementOut:
      case MovementType.judicialMovementIn:
        return 'Judicial';

      // Depósito
      case MovementType.entryCredit:
        return 'Depósito';

      // Valor padrão
      case MovementType.unknown:
        return '';
    }
  }

  bool get isReversalPix {
    return this == MovementType.pixReversalIn ||
        this == MovementType.pixReversalOut;
  }
}

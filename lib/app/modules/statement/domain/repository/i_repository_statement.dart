import 'package:siclosbank/app/modules/statement/data/models/details_statement_model.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';

import '../../../../shared/data/models/wallet/statement_response.dart';
import '../../../../shared/data/models/wallet/details_pix_model.dart';

abstract interface class IRepositoryStatement {
  Future<StatementResponse> getStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate});

  Future<DetailsTransactionModel> getDetailTED({required String transactionId});

  Future<DetailsTransactionModel> getDetailTEC({required String transactionId});

  Future<DetailsPixModel> getDetailReceivedPIX({required String movementId});
  Future<DetailsPixModel> getDetailPaymentOutPIX({required String movementId});

  Future<double> getAmountAvailableToReversalPIX({required String endToEndId});
}

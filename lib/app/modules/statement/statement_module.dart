import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/statement/data/datasource/statement_datasource.dart';
import 'package:siclosbank/app/modules/statement/data/repository/repository_statement_impl.dart';
import 'package:siclosbank/app/modules/statement/domain/repository/i_repository_statement.dart';
import 'package:siclosbank/app/modules/statement/domain/usecase/statement_usecase.dart';
import 'package:siclosbank/app/modules/statement/present/bloc/amount_available_reversal_pix/amount_available_reversal_pix_bloc.dart';
import 'package:siclosbank/app/modules/statement/present/bloc/details_transaction/details_transaction_bloc.dart';
import 'package:siclosbank/app/modules/statement/present/bloc/statement/statement_bloc.dart';
import 'package:siclosbank/app/modules/statement/present/view/receipt/details_transaction_page.dart';
import 'package:siclosbank/app/app_module.dart';

import 'present/view/statement_page.dart';

class StatementModule extends Module {
  @override
  void binds(i) {
    i.addLazySingleton<IStatementDatasource>(StatementDatasourceImpl.new);
    i.addLazySingleton<IRepositoryStatement>(RepositoryStatementImpl.new);
    i.addLazySingleton<IStatementUsecase>(StatementUsecase.new);

    i.add(StatementBloc.new);
    i.add(DetailsTransactionBloc.new);
    i.add(AmountAvailableReversalPixBloc.new);
  }

  @override
  // TODO: implement imports
  List<Module> get imports => [
        AppModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (context) => const StatementPage());
    r.child('/details', child: (context) => const DetailsTransactionPage());
  }
}

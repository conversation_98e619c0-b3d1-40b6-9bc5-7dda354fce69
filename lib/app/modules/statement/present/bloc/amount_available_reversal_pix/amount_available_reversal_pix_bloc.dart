import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../domain/usecase/statement_usecase.dart';

part 'amount_available_reversal_pix_event.dart';
part 'amount_available_reversal_pix_state.dart';

class AmountAvailableReversalPixBloc extends Bloc<
    AmountAvailableReversalPixEvent, AmountAvailableReversalPixState> {
  final IStatementUsecase _statementUsecase;
  AmountAvailableReversalPixBloc(this._statementUsecase)
      : super(AmountAvailableReversalPixInitial()) {
    on<AmountAvailableReversalPixEvent>((event, emit) async {
      if (event is GetAmountAvailableReversalPix) {
        emit(AmountAvailableReversalPixLoading());

        await _statementUsecase
            .getAmountAvailableToReversalPIX(endToEndId: event.endToEndId)
            .then(
          (value) {
            emit(AmountAvailableReversalPixSuccess(value));
          },
        ).catchError((error) {
          emit(AmountAvailableReversalPixError(
            error is ErrorResponse
                ? error
                : ErrorResponse(
                    message: 'Erro desconhecido ao obter valor disponível',
                  ),
          ));
        });
      }
    });
  }
}

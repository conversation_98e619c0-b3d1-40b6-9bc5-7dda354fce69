part of 'amount_available_reversal_pix_bloc.dart';

sealed class AmountAvailableReversalPixState extends Equatable {
  const AmountAvailableReversalPixState();

  @override
  List<Object> get props => [];
}

final class AmountAvailableReversalPixInitial
    extends AmountAvailableReversalPixState {}

final class AmountAvailableReversalPixLoading
    extends AmountAvailableReversalPixState {}

final class AmountAvailableReversalPixError
    extends AmountAvailableReversalPixState {
  final ErrorResponse error;
  const AmountAvailableReversalPixError(this.error);
}

final class AmountAvailableReversalPixSuccess
    extends AmountAvailableReversalPixState {
  final double amountAvailable;
  const AmountAvailableReversalPixSuccess(this.amountAvailable);

  @override
  List<Object> get props => [amountAvailable];

  @override
  bool get stringify => true;
}

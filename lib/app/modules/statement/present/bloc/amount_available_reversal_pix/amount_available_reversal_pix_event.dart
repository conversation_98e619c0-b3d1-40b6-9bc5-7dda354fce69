part of 'amount_available_reversal_pix_bloc.dart';

sealed class AmountAvailableReversalPixEvent extends Equatable {
  const AmountAvailableReversalPixEvent();

  @override
  List<Object> get props => [];
}

final class GetAmountAvailableReversalPix
    extends AmountAvailableReversalPixEvent {
  final String endToEndId;

  const GetAmountAvailableReversalPix(this.endToEndId);

  @override
  List<Object> get props => [endToEndId];
}

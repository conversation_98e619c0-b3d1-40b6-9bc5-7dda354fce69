import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/statement/data/models/details_statement.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/modules/statement/domain/usecase/statement_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../shared/data/models/wallet/details_pix_model.dart';
import '../../../data/models/details_statement_model.dart';

part 'details_transaction_event.dart';
part 'details_transaction_state.dart';

class DetailsTransactionBloc
    extends Bloc<DetailsTransactionEvent, DetailsTransactionState> {
  final IStatementUsecase _usecase;
  DetailsTransactionBloc(this._usecase) : super(DetailsTransactionInitial()) {
    on<DetailsTransactionEvent>((event, emit) async {
      if (event is GetDetailsTransaction) {
        emit(DetailsTransactionLoading());

        final type = switch (event.type) {
          // TEC
          MovementType.tefTransferIn => 'TEC',
          MovementType.tefTransferOut => 'TEC',
          // TED
          MovementType.tedTransferIn => 'TED',
          MovementType.tedTransferOut => 'TED',
          MovementType.tedReversalIn => 'TED',
          MovementType.tedReversalOut => 'TED',
          // PIX
          MovementType.pixPaymentIn => 'PIX',
          MovementType.pixPaymentOut => 'PIX',
          MovementType.pixReversalIn => 'PIX',
          MovementType.pixReversalOut => 'PIX',
          _ => event.type,
        };

        try {
          final response = switch (type) {
            'TEC' =>
              await _usecase.getDetailTEC(transactionId: event.transactionId),
            'TED' =>
              await _usecase.getDetailTED(transactionId: event.transactionId),
            'PIX' => await _usecase.getDetailPIX(
                movementId: event.transactionId, type: event.type),
            _ => null,
          };

          if (response != null) {
            emit(
                DetailsTransactionSuccess(details: response, type: event.type));
          } else {
            emit(DetailsTransactionError(
                error: ErrorResponse(
                    message: 'Erro ao buscar detalhes da transação')));
          }
        } catch (e) {
          emit(DetailsTransactionError(error: e as ErrorResponse));
        }
      }
    });
  }
}

part of 'details_transaction_bloc.dart';

sealed class DetailsTransactionEvent extends Equatable {
  const DetailsTransactionEvent();

  @override
  List<Object> get props => [];
}

final class GetDetailsTransaction extends DetailsTransactionEvent {
  final String transactionId;
  final MovementType type;

  const GetDetailsTransaction(
      {required this.transactionId, required this.type});

  @override
  List<Object> get props => [transactionId];
}

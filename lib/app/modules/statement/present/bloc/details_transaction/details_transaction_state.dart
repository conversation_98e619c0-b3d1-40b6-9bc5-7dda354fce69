part of 'details_transaction_bloc.dart';

sealed class DetailsTransactionState extends Equatable {
  const DetailsTransactionState();

  @override
  List<Object> get props => [];
}

final class DetailsTransactionInitial extends DetailsTransactionState {}

final class DetailsTransactionLoading extends DetailsTransactionState {}

final class DetailsTransactionSuccess extends DetailsTransactionState {
  final DetailsStatement details;
  final MovementType? type;

  const DetailsTransactionSuccess({required this.details, required this.type});
}

final class DetailsTransactionError extends DetailsTransactionState {
  final ErrorResponse error;

  const DetailsTransactionError({required this.error});
}

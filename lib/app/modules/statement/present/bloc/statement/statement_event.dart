part of 'statement_bloc.dart';

sealed class StatementEvent extends Equatable {
  const StatementEvent();

  @override
  List<Object> get props => [];
}

final class GetStatementEvent extends StatementEvent {
  final DateTime? dateFrom;
  final DateTime? dateTo;

  const GetStatementEvent({this.dateFrom, this.dateTo});
}

final class PaginationStatementEvent extends StatementEvent {
  const PaginationStatementEvent();
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/statement/domain/usecase/statement_usecase.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../shared/utils/utils.dart';

part 'statement_event.dart';
part 'statement_state.dart';

class StatementBloc extends Bloc<StatementEvent, StatementState> {
  final IStatementUsecase _usecase;
  DateTime dateTo = DateTime.now();
  int page = 1;

  TransactionResponse? transactionSelected;

  StatementBloc(this._usecase) : super(StatementInitial()) {
    on<StatementEvent>((event, emit) async {
      if (event is GetStatementEvent) {
        emit(StatementLoading());

        try {
          final dateTo = event.dateTo ?? DateTime.now();
          final dateFrom =
              event.dateFrom ?? dateTo.subtract(const Duration(days: 29));

          final result = await _usecase.getStatement(
            initialDate: Utils.getDateTimeToApi(dateFrom),
            finalDate: Utils.getDateTimeToApi(dateTo),
          );

          // _dateFrom = dateTo;

          emit(StatementSuccess(
            transactions: result.transactions!,
            isMoreItens: result.transactions!.isNotEmpty,
          ));
        } catch (e) {
          emit(StatementError(error: e as ErrorResponse));
        }
      }

      if (event is PaginationStatementEvent) {
        // emit(StatementLoading());

        // try {
        //   final _dateTo = dateTo.subtract(const Duration(days: 6));

        //   final result = await _usecase.getStatement(
        //     initialDate: Utils.getDateTimeToApi(_dateTo),
        //     finalDate: Utils.getDateTimeToApi(_dateFrom),
        //   );

        //   _dateFrom = dateTo;

        //   emit(StatementSuccess(
        //     transactions: result.transactions!,
        //     isMoreItens: result.transactions!.isNotEmpty,
        //   ));
        // } catch (e) {
        //   emit(StatementError(error: e as ErrorResponse));
        // }
      }
    });
  }
}

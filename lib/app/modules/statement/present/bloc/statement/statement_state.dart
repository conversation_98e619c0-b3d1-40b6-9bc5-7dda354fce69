part of 'statement_bloc.dart';

sealed class StatementState extends Equatable {
  const StatementState({this.isMoreItens});
  final bool? isMoreItens;

  @override
  List<Object> get props => [];
}

final class StatementInitial extends StatementState {}

final class StatementLoading extends StatementState {}

final class StatementSuccess extends StatementState {
  final List<TransactionResponse> transactions;

  const StatementSuccess({
    required this.transactions,
    required super.isMoreItens,
  });

  @override
  List<Object> get props => [transactions];
}

final class StatementError extends StatementState {
  const StatementError({required this.error});
  final ErrorResponse error;
}

import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/modules/statement/present/bloc/statement/statement_bloc.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>';
import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/custom_footer_pagination.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../shared/utils/my_behavior.dart';
import '../../../../shared/utils/utils.dart';
import 'components/item_statement.dart';
import 'components/item_header_extrato.dart';

class StatementPage extends StatelessWidget {
  const StatementPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StatementBloc>(
      create: (context) {
        return Modular.get<StatementBloc>();
      },
      child: _ExtratoView(),
    );
  }
}

class _ExtratoView extends StatefulWidget {
  _ExtratoView({Key? key}) : super(key: key);

  @override
  State<_ExtratoView> createState() => __ExtratoViewState();
}

class __ExtratoViewState extends State<_ExtratoView> {
  RefreshController _refreshController =
      RefreshController(initialRefresh: true);

  DateTime lastDate = DateTime.now();
  DateTime firstDate = DateTime.now().subtract(const Duration(days: 29));

  bool showError = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.extrato.toUpperCase(),
      ),
      body: BlocConsumer<StatementBloc, StatementState>(
        listener: (context, state) {
          if (state is StatementSuccess) {
            _refreshController.loadComplete();
            _refreshController.refreshCompleted();
          }

          if (state is StatementError) {
            _refreshController.loadComplete();
            _refreshController.refreshCompleted();
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          return _buildBody(state);
        },
      ),
    );
  }

  _buildBody(StatementState state) {
    return ContainerResponsive(
      padding: EdgeInsetsResponsive.only(left: 16, right: 16, top: 16),
      child: Column(
        children: <Widget>[
          const CardBalanceWithCredit(
            showStatementButton: false,
            compactWidget: true,
          ),
          SizedBoxResponsive(height: 8),
          _dateWidget(),
          Expanded(
            child: ScrollConfiguration(
              behavior: MyBehavior(),
              child: SmartRefresher(
                enablePullDown: true,
                enablePullUp: state.isMoreItens ?? false,
                controller: _refreshController,
                // onLoading: () {
                //   _pagination();
                // },
                onRefresh: () {
                  _refresh();
                },
                // footer: const CustomFooterPagination(),
                child: state is StatementSuccess
                    ? state.transactions.isNotEmpty
                        ? _buildList(state)
                        : _buildPlaceholder(state)
                    : state is StatementLoading
                        ? ContainerResponsive(
                            alignment: Alignment.center,
                            child: const CircularProgressIndicator(),
                          )
                        : Container(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ListTile _dateWidget() {
    return ListTile(
      title: Row(
        children: <Widget>[
          const Icon(Icons.calendar_today, size: 18),
          const SizedBox(width: 8),
          TextResponsive(
            I18n.of(context)!.data,
            style: Theme.of(context).textTheme.labelMedium,
          ),
        ],
      ),
      tileColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      trailing: Icon(
        Icons.keyboard_arrow_down,
      ),
      onTap: () async {
        var result = await showModalBottomSheet(
            backgroundColor: Colors.white,
            context: context,
            useRootNavigator: true,
            builder: (context) {
              return ContainerResponsive(
                // height: 300,
                child: Column(
                  children: <Widget>[
                    Container(
                      padding: EdgeInsets.all(24),
                      alignment: Alignment.center,
                      child: TextResponsive(
                        I18n.of(context)!.data,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    CalendarDatePicker2(
                        config: CalendarDatePicker2Config(
                          calendarType: CalendarDatePicker2Type.range,
                          selectedRangeHighlightColor: ColorsApp.verde[200],
                        ),
                        value: [firstDate, lastDate],
                        displayedMonthDate: lastDate,
                        onValueChanged: (value) {
                          if (value.length > 1) {
                            if (value[0].isBefore(value[1])) {
                              setState(() {
                                firstDate = value[0];
                                lastDate = value[1];
                              });
                            } else {
                              setState(() {
                                firstDate = value[1];
                                lastDate = value[0];
                              });
                            }
                          } else {
                            setState(() {
                              firstDate = value[0];
                              lastDate = value[0];
                            });
                          }
                        }),
                  ],
                ),
              );
            });

        if (lastDate.difference(firstDate).inDays > 29) {
          SnackBarApp.showSnack(
            context: context,
            message: I18n.of(context)!.dataMaiorQue30Dias,
            success: false,
          );
        } else {
          BlocProvider.of<StatementBloc>(context).add(
            GetStatementEvent(
              dateFrom: firstDate,
              dateTo: lastDate,
            ),
          );
        }
      },
    );
  }

  void _refresh() {
    BlocProvider.of<StatementBloc>(context).add(const GetStatementEvent());
  }

  void _pagination() {
    BlocProvider.of<StatementBloc>(context)
        .add(const PaginationStatementEvent());
  }

  _buildList(StatementState state) {
    if (state is StatementLoading) {
      _refreshController.refreshCompleted();
    } else {
      _refreshController.loadComplete();
    }

    List<Widget> children = [];

    if (state is StatementSuccess) {
      Map<String, List<TransactionResponse>> mapPerDate = {};
      state.transactions.sort((b, a) => a.createDate!.millisecondsSinceEpoch
          .compareTo(b.createDate!.millisecondsSinceEpoch));

      for (var transaction in state.transactions) {
        final date = Utils.formatDataMonthAndDay(date: transaction.createDate!);

        if (mapPerDate[date] == null) {
          mapPerDate[date] = [transaction];
        } else {
          mapPerDate[date]!.add(transaction);
        }
      }

      if (mapPerDate.isNotEmpty) {
        for (var key in mapPerDate.keys) {
          children.add(ItemHeaderExtrato(
            data: key,
          ));
          var list = mapPerDate[key];
          if (list != null) {
            for (int i = 0; i < list.length; i++) {
              final transaction = list[i];
              children.add(
                InkWell(
                  onTap: () {
                    BlocProvider.of<StatementBloc>(context)
                        .transactionSelected = transaction;
                    push(Routes.statementDetails,
                        args: {'transaction': transaction});
                  },
                  child: ItemExtrato(
                    primeiro: i == 0,
                    ultimo: i == (list.length - 1),
                    transacao: list[i],
                  ),
                ),
              );
            }
          }
        }
      }
    }

    return ListView(children: children);
  }

  _buildPlaceholder(StatementState state) {
    if (state is StatementLoading) {
      _refreshController.refreshCompleted();
    }

    // if (!state.loading) {
    //   _refreshController.loadComplete();
    // }

    return ContainerResponsive(
      alignment: Alignment.center,
      child: TextResponsive(
        I18n.of(context)!.nenhum_transacao,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }
}

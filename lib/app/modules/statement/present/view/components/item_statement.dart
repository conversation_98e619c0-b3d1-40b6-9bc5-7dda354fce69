import 'package:flutter/material.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';

import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../shared/utils/utils.dart';

class ItemExtrato extends StatelessWidget {
  final TransactionResponse? transacao;
  final bool primeiro;
  final bool ultimo;

  const ItemExtrato({
    super.key,
    this.transacao,
    this.primeiro = false,
    this.ultimo = false,
  });

  get valor => null;

  @override
  Widget build(BuildContext context) {
    return ContainerResponsive(
      margin: EdgeInsetsResponsive.only(bottom: 1),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft:
              primeiro ? const Radius.circular(10) : const Radius.circular(0),
          topRight:
              primeiro ? const Radius.circular(10) : const Radius.circular(0),
          bottomLeft:
              ultimo ? const Radius.circular(10) : const Radius.circular(0),
          bottomRight:
              ultimo ? const Radius.circular(10) : const Radius.circular(0),
        ),
      ),
      padding:
          EdgeInsetsResponsive.only(left: 16, top: 16, bottom: 12, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(
                child: TextResponsive(
                  transacao?.movementType.description ?? "",
                  style: Theme.of(context).textTheme.bodyLarge,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              TextResponsive(
                "${transacao?.isCredit() ? '+' : '-'} ${Utils.formatValueInstatement(transacao!.amount)}",
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
          SizedBoxResponsive(height: 8),
          Visibility(
            visible: transacao!.movementType.category.isNotEmpty,
            child: Container(
              decoration: BoxDecoration(
                  color: ColorsApp.cinza[400],
                  borderRadius: BorderRadius.circular(5)),
              padding: EdgeInsetsResponsive.only(
                  left: 8, right: 8, top: 8, bottom: 8),
              child: TextResponsive(
                transacao!.movementType.category,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(color: ColorsApp.cinza[700], height: 1.0),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

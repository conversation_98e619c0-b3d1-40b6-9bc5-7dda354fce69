import 'package:flutter/material.dart';

import '../../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../../shared/themes/styles/colors_app.dart';

class ItemHeaderExtrato extends StatelessWidget {
  final String data;

  const ItemHeaderExtrato({super.key, this.data = ''});

  @override
  Widget build(BuildContext context) {
    return ContainerResponsive(
      padding: EdgeInsetsResponsive.only(top: 25, bottom: 18),
      child: TextResponsive(
        data,
        style: Theme.of(context)
            .textTheme
            .bodyLarge!
            .copyWith(color: ColorsApp.cinza[700]),
      ),
    );
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/statement/domain/movement_type_enum.dart';
import 'package:siclosbank/app/modules/statement/present/bloc/amount_available_reversal_pix/amount_available_reversal_pix_bloc.dart';
import 'package:siclosbank/app/shared/data/models/wallet/details_pix_model.dart';
import 'package:siclosbank/app/modules/statement/present/bloc/details_transaction/details_transaction_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../../shared/navigation/named_routes.dart';
import '../../../../../shared/utils/image_utils.dart';
import '../../../data/models/party_statement_model.dart';
import '../../bloc/statement/statement_bloc.dart';
import 'components/to_reversal_button.dart';

class DetailsTransactionPage extends StatelessWidget {
  const DetailsTransactionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<DetailsTransactionBloc>(),
      child: _DetailsTransactionView(),
    );
  }
}

class _DetailsTransactionView extends StatefulWidget {
  const _DetailsTransactionView({super.key});

  @override
  State<_DetailsTransactionView> createState() =>
      __DetailsTransactionViewState();
}

class __DetailsTransactionViewState extends State<_DetailsTransactionView> {
  late TransactionResponse transaction;

  var key = GlobalKey();
  bool takeScreenShot = false;

  @override
  void initState() {
    final args = Modular.args.data?['args'];
    assert(args != null);
    transaction = args['transaction'] as TransactionResponse;

    BlocProvider.of<DetailsTransactionBloc>(context).add(
      GetDetailsTransaction(
        transactionId: transaction.id,
        type: transaction.movementType,
      ),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.comprovante.toUpperCase(),
        actions: [
          IconButton(
            icon: ImageUtils.icShare(),
            onPressed: () async {
              setState(() {
                takeScreenShot = true;
              });

              // Espera alguns milisegundos antes de tira o print da tela
              await Future.delayed(const Duration(milliseconds: 300));

              Utils.takeScreenShot(
                key: key,
                fileName: "comprovante-siclosbank${DateTime.now()}",
              );

              setState(() {
                takeScreenShot = false;
              });
            },
          ),
        ],
      ),
      body: BlocConsumer<DetailsTransactionBloc, DetailsTransactionState>(
        listener: (context, state) {
          if (state is DetailsTransactionError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is DetailsTransactionLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            // padding: const EdgeInsets.symmetric(vertical: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                RepaintBoundary(
                  key: key,
                  child: Material(
                    color:
                        // takeScreenShot
                        //     ?
                        ColorsApp.snow,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 24,
                      ),
                      child: Column(
                        children: [
                          Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.only(top: 16, bottom: 16),
                            child: Text(
                              I18n.of(context)!.comprovanteDe +
                                  (transaction.movementType.description),
                              style: textTheme.titleMedium,
                            ),
                          ),
                          const Divider(),
                          Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.only(top: 16, bottom: 16),
                            child: Column(
                              spacing: 8,
                              children: [
                                Text(
                                  transaction.movementType.name
                                          .toLowerCase()
                                          .contains('out')
                                      ? I18n.of(context)!.valor_pago
                                      : I18n.of(context)!.valor_recebido,
                                  style: textTheme.titleSmall,
                                ),
                                Text(
                                  Utils.formatBalance(transaction.amount),
                                  style: textTheme.displayLarge,
                                ),
                              ],
                            ),
                          ),
                          _fieldInfo(
                            context,
                            title: I18n.of(context)!.valor_documento,
                            value: Utils.formatBalance(transaction.amount),
                          ),
                          _dividerCustom(),
                          _fieldDate(state),
                          if (transaction.description != null &&
                              transaction.description!.isNotEmpty)
                            Column(
                              children: [
                                _dividerCustom(),
                                _fieldInfo(
                                  context,
                                  title: I18n.of(context)!.descricao,
                                  value: transaction.description!,
                                ),
                              ],
                            ),
                          _dividerCustom(),
                          Builder(
                            builder: (context) {
                              if (state is DetailsTransactionSuccess) {
                                print(state.details);
                                if (transaction.movementType.isReversalPix) {
                                  return Column(
                                    children: [
                                      _fieldParty(
                                        context,
                                        title: I18n.of(context)!.devolucao_por,
                                        party: state.details.creditParty,
                                      ),
                                      _dividerCustom(),
                                      _fieldParty(
                                        context,
                                        title: I18n.of(context)!.devolucao_para,
                                        party: state.details.debitParty,
                                      ),
                                      _dividerCustom(),
                                      _fieldEndToEndPix(state),
                                    ],
                                  );
                                } else {
                                  return Column(
                                    children: [
                                      _fieldParty(
                                        context,
                                        title: I18n.of(context)!.pago_por,
                                        party: state.details.debitParty,
                                      ),
                                      _dividerCustom(),
                                      _fieldParty(
                                        context,
                                        title: I18n.of(context)!.para,
                                        party: state.details.creditParty,
                                      ),
                                      _dividerCustom(),
                                      _fieldEndToEndPix(state),
                                    ],
                                  );
                                }
                              }
                              return const SizedBox();
                            },
                          ),
                          _fieldInfo(
                            context,
                            title: I18n.of(context)!.id_transacao,
                            value: transaction.id,
                          ),
                          _fieldOriginalTransactionIdReversal(state),
                          _dividerCustom(),
                          Container(
                            padding: const EdgeInsets.only(top: 24, bottom: 22),
                            child: ImageUtils.siclosAppBar(height: 28),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                if (state is DetailsTransactionSuccess &&
                    state.details is DetailsPixModel &&
                    transaction.movementType == MovementType.pixPaymentIn)
                  ToReversalButton(
                    transaction: transaction,
                    detailsPixModel: state.details as DetailsPixModel,
                  ),
                const SizedBox(height: 26),
              ],
            ),
          );

          // return Container();
        },
      ),
    );
  }

  Builder _fieldDate(DetailsTransactionState state) {
    return Builder(
      builder: (context) {
        if (state is DetailsTransactionSuccess) {
          if (state.details is DetailsPixModel) {
            final details = (state.details as DetailsPixModel);
            return _fieldInfo(
              context,
              title: I18n.of(context)!.data,
              value: Utils.formatDateTimeToStringWithHours(
                date: transaction.createDate ?? details.createdAt!,
              ),
            );
          }
        }
        if (transaction.createDate == null) {
          return _fieldInfo(context, title: I18n.of(context)!.data, value: '-');
        }
        return _fieldInfo(
          context,
          title: I18n.of(context)!.data,
          value: Utils.formatDateTimeToStringWithHours(
            date: transaction.createDate!,
          ),
        );
      },
    );
  }

  Builder _fieldOriginalTransactionIdReversal(DetailsTransactionState state) {
    return Builder(
      builder: (context) {
        if (state is DetailsTransactionSuccess) {
          if (state.details is DetailsPixModel) {
            final details = state.details as DetailsPixModel;
            if (transaction.movementType.isReversalPix) {
              return _fieldInfo(
                context,
                title: I18n.of(context)!.id_transacao_originial,
                value: details.originalTransactionId ?? '-',
              );
            }
          }
        }
        return Container();
      },
    );
  }

  Widget _fieldEndToEndPix(DetailsTransactionSuccess state) {
    if (state.details is DetailsPixModel) {
      return Builder(
        builder: (context) {
          final details = (state.details as DetailsPixModel);

          return _fieldInfo(
            context,
            title: I18n.of(context)!.endToEnd,
            value: details.endToEndId,
          );
        },
      );
    } else {
      return const SizedBox();
    }
  }

  Divider _dividerCustom() {
    return const Divider(thickness: 0.5, color: Colors.black26);
  }

  Container _fieldInfo(
    BuildContext context, {
    required String title,
    required String value,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(top: 16, bottom: 16),
      child: Column(
        spacing: 12,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.bodyMedium!.copyWith(
              color: ColorsApp.cinzaDetalhesContatoConta,
            ),
          ),
          Text(value, style: textTheme.bodyMedium),
        ],
      ),
    );
  }

  Container _fieldParty(
    BuildContext context, {
    required String title,
    required PartyStatementModel party,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(top: 16, bottom: 16),
      child: Column(
        spacing: 12,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.bodyMedium!.copyWith(
              color: ColorsApp.cinzaDetalhesContatoConta,
            ),
          ),
          Text(party.name, style: textTheme.bodyMedium),
          Text(
            FieldsUtils.isCPFValido(party.taxId)
                ? Utils.maskCpf(party.taxId)
                : FieldsUtils.obterCnpj(party.taxId),
            style: textTheme.bodyMedium,
          ),
          Text(
            Utils.getBankName(party.bank ?? '********') ?? '',
            style: textTheme.bodyMedium,
          ),
          Text(
            '${party.branch}     ${party.account}',
            style: textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }
}

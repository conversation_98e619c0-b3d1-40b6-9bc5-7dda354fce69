import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/data/models/wallet/details_pix_model.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/navigation/navigator_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/dialog_utils.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../bloc/amount_available_reversal_pix/amount_available_reversal_pix_bloc.dart';

class ToReversalButton extends StatelessWidget {
  const ToReversalButton(
      {super.key, required this.detailsPixModel, required this.transaction});

  final DetailsPixModel detailsPixModel;
  final TransactionResponse transaction;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<AmountAvailableReversalPixBloc>()
        ..add(GetAmountAvailableReversalPix(detailsPixModel.endToEndId)),
      child: _ToReversalButton(
        detailsPixModel: detailsPixModel,
        transaction: transaction,
      ),
    );
  }
}

class _ToReversalButton extends StatefulWidget {
  const _ToReversalButton({
    required this.detailsPixModel,
    required this.transaction,
  });

  final DetailsPixModel detailsPixModel;
  final TransactionResponse transaction;

  @override
  State<_ToReversalButton> createState() => _ToReversalButtonState();
}

class _ToReversalButtonState extends State<_ToReversalButton> {
  DetailsPixModel get detailsPixModel => widget.detailsPixModel;
  TransactionResponse get transaction => widget.transaction;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 24, right: 24, bottom: 24),
      child: BlocConsumer<AmountAvailableReversalPixBloc,
          AmountAvailableReversalPixState>(
        listener: (context, state) {
          if (state is AmountAvailableReversalPixError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is AmountAvailableReversalPixLoading) {
            return Center(
              child: Utils.circularProgressButton(),
            );
          }
          print(state);
          if (state is AmountAvailableReversalPixSuccess) {
            return Visibility(
              visible: state.amountAvailable > 0,
              child: ButtonApp(
                text: I18n.of(context)!.devolver,
                onPress: () {
                  push(
                    '/pix/refund',
                    args: {
                      'transactionId': transaction.id,
                      'details': detailsPixModel,
                      'amountAvailable': state.amountAvailable,
                    },
                  );
                },
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }
}

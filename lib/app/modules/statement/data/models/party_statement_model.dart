class PartyStatementModel {
  final String account;
  final String branch;
  final String taxId;
  final String name;
  final String? accountType;
  final String? bank;
  final String? personType;
  final String? key;

  PartyStatementModel({
    required this.account,
    required this.branch,
    required this.taxId,
    required this.name,
    this.accountType,
    this.bank,
    this.personType,
    this.key,
  });

  factory PartyStatementModel.fromMap(Map<String, dynamic> map) {
    var branch = map['branch'];
    if (branch is num) {
      branch = branch.toString();
    } else if (branch is String) {
      branch = branch;
    } else {
      branch = '';
    }
    return PartyStatementModel(
      account: map['account'] as String,
      branch: branch,
      taxId: map['taxId'] as String,
      name: (map['name'] as String?) ?? '',
      bank: map['bank'] as String?,
      personType: map['personType'] as String?,
      accountType: map['accountType'] as String?,
      key: map['key'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'account': account,
      'branch': branch,
      'taxId': taxId,
      'name': name,
      'bank': bank,
      'personType': personType,
      'accountType': accountType,
      'key': key,
    };
  }
}

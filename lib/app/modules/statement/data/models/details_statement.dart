import 'package:siclosbank/app/modules/statement/data/models/party_statement_model.dart';

abstract class DetailsStatement {
  final PartyStatementModel debitParty;
  final PartyStatementModel creditParty;

  DetailsStatement({
    required this.debitParty,
    required this.creditParty,
  });

  // factory DetailsStatement.fromMap(Map<String, dynamic> map) {
  //   return DetailsStatement(
  //     debitParty: PartyStatementModel.fromMap(
  //         map['debitParty'] as Map<String, dynamic>),
  //     creditParty: PartyStatementModel.fromMap(
  //         map['creditParty'] as Map<String, dynamic>),
  //   );
  // }

  // Map<String, dynamic> toMap() {
  //   return {
  //     'debitParty': debitParty.toMap(),
  //     'creditParty': creditParty.toMap(),
  //   };
  // }
}

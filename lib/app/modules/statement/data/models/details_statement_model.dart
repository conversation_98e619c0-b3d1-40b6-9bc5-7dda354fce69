import 'package:siclosbank/app/modules/statement/data/models/party_statement_model.dart';

import 'details_statement.dart';

class DetailsTransactionModel extends DetailsStatement {
  final String id;
  final String clientRequestId;
  final String? description;

  DetailsTransactionModel({
    required this.id,
    required this.clientRequestId,
    required super.debitParty,
    required super.creditParty,
    this.description,
  });

  factory DetailsTransactionModel.fromMap(Map<String, dynamic> map) {
    return DetailsTransactionModel(
      id: map['id'] as String,
      clientRequestId: (map['clientRequestId'] as String?) ?? '',
      debitParty: PartyStatementModel.fromMap(
          map['debitParty'] as Map<String, dynamic>),
      creditParty: PartyStatementModel.fromMap(
          map['creditParty'] as Map<String, dynamic>),
      description: map['description'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'clientRequestId': clientRequestId,
      'debitParty': super.debitParty.toMap(),
      'creditParty': super.creditParty.toMap(),
      'description': description,
    };
  }
}

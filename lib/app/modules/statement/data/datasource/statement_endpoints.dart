abstract class StatementEndpoints {
  static String getStatement(
          {required String cpf,
          required String initialDate,
          required String finalDate}) =>
      '/user/get-account-statement/$cpf/$initialDate/$finalDate';

  static String getDetailTED({required String transactionId}) =>
      '/user/consult-account-statement-ted-transaction-by-id/$transactionId';

  static String getDetailTEC({required String transactionId}) =>
      '/user/consult-account-statement-tec-transaction-by-id/$transactionId';

  static String getDetailReceivedPIX({required String movementId}) =>
      '/transfer_bank/consult-received-pix-by-transaction-id/$movementId';

  static String getDetailPaymentOutPIX({required String movementId}) =>
      '/transfer_bank/consult-payment-pix-by-transaction-id/$movementId';

  static String getAmountAvailableToReversalPIX({required String endToEndId}) =>
      '/transfer_bank/get-calculate-amount-remainder-return-pix/$endToEndId';
}

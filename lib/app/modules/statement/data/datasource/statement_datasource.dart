import 'package:siclosbank/app/modules/statement/data/datasource/statement_endpoints.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

abstract class IStatementDatasource {
  Future<ApiResponse> getStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate});

  Future<ApiResponse> getDetailTED({required String transactionId});

  Future<ApiResponse> getDetailTEC({required String transactionId});

  Future<ApiResponse> getDetailReceivedPIX({required String movementId});
  Future<ApiResponse> getDetailPaymentOutPIX({required String movementId});
  Future<ApiResponse> getAmountAvailableToReversalPIX(
      {required String endToEndId});
}

abstract class IStatementRepository {
  Future<ApiResponse> getStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate});
}

class StatementDatasourceImpl implements IStatementDatasource {
  final IClient _client;

  StatementDatasourceImpl(this._client);

  @override
  Future<ApiResponse> getStatement({
    required String cpf,
    required String initialDate,
    required String finalDate,
  }) async {
    final response = await _client.fetch(
      method: 'GET',
      path: StatementEndpoints.getStatement(
        cpf: cpf,
        initialDate: initialDate,
        finalDate: finalDate,
      ),
    );

    return response;
  }

  @override
  Future<ApiResponse> getDetailTEC({required String transactionId}) async {
    final response = await _client.fetch(
      method: 'GET',
      path: StatementEndpoints.getDetailTEC(transactionId: transactionId),
    );

    return response;
  }

  @override
  Future<ApiResponse> getDetailTED({required String transactionId}) async {
    final response = await _client.fetch(
      method: 'GET',
      path: StatementEndpoints.getDetailTED(transactionId: transactionId),
    );

    return response;
  }

  @override
  Future<ApiResponse> getDetailReceivedPIX({required String movementId}) async {
    final response = _client.fetch(
      method: 'GET',
      path: StatementEndpoints.getDetailReceivedPIX(movementId: movementId),
    );
    return response;
  }

  @override
  Future<ApiResponse> getDetailPaymentOutPIX(
      {required String movementId}) async {
    final response = _client.fetch(
      method: 'GET',
      path: StatementEndpoints.getDetailPaymentOutPIX(movementId: movementId),
    );
    return response;
  }

  @override
  Future<ApiResponse> getAmountAvailableToReversalPIX(
      {required String endToEndId}) async {
    final response = _client.fetch(
      method: 'GET',
      path: StatementEndpoints.getAmountAvailableToReversalPIX(
          endToEndId: endToEndId),
    );
    return response;
  }
}

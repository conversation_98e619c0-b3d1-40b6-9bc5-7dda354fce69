import 'dart:convert';

import 'package:siclosbank/app/shared/data/models/wallet/details_pix_model.dart';
import 'package:siclosbank/app/modules/statement/data/models/details_statement_model.dart';
import 'package:siclosbank/app/modules/statement/domain/repository/i_repository_statement.dart';
import 'package:siclosbank/app/shared/data/models/wallet/statement_response.dart';
import 'package:siclosbank/app/shared/data/models/wallet/transaction_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../datasource/statement_datasource.dart';

class RepositoryStatementImpl implements IRepositoryStatement {
  final IStatementDatasource _datasource;

  RepositoryStatementImpl(this._datasource);

  @override
  Future<StatementResponse> getStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate}) async {
    try {
      final response = await _datasource.getStatement(
          cpf: cpf, initialDate: initialDate, finalDate: finalDate);

      final result = StatementResponse.fromMap(response.data);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<DetailsTransactionModel> getDetailTEC(
      {required String transactionId}) async {
    try {
      final response =
          await _datasource.getDetailTEC(transactionId: transactionId);

      final result = DetailsTransactionModel.fromMap(response.data['body']);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<DetailsTransactionModel> getDetailTED(
      {required String transactionId}) async {
    try {
      final response =
          await _datasource.getDetailTED(transactionId: transactionId);

      final result = DetailsTransactionModel.fromMap(response.data['body']);

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<DetailsPixModel> getDetailReceivedPIX(
      {required String movementId}) async {
    try {
      final response =
          await _datasource.getDetailReceivedPIX(movementId: movementId);
      final result = DetailsPixModel.fromMap(response.data);
      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<DetailsPixModel> getDetailPaymentOutPIX(
      {required String movementId}) async {
    try {
      final response =
          await _datasource.getDetailPaymentOutPIX(movementId: movementId);
      final result = DetailsPixModel.fromMap(response.data);
      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<double> getAmountAvailableToReversalPIX(
      {required String endToEndId}) async {
    try {
      final response = await _datasource.getAmountAvailableToReversalPIX(
          endToEndId: endToEndId);
      final result = double.parse(response.data as String);
      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

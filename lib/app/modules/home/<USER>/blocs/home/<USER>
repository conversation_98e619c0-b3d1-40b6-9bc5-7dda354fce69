part of 'home_bloc.dart';

sealed class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object> get props => [];
}

final class HomeInitial extends HomeState {}

final class HomeNotificationLoading extends HomeState {}

final class HomeNotificationsSuccess extends HomeState {
  const HomeNotificationsSuccess({required this.hasUnreadNotifications});
  final bool hasUnreadNotifications;
}

final class HomeError extends HomeState {
  final ErrorResponse error;

  const HomeError(this.error);
}

final class CancelRequestedClaimSuccess extends HomeState {}

final class ConfirmRequestedClaimSuccess extends HomeState {}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/i_home_repository.dart';
import 'package:siclosbank/app/modules/notifications/data/repository/i_notifications_repository.dart';
import 'package:siclosbank/app/shared/domain/usecase/bank_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../app_controller.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final IHomeRepository _homeRepo;

  final INotificationsRepository _repo;
  HomeBloc(this._repo, this._homeRepo) : super(HomeInitial()) {
    on<HomeEvent>((event, emit) async {
      if (event is FetchNotificationsUnread) {
        emit(HomeNotificationLoading());
        final result =
            await _repo.notifications(page: 1, read: false, pageSize: 10);

        final list = result.where((element) => element.read == false).toList();

        emit(HomeNotificationsSuccess(hasUnreadNotifications: list.isNotEmpty));
      }
      if (event is GetRequestsClaimsPixAll) {
        await _homeRepo.consultRequestedClaims().then((result) {
          AppSession.getInstance().listClaimsPix = result;
        }).catchError((e) {
          print(e);
        });
      }
      if (event is CancelRequestedClaimPix) {
        await _homeRepo
            .cancelRequestClaim(claimId: event.claimId)
            .then((result) {
          emit(CancelRequestedClaimSuccess());
          print(result);
        }).catchError((e) {
          print(e);
        });
      }
      if (event is ConfirmRequestedClaimPix) {
        await _homeRepo
            .cancelRequestClaim(claimId: event.claimId)
            .then((result) {
          print(result);
          emit(ConfirmRequestedClaimSuccess());
        }).catchError((e) {
          print(e);
        });
      }
    });
  }
}

part of 'home_bloc.dart';

sealed class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

final class FetchNotificationsUnread extends HomeEvent {}

final class GetRequestsClaimsPixAll extends HomeEvent {}

final class CancelRequestedClaimPix extends HomeEvent {
  final String claimId;

  const CancelRequestedClaimPix(this.claimId);
}

final class ConfirmRequestedClaimPix extends HomeEvent {
  final String claimId;

  const ConfirmRequestedClaimPix(this.claimId);
}

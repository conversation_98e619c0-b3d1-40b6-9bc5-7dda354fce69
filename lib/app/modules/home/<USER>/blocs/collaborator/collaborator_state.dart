part of 'collaborator_bloc.dart';

sealed class CollaboratorState extends Equatable {
  const CollaboratorState();

  @override
  List<Object> get props => [];
}

final class CollaboratorInitial extends CollaboratorState {}

final class CollaboratorLoading extends CollaboratorState {}

final class CollaboratorSuc<PERSON> extends CollaboratorState {
  final CollaboratorResponse collaborator;
  const CollaboratorSuc<PERSON>(this.collaborator);

  @override
  List<Object> get props => [collaborator];
}

final class CollaboratorError extends CollaboratorState {
  final ErrorResponse error;
  const CollaboratorError(this.error);

  @override
  List<Object> get props => [error];
}

final class CollaboratorImageProfileSuccess extends CollaboratorState {
  final File imageProfile;
  const CollaboratorImageProfileSuccess(this.imageProfile);

  @override
  List<Object> get props => [imageProfile];
}

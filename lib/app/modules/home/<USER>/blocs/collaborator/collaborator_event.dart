part of 'collaborator_bloc.dart';

sealed class CollaboratorEvent extends Equatable {
  const CollaboratorEvent();

  @override
  List<Object> get props => [];
}

final class GetCollaboratorEvent implements CollaboratorEvent {
  const GetCollaboratorEvent();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

final class GetImageProfileEvent implements CollaboratorEvent {
  const GetImageProfileEvent();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'dart:io';

import '../../../../../app_controller.dart';
import '../../../../../shared/data/models/collaborator_response.dart';

part 'collaborator_event.dart';
part 'collaborator_state.dart';

class CollaboratorBloc extends Bloc<CollaboratorEvent, CollaboratorState> {
  final IAppUseCase _usecase;
  CollaboratorBloc(this._usecase) : super(CollaboratorInitial()) {
    on<CollaboratorEvent>((event, emit) async {
      if (event is GetCollaboratorEvent) {
        emit(CollaboratorLoading());
        final user = AppSession.getInstance().user;
        await _usecase.getCollaborator(user!.cpf!).then((value) {
          emit(CollaboratorSuccess(value));
        }).catchError((e) {
          emit(CollaboratorError((e as ErrorResponse)));
        });
      }
    });

    on<GetImageProfileEvent>((event, emit) async {
      try {
        emit(CollaboratorLoading());
        final file = await _usecase.getImageProfile();
        emit(CollaboratorImageProfileSuccess(file));
      } catch (e) {
        if (e is ErrorResponse && e.statusCode == 500) {
          return;
        } else {
          return;
        }
      }
    });
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/i_home_repository.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

import '../../../../../app_controller.dart';
import '../../../../../shared/data/models/wallet/bank_account_user_response.dart';
import '../../../../../shared/errors/errors.dart';

part 'bank_account_event.dart';
part 'bank_account_state.dart';

class BankAccountBloc extends Bloc<BankAccountEvent, BankAccountState> {
  final IHomeRepository _usecase;
  BankAccountBloc(this._usecase) : super(BankAccountInitial()) {
    on<BankAccountEvent>((event, emit) async {
      if (event is GetBankAccountEvent) {
        emit(BankAccountLoading());
        final user = AppSession.getInstance().user;
        await _usecase.getAccountInformation(user!.cpf!).then((value) {
          AppSession.getInstance().bankAccount = value;
          emit(BankAccountSuccess(value));
        }).catchError((e) {
          // somente para o caso de não encontrar a conta
          // e em caso de conta de demonstracao
          if (e is NotFound) {
            emit(BankAccountSuccess(BankAccountResponse()));
            return;
          }
          emit(BankAccountError((e as ErrorResponse)));
        });
      }
    });
  }
}

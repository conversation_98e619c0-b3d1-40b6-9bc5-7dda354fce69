part of 'bank_account_bloc.dart';

sealed class BankAccountState extends Equatable {
  const BankAccountState();

  @override
  List<Object> get props => [];
}

final class BankAccountInitial extends BankAccountState {}

final class BankAccountLoading extends BankAccountState {}

final class BankAccountSuccess extends BankAccountState {
  final BankAccountResponse bankAccount;

  const BankAccountSuccess(this.bankAccount);

  @override
  List<Object> get props => [bankAccount];
}

final class BankAccountError extends BankAccountState {
  final ErrorResponse error;

  const BankAccountError(this.error);
}

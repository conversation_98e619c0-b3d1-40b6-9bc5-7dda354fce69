import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/home/<USER>/datasource/home_datasource.dart';
import 'package:siclosbank/app/modules/home/<USER>/datasource/home_datasource_impl.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/home_repository.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/i_home_repository.dart';
import 'package:siclosbank/app/modules/notifications/notifications_module.dart';
import 'package:siclosbank/app/modules/home/<USER>/blocs/bank_account/bank_account_bloc.dart';
import 'package:siclosbank/app/modules/home/<USER>/blocs/collaborator/collaborator_bloc.dart';
import 'package:siclosbank/app/modules/home/<USER>/view/home_view.dart';
import 'package:siclosbank/app/modules/pix/pix_module.dart';
import 'package:siclosbank/app/app_module.dart';

import 'presenter/blocs/home/<USER>';

class HomeModule extends Module {
  @override
  List<Module> get imports => [
        AppModule(),
        NotificationsModule(),
      ];

  @override
  void binds(Injector i) {
    i.addLazySingleton<IHomeDatasource>(HomeDatasourceImplements.new);
    i.addLazySingleton<IHomeRepository>(HomeRepository.new);

// blocs
    // i.add(BalanceBloc.new);
    // i.add(FastCreditBloc.new);
    i.add(BankAccountBloc.new);
    i.add(CollaboratorBloc.new);
    i.add(HomeBloc.new);
    super.binds(i);
  }

  @override
  void routes(RouteManager r) {
    r.child('/', child: (_) => const HomeView());
    r.module('/notifications', module: NotificationsModule());
    r.module('/pix', module: PixModule());
  }
}

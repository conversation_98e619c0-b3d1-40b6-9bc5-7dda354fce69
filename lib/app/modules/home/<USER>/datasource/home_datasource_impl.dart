import 'package:siclosbank/app/modules/home/<USER>/datasource/home_datasource.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

import 'endpoints/home_endpoints.dart';

class HomeDatasourceImplements implements IHomeDatasource {
  final IClient _client;

  HomeDatasourceImplements({required IClient client}) : _client = client;

  @override
  Future<ApiResponse> getAccountInformation(String cpf) async {
    var result = await _client.fetch(
      method: 'GET',
      path: HomeEndpoints.accountInformation(cpf),
    );

    return result;
  }

  @override
  Future<ApiResponse> getAccountStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate}) async {
    var result = await _client.fetch(
      method: 'GET',
      path: HomeEndpoints.accountStatement(
        cpf: cpf,
        initialDate: initialDate,
        finalDate: finalDate,
      ),
    );

    return result;
  }

  @override
  Future<ApiResponse> cancelRequestClaim({required String claimId}) async {
    Map<String, dynamic> data = {
      "id": claimId, // "d63146ff-d7f5-4e6f-a821-0286e04c133e",
      "reason": "USER_REQUESTED"
    };

    const path = HomeEndpoints.cancelRequestClaimPix;
    final result = await _client.fetch(
      method: 'POST',
      path: path,
      data: data,
    );
    return result;
  }

  @override
  Future<ApiResponse> consultRequestedClaims() async {
    const path = HomeEndpoints.consultRequestedClaimsPix;
    final result = await _client.fetch(method: 'GET', path: path);
    return result;
  }
}

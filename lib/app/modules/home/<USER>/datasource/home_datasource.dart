import 'package:siclosbank/app/shared/data/client/api_response.dart';

abstract class IHomeDatasource {
  Future<ApiResponse> getAccountInformation(String cpf);
  Future<ApiResponse> getAccountStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate});

  Future<ApiResponse> consultRequestedClaims();
  Future<ApiResponse> cancelRequestClaim({required String claimId});
}

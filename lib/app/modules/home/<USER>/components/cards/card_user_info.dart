import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/home/<USER>/blocs/bank_account/bank_account_bloc.dart';
import 'package:siclosbank/app/modules/home/<USER>/blocs/collaborator/collaborator_bloc.dart';
import 'package:siclosbank/app/modules/home/<USER>/components/loading_texts_shimer.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/errors/errors.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class CardUserInfo extends StatelessWidget {
  const CardUserInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(providers: [
      BlocProvider<BankAccountBloc>(
        create: (context) => Modular.get<BankAccountBloc>(),
      ),
      BlocProvider<CollaboratorBloc>(
        create: (context) => Modular.get<CollaboratorBloc>(),
      ),
    ], child: const _CardUserInfoView());
  }
}

class _CardUserInfoView extends StatefulWidget {
  const _CardUserInfoView({super.key});

  @override
  State<_CardUserInfoView> createState() => _CardUserInfoViewState();
}

class _CardUserInfoViewState extends State<_CardUserInfoView> {
  bool isOpen = false;

  @override
  initState() {
    super.initState();
    BlocProvider.of<BankAccountBloc>(context).add(const GetBankAccountEvent());
    BlocProvider.of<CollaboratorBloc>(context)
        .add(const GetCollaboratorEvent());
    BlocProvider.of<CollaboratorBloc>(context)
        .add(const GetImageProfileEvent());
  }

  @override
  Widget build(BuildContext context) {
    final themedata = Theme.of(context);
    final user = AppSession.getInstance().user;
    final name = user?.socialName != null && user!.socialName!.isNotEmpty
        ? user.socialName!
        : user?.name ?? '_';
    return Container(
      width: double.maxFinite,
      clipBehavior: Clip.antiAlias,
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionPanelList(
        expansionCallback: (int index, bool val) {
          setState(() {
            isOpen = val;
          });
        },
        children: [
          ExpansionPanel(
            isExpanded: isOpen,
            headerBuilder: (context, isOpen) {
              return Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: 16, vertical: isOpen ? 12 : 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    BlocBuilder<CollaboratorBloc, CollaboratorState>(
                      builder: (context, state) {
                        if (state is CollaboratorLoading) {
                          return const CircleAvatar(
                            radius: 28,
                            backgroundColor: Colors.grey,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          );
                        }
                        if (state is CollaboratorImageProfileSuccess) {
                          return Row(
                            children: [
                              CircleAvatar(
                                radius: 28,
                                backgroundImage: FileImage(state.imageProfile),
                                backgroundColor: Colors.grey[200],
                                onBackgroundImageError: (e, s) {
                                  debugPrint('Error loading profile image: $e');
                                  return;
                                },
                              ),
                              const SizedBox(width: 12),
                            ],
                          );
                        }
                        return CircleAvatar(
                          radius: 28,
                          backgroundColor: Colors.grey[200],
                          child: Icon(
                            Icons.person,
                            size: 32,
                            color: Colors.grey[400],
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            I18n.of(context)!.ola,
                            style: themedata.textTheme.displaySmall,
                          ),
                          Text(
                            name,
                            style: themedata.textTheme.titleMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
            body: Container(
              height: 124,
              width: double.maxFinite,
              color: ColorsApp.cinzaDetalhesDadosUsuario,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  BlocConsumer<CollaboratorBloc, CollaboratorState>(
                    listener: (context, state) {
                      if (state is CollaboratorError) {
                        if (state.error is! NotFound) {
                          DialogUtils.showSnackError(
                            context,
                            state.error,
                          );
                        }
                      }
                    },
                    builder: (context, state) {
                      final collaborator =
                          AppSession.getInstance().collaborator;
                      return Row(
                        children: [
                          Text(
                            'Empresa: ',
                            style: themedata.textTheme.headlineMedium!.apply(
                              fontWeightDelta: 2,
                            ),
                          ),
                          state is CollaboratorLoading
                              ? const LoadingTextsShimer()
                              : Text(
                                  collaborator?.coligadaName ?? 'Sem vinculo',
                                  style: themedata.textTheme.displaySmall,
                                ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  BlocConsumer<BankAccountBloc, BankAccountState>(
                    listener: (context, state) {
                      if (state is BankAccountError) {
                        DialogUtils.showSnackError(
                          context,
                          state.error,
                        );
                      }
                    },
                    builder: (context, state) {
                      final account = AppSession.getInstance().bankAccount;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Banco: ',
                                style:
                                    themedata.textTheme.headlineMedium!.apply(
                                  fontWeightDelta: 2,
                                ),
                              ),
                              state is BankAccountLoading
                                  ? const LoadingTextsShimer()
                                  : Text(
                                      account?.bankName ?? '-',
                                      style: themedata.textTheme.displaySmall,
                                    ),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                'Agência: ',
                                style:
                                    themedata.textTheme.headlineMedium!.apply(
                                  fontWeightDelta: 2,
                                ),
                              ),
                              state is BankAccountLoading
                                  ? const LoadingTextsShimer()
                                  : Text(
                                      account?.accountBranchCode ?? '-',
                                      style: themedata.textTheme.displaySmall,
                                    ),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                'Conta corrente: ',
                                style:
                                    themedata.textTheme.headlineMedium!.apply(
                                  fontWeightDelta: 2,
                                ),
                              ),
                              state is BankAccountLoading
                                  ? const LoadingTextsShimer()
                                  : Text(
                                      account?.accountNumber ?? '-',
                                      style: themedata.textTheme.displaySmall,
                                    ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../../app_controller.dart';
import '../../../../../shared/themes/styles/colors_app.dart';
import '../home_button_in_row.dart';

class ContainerOptions extends StatelessWidget {
  const ContainerOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        spacing: 12,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: HomeButtonInRow(
                  onTap: () => push(Routes.transaction),
                  icon: IconsApp.icTransferencia(height: 32, width: 24),
                  label: I18n.of(context)!.transferencias,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: HomeButtonInRow(
                  onTap: () => push(Routes.deposit),
                  icon: IconsApp.icDeposito(height: 32, width: 32),
                  label: I18n.of(context)!.deposito,
                ),
              ),
            ],
          ),
          ValueListenableBuilder(
            valueListenable: AppSession.getInstance().bankAccountNotifier,
            builder: (context, bankAccount, _) {
              final isLoading = bankAccount == null;
              return isLoading
                  ? Utils.shimmerLoading()
                  : HomeButtonInRow(
                      onTap: () {
                        final view =
                            AppSession.getInstance()
                                .user!
                                .viewOnboardingHomePix ==
                            true;
                        if (!view) {
                          push(Routes.pixOnboarding);
                        } else {
                          push(Routes.pix);
                        }
                      },
                      icon: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: IconsApp.icPixUnfilled(height: 24, width: 32),
                      ),
                      label: I18n.of(context)!.area_pix,
                      isLong: true,
                    );
            },
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../shared/themes/styles/colors_app.dart';

class LoadingTextsShimer extends StatelessWidget {
  const LoadingTextsShimer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: 49,
        height: 15,
        decoration: BoxDecoration(
          color: ColorsApp.cinza[200],
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );
  }
}

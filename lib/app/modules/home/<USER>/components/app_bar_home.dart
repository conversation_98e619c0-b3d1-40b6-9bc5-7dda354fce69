import 'package:flutter/material.dart';

import '../../../../shared/notifications/firebase_notifications.dart';
import '../../../../shared/utils/image_utils.dart';
import '../../../../shared/utils/utils.dart';

class AppBarHome extends StatefulWidget implements PreferredSizeWidget {
  Color? colorBackground;
  bool contemNotificacaoNaoLida;
  VoidCallback? onPressedNotifications;
  VoidCallback? onPressedSearch;

  AppBarHome({
    Key? key,
    this.colorBackground,
    this.contemNotificacaoNaoLida = false,
    this.onPressedNotifications,
    this.onPressedSearch,
  }) : super(key: key);

  @override
  _AppBarHomeState createState() => _AppBarHomeState();

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight - 8);
}

class _AppBarHomeState extends State<AppBarHome> {
  get hasNotificationUnread => widget.contemNotificacaoNaoLida;

  @override
  void initState() {
    FirebaseNotifications.firebaseMessagingNotify();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return AppBar(
      actions: [
        // IconButton(
        //   icon: const Icon(Icons.search),
        //   onPressed: () async {
        //     var result =
        //         await pushSlideResult(context, const SearchServicesView());
        //   },
        // ),
        IconButton(
          icon: hasNotificationUnread
              ? ImageUtils.icNotificacaoNew()
              : ImageUtils.icNotificacao(),
          onPressed: widget.onPressedNotifications,
        ),
      ],
      centerTitle: false,
      automaticallyImplyLeading: false,
      title: ImageUtils.siclosAppBar(),
      elevation: 0,
    );
  }
}

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../../../shared/presenter/view/components/home/<USER>/dialog_fast_credit.dart';

class HomeCarouselSlide extends StatelessWidget {
  const HomeCarouselSlide({super.key, required this.listBanner});

  final List<Map<String, dynamic>> listBanner;
  @override
  Widget build(BuildContext context) {
    return CarouselSlider.builder(
      itemCount: listBanner.length,
      itemBuilder: (context, index, realIndex) {
        return GestureDetector(
          onTap: () {
            if (listBanner[index]['alias'] == 'credito-rapido') {
              DialogInfoFastCredit.show(context: context);
            }
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: listBanner[index]['image'] as Widget,
          ),
        );
      },
      options: CarouselOptions(
        // height: MediaQuery.of(context).size.height * 0.25,
        scrollDirection: Axis.horizontal,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
        enlargeCenterPage: true, // aproxima o centro (efeito snapping)
        viewportFraction: 0.85, // ocupa quase toda a largura
        disableCenter: true,
      ),
    );
  }
}
/* 
class home_carousel_cards extends StatelessWidget {
  const home_carousel_cards({
    super.key,
    required this.listBanner,
  });


  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 210,
      child: CarouselView(
        scrollDirection: Axis.horizontal,
        itemExtent: double.infinity,
        itemSnapping: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        onTap: (i) {
          if (listBanner[i]['alias'] == 'credito-rapido') {
            DialogInfoFastCredit.show(context: context);
            // push(Routes.fastCredit);
          }
        },
        children:
            List<Widget>.generate(listBanner.length, (int index) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: listBanner[index]['image'] as Widget,
          );
        }),
      ),
    );
  }
}
 */

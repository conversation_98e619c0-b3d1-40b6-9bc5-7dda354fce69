import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';

import '../../../../shared/themes/styles/icons_app.dart';

class HomeButtonInRow extends StatelessWidget {
  const HomeButtonInRow({
    super.key,
    required this.onTap,
    required this.icon,
    required this.label,
    this.isLong = false,
  });
  final VoidCallback onTap;
  final Widget icon;
  final String label;
  final bool isLong;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme.bodyMedium;
    return !isLong
        ? _HomeButtonMedium(onTap: onTap, icon: icon, label: label)
        : Container(
            height: 60,
            decoration: const BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(10),
                child: Row(
                  children: [
                    const SizedBox(width: 12),
                    icon,
                    const SizedBox(width: 10),
                    Text(
                      label,
                      style: textTheme!,
                      overflow: TextOverflow.fade,
                    ),
                    if (isLong)
                      Expanded(
                        child: Container(
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: IconsApp.icArrowRight(
                            height: 14,
                            color: Colors.black,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
  }
}

class _HomeButtonMedium extends StatelessWidget {
  const _HomeButtonMedium({
    super.key,
    required this.onTap,
    required this.icon,
    required this.label,
  });
  final VoidCallback onTap;
  final Widget icon;
  final String label;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme.displaySmall;
    return Container(
      height: 60,
      decoration: const BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(10),
          child: Flex(
            direction: Axis.horizontal,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: icon,
              ),
              Flexible(
                fit: FlexFit.loose,
                child: Padding(
                  padding: const EdgeInsets.only(right: 4),
                  child: TextResponsive(
                    label,
                    style: textTheme,
                    overflow: TextOverflow.fade,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

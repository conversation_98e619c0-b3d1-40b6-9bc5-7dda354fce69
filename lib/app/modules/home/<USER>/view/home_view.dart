import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/home/<USER>/blocs/home/<USER>';
import 'package:siclosbank/app/modules/home/<USER>/components/app_bar_home.dart';

import 'package:siclosbank/app/modules/home/<USER>/components/cards/container_options.dart';
import 'package:siclosbank/app/modules/home/<USER>/components/home_carousel_slide.dart';
import 'package:siclosbank/app/modules/pin/presenter/view/check_pin_page.dart';
import 'package:siclosbank/app/modules/pix/models/enums/claim_type_enum.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import 'package:siclosbank/app/shared/presenter/bloc/banks/banks_bloc.dart';

import 'package:siclosbank/app/modules/home/<USER>/components/cards/card_user_info.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/themes/styles/images_app.dart';
import 'package:siclosbank/app/shared/utils/fields_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import '../../../../shared/config/flavor.dart';
import '../../../../shared/data/models/pix/claim_requested_model.dart';
import '../../../../shared/data/models/pix/claim_requested_model.dart';
import '../../../../shared/navigation/named_routes.dart';
import '../../../../shared/presenter/bloc/balance/balance_bloc.dart';
import '../../../../shared/presenter/bloc/fast_credit/fast_credit_bloc.dart';

class HomeView extends StatelessWidget {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => Modular.get<HomeBloc>(),
      child: const _HomeView(),
    );
  }
}

class _HomeView extends StatefulWidget {
  const _HomeView({super.key});

  @override
  State<_HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<_HomeView> {
  int notifyCount = 0;

  List<Map<String, dynamic>> listBanner = [
    // ImagesApp.card2(),
    {
      'alias': 'novo-app',
      'image': ImagesApp.card3(),
    },
  ];

  addCount() {
    setState(() {
      notifyCount += 1;
    });
  }

  bool hasNewNotifications = false;
  @override
  void initState() {
    BlocProvider.of<HomeBloc>(context).add(FetchNotificationsUnread());
    BlocProvider.of<BanksBloc>(context).add(const GetBankList());
    if (isHomologOrDev) {
      BlocProvider.of<FastCreditBloc>(context).add(InitFetchEvent());
      BlocProvider.of<HomeBloc>(context).add(GetRequestsClaimsPixAll());
    }

    // adiciona o banner do cartão de crédito rápido apenas em develop ou homolog
    if (isHomologOrDev) {
      listBanner.insert(0, {
        'alias': 'credito-rapido',
        'image': ImagesApp.card1(),
      });
    }
    super.initState();

    AppSession.getInstance().listClaimsPixState.addListener(() {
      final list = AppSession.getInstance().listClaimsPix;

      if (list.isNotEmpty) {
        // final cpf = AppSession.getInstance().user!.cpf ?? '';
        final id = AppSession.getInstance().user!.id!;
        final claimsByOthers = list
            .where((claim) => !claim.isClaimerById(id) && claim.isOpen)
            .toList();
// claim.isDonor(cpf)
        final i18n = I18n.of(context)!;
        if (claimsByOthers.isNotEmpty) {
          claimsByOthers.map((claim) {
            _showSheetRespondPortability(context, i18n, claim);
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () {
        BlocProvider.of<BalanceBloc>(context).add(GetBalanceEvent());
        BlocProvider.of<HomeBloc>(context).add(FetchNotificationsUnread());
        if (isHomologOrDev) {
          BlocProvider.of<FastCreditBloc>(context).add(InitFetchEvent());
        }
        return Future.value();
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is HomeNotificationsSuccess) {
            setState(() {
              hasNewNotifications = state.hasUnreadNotifications;
            });
          }
        },
        child: Scaffold(
          appBar: AppBarHome(
            onPressedNotifications: () {
              push(Routes.notifications).then((value) {
                setState(() {
                  hasNewNotifications = false;
                });
              });
            },
            contemNotificacaoNaoLida: hasNewNotifications,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              spacing: 4,
              children: [
                const CardUserInfo(),
                const CardBalanceWithCredit(
                    showStatementButton: true, compactWidget: false),
                const ContainerOptions(),
                HomeCarouselSlide(listBanner: listBanner),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _showSheetRespondPortability(
    BuildContext context,
    I18n i18n,
    ClaimRequestedModel claim,
  ) {
    final controller = MaskedTextController(
        mask: '(00) 00000-0000', text: claim.key.replaceFirst('+55', ''));

    SheetAlertConfirm.showSheet(
      context,
      title: claim.claimType.isPortability
          ? i18n.requested_portability_title
          : i18n.requested_claim_title,
      child: Column(children: [
        TextFormFieldApp(
          controller: controller,
          enable: false,
        ),
        Text(claim.claimType.isPortability
                ? i18n.requested_portability_message('')
                // Utils.formatDateTimeToBr())
                : i18n.requested_claim_message('')
            // Utils.formatDateTimeToBr(claim.completionPeriodEnd)),
            ),
      ]),
      textPositive: i18n.keep_key_here,
      clickPositive: () {
        BlocProvider.of<HomeBloc>(context)
            .add(CancelRequestedClaimPix(claim.id));
      },
      textNegative: claim.claimType.isPortability
          ? i18n.confirm_requested_portability
          : i18n.confirm_requested_claim,
      clickNegative: () {
        BlocProvider.of<HomeBloc>(context)
            .add(ConfirmRequestedClaimPix(claim.id));
      },
    );
  }
}

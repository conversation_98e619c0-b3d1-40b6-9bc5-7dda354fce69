// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:siclosbank/app/modules/home/<USER>/datasource/home_datasource.dart';
import 'package:siclosbank/app/modules/home/<USER>/repositories/i_home_repository.dart';
import 'package:siclosbank/app/shared/data/models/wallet/bank_account_user_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../../../../shared/data/models/pix/cancel_claim_model.dart';
import '../../../../shared/data/models/pix/claim_requested_model.dart';

class HomeRepository implements IHomeRepository {
  final IHomeDatasource _datasource;
  HomeRepository(this._datasource);

  @override
  Future<BankAccountResponse> getAccountInformation(String cpf) async {
    try {
      final result = await _datasource.getAccountInformation(cpf);
      return BankAccountResponse.fromMap(result.data['body']['account']);
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future getAccountStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate}) {
    // TODO: implement getAccountStatement
    throw UnimplementedError();
  }

  @override
  Future<CancelClaimModel> cancelRequestClaim({required String claimId}) async {
    final result = await _datasource.cancelRequestClaim(claimId: claimId);
    print(result.data);
    return CancelClaimModel.fromMap(result.data['body']);
  }

  @override
  Future<List<ClaimRequestedModel>> consultRequestedClaims() async {
    final result = await _datasource.consultRequestedClaims();
    print(result.data);
    final claims = result.data as List;

    return claims.map((e) => ClaimRequestedModel.fromMap(e)).toList();
  }
}

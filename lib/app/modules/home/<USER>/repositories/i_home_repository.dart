import 'package:siclosbank/app/shared/data/models/wallet/bank_account_user_response.dart';

import '../../../../shared/data/models/pix/cancel_claim_model.dart';
import '../../../../shared/data/models/pix/claim_requested_model.dart';
import '../../../../shared/data/models/pix/claim_requested_model.dart';

abstract class IHomeRepository {
  Future<BankAccountResponse> getAccountInformation(String cpf);
  Future getAccountStatement(
      {required String cpf,
      required String initialDate,
      required String finalDate});

  Future<List<ClaimRequestedModel>> consultRequestedClaims();
  Future<CancelClaimModel> cancelRequestClaim({required String claimId});
}

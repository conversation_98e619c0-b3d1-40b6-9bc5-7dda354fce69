import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/deposit/presenter/deposit_module.dart';
import 'package:siclosbank/app/modules/home/<USER>';
import 'package:siclosbank/app/modules/investment/investment_module.dart';
import 'package:siclosbank/app/modules/layout/presenter/views/home_navigation_view.dart';
import 'package:siclosbank/app/modules/loan/loan_module.dart';
import 'package:siclosbank/app/modules/profile/profile_module.dart';
import 'package:siclosbank/app/modules/transaction/transaction_module.dart';
import 'package:siclosbank/app/app_module.dart';

import '../professional/professional_module.dart';
// import 'package:siclosbank/src/shared/navigation/named_routes.dart';

class LayoutModule extends Module {
  @override
  List<Module> get imports => [AppModule()];

  @override
  void routes(RouteManager r) {
    r.child('/', child: (_) => const HomeNavigationProvider(), children: [
      // abas da tela inicial/home
      ModuleRoute("/home", module: HomeModule()),
      ModuleRoute('/profile', module: ProfileModule()),
      ModuleRoute('/professional', module: ProfessionalModule()),
      ModuleRoute('/loan', module: LoanModule()),
      ModuleRoute("/investments", module: InvestmentModule()),
    ]);
  }
}

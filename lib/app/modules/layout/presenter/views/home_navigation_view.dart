// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/layout/presenter/views/pin_blocked/pin_blocked_view.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import '../../../../shared/config/flavor.dart';
import '../../../../shared/utils/auth/local_auth.dart';
import '../components/item_menu_bar.dart';

class HomeNavigationProvider extends StatelessWidget {
  const HomeNavigationProvider({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // return BlocProvider<HomePageBloc>(
    //   create: (context) {
    //     return HomePageBloc();
    //   },
    //   child:
    // );
    return const _HomePageView();
  }
}

class _HomePageView extends StatefulWidget {
  const _HomePageView();

  @override
  State<_HomePageView> createState() => __HomePageViewState();
}

class __HomePageViewState extends State<_HomePageView>
    with WidgetsBindingObserver {
  DateTime? _datetimeMinimized;

  @override
  void initState() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // _checkBiometria();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _trataSessao(state);
    // _trataBlurView(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    final pinBlocked = AppSession.getInstance().user!.numberWrongPinAttempt!;
    if (pinBlocked >= 3) {
      return const PinBlockedView();
    }
    return homePageView(context);
  }

  Widget homePageView(context) {
    return Scaffold(
      backgroundColor: Colors.white70,
      body: const RouterOutlet(),
      bottomNavigationBar: NavigationListener(builder: (context, child) {
        return Modular.to.path.contains(Routes.layout)
            ? _buildNavigation()
            : const SizedBox();
      }),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  _buildNavigation() {
    return BottomAppBar(
      color: Colors.white,
      child: Container(
        margin: EdgeInsetsResponsive.only(
            bottom: AppSession.getInstance().bottomIOS ?? false
                ? Constants.BOTTOM_NOTCH_HEIGHT
                : 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            ItemMenuHome(
              iconSelected: ImageUtils.icGetaoSelected(),
              iconUnselected: ImageUtils.icGestaoUnselected(),
              text: I18n.of(context)!.gestao,
              page: Routes.layoutProfessional,
            ),
            Visibility(
              visible: isHomologOrDev,
              child: ItemMenuHome(
                iconSelected: ImageUtils.icEmprestimoSelected(),
                iconUnselected: ImageUtils.icEmprestimoUnselected(),
                text: I18n.of(context)!.emprestimo,
                page: Routes.layoutLoan,
              ),
            ),
            ItemMenuHome(
              iconSelected: ImageUtils.icHomeSelected(),
              iconUnselected: ImageUtils.icHomeUnselected(),
              text: I18n.of(context)!.home,
              page: Routes.layoutHome,
            ),
            Visibility(
              visible: isHomologOrDev,
              child: ItemMenuHome(
                iconSelected: ImageUtils.icInvestimentos(),
                iconUnselected: ImageUtils.icInvestimentosUnselected(),
                text: I18n.of(context)!.investimentos,
                page: Routes.layoutInvestments,
              ),
            ),
            ItemMenuHome(
              iconSelected: ImageUtils.icInfoBottomBar(),
              iconUnselected: ImageUtils.icInfoBottomBarUnselected(),
              text: I18n.of(context)!.info,
              page: Routes.layoutProfile,
            ),
          ],
        ),
      ),
    );
  }

  // _checkBiometria() async {
  //   if (await LocalAuthUtils.canCheck()) {
  //     if (!(await SharedPreferencesUtils.loginDigitalHabilitado()) &&
  //         (await SharedPreferencesUtils.showSheetLoginDigital()) &&
  //         (AppSession.getInstance().lastLogin != null &&
  //             AppSession.getInstance().lastLogin!.isNotEmpty) &&
  //         (AppSession.getInstance().lastSenha != null &&
  //             AppSession.getInstance().lastSenha!.isNotEmpty)) {
  //       SheetAlertConfirm.showSheet(context,
  //           titulo: AppSession.getInstance().useFaceID
  //               ? I18n.of(context)!.habilitar_login_face_id
  //               : I18n.of(context)!.habilitar_login_digital,
  //           mensagem: AppSession.getInstance().useFaceID
  //               ? I18n.of(context)!.login_face_id_seguro
  //               : I18n.of(context)!.login_digital_seguro,
  //           textPositive: AppSession.getInstance().useFaceID
  //               ? I18n.of(context)!.habilitar_login_face_id_btn
  //               : I18n.of(context)!.habilitar_login_digital_btn,
  //           textNegative: I18n.of(context)!.cancelar, clickNegative: () {
  //         BlocProvider.of<HomePageBloc>(context).add(ShowSnackHomeEvent(
  //             mensagem: AppSession.getInstance().useFaceID
  //                 ? I18n.of(context)!.msg_habilitar_digital_depois_face_id
  //                 : I18n.of(context)!.msg_habilitar_digital_depois,
  //             sucesso: false));
  //       }, clickPositive: () async {
  //         LocalAuthUtils localAuthUtils = LocalAuthUtils();
  //         var result = await localAuthUtils.auth(
  //             isCheck: false,
  //             showAlert: (term) {
  //               BlocProvider.of<HomePageBloc>(context)
  //                   .add(ShowSnackHomeEvent(mensagem: term, sucesso: false));
  //             });
  //         if (result != null && result) {
  //           BlocProvider.of<HomePageBloc>(context).add(ShowSnackHomeEvent(
  //               mensagem: AppSession.getInstance().useFaceID
  //                   ? I18n.of(context)!.msg_habilitar_sucesso_face_id
  //                   : I18n.of(context)!.msg_habilitar_sucesso,
  //               sucesso: true));
  //         } else {
  //           BlocProvider.of<HomePageBloc>(context).add(ShowSnackHomeEvent(
  //               mensagem: AppSession.getInstance().useFaceID
  //                   ? I18n.of(context)!.msg_habilitar_digital_depois_face_id
  //                   : I18n.of(context)!.msg_habilitar_digital_depois,
  //               sucesso: false));
  //         }
  //       });
  //     }
  //   }
  // }

  // _trataBlurView(AppLifecycleState state) {
  //   log(state.toString());
  //   if (state == AppLifecycleState.resumed) {
  //     isInactiveState.value = false;
  //     isInactiveState.notifyListeners();
  //   } else {
  //     isInactiveState.value = true;
  //     isInactiveState.notifyListeners();
  //   }
  // }

  _trataSessao(AppLifecycleState state) {
    final timeSession = AppSession.getInstance().timeSession;
    if (state != AppLifecycleState.resumed) {
      _datetimeMinimized ??= DateTime.now();
    } else if (state == AppLifecycleState.resumed &&
        _datetimeMinimized != null) {
      DateTime datetimeNow = DateTime.now();
      var duration = datetimeNow.difference(_datetimeMinimized!);
      int diferenca = duration.inSeconds;
      if (diferenca >= timeSession) {
        StorageUtils.clearDataLogin();
        navigate(Routes.intro);
      } else {
        _datetimeMinimized = null;
      }
    }
  }
}

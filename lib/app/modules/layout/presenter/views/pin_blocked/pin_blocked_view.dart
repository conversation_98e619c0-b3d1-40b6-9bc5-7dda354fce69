import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/navigation/named_routes.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../shared/themes/styles/icons_app.dart';
import '../../../../../shared/utils/image_utils.dart';

class PinBlockedView extends StatelessWidget {
  const PinBlockedView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBarApp(
        showLine: false,
        titleWidget: ImageUtils.siclosAppBar(),
        actions: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: IconsApp.icHelp(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              const I18n().conta_bloqueada_title,
              style: theme.textTheme.displayLarge,
            ),
            const SizedBox(
              height: 26,
            ),
            Text(
              const I18n().conta_bloqueada_pin_msg,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(
              height: 16,
            ),
            Text(
              const I18n().enviamos_email_desbloqueio,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(
              height: 48,
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 32),
        child: ButtonApp(
          buttonColor: Colors.transparent,
          text: const I18n().back_login_screen,
          onPress: () {
            navigate(Routes.intro);
          },
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

class ItemMenuHome extends StatelessWidget {
  final Widget iconUnselected;
  final Widget iconSelected;
  final String text;
  final String page;
  final String? currentPage;
  final Function? onClick;

  const ItemMenuHome({
    super.key,
    required this.iconSelected,
    this.currentPage,
    required this.iconUnselected,
    required this.page,
    this.onClick,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    final current = Modular.to.path;
    final toPage = getRoute(page);
    return Expanded(
      child: InkWell(
        borderRadius: const BorderRadius.all(Radius.circular(11)),
        hoverColor: Colors.transparent,
        focusColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () {
          // if (onClick != null) onClick!();
          navigate(page);
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Expanded(
              child: Container(
                child: current == toPage ? iconSelected : iconUnselected,
              ),
            ),
            Visibility(
              visible: current != toPage,
              child: Text(
                text,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(letterSpacing: 0),
              ),
            )
          ],
        ),
      ),
    );
  }
}

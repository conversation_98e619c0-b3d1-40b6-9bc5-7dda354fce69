// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/shared/data/models/wallet/ted/conta_ted_response.dart';

@JsonSerializable()
class TecUsuario {
  String id;
  String nome;
  @JsonKey(name: "imagem_perfil")
  String imagemPerfil;
  String cpf;
  List<TransactionData> accountData = [TransactionData()];

  TecUsuario({
    this.id = "",
    this.nome = "",
    this.imagemPerfil = "",
    this.cpf = "",
  });

  factory TecUsuario.fromJson(Map<String, dynamic> json) {
    List<TransactionData> transaction = [];
    if (json['accounts'] != null && json['accounts'] != "") {
      final accounts = json['accounts'] as List;
      transaction =
          accounts.map((item) => TransactionData.fromMap(item)).toList();
    }

    TecUsuario tecUser = TecUsuario(
      id: json['id'] ?? '',
      nome: json['name'] ?? '',
      imagemPerfil: json['imagem_perfil'] ?? '',
      cpf: json['cpf'] ?? '',
    );
    tecUser.accountData = transaction;

    return tecUser;
  }

  Map<String, dynamic> toJson() {
    // 'imagem_perfil': imagemPerfil,
    String cpfConverted = cpf.replaceAll('.', '');
    cpfConverted = cpfConverted.replaceAll('-', '');

    final accout = accountData.map((item) => item.toMap()).toList().first;

    return {
      'user_id': id,
      'name': nome,
      'cpf': cpfConverted,
      'account_data': accout,
    };
  }

  ContaTedResponse getContaDigital() {
    return ContaTedResponse(
      tipo: const I18n().digital,
      nome: nome ?? '',
      idUsuario: id ?? '',
    );
  }

  TecUsuario copyWith({
    String? id,
    String? nome,
    String? imagemPerfil,
    String? cpf,
  }) {
    return TecUsuario(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      imagemPerfil: imagemPerfil ?? this.imagemPerfil,
      cpf: cpf ?? this.cpf,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'nome': nome,
      'imagemPerfil': imagemPerfil,
      'cpf': cpf,
      'accountData': {},
    };
  }

  @override
  String toString() {
    return 'TecUsuario(id: $id, nome: $nome, imagemPerfil: $imagemPerfil, cpf: $cpf, accountData: $accountData)\n';
  }
}

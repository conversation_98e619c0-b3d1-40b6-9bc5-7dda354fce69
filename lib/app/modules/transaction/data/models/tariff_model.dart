// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:siclosbank/app/modules/profile/models/tariff/tariff_model.dart';

@JsonSerializable()
class TariffTransactionModel extends Equatable {
  final String title;
  final String value;

  TariffTransactionModel({
    required this.title,
    required this.value,
  });

  factory TariffTransactionModel.fromMap(List tariffs) {
    final filteredTariffs = tariffs.where((tariff) {
      return tariff['serviceName'].toString().contains('TED');
    }).toList();
    if (filteredTariffs.isNotEmpty) {
      return TariffTransactionModel(
        title:
            filteredTariffs.isNotEmpty ? filteredTariffs[0]['serviceName'] : '',
        value: filteredTariffs.isNotEmpty
            ? filteredTariffs[0]['pricePerExceedingOp']
                .toString()
                .replaceAll('.', ',')
            : '',
      );
    } else {
      return TariffTransactionModel(
        title: '',
        value: '',
      );
    }
  }

  // static List<TariffSectionModel> generetadTariffList(List source) {
  //   final tax = [
  //     {
  //       'title': "Anuidade",
  //       'tariffs': [],
  //     },
  //     {
  //       'title': "Transferências",
  //       'tariffs': [],
  //     },
  //     {
  //       'title': "Depósito",
  //       'tariffs': [],
  //     },
  //   ];
  //   final anuidade = [
  //     "c3036e17-092a-4916-80ff-5c1053a457d9",
  //   ];
  //   final transferencias = [
  //     "33ce34cf-5298-48ac-98bf-eddce78d89d9",
  //     "3ff68bb2-da23-4a04-b2f8-ce4e25b13fdc",
  //     "bc4ca575-3aac-43bf-8f63-0407414ef9f5",
  //     "35acf411-f2c4-4b0e-bff1-5700995fd301",
  //   ];
  //   final deposito = [
  //     "af756e6d-ffee-4555-a661-e0c57b78fbd5",
  //     "d8ddbf4a-7199-42a0-a981-b37f11c343a8"
  //   ];

  //   for (var item in source) {
  //     if (anuidade.contains(item['service_id'])) {
  //       final tariffs = tax[0]['tariffs'] as List;
  //       tariffs.add(item);
  //       tax[0]['tariffs'] = tariffs;
  //     }
  //     if (transferencias.contains(item['service_id'])) {
  //       final tariffs = tax[1]['tariffs'] as List;
  //       tariffs.add(item);
  //       tax[1]['tariffs'] = tariffs;
  //     }
  //     if (deposito.contains(item['service_id'])) {
  //       final tariffs = tax[2]['tariffs'] as List;
  //       tariffs.add(item);
  //       tax[2]['tariffs'] = tariffs;
  //     }
  //   }

  //   return tax.map((item) => TariffSectionModel.fromMap(item)).toList();
  // }

  @override
  List<Object> get props => [title, value];
}

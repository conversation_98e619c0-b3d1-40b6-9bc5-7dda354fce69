// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';

import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/modules/transaction/presenter/utils/account_formmatrer.dart';
import 'package:string_validator/string_validator.dart';

class TransactionModel extends Equatable {
  final String amount;
  String clientRequestId;
  final String clientFinality;
  String debitPartyAccount;
  final TransactionData creditParty;
  final String description;

  TransactionModel({
    required this.amount,
    required this.clientRequestId,
    required this.clientFinality,
    required this.debitPartyAccount,
    required this.creditParty,
    required this.description,
  });

  Map<String, dynamic> toTecMap() {
    String amountConverted = amount.replaceAll("R\$", "");
    amountConverted = amountConverted.replaceAll(".", "");
    amountConverted = amountConverted.replaceAll(",", ".");
    double amountvalue = amountConverted.toDouble();
    return <String, dynamic>{
      'amount': amountvalue,
      'debitParty': {
        'account': debitPartyAccount,
      },
      'creditParty': creditParty.toTecTransfer(),
      'description': description ?? "",
    };
  }

  Map<String, dynamic> toTedMap() {
    double amountvalue = formatAmountoToNumber(amount);

    return <String, dynamic>{
      'amount': amountvalue,
      'clientFinality': "110",
      'debitParty': {
        "account": debitPartyAccount,
      },
      'creditParty': creditParty.toTedTransfer(),
      'description': description,
    };
  }

  factory TransactionModel.createTransaction(
      TecUsuario tecUsuario, TransactionData account, String valueToTransfer) {
    return TransactionModel(
      amount: valueToTransfer,
      clientRequestId: "",
      clientFinality: "",
      debitPartyAccount: "",
      creditParty: account,
      description: "",
    );
  }

  @override
  List<Object> get props {
    return [
      amount,
      clientRequestId,
      clientFinality,
      debitPartyAccount,
      creditParty,
      description,
    ];
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_type.dart';

class TransactionData extends Equatable {
  String contactId;
  String bank;
  String account;
  String branch;
  String taxId;
  String name;
  String accountType;
  String personType;
  TransactionType transactionType;

  TransactionData({
    this.bank = "",
    this.account = "",
    this.branch = "",
    this.taxId = "",
    this.name = "",
    this.accountType = "",
    this.personType = "",
    this.contactId = "",
    this.transactionType = TransactionType.TED,
  });

  factory TransactionData.of() => TransactionData();

  Map<String, dynamic> toMap() {
    String accountConverted = account.replaceAll('-', '');
    return {
      'bank': bank,
      'account': accountConverted,
      'branch': branch,
      'taxId': taxId,
      'name': name,
      'accountType': accountType,
      'personType': personType,
      'contactId': contactId
    };
  }

  Map<String, dynamic> toTecTransfer() {
    String accountConverted = account.replaceAll('-', '');
    return {
      'account': accountConverted,
    };
  }

  Map<String, dynamic> toTedTransfer() {
    String accountConverted = account.replaceAll('-', '');
    String taxIdConverted = taxId.replaceAll('.', '');
    taxIdConverted = taxIdConverted.replaceAll('-', '');
    taxIdConverted = taxIdConverted.replaceAll('/', '');
    return {
      'bank': bank,
      'account': accountConverted,
      'branch': branch,
      'taxId': taxIdConverted,
      'name': "bank name",
      'accountType': accountType,
      'personType': personType,
    };
  }

  factory TransactionData.fromMap(Map<String, dynamic> map) {
    return TransactionData(
      contactId: map['userId'] ?? '',
      bank: map['bank'] ?? '',
      account: map['account'] ?? '',
      branch: map['branch'] ?? '',
      taxId: map['taxId'] ?? '',
      name: map['name'] ?? '',
      accountType: map['accountType'] ?? '',
      personType: map['personType'] ?? '',
      transactionType:
          TransactionType.getTypeTransactionByBank(map['bank'], map['name']),
    );
  }

  String toJson() => json.encode(toMap());

  factory TransactionData.fromJson(String source) =>
      TransactionData.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  List<Object> get props {
    return [
      bank,
      account,
      branch,
      taxId,
      name,
      accountType,
      personType,
      contactId,
      transactionType,
    ];
  }
}

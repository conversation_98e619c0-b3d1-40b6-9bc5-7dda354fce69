// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class TransactionLimitModel {
  final int usedQuantity;
  final int exemptQuantity;

  TransactionLimitModel({
    required this.usedQuantity,
    required this.exemptQuantity,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'usedQuantity': usedQuantity,
      'exemptQuantity': exemptQuantity,
    };
  }

  int get quantityAvailable => (exemptQuantity - usedQuantity);

  factory TransactionLimitModel.fromMap(List map) {
    final services = map;
    List serviceLimit = [];

    for (var service in services) {
      if (service['serviceName'] == 'TED para outros bancos') {
        serviceLimit = service['userServiceLimits'] as List;
      }
    }

    if (serviceLimit.isEmpty) {
      return TransactionLimitModel(
        usedQuantity: 0,
        exemptQuantity: 0,
      );
    }

    return TransactionLimitModel(
      usedQuantity: serviceLimit[0]['usedQuantity'] ?? 0,
      exemptQuantity: serviceLimit[0]['exemptQuantity'] ?? 0,
    );
  }

  String toJson() => json.encode(toMap());
}

enum TransactionType {
  TED("TED", 0),
  TEC("TEC", 1);

  final String description;
  final int val;

  const TransactionType(this.description, this.val);

  static TransactionType getTypeTransactionByBank(String? bank, String? name) {
    if (bank == null || bank == '') {
      return TransactionType.TEC;
    }

    if (name == null || name == '<PERSON><PERSON><PERSON> (Celcoin)') {
      return TransactionType.TEC;
    }

    return TransactionType.TED;
  }
}

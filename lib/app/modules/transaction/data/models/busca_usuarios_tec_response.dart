import 'dart:convert';

import 'tec_usuario.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class BuscaUsuariosTecResponse {
  int? total;
  int? quantidade;
  int? pagina;
  List<TecUsuario>? contatos;

  BuscaUsuariosTecResponse({
    this.total,
    this.quantidade,
    this.pagina,
    this.contatos,
  });

  factory BuscaUsuariosTecResponse.fromJson(Map<String, dynamic> json) =>
      BuscaUsuariosTecResponse(
        total: json['total'] ?? 0,
        quantidade: json['quantidade'] ?? 0,
        pagina: json['pagina'] ?? 0,
        contatos: (json['contatos'] as List<dynamic>?)
            ?.map((e) => TecUsuario.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'total': total,
        'quantidade': quantidade,
        'pagina': pagina,
        'contatos': contatos,
      };

  @override
  String toString() {
    return json.encode(toJson());
  }
}

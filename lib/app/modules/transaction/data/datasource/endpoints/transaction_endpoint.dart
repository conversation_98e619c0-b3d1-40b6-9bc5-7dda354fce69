abstract class TransactionsEndpoints {
  static String addContact() => "/contact";

  static String getContacts(String myId) =>
      "/contact/get-contact-by-user-id/$myId";

  static String remoevContact(String id) => "/contact/$id";

  static String transferBank(String type) => "/transfer_bank/$type";

  static String transactionLimit() => "/user/get-user-service-limit";

  static String serviceTariff = "/service/get-all-service-quantity";
}

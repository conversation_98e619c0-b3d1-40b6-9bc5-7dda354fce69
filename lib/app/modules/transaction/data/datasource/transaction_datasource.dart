import 'package:siclosbank/app/shared/data/client/api_response.dart';

abstract class ITransactionDatasource {
  Future<ApiResponse> getContacts({required String myId});

  Future<ApiResponse> addContacts(Map<String, dynamic> request);

  Future<ApiResponse> removeContacts({required String id});

  Future<ApiResponse> transferBank(
      Map<String, dynamic> request, String typeTransaction);

  Future<ApiResponse> getTransactionLimit();

  Future<ApiResponse> getTariffService();
}

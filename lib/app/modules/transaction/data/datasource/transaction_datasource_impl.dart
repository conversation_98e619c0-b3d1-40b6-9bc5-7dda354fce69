import 'package:siclosbank/app/modules/transaction/data/datasource/endpoints/transaction_endpoint.dart';
import 'package:siclosbank/app/modules/transaction/data/datasource/transaction_datasource.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

class TransactionDatasource implements ITransactionDatasource {
  final IClient _client;
  TransactionDatasource(this._client);

  Future? buscaTask;

  @override
  Future<ApiResponse> getContacts({required String myId}) async {
    var result = await _client.fetch(
      method: 'GET',
      path: TransactionsEndpoints.getContacts(myId),
    );

    return result;
  }

  @override
  Future<ApiResponse> addContacts(Map<String, dynamic> request) async {
    var result = await _client.fetch(
        method: 'POST',
        path: TransactionsEndpoints.addContact(),
        data: request);

    return result;
  }

  @override
  Future<ApiResponse> removeContacts({required String id}) async {
    var result = await _client.fetch(
      method: 'DELETE',
      path: TransactionsEndpoints.remoevContact(id),
    );

    return result;
  }

  @override
  Future<ApiResponse> transferBank(
      Map<String, dynamic> request, String typeTransaction) async {
    var result = await _client.fetch(
      method: 'POST',
      path: TransactionsEndpoints.transferBank(typeTransaction),
      data: request,
    );

    return result;
  }

  @override
  Future<ApiResponse> getTransactionLimit() async {
    var result = await _client.fetch(
      method: 'GET',
      path: TransactionsEndpoints.transactionLimit(),
    );

    return result;
  }

  @override
  Future<ApiResponse> getTariffService() {
    var result = _client.fetch(
      method: 'GET',
      path: TransactionsEndpoints.serviceTariff,
    );

    return result;
  }
}

import 'package:siclosbank/app/modules/transaction/data/datasource/transaction_datasource.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tariff_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_limit_model.dart';
import 'package:siclosbank/app/modules/transaction/domain/repository/transaction_repository.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class TransactionRepository implements ITransactionRepository {
  final ITransactionDatasource _datasource;

  TransactionRepository(this._datasource);

  @override
  Future<dynamic> addContact(TecUsuario user) async {
    try {
      final userId = AppSession.getInstance().user!.id!;
      user.id = userId;

      final request = user.toJson();

      final result = await _datasource.addContacts(request);

      if (result.statusCode != 201) {
        throw Exception(result);
      }

      if (result.data == null) {
        throw Exception(result);
      }

      return result.data != null;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<List<TecUsuario>> getContacts({required String id}) async {
    try {
      final user = AppSession.getInstance().user;

      final result = await _datasource.getContacts(myId: user!.id!);
      if (result.statusCode != 201) {
        throw Exception(result);
      }

      if (result.data == null) {
        return [];
      }

      final contacs = result.data as List;
      final convertedContacts = [];

      for (var contact in contacs) {
        contact['accounts'] = [contact['account_data']];
        final index =
            verifyContactInListconvertedContacts(contact, convertedContacts);

        if (contact['account_data'] != "") {
          contact['account_data']['userId'] = contact['id'];
        }

        if (index == -1) {
          convertedContacts.add(contact);
        } else {
          convertedContacts[index]['accounts'].add(contact['account_data']);
        }
      }

      return convertedContacts
          .map((contact) => TecUsuario.fromJson(contact))
          .toList();
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<dynamic> removeContact({required String id}) async {
    final result = await _datasource.removeContacts(id: id);

    try {
      if (result.statusCode != 200) {
        throw Exception(result);
      }

      if (result.data == null) {
        throw Exception(result);
      }

      return result.data != null;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  verifyContactInListconvertedContacts(
      Map<String, dynamic> contact, List contacts) {
    try {
      int index = contacts.indexWhere((item) => item['cpf'] == contact['cpf']);
      return index;
    } catch (e) {
      return -1;
    }
  }

  @override
  Future<dynamic> tecTransaction({
    required TransactionModel tecTransaction,
  }) async {
    try {
      final user = AppSession.getInstance().user;
      final bankAccount = AppSession.getInstance().bankAccount;

      if (user != null) {
        tecTransaction.clientRequestId = user.id!;
        tecTransaction.debitPartyAccount = bankAccount!.accountNumber!;

        final result =
            await _datasource.transferBank(tecTransaction.toTecMap(), "tec");

        return result.data['status'] == "PROCESSING";
      }

      throw Exception("User not found");
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future tedTransaction({required TransactionModel tedTransaction}) async {
    try {
      final user = AppSession.getInstance().user;
      final bankAccount = AppSession.getInstance().bankAccount;

      if (user != null) {
        tedTransaction.clientRequestId = user.id!;
        tedTransaction.debitPartyAccount = bankAccount!.accountNumber!;

        final result =
            await _datasource.transferBank(tedTransaction.toTedMap(), "ted");
        return result.data['status'] == "PROCESSING";
      }

      throw Exception("User not found");
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<TransactionLimitModel> getLimitTransaction() async {
    try {
      final result = await _datasource.getTransactionLimit();
      final services = result.data as List;

      return TransactionLimitModel.fromMap(services);
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<TariffTransactionModel> getTariffService() async {
    try {
      final result = await _datasource.getTariffService();
      final tariffsData = result.data as List;

      final tariffs = TariffTransactionModel.fromMap(tariffsData);

      return tariffs;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }
}

import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pin/pin_module.dart';
import 'package:siclosbank/app/modules/transaction/data/datasource/transaction_datasource.dart';
import 'package:siclosbank/app/modules/transaction/data/datasource/transaction_datasource_impl.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/repository/transaction_repository_impl.dart';
import 'package:siclosbank/app/modules/transaction/domain/repository/transaction_repository.dart';
import 'package:siclosbank/app/modules/transaction/domain/usecase/contact_usecase.dart';
import 'package:siclosbank/app/modules/transaction/domain/usecase/transaction_usecase.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/confirm_transaction/confirm_transaction_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/contact/add_contact_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/transaction_value/transaction_value_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/transferir_buscar_contatos/transferir_busca_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/pages/add_bank_contact.dart';
import 'package:siclosbank/app/modules/transaction/presenter/pages/confirm_transaction.dart';
import 'package:siclosbank/app/modules/transaction/presenter/pages/transaction_page.dart';
import 'package:siclosbank/app/modules/transaction/presenter/pages/transaction_value_page.dart';
import 'package:siclosbank/app/modules/transaction/presenter/pages/transfer_receipt.dart';
import 'package:siclosbank/app/app_module.dart';

class TransactionModule extends Module {
  @override
  void binds(i) {
    i.addLazySingleton<ITransactionDatasource>(TransactionDatasource.new);
    i.addLazySingleton<ITransactionRepository>(TransactionRepository.new);
    i.addLazySingleton<IContactUsecase>(ContactUsecase.new);
    i.addLazySingleton<ITransactionUsecase>(TransactionUsecase.new);

    i.add<AddContactBloc>(AddContactBloc.new);

    i.add(TransactionValueBloc.new);
    i.add(TransactionBloc.new);
    i.add(ConfirmTransactionBloc.new);

    super.binds(i);
  }

  @override
  List<Module> get imports => [
        AppModule(),
        PinModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (context) => const TransactionPageProvider());
    r.child(
      '/add-user-bank',
      child: (context) {
        TecUsuario? user;
        String? cpf;

        if (r.args.data['args'] is String) {
          cpf = r.args.data['args'];
        } else {
          user = r.args.data['args'];
        }

        return AddBankContactProvider(
          tecUsuario: user,
          cpf: cpf,
        );
      },
    );
    r.child(
      '/transaction-value',
      child: (context) {
        return TransactionValuePageProvider(
          tecUsuario: r.args.data['args'][0],
          account: r.args.data['args'][1],
        );
      },
    );
    r.child(
      '/confirm-transaction',
      child: (context) {
        return ConfirmTransactionPageProvider(
          tecUsuario: r.args.data['args'][0],
          account: r.args.data['args'][1],
          valueToTransfer: r.args.data['args'][2],
          transactionLimit: r.args.data['args'][3],
          tariff: r.args.data['args'][4],
        );
      },
    );

    r.child(
      '/transfer-receipt',
      child: (context) {
        return TransferReceipt(
          tecUsuario: r.args.data['args'][0],
          account: r.args.data['args'][1],
          valueToTransfer: r.args.data['args'][2],
        );
      },
    );
  }
}

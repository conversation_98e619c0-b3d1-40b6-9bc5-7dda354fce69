import 'package:siclosbank/app/modules/profile/models/tariff/tariff_section_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tariff_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_limit_model.dart';

abstract class ITransactionRepository {
  Future<dynamic> addContact(TecUsuario user);

  Future<List<TecUsuario>> getContacts({
    required String id,
  });

  Future<dynamic> removeContact({
    required String id,
  });

  Future<dynamic> tecTransaction({required TransactionModel tecTransaction});

  Future<dynamic> tedTransaction({required TransactionModel tedTransaction});

  Future<TransactionLimitModel> getLimitTransaction();

  Future<TariffTransactionModel> getTariffService();
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:siclosbank/app/modules/transaction/domain/repository/transaction_repository.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';

abstract class IContactUsecase {
  Future<List<TecUsuario>> getcontacts({
    required String id,
  });

  Future<dynamic> addcontacts({required TecUsuario user});

  Future<dynamic> removerContacts({required String id});
}

class ContactUsecase implements IContactUsecase {
  final ITransactionRepository repository;
  ContactUsecase({
    required this.repository,
  });

  @override
  Future<List<TecUsuario>> getcontacts({required String id}) async {
    return await repository.getContacts(id: id);
  }

  @override
  Future<dynamic> addcontacts({required TecUsuario user}) async {
    try {
      return await repository.addContact(user);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<dynamic> removerContacts({required String id}) async {
    return await repository.removeContact(id: id);
  }
}

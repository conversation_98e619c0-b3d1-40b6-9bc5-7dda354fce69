import 'package:siclosbank/app/modules/transaction/data/models/tariff_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_limit_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_model.dart';
import 'package:siclosbank/app/modules/transaction/domain/repository/transaction_repository.dart';

abstract class ITransactionUsecase {
  Future<dynamic> tecTransaction({required TransactionModel tecTransaction});

  Future<dynamic> tedTransaction({required TransactionModel tedTransaction});

  Future<TransactionLimitModel> getLimitTransaction();

  Future<TariffTransactionModel> getTariffTransaction();
}

class TransactionUsecase implements ITransactionUsecase {
  final ITransactionRepository repository;

  TransactionUsecase({required this.repository});

  @override
  Future<dynamic> tecTransaction(
      {required TransactionModel tecTransaction}) async {
    try {
      return await repository.tecTransaction(tecTransaction: tecTransaction);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<dynamic> tedTransaction(
      {required TransactionModel tedTransaction}) async {
    try {
      return await repository.tedTransaction(tedTransaction: tedTransaction);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<TransactionLimitModel> getLimitTransaction() async {
    return await repository.getLimitTransaction();
  }

  @override
  Future<TariffTransactionModel> getTariffTransaction() async {
    return await repository.getTariffService();
  }
}

import 'package:flutter/services.dart';
import 'package:string_validator/string_validator.dart';

class AccountFormmatrer extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String text = newValue.text
        .replaceAll(RegExp(r'\D'), ''); // Remove tudo que não for número

    int accountSize = 15;

    if (text.length > accountSize) {
      text = text.substring(0, accountSize); // <PERSON>ita a 8 caracteres
    }

    String formatted = '';
    int lastItemSubString = text.length - 1;

    if (text.isEmpty) {
      formatted = text;
    } else if (text.length > lastItemSubString) {
      formatted =
          '${text.substring(0, lastItemSubString)}-${text.substring(lastItemSubString)}';
    } else {
      formatted = text;
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

double formatAmountoToNumber(String amount) {
  String amountConverted = amount.replaceAll("R\$", "");
  amountConverted = amountConverted.replaceAll(".", "");
  amountConverted = amountConverted.replaceAll(",", ".");
  return amountConverted.toDouble();
}

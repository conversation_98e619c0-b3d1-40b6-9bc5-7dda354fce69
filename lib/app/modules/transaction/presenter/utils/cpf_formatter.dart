import 'package:flutter/services.dart';

class CpfInputFormatter extends TextInputFormatter {
  // @override
  // TextEditingValue formatEditUpdate(
  //   TextEditingValue oldValue,
  //   TextEditingValue newValue,
  // ) {
  //   String text = newValue.text
  //       .replaceAll(RegExp(r'\D'), ''); // Remove tudo que não for número
  //   if (text.length > 11) {
  //     text = text.substring(0, 11); // Limita a 11 caracteres
  //   }
  //   String formatted = '';
  //   for (int i = 0; i < text.length; i++) {
  //     if (i == 3 || i == 6) {
  //       formatted += '.';
  //     } else if (i == 9) {
  //       formatted += '-';
  //     }
  //     formatted += text[i];
  //   }
  //   return TextEditingValue(
  //     text: formatted,
  //     selection: TextSelection.collapsed(offset: formatted.length),
  //   );
  // }
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove tudo que não for dígito
    String digits = newValue.text.replaceAll(RegExp(r'\D'), '');
    String formatted = '';

    // Se o número de dígitos for até 11, formata como CPF.
    if (digits.length <= 11) {
      if (digits.length > 11) {
        digits = digits.substring(0, 11);
      }
      for (int i = 0; i < digits.length; i++) {
        if (i == 3 || i == 6) {
          formatted += '.';
        } else if (i == 9) {
          formatted += '-';
        }
        formatted += digits[i];
      }
    } else {
      // Se tiver mais que 11 dígitos, formata como CNPJ (limita a 14 dígitos)
      if (digits.length > 14) {
        digits = digits.substring(0, 14);
      }

      for (int i = 0; i < digits.length; i++) {
        if (i == 2 || i == 5) {
          formatted += '.';
        } else if (i == 8) {
          formatted += '/';
        } else if (i == 12) {
          formatted += '-';
        }
        formatted += digits[i];
      }
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }

  static String formatCpfCnpj(String input) {
    // Remove quaisquer caracteres que não sejam dígitos
    String numbers = input.replaceAll(RegExp(r'[^0-9]'), '');

    // Verifica se é CPF (11 dígitos) ou CNPJ (14 dígitos)
    if (numbers.length == 11) {
      // Formata como CPF: 000.000.000-00
      return '${numbers.substring(0, 3)}.${numbers.substring(3, 6)}.${numbers.substring(6, 9)}-${numbers.substring(9, 11)}';
    } else if (numbers.length == 14) {
      // Formata como CNPJ: 00.000.000/0000-00
      return '${numbers.substring(0, 2)}.${numbers.substring(2, 5)}.${numbers.substring(5, 8)}/${numbers.substring(8, 12)}-${numbers.substring(12, 14)}';
    }

    // Se não for CPF nem CNPJ, retorna a string original ou lança um erro
    return input;
  }

  static String formatCpfTohidden(String cpf) {
    // Remove qualquer caractere que não seja dígito
    cpf = cpf.replaceAll(RegExp(r'\D'), '');
    if (cpf.length != 11) return cpf; // ou trate o erro conforme necessário

    // Neste exemplo, vamos mascarar os 3 primeiros e os 2 últimos dígitos,
    // exibindo apenas os 6 dígitos do meio.
    String parteVisivel1 = cpf.substring(3, 6); // dígitos 4 a 6
    String parteVisivel2 = cpf.substring(6, 9); // dígitos 7 a 9

    return '***.$parteVisivel1.$parteVisivel2-**';
  }
}

import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

enum ImageAvatarSize { SMALL, MEDIUM, BIG }

class ImageAvatar extends StatefulWidget {
  final double size;
  final String? urlImage;
  final Uint8List? imageBytes;
  bool showProgress;
  bool showEditar;
  String? nomeUsuario;
  bool isQuadrada;
  bool isExpanded;
  File? fileImage;

  ImageAvatar({
    super.key,
    required this.size,
    this.urlImage,
    this.imageBytes,
    this.showProgress = false,
    this.showEditar = false,
    this.nomeUsuario,
    this.isQuadrada = false,
    this.isExpanded = false,
    this.fileImage,
  });

  @override
  _ImageAvatarState createState() => _ImageAvatarState();
}

class _ImageAvatarState extends State<ImageAvatar> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        children: <Widget>[
          Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: widget.isQuadrada ? BoxShape.rectangle : BoxShape.circle,
              borderRadius: widget.isQuadrada
                  ? widget.isExpanded
                      ? const BorderRadius.only(
                          topLeft: Radius.circular(10),
                          topRight: Radius.circular(10))
                      : BorderRadius.circular(10)
                  : null,
              color: ColorsApp.bgAvatarImg,
            ),
            child: Center(
              child: Text(
                Utils.getInitialName(nome: widget.nomeUsuario ?? '')
                    .toUpperCase(),
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      height: 1.0,
                      color: ColorsApp.cinza[700],
                    ),
              ),
            ),
          ),
          if (widget.imageBytes != null)
            SizedBox(
              width: widget.size,
              height: widget.size,
              child: ClipRRect(
                borderRadius: widget.isQuadrada
                    ? widget.isExpanded
                        ? const BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10))
                        : BorderRadius.circular(10)
                    : BorderRadius.circular(widget.size / 2),
                child: Image.memory(
                  widget.imageBytes!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: ColorsApp.bgAvatarImg,
                      child: Center(
                        child: Text(
                          Utils.getInitialName(nome: widget.nomeUsuario ?? '')
                              .toUpperCase(),
                          style:
                              Theme.of(context).textTheme.titleMedium!.copyWith(
                                    height: 1.0,
                                    color: ColorsApp.cinza[700],
                                  ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            )
          else if (widget.fileImage != null)
            SizedBox(
              width: widget.size,
              height: widget.size,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.file(
                  widget.fileImage!,
                  fit: BoxFit.cover,
                ),
              ),
            )
          else if (widget.urlImage != null)
            Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: widget.isQuadrada ? BoxShape.rectangle : BoxShape.circle,
                borderRadius: widget.isQuadrada
                    ? widget.isExpanded
                        ? const BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10))
                        : BorderRadius.circular(10)
                    : null,
                image: DecorationImage(
                  fit: BoxFit.cover,
                  image: NetworkImage(widget.urlImage ?? ""),
                ),
              ),
            ),
          if (widget.showEditar)
            SizedBox(
              child: Stack(
                children: <Widget>[
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      padding: const EdgeInsets.only(bottom: 3),
                      child: ImageUtils.icEdit(),
                    ),
                  )
                ],
              ),
            ),
          Align(
            alignment: Alignment.center,
            child: widget.showProgress
                ? Utils.circularProgressButton(size: widget.size / 4)
                : Container(),
          )
        ],
      ),
    );
  }
}

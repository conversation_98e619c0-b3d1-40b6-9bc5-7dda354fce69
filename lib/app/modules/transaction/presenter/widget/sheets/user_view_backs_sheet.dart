// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/modules/transaction/presenter/utils/cpf_formatter.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

// ignore: must_be_immutable
class SheetUserTransactionBanks extends StatelessWidget {
  Function clickAccount;
  Function clickAddAccount;
  Function clickReomoveAccout;
  Function clickReomoveContact;
  TecUsuario tecUsuario;

  SheetUserTransactionBanks({
    super.key,
    required this.clickAccount,
    required this.clickAddAccount,
    required this.clickReomoveAccout,
    required this.clickReomoveContact,
    required this.tecUsuario,
  });

  static showSheet(
    BuildContext context, {
    required Function clickAccount,
    required Function clickAddAccount,
    required Function clickReomoveAccout,
    required Function clickReomoveContact,
    required TecUsuario tecUsuario,
  }) {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      builder: (context) {
        return SheetUserTransactionBanks(
          clickAccount: clickAccount,
          clickAddAccount: clickAddAccount,
          clickReomoveAccout: clickReomoveAccout,
          clickReomoveContact: clickReomoveContact,
          tecUsuario: tecUsuario,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _SheetUserTransactionBanksl(
      clickAccount: clickAccount,
      clickAddAccount: clickAddAccount,
      clickReomoveAccout: clickReomoveAccout,
      clickReomoveContact: clickReomoveContact,
      tecUsuario: tecUsuario,
      // ),
    );
  }
}

class _SheetUserTransactionBanksl extends StatefulWidget {
  final Function clickAccount;
  final Function clickAddAccount;
  final Function clickReomoveAccout;
  final Function clickReomoveContact;
  final TecUsuario tecUsuario;

  const _SheetUserTransactionBanksl({
    super.key,
    required this.clickAccount,
    required this.clickAddAccount,
    required this.clickReomoveAccout,
    required this.clickReomoveContact,
    required this.tecUsuario,
  });

  @override
  __SheetUserTransactionBankslState createState() =>
      __SheetUserTransactionBankslState();
}

class __SheetUserTransactionBankslState
    extends State<_SheetUserTransactionBanksl> {
  User? get usuario => AppSession.getInstance().user;
  TecUsuario get tecUsuario => widget.tecUsuario;
  bool isEditUser = false;

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return PopScope(
      // onPopInvokedWithResult: (value, _) {
      //   // return value;
      // },
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          // pop(context);
          Navigator.pop(context);
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          bottomNavigationBar: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                )
              ],
            ),
            padding: EdgeInsetsResponsive.only(
                top: 30, left: 24, right: 24, bottom: 16),
            child: _buildBody(),
          ),
          // ),
        ),
      ),
    );
  }

  _buildBody() {
    var textTheme = Theme.of(context).textTheme;
    var theme = Theme.of(context);

    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        shrinkWrap: true,
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextResponsive(
                            tecUsuario.nome ?? "",
                            style: textTheme.titleMedium,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          // tecUsuario.cpf ??
                          TextResponsive(
                            CpfInputFormatter.formatCpfTohidden(tecUsuario.cpf),
                            style: textTheme.labelSmall,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 14,
                    ),
                    isEditUser
                        ? InkWell(
                            onTap: () {
                              widget.clickReomoveContact(tecUsuario);
                            },
                            child: ImageUtils.icDelete(
                              width: 32,
                              height: 32,
                              color: ColorsApp.error[300],
                            ),
                          )
                        : InkWell(
                            onTap: () {
                              setState(() {
                                isEditUser = true;
                              });
                            },
                            child: ImageUtils.icEdit(),
                          )
                  ],
                ),
                SizedBoxResponsive(height: 16),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxHeight: 140),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: widget.tecUsuario.accountData.length,
                    itemBuilder: (context, index) {
                      final account = widget.tecUsuario.accountData[index];
                      return _buildContaDigital(account);
                    },
                  ),
                ),
                SizedBoxResponsive(height: 8),
                if (!isEditUser) _buildOtherBank(),
                if (isEditUser)
                  ButtonApp(
                    border: 1,
                    text: const I18n().cancelar,
                    buttonColor: Colors.transparent,
                    onPress: () async {
                      setState(() {
                        isEditUser = false;
                      });
                    },
                  ),
              ],
            ),
          )
        ],
      ),
    );
  }

  _buildContaDigital(TransactionData account) {
    var textTheme = Theme.of(context).textTheme;
    return InkWell(
      onTap: () {
        widget.clickAccount(tecUsuario, account);
      },
      child: ContainerResponsive(
        padding: EdgeInsetsResponsive.only(top: 8, bottom: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            account.name == 'Siclos (Celcoin)'
                ? ImageUtils.icSiclos(width: 24, height: 24)
                : ImageUtils.icBank(width: 24, height: 24),
            SizedBoxResponsive(width: 24),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  TextResponsive(
                    account.name,
                    style: textTheme.bodyMedium,
                  ),
                  // I18n.of(context)!.carteira_digital
                  TextResponsive(
                    "AG ${account.branch} | ${account.accountType} ${account.account}",
                    style: textTheme.labelSmall!.copyWith(
                      color: ColorsApp.cinzaDetalhesContatoConta,
                    ),
                  ),
                ],
              ),
            ),
            if (isEditUser)
              InkWell(
                onTap: () {
                  widget.clickReomoveAccout(tecUsuario, account);
                },
                child: ImageUtils.icDelete(
                  width: 32,
                  height: 32,
                  color: ColorsApp.error[300],
                ),
              )
          ],
        ),
      ),
    );
  }

  _buildOtherBank() {
    return InkWell(
      onTap: () {
        // pop(context);
        widget.clickAddAccount(tecUsuario);
      },
      child: ContainerResponsive(
        padding: EdgeInsetsResponsive.only(top: 8, bottom: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            ImageUtils.icAdd(),
            SizedBoxResponsive(width: 16),
            TextResponsive(
              I18n.of(context)!.outro_banco,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/user_response.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

// ignore: must_be_immutable
class SheetUserRemove extends StatelessWidget {
  TecUsuario tecUsuario;
  String title;
  Function removeAccount;

  SheetUserRemove({
    Key? key,
    required this.tecUsuario,
    required this.title,
    required this.removeAccount,
  }) : super(key: key);

  static showSheet(
    BuildContext context, {
    required TecUsuario tecUsuario,
    required String title,
    required Function removeAccount,
  }) {
    showModalBottomSheet(
      elevation: 0,
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      builder: (context) {
        return SheetUserRemove(
          tecUsuario: tecUsuario,
          title: title,
          removeAccount: removeAccount,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _SheetUserRemovel(
        tecUsuario: tecUsuario, title: title, removeAccount: removeAccount
        // ),
        );
  }
}

class _SheetUserRemovel extends StatefulWidget {
  final TecUsuario tecUsuario;
  final String title;
  final Function removeAccount;

  const _SheetUserRemovel({
    Key? key,
    required this.tecUsuario,
    required this.title,
    required this.removeAccount,
  }) : super(key: key);

  @override
  __SheetUserRemovelState createState() => __SheetUserRemovelState();
}

class __SheetUserRemovelState extends State<_SheetUserRemovel> {
  User? get usuario => AppSession.getInstance().user;
  TecUsuario get tecUsuario => widget.tecUsuario;

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return PopScope(
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          Navigator.pop(context);
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          bottomNavigationBar: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                )
              ],
            ),
            padding: EdgeInsetsResponsive.only(
                top: 30, left: 24, right: 24, bottom: 16),
            child: _buildBody(),
          ),
          // ),
        ),
      ),
    );
  }

  _buildBody() {
    var textTheme = Theme.of(context).textTheme;
    var theme = Theme.of(context);
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        shrinkWrap: true,
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                TextResponsive(
                  widget.title,
                  style: textTheme.titleMedium,
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(
                  height: 16,
                ),
                ButtonApp(
                  border: 1,
                  text: const I18n().remover,
                  onPress: () async {
                    widget.removeAccount(widget.tecUsuario);
                  },
                ),
                const SizedBox(
                  height: 8,
                ),
                ButtonApp(
                  border: 1,
                  text: const I18n().cancelar,
                  buttonColor: Colors.transparent,
                  onPress: () async {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class DialogTransactionTimer extends StatefulWidget {
  const DialogTransactionTimer({super.key});

  @override
  State<DialogTransactionTimer> createState() => _DialogTransactionTimerState();
}

class _DialogTransactionTimerState extends State<DialogTransactionTimer> {
  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return PopScope(
      canPop: false,
      child: Align(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.only(
            top: 24,
            bottom: 16,
            left: 16,
            right: 16,
          ),
          margin: const EdgeInsets.only(left: 16, right: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(8),
            boxShadow: const [
              BoxShadow(
                color: ColorsApp.drop1,
                blurRadius: 36,
                offset: Offset(0.0, 16.0),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              const SizedBox(height: 16),
              Text(
                I18n.of(context)!.atencao_ao_horario,
                style: theme.textTheme.titleMedium!.copyWith(
                  fontSize: 18,
                ),
              ),
              Text(
                I18n.of(context)!.exclusivamente,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 18),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 70),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    text: I18n.of(context)!.segunda_sexta,
                    style: theme.textTheme.bodyMedium!
                        .apply(color: ColorsApp.verde[500]),
                    children: [
                      TextSpan(
                        text: I18n.of(context)!.entre,
                        style: theme.textTheme.bodyMedium,
                      ),
                      TextSpan(
                        text: I18n.of(context)!.horaInicio,
                        style: theme.textTheme.bodyMedium!
                            .apply(color: ColorsApp.verde[500]),
                      ),
                      TextSpan(
                        text: I18n.of(context)!.e,
                        style: theme.textTheme.bodyMedium,
                      ),
                      TextSpan(
                        text: I18n.of(context)!.horaFim,
                        style: theme.textTheme.bodyMedium!
                            .apply(color: ColorsApp.verde[500]),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                I18n.of(context)!.horario_brasilia,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 18),
              Text(
                I18n.of(context)!.msg_limite_investimento_4,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              ButtonApp(
                text: I18n.of(context)!.ok,
                width: MediaQuery.of(context).size.width,
                onPress: () {
                  pop();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

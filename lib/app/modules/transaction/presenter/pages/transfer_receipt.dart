import 'package:flutter/material.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/image_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class TransferReceipt extends StatelessWidget {
  final TecUsuario tecUsuario;
  final TransactionData account;
  final String valueToTransfer;
  final bank = AppSession.getInstance().bankAccount;
  final user = AppSession.getInstance().user!;
  var scr = new GlobalKey();

  TransferReceipt({
    super.key,
    required this.tecUsuario,
    required this.account,
    required this.valueToTransfer,
  });

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBarApp(
        showBack: true,
        title: const I18n().comprovante.toUpperCase(),
        clickBack: () {
          Navigator.pop(context);
        },
        actions: [
          IconButton(
            icon: ImageUtils.icShare(),
            onPressed: () {
              Utils.takeScreenShot(key: scr, fileName: "Comprovante_Boleto");
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 32, left: 16, right: 16),
        child: SingleChildScrollView(
          child: RepaintBoundary(
            key: scr,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  const I18n().transferido_de,
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 12),
                TextResponsive(
                  user.name!,
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                TextResponsive(
                  'Em cash',
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                TextResponsive(
                  "AG ${bank!.accountBranchCode} CC ${bank!.accountNumber}",
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 32),
                Text(
                  const I18n().transferir_para,
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 12),
                TextResponsive(
                  tecUsuario.nome,
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                TextResponsive(
                  account.name,
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                TextResponsive(
                  "AG ${account.branch} ${account.accountType} ${account.account}",
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Divider(thickness: .7, color: ColorsApp.lineColor),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(const I18n().data, style: theme.textTheme.bodyMedium),
                    Text(
                      "10:52:23 - 11/05/2021",
                      style: theme.textTheme.bodyLarge,
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(const I18n().valor, style: theme.textTheme.bodyMedium),
                    Text(valueToTransfer, style: theme.textTheme.bodyLarge),
                  ],
                ),
                const SizedBox(height: 30),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      const I18n().autenticacao,
                      style: theme.textTheme.bodyMedium,
                    ),
                    TextResponsive(
                      "55d5dds-ddssd5d5-dadsdav5sd5-5d5f5sf5d5s6ffsd",
                      style: theme.textTheme.bodyLarge,
                      maxLines: 2,
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tariff_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_limit_model.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/confirm_transaction/confirm_transaction_bloc.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/themes/styles/icons_app.dart';

import '../../../../shared/navigation/named_routes.dart';
import '../../../../shared/navigation/navigator_app.dart';
import '../../../pin/presenter/view/check_pin_page.dart';

class ConfirmTransactionPageProvider extends StatelessWidget {
  final TecUsuario tecUsuario;
  final TransactionData account;
  final String valueToTransfer;
  final TransactionLimitModel transactionLimit;
  final TariffTransactionModel tariff;
  const ConfirmTransactionPageProvider({
    super.key,
    required this.tecUsuario,
    required this.valueToTransfer,
    required this.account,
    required this.transactionLimit,
    required this.tariff,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ConfirmTransactionBloc>(
      create: (context) {
        return Modular.get();
      },
      child: BlocBuilder<ConfirmTransactionBloc, ConfirmTransactionState>(
        builder: (context, state) {
          return _ConfirmTransactionPage(
            tecUsuario: tecUsuario,
            account: account,
            valueToTransfer: valueToTransfer,
            transactionLimit: transactionLimit,
            tariff: tariff,
          );
        },
      ),
    );
  }
}

class _ConfirmTransactionPage extends StatefulWidget {
  final TecUsuario tecUsuario;
  final TransactionData account;
  final String valueToTransfer;
  final TransactionLimitModel transactionLimit;
  final TariffTransactionModel tariff;

  const _ConfirmTransactionPage({
    super.key,
    required this.tecUsuario,
    required this.valueToTransfer,
    required this.account,
    required this.transactionLimit,
    required this.tariff,
  });

  @override
  State<_ConfirmTransactionPage> createState() =>
      __ConfirmTransactionPageState();
}

class __ConfirmTransactionPageState extends State<_ConfirmTransactionPage> {
  final user = AppSession.getInstance().user!;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    Widget getConfirmationPage(newContext) {
      return Padding(
        padding: const EdgeInsets.only(top: 32, left: 16, right: 16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                const I18n().transferido_de,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(
                height: 12,
              ),
              TextResponsive(
                user.name!,
                style: theme.textTheme.titleMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              TextResponsive(
                Constants.BANK_ACCOUNT_NAME,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              TextResponsive(
                "CC",
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(
                height: 32,
              ),
              Text(
                const I18n().transferir_para,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(
                height: 12,
              ),
              TextResponsive(
                widget.tecUsuario.nome,
                style: theme.textTheme.titleMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              TextResponsive(
                widget.account.name,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              TextResponsive(
                widget.account.accountType,
                // "AG ${widget.tecUsuario.accountData.branch} ${widget.tecUsuario.accountData.accountType} ${widget.tecUsuario.accountData.account}",
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Divider(
                thickness: .7,
                color: ColorsApp.lineColor,
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    const I18n().valor,
                    style: theme.textTheme.bodyMedium,
                  ),
                  Text(
                    widget.valueToTransfer,
                    style: theme.textTheme.bodyLarge,
                  ),
                ],
              ),
              const SizedBox(
                height: 16,
              ),
              if (widget.transactionLimit.quantityAvailable <= 0 &&
                  widget.account.name != Constants.BANK_ACCOUNT_NAME)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      const I18n().tarifa,
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      widget.tariff.value,
                      style: theme.textTheme.bodyLarge,
                    ),
                  ],
                ),
              const SizedBox(
                height: 30,
              ),
              const CardBalanceWithCredit(
                showStatementButton: false,
                compactWidget: true,
              ),
              const SizedBox(
                height: 38,
              ),
              ButtonApp(
                width: size.width,
                border: 1,
                text: const I18n().confirmar_transferencia,
                onPress: () async {
                  CheckPinPage.showSheet(
                    context,
                    () {
                      // widget.onClick();
                      // BlocProvider.of<CheckPinBloc>(context)
                      //     .add(CheckPinEventSend(pin: pin));
                      BlocProvider.of<ConfirmTransactionBloc>(context).add(
                        ToConfirmTransactionEvent(
                          account: widget.account,
                          tecUsuario: widget.tecUsuario,
                          valueToTransfer: widget.valueToTransfer,
                        ),
                      );
                    },
                  );
                },
              ),
              // ConfirmationButtonProvider(
              //   onClick: () {
              //     // BlocProvider.of<ConfirmTransactionBloc>(context).add(
              //     //   ToConfirmTransactionEvent(
              //     //     account: widget.account,
              //     //     tecUsuario: widget.tecUsuario,
              //     //     valueToTransfer: widget.valueToTransfer,
              //     //   ),
              //     // );
              //   },
              // ),
              ButtonApp(
                width: size.width,
                border: 1,
                buttonColor: theme.scaffoldBackgroundColor,
                text: const I18n().cancelar,
                onPress: () async {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ),
      );
    }

    Widget getTransactionStatus() {
      return Padding(
        padding: const EdgeInsets.only(top: 32, left: 32, right: 32),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(
                height: 70,
              ),
              IconsApp.icCheckConfirm(color: ColorsApp.cinza),
              const SizedBox(
                height: 50,
              ),
              Text(
                const I18n().processamento_de_transferencia,
                textAlign: TextAlign.center,
                style: theme.textTheme.titleMedium!.apply(fontSizeFactor: 1.25),
              ),
              const SizedBox(
                height: 55,
              ),
              Text(
                const I18n().transferencia_status_detalhe,
                style:
                    theme.textTheme.headlineMedium!.apply(fontSizeFactor: 1.16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 155,
              ),
              ButtonApp(
                width: size.width,
                border: 1,
                text: const I18n().continuar,
                onPress: () async {
                  // push('${Routes.transaction}/transfer-receipt', args: [
                  //   widget.tecUsuario,
                  //   widget.account,
                  //   widget.valueToTransfer
                  // ]);
                  // Navigator.pop(context);
                  pushReplacement(Routes.layoutHome);
                },
              )
            ],
          ),
        ),
      );
    }

    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBarApp(
        showBack: true,
        title: const I18n().confirmacao_de_transferencia.toUpperCase(),
        clickBack: () {
          Navigator.pop(context);
        },
      ),
      body: BlocConsumer<ConfirmTransactionBloc, ConfirmTransactionState>(
        listener: (context, state) {
          // if (state is ConfirmTransactionInitial) {
          // }
          if (state is ConfirmTransactionError) {
            DialogUtils.showSnackError(context, state.error);
          }
        },
        builder: (context, state) {
          if (state is ConfirmTransactionInitial) {
            return getConfirmationPage(context);
          }

          if (state is ConfirmTransactionLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is ConfirmTransactionError) {
            return getConfirmationPage(context);
          }

          return getTransactionStatus();
        },
      ),
    );
  }
}

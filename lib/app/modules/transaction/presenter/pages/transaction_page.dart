import 'package:cpf_cnpj_validator/cpf_validator.dart';
import 'package:cpf_cnpj_validator/cnpj_validator.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/modules/transaction/presenter/widget/dialog/dialog_transaction_time.dart';
import 'package:siclosbank/app/modules/transaction/presenter/widget/sheets/user_remove_sheet.dart';
import 'package:siclosbank/app/modules/transaction/presenter/widget/sheets/user_view_backs_sheet.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/transferir_buscar_contatos/transferir_busca_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/transferir_buscar_contatos/transferir_busca_event.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/transferir_buscar_contatos/transferir_busca_state.dart';
import 'package:siclosbank/app/modules/transaction/presenter/utils/cpf_formatter.dart';
import 'package:siclosbank/app/modules/transaction/presenter/widget/image_avatar.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/date_time/check_business_time.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class TransactionPageProvider extends StatelessWidget {
  const TransactionPageProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TransactionBloc>(
      create: (context) {
        return Modular.get();
      },
      child: BlocBuilder<TransactionBloc, TransactionState>(
        builder: (context, state) {
          return _TransactionView(
            state: state,
          );
        },
      ),
    );
  }
}

class _TransactionView extends StatefulWidget {
  final TransactionState state;

  const _TransactionView({required this.state});

  @override
  __TransactionViewState createState() => __TransactionViewState();
}

class __TransactionViewState extends State<_TransactionView> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _cpfController = TextEditingController();
  // bool isShowContinueButton = false;
  bool _isBuscaNumero = false;

  @override
  void initState() {
    BlocProvider.of<TransactionBloc>(context).add(InitalEvent());

    super.initState();
    _initListeners();
  }

  _initListeners() {
    _cpfController.addListener(() {
      // if (_cpfController.text == _lastSearch) return;
      String text = _cpfController.text;
      if (_cpfController.text.isEmpty) {
        setState(() {
          _isBuscaNumero = false;
        });
      }

      if (_cpfController.text.isNotEmpty) {
        try {
          text = text.replaceAll('.', '');
          text = text.replaceAll('-', '');
          text = text.replaceAll('/', '');

          final isParse = int.tryParse(text) != null;

          if (isParse) {
            text = text.replaceAll(RegExp(r'\D'), '');
          }
          setState(() {
            _isBuscaNumero = isParse;
          });
        } catch (e) {
          setState(() {
            _isBuscaNumero = true;
          });
        }
      }

      BlocProvider.of<TransactionBloc>(context).add(
        BuscaUsuarioEvent(
          term: text,
          isNumero: _isBuscaNumero,
          validate: _formKey.currentState?.validate,
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        showBack: true,
        title: const I18n().transferencia.toUpperCase(),
        clickBack: () {
          Navigator.pop(context);
        },
      ),
      body: BlocListener<TransactionBloc, TransactionState>(
        listener: (context, state) {
          if (state.error != null) {
            DialogUtils.showSnackError(context, state.error!);
          }

          if (state.showBtnProsseguirCadastroContato) {
            Utils.unFocus(context);
          }
        },
        child: widget.state.progressCentral
            ? _buildProgressCentral()
            : _buildBody(context),
      ),
    );
  }

  getSheetUserBank(TecUsuario user) {
    SheetUserTransactionBanks.showSheet(
      context,
      tecUsuario: user,
      clickReomoveAccout: (TecUsuario tecUsuario, TransactionData account) {
        Navigator.pop(context);
        SheetUserRemove.showSheet(
          context,
          tecUsuario: tecUsuario,
          title: 'Remover a conta do contato ${user.nome}?',
          removeAccount: (TecUsuario tecUsuario) {
            if (tecUsuario.accountData.length > 1) {
              BlocProvider.of<TransactionBloc>(context).add(
                RemoveUser(tecUsuario: tecUsuario, account: account),
              );
            } else {
              BlocProvider.of<TransactionBloc>(context).add(
                RemoveAllBankToUser(tecUsuario: tecUsuario),
              );
            }
            SnackBarApp.showSnack(
                context: context,
                message: I18n().lista_contatos_atualizada,
                success: true);
            Navigator.pop(context);
          },
        );
      },
      clickReomoveContact: (TecUsuario tecUsuario) {
        Navigator.pop(context);
        SheetUserRemove.showSheet(
          context,
          tecUsuario: tecUsuario,
          title: 'Remover ${user.nome} de sua lista de contatos?',
          removeAccount: (TecUsuario tecUsuario) {
            BlocProvider.of<TransactionBloc>(context).add(
              RemoveAllBankToUser(tecUsuario: tecUsuario),
            );
            SnackBarApp.showSnack(
                context: context,
                message: I18n().lista_contatos_atualizada,
                success: true);
            Navigator.pop(context);
          },
        );
      },
      clickAccount: (TecUsuario tecUsuario, TransactionData account) {
        Navigator.pop(context);
        if (!isBusinessHour()) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return const DialogTransactionTimer();
            },
          );
          return;
        }
        push('${Routes.transaction}/transaction-value',
            args: [tecUsuario, account]);
      },
      clickAddAccount: (TecUsuario tecUsuario) {
        Navigator.pop(context);
        push('${Routes.transaction}/add-user-bank', args: tecUsuario)
            .then((resul) {
          if (resul == true) {
            SnackBarApp.showSnack(
                context: context,
                message: I18n().contatos_adicionado,
                success: true);
            BlocProvider.of<TransactionBloc>(context).add(InitalEvent());
          }
        });
        // _prosseguirCadastroConta(nomeUsuario: tecUsuario.nome);
      },
    );
  }

  ContainerResponsive _buildProgressCentral() {
    return ContainerResponsive(
      color: Colors.white,
      child: Center(
        child: Utils.circularProgressButton(),
      ),
    );
  }

  Padding _buildBody(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            const I18n().busque_seus_contatos,
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(
            height: 22,
          ),
          Form(
            key: _formKey,
            child: TextFormFieldApp(
              controller: _cpfController,
              label: I18n.of(context)!.nome_ou_cpf_cnpj,
              textInputType: TextInputType.text,

              // textInputAction: TextInputAction.search,
              validator: (String? text) {
                if (_isBuscaNumero &&
                    !CPFValidator.isValid(_cpfController.text) &&
                    _cpfController.text.length == 14) {
                  return I18n.of(context)!.cpf_error;
                } else if (_isBuscaNumero &&
                    !CNPJValidator.isValid(_cpfController.text) &&
                    _cpfController.text.length == 18) {
                  return I18n.of(context)!.cnpj_error;
                } else {
                  return null;
                }
              },
              formatter: _isBuscaNumero ? CpfInputFormatter() : null,
              isPrefixLabel: false,
              highLight: true,
              onFieldSubmitted: (p0) {
                FocusScope.of(context).nextFocus();
              },
            ),
          ),
          // _frequencyContacts(theme, size, widget.state.listUsuarioFrequente),
          SizedBoxResponsive(height: 24),
          ..._contacts(theme, widget.state.listBusca!),
          const SizedBox(
            height: 8,
          ),
          if (widget.state.isShowContinueButton)
            ButtonApp(
              width: size.width,
              border: 1,
              text: const I18n().continuar,
              onPress: () async {
                if (!isBusinessHour()) {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) {
                      return const DialogTransactionTimer();
                    },
                  );
                  return;
                }
                push('${Routes.transaction}/add-user-bank',
                        args: _cpfController.text)
                    .then((resul) {
                  if (resul == true) {
                    BlocProvider.of<TransactionBloc>(context)
                        .add(InitalEvent());
                  }
                });
              },
            ),
        ],
      ),
    );
  }

  List<Widget> _contacts(ThemeData theme, List<TecUsuario> contacts) {
    if (contacts.isEmpty) {
      return const [SizedBox()];
    }
    return [
      Text(
        const I18n().contatos,
        style: theme.textTheme.bodyMedium!.apply(color: ColorsApp.cinzaSolo),
      ),
      SizedBoxResponsive(height: 12),
      _contactsList(theme, contacts),
    ];
  }

  Widget _contactsList(ThemeData theme, List<TecUsuario> contacts) {
    return Expanded(
      flex: 1,
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: contacts.length,
        itemBuilder: (context, index) {
          final item = contacts[index];
          return InkWell(
            onTap: () {
              // pop(context);
              getSheetUserBank(item);
            },
            child: Row(
              children: [
                ImageAvatar(
                  size: 48,
                  showEditar: false,
                  showProgress: false,
                  // urlImage: "",
                  nomeUsuario: item.nome,
                ),
                SizedBoxResponsive(width: 12),
                TextResponsive(
                  item.nome!,
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        },
        separatorBuilder: (context, index) {
          return SizedBoxResponsive(height: 8);
        },
      ),
    );
  }

  Widget _frequencyContacts(
      ThemeData theme, Size size, List<TecUsuario> frequencyUsers) {
    if (frequencyUsers.isEmpty) {
      return const SizedBox();
    }

    return Column(
      children: [
        const SizedBox(
          height: 32,
        ),
        SizedBox(
          height: 140,
          width: size.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                const I18n().frequentes,
                style: theme.textTheme.bodyMedium!
                    .apply(color: ColorsApp.cinzaSolo),
              ),
              const SizedBox(
                height: 24,
              ),
              SizedBox(
                height: 90,
                child: ListView.separated(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: frequencyUsers.length,
                  itemBuilder: (context, index) {
                    final item = frequencyUsers[index];

                    return Column(
                      children: [
                        ImageAvatar(
                          size: 48,
                          showEditar: false,
                          showProgress: false,
                          // urlImage: "",
                          nomeUsuario: item.nome,
                        ),
                        SizedBoxResponsive(width: 12),
                        SizedBox(
                          width: 60,
                          child: TextResponsive(
                            item.nome,
                            style: theme.textTheme.labelSmall,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(
                      width: 24,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

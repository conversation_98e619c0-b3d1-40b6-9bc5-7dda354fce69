import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/account_type.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_type.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/data/models/wallet/bank_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/contact/add_contact_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/utils/account_formmatrer.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';

class AddBankContactProvider extends StatelessWidget {
  final TecUsuario? tecUsuario;
  final String? cpf;
  const AddBankContactProvider({super.key, this.tecUsuario, this.cpf});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AddContactBloc>(
      create: (context) {
        return Modular.get();
      },
      child: _AddContactView(
        cpf: cpf,
        tecUsuario: tecUsuario,
      ),
    );
  }
}

class _AddContactView extends StatefulWidget {
  final TecUsuario? tecUsuario;
  final String? cpf;

  const _AddContactView({
    this.tecUsuario,
    this.cpf,
  });

  @override
  __AddBankContactState createState() => __AddBankContactState();
}

class __AddBankContactState extends State<_AddContactView> {
  final _formKey = GlobalKey<FormState>();

  bool addContactListValue = false;
  bool hasContactAdded = false;

  void toogleAddListContact() {
    setState(() {
      addContactListValue = !addContactListValue;
    });
  }

  @override
  void initState() {
    BlocProvider.of<AddContactBloc>(context).add(
      InitalEvent(userToTransfer: widget.tecUsuario),
    );

    if (widget.tecUsuario != null) {
      addContactListValue = true;
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final theme = Theme.of(context);

    _getFormData(TecUsuario userToTransfer, List<AccountTypeModel> accounts,
        List<BanksModel> banks) {
      if (widget.cpf != null) {
        userToTransfer.cpf = widget.cpf!;

        if (widget.cpf!.length == 14) {
          userToTransfer.accountData.first.personType = "F";
        } else {
          userToTransfer.accountData.first.personType = "J";
        }

        userToTransfer.accountData.first.taxId = widget.cpf!;
      }
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 32,
                    ),
                    _getUserName(theme),
                    _getUserCpf(theme),
                    const SizedBox(
                      height: 32,
                    ),
                    if (widget.tecUsuario == null)
                      TextFormFieldApp(
                        label: I18n.of(context)!.nome_completo,
                        textInputType: TextInputType.text,
                        onChanged: (value) {
                          userToTransfer.nome = value;
                        },
                        // textInputAction: TextInputAction.search,
                        validator: (String? text) {
                          if (text == null || text.isEmpty) {
                            return I18n.of(context)!.nome_completo_erro;
                          } else {
                            return null;
                          }
                        },
                        isPrefixLabel: false,
                        highLight: true,
                        onFieldSubmitted: (p0) {
                          FocusScope.of(context).nextFocus();
                        },
                      ),
                    const SizedBox(
                      height: 16,
                    ),
                    ListTile(
                      title: Text(I18n.of(context)!.ted_transferencia),
                      titleTextStyle: theme.textTheme.bodyMedium,
                      leading: Radio<TransactionType>(
                        value: TransactionType.TED,
                        groupValue:
                            userToTransfer.accountData[0].transactionType,
                        onChanged: (TransactionType? value) {
                          setState(() {
                            userToTransfer.accountData[0].transactionType =
                                value!;
                          });
                        },
                      ),
                    ),
                    ListTile(
                      title: Text(I18n.of(context)!.tec_transferencia),
                      titleTextStyle: theme.textTheme.bodyMedium,
                      leading: Radio<TransactionType>(
                        value: TransactionType.TEC,
                        groupValue:
                            userToTransfer.accountData[0].transactionType,
                        onChanged: (TransactionType? value) {
                          setState(() {
                            userToTransfer.accountData[0].transactionType =
                                value!;
                            try {
                              // final selectedBank = banks.firstWhere((bank) {
                              //   // Assuming '********' is the ISPB for (CELCOIN) Siclos Bank
                              //   return bank.ispb == '**********';
                              // });
                              // userToTransfer.accountData.first.bank =
                              //     selectedBank.ispb;
                              userToTransfer.accountData.first.branch = '0001';
                              userToTransfer.accountData.first.name =
                                  Constants.BANK_ACCOUNT_NAME;
                              userToTransfer.accountData.first.accountType =
                                  accounts[0].code;
                            } catch (e) {
                              print("ispb not found");
                            }
                          });
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    if (userToTransfer.accountData[0].transactionType ==
                        TransactionType.TED)
                      DropdownMenu<BanksModel>(
                        initialSelection: null,
                        requestFocusOnTap: true,
                        enableFilter: true,
                        width: size.width,
                        menuHeight: 350,
                        trailingIcon: const SizedBox(),
                        label: Text(I18n.of(context)!.instituicao_bancaria),
                        onSelected: (value) {
                          if (value != null) {
                            userToTransfer.accountData.first.bank = value.ispb;
                            userToTransfer.accountData.first.name = value.name;
                          }
                        },
                        dropdownMenuEntries: banks
                            .map(
                              (item) => DropdownMenuEntry<BanksModel>(
                                label: "${item.code} - ${item.name}",
                                value: item,
                              ),
                            )
                            .toList(),
                      ),
                    if (userToTransfer.accountData[0].transactionType ==
                        TransactionType.TED)
                      const SizedBox(
                        height: 16,
                      ),
                    if (userToTransfer.accountData[0].transactionType ==
                        TransactionType.TED)
                      TextFormFieldApp(
                        label: I18n.of(context)!.agencia_sem_digito,
                        textInputType: TextInputType.number,
                        // textInputAction: TextInputAction.search,
                        maxLength: 4,
                        onChanged: (value) {
                          userToTransfer.accountData.first.branch = value;
                        },
                        validator: (String? text) {
                          if (text == null || text.isEmpty) {
                            return I18n.of(context)!.agencia_sem_digito;
                          } else {
                            return null;
                          }
                        },
                        isPrefixLabel: false,
                        highLight: true,

                        onFieldSubmitted: (p0) {
                          FocusScope.of(context).nextFocus();
                        },
                      ),
                    if (userToTransfer.accountData[0].transactionType ==
                        TransactionType.TED)
                      const SizedBox(
                        height: 16,
                      ),
                    TextFormFieldApp(
                      label: I18n.of(context)!.conta_com_digito,
                      textInputType: TextInputType.number,
                      onChanged: (value) {
                        userToTransfer.accountData.first.account = value;
                      },
                      validator: (String? text) {
                        if (text == null || text.isEmpty) {
                          return I18n.of(context)!.conta_com_digito;
                        } else {
                          return null;
                        }
                      },
                      isPrefixLabel: false,
                      highLight: true,
                      formatter: AccountFormmatrer(),
                      onFieldSubmitted: (p0) {
                        FocusScope.of(context).nextFocus();
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    if (userToTransfer.accountData[0].transactionType ==
                        TransactionType.TED)
                      DropdownMenu<AccountTypeModel>(
                        initialSelection: null,
                        requestFocusOnTap: true,
                        width: size.width,
                        trailingIcon: const SizedBox(),
                        label: Text(I18n.of(context)!.tipo_conta),
                        onSelected: (value) {
                          if (value != null) {
                            userToTransfer.accountData.first.accountType =
                                value.code;
                          }
                        },
                        dropdownMenuEntries: accounts
                            .map(
                              (item) => DropdownMenuEntry<AccountTypeModel>(
                                label: item.name,
                                value: item,
                              ),
                            )
                            .toList(),
                      ),
                    const SizedBox(
                      height: 16,
                    ),
                    if (widget.tecUsuario == null)
                      CheckboxListTile(
                        title: Text(
                          I18n.of(context)!.adicionar_lista_contato,
                          style: theme.textTheme.labelLarge,
                        ),
                        value: addContactListValue,
                        contentPadding: const EdgeInsets.all(0),
                        dense: true,
                        controlAffinity: ListTileControlAffinity.leading,
                        onChanged: (newValue) => toogleAddListContact(),
                      )
                  ],
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: ButtonApp(
                  width: size.width,
                  border: 1,
                  text: widget.tecUsuario == null
                      ? const I18n().continuar
                      : const I18n().salvar,
                  onPress: () async {
                    print(_formKey.currentState!.validate());
                    if (_formKey.currentState!.validate()) {
                      BlocProvider.of<AddContactBloc>(context).add(
                        SendContactEvent(
                          isAddToContact: addContactListValue,
                          userToTransfer: userToTransfer,
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBarApp(
        showBack: true,
        title: const I18n().transferencia.toUpperCase(),
        clickBack: () {
          Navigator.pop(context, false);
        },
      ),
      body: BlocConsumer<AddContactBloc, AddContactState>(
        listener: (context, state) {
          if (state is AddContactError) {
            DialogUtils.showSnackError(
                context, ErrorResponse(message: state.messageError));
            return;
          }

          if (state is AddContactSucess) {
            if (widget.tecUsuario != null) {
              Navigator.pop(context, state.hasUserAdded);
              return;
            }

            push(
              '${Routes.transaction}/transaction-value',
              args: [
                state.userToTransfer,
                state.userToTransfer.accountData.first
              ],
            );

            Navigator.pop(context, hasContactAdded);
          }
        },
        builder: (context, state) {
          if (state is AddContactInitial) {
            return _getFormData(
                state.userToTransfer, state.accounts, state.banks);
          }

          if (state is AddContactError) {
            return _getFormData(
                state.userToTransfer, state.accounts, state.banks);
          }
          return SizedBox();
        },
      ),
    );
  }

  _getUserName(theme) {
    if (widget.tecUsuario == null) return const SizedBox();

    return Text(
      widget.tecUsuario!.nome!,
      style: theme.textTheme.labelLarge,
    );
  }

  _getUserCpf(theme) {
    if (widget.cpf == null) return const SizedBox();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          const I18n().transferir_para,
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(
          height: 16,
        ),
        Text(
          widget.cpf!,
          style: theme.textTheme.labelLarge,
        ),
      ],
    );
  }
}

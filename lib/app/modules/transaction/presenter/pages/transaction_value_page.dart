import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/modules/transaction/presenter/bloc/transferir/transaction_value/transaction_value_bloc.dart';
import 'package:siclosbank/app/modules/transaction/presenter/utils/account_formmatrer.dart';
import 'package:siclosbank/app/modules/transaction/presenter/widget/image_avatar.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/home/<USER>';
import 'package:siclosbank/app/shared/presenter/view/components/others/text_form_field_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

class TransactionValuePageProvider extends StatelessWidget {
  final TecUsuario tecUsuario;
  final TransactionData account;
  const TransactionValuePageProvider(
      {super.key, required this.tecUsuario, required this.account});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TransactionValueBloc>(
      create: (context) {
        return Modular.get();
      },
      child: _TransactionValuePage(
        tecUsuario: tecUsuario,
        account: account,
      ),
    );
  }
}

class _TransactionValuePage extends StatefulWidget {
  final TecUsuario tecUsuario;
  final TransactionData account;
  const _TransactionValuePage(
      {super.key, required this.tecUsuario, required this.account});

  @override
  State<_TransactionValuePage> createState() => __TransactionValuePageState();
}

class __TransactionValuePageState extends State<_TransactionValuePage> {
  final _formKey = GlobalKey<FormState>();
  MoneyMaskedTextController controllerTextValue = MoneyMaskedTextController(
    decimalSeparator: ',',
    thousandSeparator: '.',
    leftSymbol: r'R$ ',
  );

  @override
  void initState() {
    BlocProvider.of<TransactionValueBloc>(context).add(InitalEvent());

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBarApp(
        showBack: true,
        title: const I18n().transferencia.toUpperCase(),
        clickBack: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
        child: BlocConsumer<TransactionValueBloc, TransactionValueState>(
            listener: (context, state) {
          if (state is TransactionValueError) {
            DialogUtils.showSnackError(context, state.error);
          }
        }, builder: (context, state) {
          if (state is TransactionValueLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (state is TransactionValueSucess) {
            return SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        const I18n().transferir_para,
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      if (widget.account.name != Constants.BANK_ACCOUNT_NAME)
                        if (state.transavtionLimit.quantityAvailable <= 0)
                          Text(
                            const I18n().msgTaxaTransferencia(
                                state.tariffTransaction.value),
                            style: theme.textTheme.bodyMedium,
                          )
                        else
                          Text(
                            const I18n().isencaoTarifaTransferir(
                                state.transavtionLimit.quantityAvailable
                                    .toString(),
                                state.transavtionLimit.exemptQuantity
                                    .toString()),
                            style: theme.textTheme.bodyMedium,
                          ),
                      const SizedBox(
                        height: 32,
                      ),
                      Row(
                        children: [
                          ImageAvatar(
                            size: 58,
                            showEditar: false,
                            showProgress: false,
                            // urlImage: "",
                            nomeUsuario: widget.tecUsuario.nome,
                          ),
                          SizedBoxResponsive(width: 12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextResponsive(
                                widget.tecUsuario.nome,
                                style: theme.textTheme.titleMedium,
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              TextResponsive(
                                widget.account.name,
                                style: theme.textTheme.bodyMedium,
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              TextResponsive(
                                "AG ${widget.account.branch} ${widget.account.accountType} ${widget.account.account}",
                                style: theme.textTheme.bodyMedium,
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const Divider(
                        thickness: .7,
                        color: ColorsApp.lineColor,
                      ),
                      const CardBalanceWithCredit(
                        showStatementButton: false,
                        compactWidget: true,
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      Form(
                        key: _formKey,
                        child: TextFormFieldApp(
                          controller: controllerTextValue,
                          textInputType: TextInputType.number,
                          validator: (String? text) {
                            if (text == null ||
                                text.isEmpty ||
                                text == "R\$ 0,00") {
                              return const I18n().erro_valor_transferencia;
                            } else {
                              return null;
                            }
                          },
                          isPrefixLabel: false,
                          highLight: true,
                          onFieldSubmitted: (p0) {
                            FocusScope.of(context).nextFocus();
                          },
                        ),
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                    ],
                  ),
                  ButtonApp(
                    width: size.width,
                    border: 1,
                    text: const I18n().transferir,
                    onPress: () async {
                      final amount =
                          formatAmountoToNumber(controllerTextValue.text);

                      final balance = AppSession.getInstance().balance;

                      if (balance < amount) {
                        DialogUtils.dialogErrorText(
                          context,
                          "Você precisa indicar um valor menor ou igual ao seu saldo!",
                        );
                        return;
                      }
                      if (_formKey.currentState!.validate()) {
                        Modular.to.pushReplacementNamed(
                          '${Routes.transaction}/confirm-transaction',
                          arguments: {
                            'args': [
                              widget.tecUsuario,
                              widget.account,
                              controllerTextValue.text,
                              state.transavtionLimit,
                              state.tariffTransaction,
                            ],
                          },
                        );
                      }
                    },
                  ),
                ],
              ),
            );
          }
          return const SizedBox();
        }),
      ),
    );
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/transaction/data/models/account_type.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';

import 'package:siclosbank/app/modules/transaction/domain/usecase/contact_usecase.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/wallet/bank_model.dart';

part 'add_contact_event.dart';
part 'add_contact_state.dart';

class AddContactBloc extends Bloc<AddContactEvent, AddContactState> {
  final IContactUsecase _usecase;
  AddContactBloc(this._usecase)
      : super(AddContactInitial(userToTransfer: TecUsuario())) {
    on<AddContactEvent>((event, emit) async {
      if (event is InitalEvent) {
        final result = await AppSession.getInstance().getBanks;
        final newBank = TecUsuario();
        if (event.userToTransfer != null) {
          event.userToTransfer!.accountData = newBank.accountData;
        }

        final state = AddContactInitial(
          userToTransfer: event.userToTransfer ?? newBank,
          banks: result,
          accounts: [
            AccountTypeModel(name: "Conta corrente", code: "CC"),
            AccountTypeModel(name: "Conta poupança", code: "CP")
          ],
        );

        emit(state);
      } else if (event is SendContactEvent) {
        if (event.isAddToContact) {
          final oldState = state as AddContactInitial;
          final result = await _usecase.addcontacts(user: event.userToTransfer);

          if (result is bool) {
            if (result == false) {
              emit(AddContactError(
                messageError: "Esta conta já está associada a este contato",
                isAddToContact: event.isAddToContact,
                accounts: oldState.accounts,
                banks: oldState.banks,
                userToTransfer: event.userToTransfer,
              ));
              return;
            }
            emit(AddContactSucess(
                userToTransfer: event.userToTransfer, hasUserAdded: result));
          } else {
            emit(AddContactError(
              messageError: result.message!,
              isAddToContact: event.isAddToContact,
              accounts: oldState.accounts,
              banks: oldState.banks,
              userToTransfer: event.userToTransfer,
            ));
          }
        }
        emit(AddContactSucess(
            userToTransfer: event.userToTransfer, hasUserAdded: false));
      } else if (event is CheckContactEvent) {}
    });
  }
}

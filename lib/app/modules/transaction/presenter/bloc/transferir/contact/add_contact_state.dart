// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'add_contact_bloc.dart';

sealed class AddContactState extends Equatable {}

class AddContactInitial implements AddContactState {
  TecUsuario userToTransfer;
  List<BanksModel> banks;
  List<AccountTypeModel> accounts;

  bool isAddToContact;

  AddContactInitial({
    required this.userToTransfer,
    this.banks = const [],
    this.accounts = const [],
    this.isAddToContact = false,
  });

  AddContactInitial copyWith(
      {TecUsuario? userToTransfer,
      bool? isAddToContact,
      List<BanksModel>? banks}) {
    return AddContactInitial(
      userToTransfer: userToTransfer ?? this.userToTransfer,
      isAddToContact: isAddToContact ?? this.isAddToContact,
      banks: banks ?? this.banks,
    );
  }

  @override
  List<Object?> get props => [userToTransfer, isAddToContact];

  @override
  bool? get stringify => true;
}

class AddContactLoading implements AddContactState {
  @override
  List<Object?> get props => [];

  @override
  bool? get stringify => true;
}

class AddContactError implements AddContactState {
  final String messageError;
  TecUsuario userToTransfer;
  List<BanksModel> banks;
  List<AccountTypeModel> accounts;

  bool isAddToContact;

  AddContactError({
    required this.messageError,
    required this.userToTransfer,
    required this.banks,
    required this.accounts,
    required this.isAddToContact,
  });

  @override
  List<Object?> get props => [];

  @override
  bool? get stringify => true;
}

class AddContactSucess implements AddContactState {
  final TecUsuario userToTransfer;
  double valueToTranfer;
  bool hasUserAdded;

  AddContactSucess(
      {required this.userToTransfer,
      this.valueToTranfer = 0,
      this.hasUserAdded = false});

  @override
  List<Object?> get props => [
        userToTransfer,
        valueToTranfer,
      ];

  @override
  bool? get stringify => true;
}

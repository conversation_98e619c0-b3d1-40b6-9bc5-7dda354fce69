part of 'add_contact_bloc.dart';

sealed class AddContactEvent extends Equatable {
  const AddContactEvent();

  @override
  List<Object> get props => [];
}

final class InitalEvent implements AddContactEvent {
  final TecUsuario? userToTransfer;

  InitalEvent({this.userToTransfer});

  @override
  // TODO: implement props
  List<Object> get props => throw UnimplementedError();

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

final class SendContactEvent implements AddContactEvent {
  final TecUsuario userToTransfer;
  final bool isAddToContact;

  SendContactEvent({
    required this.userToTransfer,
    required this.isAddToContact,
  });

  @override
  List<Object> get props => [
        userToTransfer,
        isAddToContact,
      ];

  @override
  bool? get stringify => false;
}

final class CheckContactEvent implements AddContactEvent {
  @override
  List<Object> get props => [success];

  @override
  bool? get stringify => false;

  final bool success;

  CheckContactEvent(this.success);
}

import 'dart:developer';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/modules/transaction/domain/usecase/contact_usecase.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'transferir_busca_event.dart';
import 'transferir_busca_state.dart';

class TransactionBloc extends Bloc<TrasnactionEvent, TransactionState> {
  final IContactUsecase usecase;

  // TransferenciaService? buscaUsuarioService;
  TransactionBloc({required this.usecase})
      : super(TransactionState.initState()) {
    // add(InicialBuscaEvent());
    // buscaUsuarioService = TransferenciaService();

    on((event, emit) async {
      if (event is InitalEvent) {
        try {
          emit(state.copy(
            showProgressUsuario: true,
          ));
          final List<TecUsuario> result = await usecase.getcontacts(id: '');
          emit(state.copy(
              showProgressUsuario: false,
              listUsuarioFrequente: result,
              listBusca: result,
              isMore: false,
              listUsuario: result,
              listDeContatosAtualizada: false));
        } catch (error) {
          emit(state.copy(
            showProgressUsuario: false,
            error: (error as Exception),
          ));
        }
      } else if (event is BuscaUsuarioEvent) {
        final List<TecUsuario>? filteredUsers = [];
        if (event.term.length < 3) {
          final users = state.listUsuario;
          emit(
            state.copy(
              showProgressUsuario: false,
              isMore: false,
              listDeContatosAtualizada: false,
              listBusca: users,
            ),
          );
          return;
        }

        try {
          for (var user in state.listUsuario) {
            if (event.isNumero! == false) {
              if (user.nome!.toLowerCase().contains(event.term.toLowerCase())) {
                filteredUsers!.add(user);
              }
            }
            if (user.cpf!.toLowerCase().contains(event.term.toLowerCase())) {
              filteredUsers!.add(user);
            }
          }
          final isNotName = int.tryParse(event.term) != null;

          if (filteredUsers!.isEmpty && isNotName) {
            final result = event.validate!();

            if (result) {
              emit(
                state.copy(
                  showProgressUsuario: false,
                  isMore: false,
                  listDeContatosAtualizada: false,
                  listBusca: filteredUsers,
                  isShowContinueButton: true,
                ),
              );
            }

            return;
          }

          emit(
            state.copy(
                showProgressUsuario: false,
                isMore: false,
                listDeContatosAtualizada: false,
                listBusca: filteredUsers,
                isShowContinueButton: false),
          );
          // if (event.term == null || event.term.isEmpty) {
          //   emit(state.copy(
          //     listBusca: [],
          //   ));
          // } else {
          //   emit(state.copy(
          //     showProgressUsuario: true,
          //   ));
          //   // BuscaUsuariosTecResponse response;
          //   // response =
          //   //     await buscaUsuarioService!.busca(term: event.term, pagina: 1);
          //   // response.contatos
          //   // response.contatos != null &&
          //   //       response.contatos!.length ==
          //   //           TransferenciaService.LIMITE_PAGINACAO,
          //   //  event.isNumero! &&
          //   //       Utils.isCpfOuCnpj(event.term) &&
          //   //       response.contatos!.isEmpty,
          //   emit(state.copy(
          //       showProgressUsuario: false,
          //       listBusca: [],
          //       isMore: false,
          //       showBtnProsseguirCadastroContato: false));
          // }
        } catch (error) {
          emit(state.copy(
            showProgressUsuario: false,
            error: (error as Exception),
          ));
        }
      } else if (event is RemoveUser) {
        final result =
            await usecase.removerContacts(id: event.account.contactId);

        if (result == true) {
          final newAccounts = event.tecUsuario.accountData.where((item) {
            return item.contactId != event.account.contactId;
          }).toList();

          event.tecUsuario.accountData = newAccounts;

          final newContacts = state.listBusca!.map((item) {
            if (item.id == event.tecUsuario.id) {
              item = event.tecUsuario;
            }
            return item;
          }).toList();

          final result = await usecase.getcontacts(id: '');

          emit(state.copy(
              showProgressUsuario: false,
              listUsuarioFrequente: result,
              listBusca: result,
              isMore: false,
              listUsuario: result,
              listDeContatosAtualizada: false));

          // emit(state.copy(
          //   listUsuarioFrequente: newContacts,
          //   listBusca: newContacts,
          // ));
        }
      } else if (event is RemoveAllBankToUser) {
        final user = event.tecUsuario;

        if (user.accountData.length > 1) {
          for (var account in user.accountData) {
            await usecase.removerContacts(id: account.contactId);
          }
        } else {
          await usecase.removerContacts(id: user.id);
        }

        final result = await usecase.getcontacts(id: '');

        emit(state.copy(
          showProgressUsuario: false,
          listUsuarioFrequente: result,
          listBusca: result,
          isMore: false,
          listUsuario: result,
          listDeContatosAtualizada: false,
        ));
      } else if (event is AtualizaContatosEvent) {
        emit(state.copy(progressAppBar: true));
        try {
          // BuscaUsuariosTecResponse? responseTodosUsuarios;
          // Iterable<Contact>? contacts;
          if (Platform.isAndroid) {
            // List<PermissionName> permissionNames = [];
            // permissionNames.add(PermissionName.Contacts);
            // List<Permissions> permissions =
            //     await Permission.requestPermissions(permissionNames);
            // if (permissions[0].permissionName == PermissionName.Contacts &&
            //     permissions[0].permissionStatus == PermissionStatus.allow) {
            //   contacts = await ContactsService.getContacts();
            // }
          } else {
            // contacts = await FastContacts.getAllContacts();
          }

          // if (contacts != null && contacts.isNotEmpty) {
          // responseTodosUsuarios = await CarteiraService.contatosAgendaUsuario(
          //     celulares: _getListTelefones(contacts));
          // }
          // if (responseTodosUsuarios != null &&
          //     responseTodosUsuarios.total! > 0) {
          //   emit(state.copy(progressAppBar: false));
          //   add(InicialBuscaEvent());
          // } else {
          //   emit(state.copy(
          //     progressAppBar: false,
          //     error: ErrorResponse(
          //       code: 444,
          //       message: const I18n().nenhum_usuario_encontrado_agenda,
          //     ),
          //   ));
          // }
        } on Exception catch (erroContatos) {
          emit(state.copy(
            progressAppBar: false,
            error: erroContatos,
          ));
        }
      } else if (event is HideBotaoCadastroContaEvent) {
        emit(state.copy(showBtnProsseguirCadastroContato: false));
      } else if (event is LimpaDadosBuscaEvent) {
        emit(state.copy(
          listBusca: null,
          isMore: false,
          listUsuarioFrequente: state.listUsuarioFrequente,
          pageUsuarios: 1,
        ));
      } else if (event is ShowProgressInicioBuscaContatosEvent) {
        emit(state.copy(showProgressUsuario: event.showProgress));
      }
    });
  }

  // _getListTelefones(Iterable<Contact> listContact) {
  //   List<String> celulares = [];
  //   for (Contact contact in listContact) {
  //     for (Phone item in contact.phones) {
  //       if (item.number.isNotEmpty) {
  //         celulares.add(item.number);
  //       }
  //     }
  //   }
  //   return celulares;
  // }
}

import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';

class TransactionState implements Equatable {
  bool showProgressUsuario;
  List<TecUsuario> listUsuarioFrequente;
  List<TecUsuario> listUsuario;
  List<TecUsuario>? listBusca;

  bool progressPaginacaoUsuario;
  bool isMore;
  bool isShowContinueButton;
  int pageUsuarios;

  bool progressCentral;
  bool progressAppBar;
  bool showBtnProsseguirCadastroContato;
  bool listaDeContatosAtualizada;

  Exception? error;

  bool get isDataUsuario =>
      (listUsuario != null && listUsuario!.length > 0) ||
      (listUsuarioFrequente != null && listUsuarioFrequente!.length > 0) ||
      (listBusca != null && listBusca!.length > 0) ||
      showBtnProsseguirCadastroContato;

  bool get isContatoUsuario => listUsuario != null && listUsuario!.length > 0;

  bool get isRecenteUsuario =>
      listUsuarioFrequente != null && listUsuarioFrequente!.length > 0;

  TransactionState({
    this.showProgressUsuario = false,
    this.listUsuarioFrequente = const [],
    this.listUsuario = const [],
    this.listBusca,
    this.isMore = false,
    this.pageUsuarios = 1,
    this.progressCentral = true,
    this.progressPaginacaoUsuario = false,
    this.error,
    this.progressAppBar = false,
    this.showBtnProsseguirCadastroContato = false,
    this.listaDeContatosAtualizada = false,
    this.isShowContinueButton = false,
  });

  static TransactionState initState() => TransactionState();

  TransactionState copy({
    bool? showProgressUsuario,
    List<TecUsuario>? listUsuarioFrequente,
    List<TecUsuario>? listUsuario,
    List<TecUsuario>? listBusca,
    bool? isMore,
    bool? progressPaginacaoUsuario,
    int? pageUsuarios,
    bool? progressCentral,
    bool? progressAppBar,
    Exception? error,
    bool? isListAgenda,
    bool? showBtnProsseguirCadastroContato,
    bool listDeContatosAtualizada = false,
    bool? isShowContinueButton,
  }) =>
      TransactionState(
          showProgressUsuario: showProgressUsuario ?? this.showProgressUsuario,
          listUsuario: listUsuario ?? this.listUsuario,
          listBusca: listBusca ?? [],
          listUsuarioFrequente:
              listUsuarioFrequente ?? this.listUsuarioFrequente,
          isMore: isMore ?? this.isMore,
          progressPaginacaoUsuario: progressPaginacaoUsuario ?? false,
          pageUsuarios: pageUsuarios ?? this.pageUsuarios,
          progressCentral: progressCentral ?? false,
          progressAppBar: progressAppBar ?? false,
          error: error,
          showBtnProsseguirCadastroContato:
              showBtnProsseguirCadastroContato ?? false,
          listaDeContatosAtualizada: listDeContatosAtualizada,
          isShowContinueButton:
              isShowContinueButton ?? this.isShowContinueButton);

  @override
  List<dynamic> get props => [
        showProgressUsuario,
        listUsuarioFrequente,
        listUsuario,
        listBusca,
        progressCentral,
        isMore,
        pageUsuarios,
        progressPaginacaoUsuario,
        progressAppBar,
        error,
        showBtnProsseguirCadastroContato,
        listaDeContatosAtualizada
      ];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

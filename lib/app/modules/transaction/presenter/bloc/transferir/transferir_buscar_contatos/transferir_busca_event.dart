// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';

abstract class TrasnactionEvent implements Equatable {}

class InitalEvent extends TrasnactionEvent {
  @override
  List<dynamic> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class BuscaUsuarioEvent extends TrasnactionEvent {
  String term;
  Function? validate;
  bool? isNumero;

  BuscaUsuarioEvent({
    required this.term,
    required this.validate,
    this.isNumero,
  });

  @override
  List<dynamic> get props => [
        term,
        isNumero,
      ];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class PaginacaoBuscaUsuarioEvent extends TrasnactionEvent {
  String? term;

  PaginacaoBuscaUsuarioEvent({this.term});

  @override
  List<dynamic> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class AtualizaContatosEvent extends TrasnactionEvent {
  @override
  List<dynamic> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class HideBotaoCadastroContaEvent extends TrasnactionEvent {
  @override
  List<dynamic> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class LimpaDadosBuscaEvent extends TrasnactionEvent {
  @override
  List<dynamic> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class ShowProgressInicioBuscaContatosEvent extends TrasnactionEvent {
  bool? showProgress;

  ShowProgressInicioBuscaContatosEvent({
    this.showProgress,
  });

  @override
  List<dynamic> get props => [];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class RemoveUser extends TrasnactionEvent {
  final TecUsuario tecUsuario;
  final TransactionData account;

  RemoveUser({
    required this.tecUsuario,
    required this.account,
  });

  @override
  // TODO: implement props
  List<Object?> get props => throw UnimplementedError();

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class RemoveAllBankToUser extends TrasnactionEvent {
  final TecUsuario tecUsuario;

  RemoveAllBankToUser({
    required this.tecUsuario,
  });

  @override
  // TODO: implement props
  List<Object?> get props => [
        tecUsuario,
      ];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

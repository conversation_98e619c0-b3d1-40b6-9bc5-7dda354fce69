import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tec_usuario.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_data.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_type.dart';
import 'package:siclosbank/app/modules/transaction/domain/usecase/contact_usecase.dart';
import 'package:siclosbank/app/modules/transaction/domain/usecase/transaction_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:string_validator/string_validator.dart';

part 'confirm_transaction_event.dart';
part 'confirm_transaction_state.dart';

class ConfirmTransactionBloc
    extends Bloc<ConfirmTransactionEvent, ConfirmTransactionState> {
  final IContactUsecase _usecase;
  final ITransactionUsecase _usecaseTransfer;
  ConfirmTransactionBloc(this._usecase, this._usecaseTransfer)
      : super(ConfirmTransactionInitial()) {
    on<ConfirmTransactionEvent>((event, emit) async {
      if (event is ToConfirmTransactionEvent) {
        try {
          final transaction = TransactionModel.createTransaction(
              event.tecUsuario, event.account, event.valueToTransfer);

          dynamic result = false;

          emit(ConfirmTransactionLoading());

          if (event.account.transactionType == TransactionType.TEC) {
            result = await _usecaseTransfer.tecTransaction(
                tecTransaction: transaction);
          } else {
            result = await _usecaseTransfer.tedTransaction(
                tedTransaction: transaction);
          }

          if (result is bool) {
            if (result == true) {
              String amountConverted =
                  event.valueToTransfer.replaceAll("R\$", "");
              amountConverted = amountConverted.replaceAll(".", "");
              amountConverted = amountConverted.replaceAll(",", ".");
              double amountvalue = amountConverted.toDouble();

              emit(ConfirmTransactionSucess(
                userToTransfer: event.tecUsuario,
                valueToTranfer: amountvalue,
              ));
            } else {
              emit(
                ConfirmTransactionError(
                  error: ErrorResponse(
                      message: "Não foi possivel realizar a transação"),
                ),
              );
            }
            return;
          }

          //
          emit(
            ConfirmTransactionError(
              error: ErrorResponse(
                  message: result.message ??
                      "Não foi possivel realizar a transação"),
            ),
          );
        } on ErrorResponse catch (e) {
          emit(ConfirmTransactionError(error: e));
        }
      } else if (event is ConfirmPinToTransactionEvent) {
      } else if (event is TransactionStatusEvent) {}
    });
  }
}

part of 'confirm_transaction_bloc.dart';

sealed class ConfirmTransactionEvent extends Equatable {
  const ConfirmTransactionEvent();

  @override
  List<Object> get props => [];
}

final class ConfirmPinToTransactionEvent implements ConfirmTransactionEvent {
  @override
  // TODO: implement props
  List<Object> get props => throw UnimplementedError();

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

final class ToConfirmTransactionEvent implements ConfirmTransactionEvent {
  final TecUsuario tecUsuario;
  final TransactionData account;
  final String valueToTransfer;

  ToConfirmTransactionEvent({
    required this.tecUsuario,
    required this.account,
    required this.valueToTransfer,
  });

  @override
  // TODO: implement props
  List<Object> get props => [
        tecUsuario,
        account,
        valueToTransfer,
      ];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

final class TransactionStatusEvent implements ConfirmTransactionEvent {
  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

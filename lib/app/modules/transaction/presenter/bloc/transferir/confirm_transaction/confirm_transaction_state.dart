// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'confirm_transaction_bloc.dart';

sealed class ConfirmTransactionState extends Equatable {}

class ConfirmTransactionInitial implements ConfirmTransactionState {
  @override
  // TODO: implement props
  List<Object?> get props => throw UnimplementedError();

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

class ConfirmTransactionData implements ConfirmTransactionState {
  TecUsuario userToTransfer;

  bool isAddToContact;

  ConfirmTransactionData({
    required this.userToTransfer,
    this.isAddToContact = false,
  });

  ConfirmTransactionData copyWith({
    TecUsuario? userToTransfer,
  }) {
    return ConfirmTransactionData(
      userToTransfer: userToTransfer ?? this.userToTransfer,
    );
  }

  @override
  List<Object?> get props => [userToTransfer, isAddToContact];

  @override
  bool? get stringify => true;
}

class ConfirmTransactionLoading implements ConfirmTransactionState {
  @override
  List<Object?> get props => [];

  @override
  bool? get stringify => true;
}

class ConfirmTransactionError implements ConfirmTransactionState {
  final ErrorResponse error;
  ConfirmTransactionError({
    required this.error,
  });

  @override
  List<Object?> get props => [];

  @override
  bool? get stringify => true;
}

class ConfirmTransactionSucess implements ConfirmTransactionState {
  final TecUsuario userToTransfer;
  double valueToTranfer;

  ConfirmTransactionSucess(
      {required this.userToTransfer, this.valueToTranfer = 0});

  @override
  List<Object?> get props => [
        userToTransfer,
        valueToTranfer,
      ];

  @override
  bool? get stringify => true;
}

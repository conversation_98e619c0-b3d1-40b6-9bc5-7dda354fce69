// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/transaction/data/models/tariff_model.dart';
import 'package:siclosbank/app/modules/transaction/data/models/transaction_limit_model.dart';
import 'package:siclosbank/app/modules/transaction/domain/usecase/transaction_usecase.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

part 'transaction_event.dart';
part 'transaction_state.dart';

class TransactionValueBloc
    extends Bloc<TransactionValueEvent, TransactionValueState> {
  final IAppUseCase _usecase;
  final ITransactionUsecase _transactionUsecase;

  TransactionValueBloc(this._usecase, this._transactionUsecase)
      : super(TransactionValueInitial()) {
    on<TransactionValueEvent>((event, emit) async {
      if (event is InitalEvent) {
        try {
          emit(TransactionValueLoading());
          // final result = await _usecase.getUserbalance();

          final transaction = await _transactionUsecase.getLimitTransaction();

          final tariff = await _transactionUsecase.getTariffTransaction();

          emit(TransactionValueSucess(
              transavtionLimit: transaction, tariffTransaction: tariff));
        } on ErrorResponse catch (e) {
          emit(TransactionValueError(e));
        }
      }
    });
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'transaction_value_bloc.dart';

sealed class TransactionValueState {}

class TransactionValueInitial implements TransactionValueState {}

class TransactionValueLoading implements TransactionValueState {}

class TransactionValueError implements TransactionValueState {
  final ErrorResponse error;
  TransactionValueError(this.error);
}

class TransactionValueSucess implements TransactionValueState {
  final TransactionLimitModel transavtionLimit;
  final TariffTransactionModel tariffTransaction;

  TransactionValueSucess({
    required this.transavtionLimit,
    required this.tariffTransaction,
  });
}

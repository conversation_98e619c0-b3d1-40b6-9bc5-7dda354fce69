part of 'transaction_value_bloc.dart';

sealed class TransactionValueEvent extends Equatable {
  const TransactionValueEvent();

  @override
  List<Object> get props => [];
}

final class InitalEvent implements TransactionValueEvent {
  InitalEvent();

  @override
  // TODO: implement props
  List<Object> get props => throw UnimplementedError();

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

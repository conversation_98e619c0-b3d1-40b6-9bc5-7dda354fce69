import 'package:siclosbank/app/modules/pin/data/datasource/pin_datasource.dart';
import 'package:siclosbank/app/modules/pin/data/repository/i_pin_repository.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

class PinRepositoryImpl implements IPinRepository {
  final IPinDatasource _datasource;
  PinRepositoryImpl(this._datasource);

  @override
  Future verifyPin({required String userId, required String pin}) async {
    try {
      final response = await _datasource.verifyPin(userId: userId, pin: pin);
      return response.data;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future createOrUpdatePin(String pin) async {
    try {
      final response = await _datasource.createOrUpdatePin(pin);
      return response.data;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }
}

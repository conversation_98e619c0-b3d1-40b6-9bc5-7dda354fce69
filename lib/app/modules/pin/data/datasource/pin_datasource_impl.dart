import 'package:siclosbank/app/modules/pin/data/datasource/pin_datasource.dart';
import 'package:siclosbank/app/modules/pin/data/datasource/pin_endpoints.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

class PinDatasourceImpl implements IPinDatasource {
  final IClient _client;
  PinDatasourceImpl(this._client);

  @override
  Future<ApiResponse> verifyPin(
      {required String userId, required String pin}) async {
    final data = {'pin': pin, 'user_id': userId};

    var result = await _client.fetch(
      method: 'POST',
      path: PinEndPoints.verifyPin,
      data: data,
    );

    return result;
  }

  @override
  Future<ApiResponse> createOrUpdatePin(String pin) async {
    final data = {'pin': pin};
    
    var result = await _client.fetch(
      method: 'POST',
      path: PinEndPoints.createOrUpdatePin,
      data: data,
    );

    return result;
  }
}

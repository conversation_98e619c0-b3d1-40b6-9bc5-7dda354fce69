import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/pin/data/datasource/pin_datasource.dart';
import 'package:siclosbank/app/modules/pin/data/datasource/pin_datasource_impl.dart';
import 'package:siclosbank/app/modules/pin/data/repository/pin_repository_impl.dart';
import 'package:siclosbank/app/modules/pin/data/repository/i_pin_repository.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/check_pin/check_pin_bloc.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/pin_registration/pin_registration_bloc.dart';
import 'package:siclosbank/app/modules/pin/presenter/view/account_blocked_pin_page.dart';
import 'package:siclosbank/app/app_module.dart';

class PinModule extends Module {
  @override
  void exportedBinds(Injector i) {
    i.addLazySingleton<IPinDatasource>(PinDatasourceImpl.new);
    i.addLazySingleton<IPinRepository>(PinRepositoryImpl.new);

    i.add(CheckPinBloc.new);
    i.add(PinRegistrationBloc.new);
    super.binds(i);
  }

  @override
  List<Module> get imports => [
        AppModule(),
      ];

  @override
  void routes(RouteManager r) {
    r.child('/pin-blocked', child: (_) => const AccountBlockedPinView());
    super.routes(r);
  }
}

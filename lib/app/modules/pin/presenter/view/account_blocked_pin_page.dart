import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../shared/utils/storage_utils.dart';

class AccountBlockedPinView extends StatefulWidget {
  const AccountBlockedPinView({super.key});

  @override
  createState() => _AccountBlockedPinViewState();
}

class _AccountBlockedPinViewState extends State<AccountBlockedPinView> {
  _AccountBlockedPinViewState();
  // final _service = locator.get<CallsAndMessagesService>();

  @override
  void initState() {
    StorageUtils.clearDataLogin();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    Utils.setScreeenResponsive(context: context);
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: ContainerResponsive(
            padding: EdgeInsetsResponsive.only(
                left: 16, right: 16, top: 40, bottom: 16),
            child: CustomScrollView(
              physics: const ClampingScrollPhysics(),
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      TextResponsive(
                        I18n.of(context)!.conta_bloqueada_title,
                        style: textTheme.displayLarge,
                      ),
                      SizedBoxResponsive(height: 16),
                      TextResponsive(
                        I18n.of(context)!.conta_bloqueada_pin_msg,
                        style: textTheme.bodyMedium,
                      ),
                      SizedBoxResponsive(height: 16),
                      TextResponsive(
                        I18n.of(context)!.enviamos_email_desbloqueio,
                        style: textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  fillOverscroll: false,
                  child: Container(
                    alignment: Alignment.bottomCenter,
                    child: InkWell(
                      onTap: () {
                        navigate(Routes.intro);
                      },
                      child: ContainerResponsive(
                        padding: EdgeInsetsResponsive.only(top: 8, bottom: 8),
                        child: Text(
                          I18n.of(context)!.back_login_screen,
                          style: textTheme.bodyLarge,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

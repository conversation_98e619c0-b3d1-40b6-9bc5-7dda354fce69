import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import 'package:siclosbank/app/modules/pin/presenter/blocs/pin_registration/pin_registration_bloc.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/pin_registration/pin_registration_event.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/pin_registration/pin_registration_state.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/edit_pin_app.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import 'package:siclosbank/app/shared/utils/storage_utils.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

class PinRegistrationPage extends StatelessWidget {
  static showSheet(
    BuildContext context, {
    required void Function(BuildContext context, bool result) onSuccess,
    bool isUpdatePin = false,
  }) {
    showModalBottomSheet(
      elevation: 10,
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      enableDrag: true,
      builder: (context) {
        return PinRegistrationPage(
          onSuccess: onSuccess,
          isUpdatePin: isUpdatePin,
        );
      },
    );
  }

  final void Function(BuildContext context, bool result)? onSuccess;
  final bool isUpdatePin;

  const PinRegistrationPage({
    super.key,
    this.onSuccess,
    this.isUpdatePin = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: BlocProvider<PinRegistrationBloc>(
        create: (context) => Modular.get<PinRegistrationBloc>(),
        child: _PinRegistrationView(
          onSuccess: onSuccess,
          isUpdatePin: isUpdatePin,
        ),
      ),
    );
  }
}

class _PinRegistrationView extends StatefulWidget {
  final Function(BuildContext context, bool result)? onSuccess;
  final bool isUpdatePin;

  const _PinRegistrationView({
    super.key,
    this.onSuccess,
    required this.isUpdatePin,
  });

  @override
  State<_PinRegistrationView> createState() => __PinRegistrationView();
}

class __PinRegistrationView extends State<_PinRegistrationView> {
  Function(BuildContext context, bool result)? get onSuccess =>
      widget.onSuccess;
  bool get isUpdatePin => widget.isUpdatePin;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: BlocListener<PinRegistrationBloc, PinRegistrationState>(
          listener: (context, state) {
            if (state.isSuccess) {
              Navigator.pop(context);

              if (onSuccess != null) onSuccess!(context, state.isSuccess);
            }

            if (state.error != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (!mounted) return;

                DialogUtils.showSnackError(context, state.error!);
              });
            }
          },
          child: Container(
            // height: MediaQuery.of(context).size.height == 592
            //     ? MediaQuery.of(context).size.height
            //     : MediaQuery.of(context).size.height * 0.4,
            margin: const EdgeInsets.only(top: 85),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 64,
                  color: ColorsApp.drop1,
                  offset: Offset(0, -4),
                )
              ],
            ),
            child: BlocBuilder<PinRegistrationBloc, PinRegistrationState>(
              builder: (context, state) {
                if (state.isProgress) {
                  return _buildProgress();
                } else {
                  return _buildBody(
                    isConfirmPin: state.isConfirmPin,
                    error: state.error,
                    tryAgain: state.tryAgain,
                  );
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  _buildBody({
    required bool isConfirmPin,
    Exception? error,
    required bool tryAgain,
  }) {
    var textTheme = Theme.of(context).textTheme;
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 26, bottom: 26),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            isUpdatePin ? "Novo PIN" : I18n.of(context)!.cadastro_pin,
            style: textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            isUpdatePin
                ? "Insira a nova senha de 4 digitos que deseja usar."
                : I18n.of(context)!.cadastro_pin_msg,
            style: textTheme.bodyMedium!.copyWith(color: ColorsApp.cinza[800]),
          ),
          const SizedBox(height: 23),
          Text(
            isConfirmPin
                ? I18n.of(context)!.digite_novamente_pin
                : tryAgain
                    ? const I18n().pin_nao_confere
                    : error != null && error is ErrorResponse
                        ? error.message ?? ""
                        : isUpdatePin
                            ? ""
                            : I18n.of(context)!.digite_pin_4,
            textAlign: TextAlign.center,
            style: textTheme.bodyLarge!.copyWith(
                color: error != null
                    ? ColorsApp.error[500]
                    : ColorsApp.cinza[800]),
          ),
          const SizedBox(height: 26),
          EditPinApp(
            isEditRegistration: !isConfirmPin,
            resetPin: error != null && (!isConfirmPin),
            onText: (String pin) {
              if (isConfirmPin) {
                BlocProvider.of<PinRegistrationBloc>(context)
                    .add(ConfirmPinEvent(confirmPin: pin));
              } else {
                BlocProvider.of<PinRegistrationBloc>(context)
                    .add(SendPinEvent(pin: pin));
              }
            },
          ),
        ],
      ),
    );
  }

  _buildProgress() {
    return Container(
      child: Center(
        child: Container(
          child: Utils.circularProgressButton(size: 40),
        ),
      ),
    );
  }
}

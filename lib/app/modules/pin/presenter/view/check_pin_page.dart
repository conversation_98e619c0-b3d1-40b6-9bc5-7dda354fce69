import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/check_pin/check_pin_bloc.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/check_pin/check_pin_event.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/check_pin/check_pin_state.dart';

import 'package:siclosbank/app/modules/pin/presenter/view/pin_registration_page.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/edit_pin_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../shared/navigation/named_routes.dart';

enum PinProviderType { PIN, PASSWORD, REGISTRATION_PASSWORD }

class CheckPinPage {
  static showSheet(
    BuildContext context,
    Function() onSuccess, {
    PinProviderType pinProviderType = PinProviderType.PIN,
    bool isUpdatePin = false,
    bool autoClose =
        true, // Novo parâmetro para controlar fechamento automático
  }) {
    show(context) => _showSheetBottom(
      context: context,
      onSuccess: onSuccess,
      // errorResponse: errorResponse,
      pinProviderType: pinProviderType,
      isUpdatePin: isUpdatePin,
      autoClose: autoClose, // Passa o parâmetro adiante
    );
    final user = AppSession.getInstance().user;
    if (user == null) {
      final hasPin = AppSession.getInstance().authorizeDeviceResponse?.pin;
      if (hasPin == true) {
        return show(context);
      }
    }
    final pin = user?.pin;

    if (pin == null || pin.isEmpty) {
      return PinRegistrationPage.showSheet(
        context,
        isUpdatePin: isUpdatePin,
        onSuccess: (context, success) => show(context),
      );
    } else {
      return show(context);
    }
  }

  static _showSheetBottom({
    required BuildContext context,
    required Function onSuccess,
    required PinProviderType? pinProviderType,
    required bool isUpdatePin,
    bool autoClose = true, // Novo parâmetro
  }) {
    showModalBottomSheet(
      elevation: 10,
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      enableDrag: true,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: BlocProvider<CheckPinBloc>(
            create: (context) => Modular.get<CheckPinBloc>(),
            child: _CheckPinView(
              onPin: onSuccess,
              pinProviderType: pinProviderType,
              isUpdatePin: isUpdatePin,
              autoClose: autoClose, // Passa para o widget
            ),
          ),
        );
      },
    );
  }
}

class _CheckPinView extends StatefulWidget {
  final Function onPin;
  final PinProviderType? pinProviderType;
  final bool isUpdatePin;
  final bool autoClose; // Novo parâmetro

  const _CheckPinView({
    required this.onPin,
    this.pinProviderType,
    this.isUpdatePin = false,
    this.autoClose = true, // Valor padrão
  });

  @override
  State<_CheckPinView> createState() => __CheckPinView();
}

class __CheckPinView extends State<_CheckPinView> {
  // bool _resetPin = false;

  Function get onPin => widget.onPin;
  PinProviderType? get pinProviderType => widget.pinProviderType;
  bool get isUpdatePin => widget.isUpdatePin;

  ErrorResponse? errorResponse;
  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    return SafeArea(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: ContainerResponsive(
          // height: MediaQuery.of(context).size.height <= 592
          //     ? MediaQuery.of(context).size.height
          //     : MediaQuery.of(context).size.height * 0.37,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            boxShadow: [
              BoxShadow(
                blurRadius: 64,
                color: ColorsApp.drop1,
                offset: Offset(0, -4),
              ),
            ],
          ),
          child: _buildBodyPin(),
        ),
      ),
    );
  }

  _buildBodyPin() {
    var textTheme = Theme.of(context).textTheme;

    return BlocConsumer<CheckPinBloc, CheckPinState>(
      listener: (context, state) async {
        if (state is ErrorCheckPinState) {
          if (state.error.statusCode == 406) {
            errorResponse = ErrorResponse(
              message: I18n.of(context)!.tentativasPinBloqueio("3"),
            );
          }

          if (state.error.statusCode == 407) {
            push(Routes.pinBlocked);
          }
        }

        if (state is SuccessCheckPinState) {
          setState(() {
            errorResponse = null;
          });

          // Só fecha automaticamente se autoClose for true
          if (widget.autoClose) {
            Navigator.pop(context);
          }

          if (isUpdatePin) {
            await PinRegistrationPage.showSheet(
              context,
              onSuccess: (context, result) {
                SnackBarApp.showSnack(
                  context: context,
                  message: I18n.of(context)!.pin_alterado_sucesso,
                  success: true,
                );
              },
              isUpdatePin: true,
            );
          } else {
            onPin();
          }
        }
      },
      builder: (context, state) => GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Container(
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
            top: 24,
            bottom: 24,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              TextResponsive(
                isUpdatePin
                    ? I18n.of(context)!.digite_pin_atual
                    : I18n.of(context)!.pin,
                textAlign: TextAlign.center,
                style: textTheme.titleSmall!.copyWith(
                  color: ColorsApp.cinza[900],
                  fontSize: 16.0,
                  fontWeight: FontWeight.w700,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              SizedBoxResponsive(height: 16),
              errorResponse != null
                  ? TextResponsive(
                      errorResponse?.message ?? '',
                      style: textTheme.bodyMedium!.copyWith(
                        color: ColorsApp.error[300],
                      ),
                    )
                  : TextResponsive(
                      I18n.of(context)!.digite_seu_pin_text,
                      style: textTheme.bodyMedium!.copyWith(
                        color: ColorsApp.cinza[800],
                      ),
                    ),
              SizedBoxResponsive(height: 26),
              state is LoadingCheckPinState
                  ? const Center(child: CircularProgressIndicator())
                  : Builder(
                      builder: (context) {
                        return EditPinApp(
                          // resetPin: _resetPin,
                          onText: (String pin) {
                            if (errorResponse != null) {
                              setState(() {
                                errorResponse = null;
                              });
                            }
                            BlocProvider.of<CheckPinBloc>(
                              context,
                            ).add(CheckPinEventSend(pin: pin));
                          },
                        );
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

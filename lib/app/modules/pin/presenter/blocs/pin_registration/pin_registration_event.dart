import 'package:equatable/equatable.dart';

abstract class PinRegistrationEvent extends Equatable {}

class SendPinEvent extends PinRegistrationEvent {
  final String pin;

  SendPinEvent({required this.pin});

  @override
  List<Object> get props => [pin];
}

class ConfirmPinEvent extends PinRegistrationEvent {
  final String confirmPin;

  ConfirmPinEvent({required this.confirmPin});

  @override
  List<Object> get props => [confirmPin];
}

import 'package:equatable/equatable.dart';

class PinRegistrationState extends Equatable {
  bool isProgress;
  bool isConfirmPin;
  bool tryAgain;

  Exception? error;

  String? pin;

  bool isSuccess;

  PinRegistrationState({
    this.isProgress = false,
    this.isConfirmPin = false,
    this.error,
    this.pin,
    this.isSuccess = false,
    this.tryAgain = false,
  });

  static PinRegistrationState initState() => PinRegistrationState();

  PinRegistrationState copy({
    String? pin,
    bool? isProgress,
    bool? isConfirmaPin,
    Exception? error,
    bool? isSuccess,
    bool? tryAgain,
  }) =>
      PinRegistrationState(
        pin: pin ?? this.pin,
        isProgress: isProgress ?? this.isProgress,
        isConfirmPin: isConfirmaPin ?? isConfirmPin,
        error: error,
        isSuccess: isSuccess ?? this.isSuccess,
        tryAgain: tryAgain ?? this.tryAgain,
      );

  @override
  List<dynamic> get props => [
        pin,
        isProgress,
        isConfirmPin,
        error,
        isSuccess,
      ];
}

import 'package:bloc/bloc.dart';
import 'package:siclosbank/app/modules/pin/data/repository/i_pin_repository.dart';
import 'package:siclosbank/localization/generated/i18n.dart';

import 'package:siclosbank/app/modules/pin/presenter/blocs/pin_registration/pin_registration_event.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/pin_registration/pin_registration_state.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

class PinRegistrationBloc
    extends Bloc<PinRegistrationEvent, PinRegistrationState> {
  final IPinRepository _repo;
  final IAppUseCase _appUseCase;

  PinRegistrationBloc(this._repo, this._appUseCase)
      : super(PinRegistrationState()) {
    on<PinRegistrationEvent>((event, emit) async {
      if (event is SendPinEvent) {
        emit(state.copy(pin: event.pin, isConfirmaPin: true));
      } else if (event is ConfirmPinEvent) {
        if (state.pin != event.confirmPin) {
          final error = ErrorResponse(message: const I18n().pin_nao_confere);
          emit(state.copy(
            error: error,
            isConfirmaPin: false,
            tryAgain: true,
          ));
        } else {
          try {
            emit(state.copy(isProgress: true));

            final result = await _repo.createOrUpdatePin(event.confirmPin);

            if (result) {
              final resultUser = await _appUseCase.getUserByCpf();
              AppSession.getInstance().user?.pin = resultUser.pin;
              emit(state.copy(isProgress: false, isSuccess: true));
            }
          } on Exception catch (error) {
            emit(state.copy(
              isProgress: false,
              error: error,
              isConfirmaPin: false,
            ));
          }
        }
      }
    });
  }
}

import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';

class CheckPinState extends Equatable {
  // bool isProgress;
  // bool isUpdatePin;
  // ErrorResponse? error;
  // String? pin;
  // bool isSuccess;

  const CheckPinState(
      // this.isProgress = false,
      // this.isUpdatePin = false,
      // this.error,
      // this.pin,
      // this.isSuccess = false,
      );

  @override
  // TODO: implement props
  List<Object?> get props => throw UnimplementedError();

  // static CheckPinState initState() => CheckPinState();

  // CheckPinState copy({
  //   // String? pin,
  //   // bool? isProgress,
  //   // bool? isUpdatePin,
  //   // ErrorResponse? error,
  //   // bool? isSuccess,
  // }) =>
  //     CheckPinState(
  //       // pin: pin ?? this.pin,
  //       // isProgress: isProgress ?? this.isProgress,
  //       // isUpdatePin: isUpdatePin ?? this.isUpdatePin,
  //       // error: error ?? this.error,
  //       // isSuccess: isSuccess ?? this.isSuccess,
  //     );

  // @override
  // List<dynamic> get props => [
  //       // pin,
  //       // isProgress,
  //       isUpdatePin,
  //       // error,
  //       // isSuccess,
  //     ];
}

final class LoadingCheckPinState extends CheckPinState {
  const LoadingCheckPinState();
}

final class ErrorCheckPinState extends CheckPinState {
  final ErrorResponse error;
  const ErrorCheckPinState({required this.error});
  // : super(error: error, isProgress: false);
}

final class SuccessCheckPinState extends CheckPinState {
  final String pin;
  const SuccessCheckPinState({required String this.pin});
}

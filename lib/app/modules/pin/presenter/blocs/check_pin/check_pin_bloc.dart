import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/pin/data/repository/i_pin_repository.dart';
import 'package:siclosbank/app/modules/pin/presenter/blocs/check_pin/check_pin_event.dart';

import 'package:siclosbank/app/modules/pin/presenter/blocs/check_pin/check_pin_state.dart';
import 'package:siclosbank/app/app_controller.dart';

import 'package:siclosbank/app/shared/errors/error_response.dart';

class CheckPinBloc extends Bloc<CheckPinEvent, CheckPinState> {
  final IPinRepository _repo;

  CheckPinBloc(this._repo) : super(const CheckPinState()) {
    on<CheckPinEvent>((event, emit) async {
      if (event is CheckPinEventSend) {
        try {
          String? userId;
          emit(const LoadingCheckPinState());

          if (AppSession.getInstance().authorizeDeviceResponse == null) {
            userId = AppSession.getInstance().user?.id;
            // emit(state.copy(isUpdatePin: true));
          } else {
            userId = AppSession.getInstance().authorizeDeviceResponse!.id;
          }
          final result = await _repo.verifyPin(pin: event.pin, userId: userId!);

          if (result == true) {
            emit(SuccessCheckPinState(pin: event.pin));
          }
        } on ErrorResponse catch (error) {
          emit(ErrorCheckPinState(error: error));
        }
      }
    });
  }
}

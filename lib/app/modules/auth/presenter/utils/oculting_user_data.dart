String getUserEmailCodify(email) {
  try {
    final parts = email.split('@');

    final localPart = parts[0]; // Parte antes do "@"
    final domainPart = parts[1]; // Parte após o "@"

    final firstChar = localPart[0];
    final lastChar = localPart[localPart.length - 1];
    final maskedLocalPart = firstChar + '*' * (localPart.length - 2) + lastChar;

    return '$maskedLocalPart@$domainPart';
  } catch (e) {
    return '';
  }
}

String getUserPhoneCodify(String phone) {
  try {
    if (phone.length != 14) return '';

    String ddd = phone.substring(3, 5); // DDD (77)
    String firstDigit = phone.substring(5, 6); // Primeiro dígito do celular (9)
    String lastTwo = phone.substring(12); // Últimos dois dígitos (51)

    return "($ddd) $firstDigit****-**$lastTwo";
  } catch (e) {
    return '';
  }
}

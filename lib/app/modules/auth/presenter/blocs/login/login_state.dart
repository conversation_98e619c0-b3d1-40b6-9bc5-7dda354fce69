part of 'login_bloc.dart';

sealed class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object> get props => [];
}

final class LoginInitial implements LoginState {
  final bool isEnableIdDigitalLogin;

  const LoginInitial({this.isEnableIdDigitalLogin = false});

  @override
  List<Object> get props => [
        isEnableIdDigitalLogin,
      ];

  @override
  bool? get stringify => true;
}

final class LoginLoading implements LoginState {
  @override
  bool? get stringify => true;

  @override
  List<Object> get props => [];
}

final class LoginSuccess implements LoginState {
  final TokenResponse tokenResponse;

  LoginSuccess(this.tokenResponse);

  @override
  bool? get stringify => true;

  @override
  List<Object> get props => [tokenResponse];
}

final class LoginError implements LoginState {
  final ErrorResponse error;

  LoginError(this.error);

  @override
  bool? get stringify => true;

  @override
  List<Object> get props => [error];
}

final class LoginCheckDevice implements LoginState {
  final CheckDeviceReturn checkDevicesType;
  final AuthorizeDeviceResponse authorizeDeviceResponse;

  LoginCheckDevice(
      {required this.checkDevicesType, required this.authorizeDeviceResponse});

  @override
  List<Object> get props => [checkDevicesType];

  @override
  bool? get stringify => true;
}

final class LoginRegisterStageState implements LoginState {
  LoginRegisterStageState({required this.user});
  final RegisterUserResponse user;

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => true;
}

final class LoginCheckCodeSuccess implements LoginState {
  final bool success;

  LoginCheckCodeSuccess(this.success);

  @override
  List<Object> get props => [success];

  @override
  bool? get stringify => true;
}

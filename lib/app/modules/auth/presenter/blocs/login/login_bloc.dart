import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/auth/data/models/login_request.dart';
import 'package:siclosbank/app/modules/auth/domain/usecases/login_usecase.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/2fa/check_device_page.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/data/models/register_user_response.dart';
import 'package:siclosbank/app/shared/data/models/token_response.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/errors/errors.dart';
import 'package:siclosbank/app/shared/utils/storage_utils.dart';

import '../../../../../shared/constants/stage_register_user.dart';
import '../../../../../shared/notifications/firebase_notifications.dart';

part 'login_event.dart';
part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final ILoginUsecase _usecase;
  final IAppUseCase _appUseCase;

  LoginBloc(this._usecase, this._appUseCase) : super(const LoginInitial()) {
    on<LoginEvent>((event, emit) async {
      if (event is SendLoginEvent) {
        emit(LoginLoading());

        try {
          var token = AppSession.getInstance().tokenFirebaseMessaging;
          if (kIsWeb) {
            token ??= "none";
          } else {
            token ??= await FirebaseNotifications.getToken();
            print("TOKEN: $token");
            AppSession.getInstance().tokenFirebaseMessaging = token;
          }

          var request = LoginRequest(
              login: event.cpf, password: event.password, tokenFCM: token);
          final result = await _usecase.login(request);

          _appUseCase.getTimeSession();

          if (result is TokenResponse) {
            if (result.user!.stage == StageEnum.FINISHED) {
              await _usecase.setLogin(event.cpf);

              await _usecase.setPassword(event.password);

              emit(LoginSuccess(result));
            } else {
              emit(LoginRegisterStageState(
                user: RegisterUserResponse(
                  id: result.user!.id!,
                  stage: result.user!.stage!.label,
                  isRegistered: true,
                  isCollaborator: true,
                ),
              ));
            }
          } else if (result is AuthorizeDeviceResponse) {
            emit(LoginCheckDevice(
                checkDevicesType: result.pin == true
                    ? CheckDeviceReturn.CADASTRAR_DISPOSITIVO
                    : CheckDeviceReturn.NAO_AUTORIZADO,
                authorizeDeviceResponse: result));
          }
        } catch (e) {
          if (event.isViaDigitalFaceId) {
            StorageUtils.clearDigitalData();
          }

          if (e is NotFound) {
            emit(LoginError(
                e.copyWith(message: const I18n().cpf_senha_incorretos)));
          } else {
            if (e is ErrorResponse) {
              emit(LoginError(e));
            } else {
              emit(LoginError(ErrorResponse(message: I18n().errorsGeneric)));
            }
          }
        }
      }
      if (event is CheckCodeResult) {
        emit(LoginCheckCodeSuccess(event.success));
      }
      if (event is LoginDigitalEvent) {
        final login = await _usecase.getLogin();
        final password = await _usecase.getPassword();
        add(SendLoginEvent(
          cpf: login,
          password: password,
          isViaDigitalFaceId: true,
        ));
      }
      if (event is IsEnableIdDigitalLogin) {
        final isloginEnabled = await _appUseCase.getEnableIdDigitalLogin();

        emit(LoginInitial(isEnableIdDigitalLogin: isloginEnabled));
      }
    });
  }
}

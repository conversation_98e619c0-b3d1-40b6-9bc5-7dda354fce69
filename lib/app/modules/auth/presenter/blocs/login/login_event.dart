part of 'login_bloc.dart';

sealed class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object> get props => [];
}

final class SendLoginEvent implements LoginEvent {
  @override
  List<Object> get props => [cpf, password, isViaDigitalFaceId];

  @override
  bool? get stringify => false;

  final String cpf;
  final String password;
  final bool isViaDigitalFaceId;

  SendLoginEvent({
    required this.cpf,
    required this.password,
    this.isViaDigitalFaceId = false,
  });
}

final class CheckCodeResult implements LoginEvent {
  @override
  List<Object> get props => [success];

  @override
  bool? get stringify => false;

  final bool success;

  CheckCodeResult(this.success);
}

final class LoginDigitalEvent implements LoginEvent {
  LoginDigitalEvent();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

final class IsEnableIdDigitalLogin implements LoginEvent {
  IsEnableIdDigitalLogin();

  @override
  List<Object> get props => [];

  @override
  bool? get stringify => false;
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'check_device_bloc.dart';

abstract class CheckDeviceEvent implements Equatable {
  @override
  List<dynamic> get props => [];

  @override
  bool? get stringify => true;
}

class SetCheckDeviceResponse extends CheckDeviceEvent {
  CheckDeviceReturn? checkDeviceReturn;
  SetCheckDeviceResponse(
    this.checkDeviceReturn,
  );
}

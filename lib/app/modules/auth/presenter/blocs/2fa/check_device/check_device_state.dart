part of 'check_device_bloc.dart';

sealed class CheckDeviceState extends Equatable {
  double percent;
  CheckDeviceStatus status;

  double get percentLoading => percent / 100;

  CheckDeviceState({
    this.percent = 10,
    this.status = CheckDeviceStatus.VERIFICANDO,
  });

  static CheckDeviceState initState() => CheckDeviceInitial();

  CheckDeviceState copy({
    double? percent,
    CheckDeviceStatus? status,
  }) =>
      CheckDeviceInitial()
        ..percent = percent ?? this.percent
        ..status = status ?? this.status;

  @override
  List<dynamic> get props => [
        percent,
        status,
      ];

  @override
  // TODO: implement stringify
  bool? get stringify => throw UnimplementedError();
}

final class CheckDeviceInitial extends CheckDeviceState {}

enum CheckDeviceStatus {
  VERIFICANDO,
  AUTORIZADO,
  NAO_AUTORIZADO,
  CADASTRAR_AUTORIZACAO
}

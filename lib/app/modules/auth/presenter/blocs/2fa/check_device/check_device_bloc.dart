import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/2fa/check_device_page.dart';

part 'check_device_event.dart';
part 'check_device_state.dart';

class CheckDeviceBloc extends Bloc<CheckDeviceEvent, CheckDeviceState> {
  CheckDeviceReturn? checkDeviceReturn;

  CheckDeviceBloc() : super(CheckDeviceState.initState()) {
    on<CheckDeviceEvent>(
        (CheckDeviceEvent event, Emitter<CheckDeviceState> emit) async {
      if (event is SetCheckDeviceResponse) {
        checkDeviceReturn = event.checkDeviceReturn;

        emit(state.copy(status: CheckDeviceStatus.VERIFICANDO, percent: 10));
        await Future.delayed(const Duration(milliseconds: 250));
        emit(state.copy(status: CheckDeviceStatus.VERIFICANDO, percent: 40));
        await Future.delayed(const Duration(milliseconds: 500));
        emit(state.copy(status: CheckDeviceStatus.VERIFICANDO, percent: 70));
        await Future.delayed(const Duration(milliseconds: 250));
        emit(state.copy(status: CheckDeviceStatus.VERIFICANDO, percent: 90));
        await Future.delayed(const Duration(milliseconds: 150));

        if (checkDeviceReturn == CheckDeviceReturn.AUTORIZADO) {
          emit(state.copy(status: CheckDeviceStatus.AUTORIZADO, percent: 100));
        } else if (checkDeviceReturn ==
            CheckDeviceReturn.CADASTRAR_DISPOSITIVO) {
          emit(state.copy(
              status: CheckDeviceStatus.CADASTRAR_AUTORIZACAO, percent: 100));
        } else {
          emit(state.copy(
              status: CheckDeviceStatus.NAO_AUTORIZADO, percent: 100));
        }
      }
      // return mapEventToState(event, emit);
    });
  }
}

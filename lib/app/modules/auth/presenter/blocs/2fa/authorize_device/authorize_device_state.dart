import 'package:equatable/equatable.dart';

import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';

class AuthorizeDeviceState implements Equatable {
  final bool? progress;
  final Exception? error;
  final bool? isSend;
  final CodeSentType? codeSentType;
  final bool? blockedAccount;
  final AuthorizeDeviceResponse? authorizeDeviceResponse;

  AuthorizeDeviceState({
    this.progress,
    this.error,
    this.isSend,
    this.codeSentType,
    this.blockedAccount,
    this.authorizeDeviceResponse,
  });

  static AuthorizeDeviceState initState(
          {required AuthorizeDeviceResponse authorizeDeviceResponse}) =>
      AuthorizeDeviceState(authorizeDeviceResponse: authorizeDeviceResponse);

  AuthorizeDeviceState copy({
    bool? progress,
    Exception? error,
    String? pin,
    bool? isSend,
    CodeSentType? codeSentType,
    bool? errorPinError,
    bool? blockedAccount,
    AuthorizeDeviceResponse? authorizeDeviceResponse,
  }) =>
      AuthorizeDeviceState(
        progress: progress ?? false,
        error: error,
        isSend: isSend ?? false,
        codeSentType: codeSentType ?? this.codeSentType,
        blockedAccount: blockedAccount ?? false,
        authorizeDeviceResponse:
            authorizeDeviceResponse ?? this.authorizeDeviceResponse,
      );

  @override
  List<Object?> get props => [
        progress,
        error,
        isSend,
        codeSentType,
        blockedAccount,
      ];

  @override
  bool? get stringify => throw UnimplementedError();
}

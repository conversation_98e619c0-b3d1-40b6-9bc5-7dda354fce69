import 'package:bloc/bloc.dart';

import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/authorize_device/authorize_device_event.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/authorize_device/authorize_device_state.dart';

import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/domain/usecase/app_usecase.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';

class AuthorizeDeviceBloc
    extends Bloc<AuthorizeDeviceEvent, AuthorizeDeviceState> {
  final IAppUseCase _usecase;
  AuthorizeDeviceBloc(this._usecase)
      : super(AuthorizeDeviceState.initState(
            authorizeDeviceResponse:
                AppSession.getInstance().authorizeDeviceResponse!)) {
    on<AuthorizeDeviceEvent>((event, emit) async {
      if (event is SendingTokenRegistrationDeviceEvent) {
        try {
          AuthorizeDeviceResponse authorizeDeviceResponse =
              AppSession.getInstance().authorizeDeviceResponse!;
          emit(state.copy(progress: true));

          await _usecase.sendCheckDeviceCode(
              userId: authorizeDeviceResponse.id!,
              isEmail: event.codeSentType == CodeSentType.EMAIL);

          emit(state.copy(
            progress: false,
            isSend: true,
            codeSentType: event.codeSentType,
          ));
        } on Exception catch (error) {
          if (error is ErrorResponse && error.code == 4261) {
            emit(state.copy(
              progress: false,
              error: error,
              codeSentType: event.codeSentType,
            ));
          } else if (error is ErrorResponse && error.code == 4262) {
            emit(state.copy(progress: false, blockedAccount: true));
          } else {
            emit(state.copy(progress: false, error: error));
          }
        }
      }
    });
  }
}

import 'package:equatable/equatable.dart';

import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';

abstract class AuthorizeDeviceEvent implements Equatable {}

class SendingTokenRegistrationDeviceEvent extends AuthorizeDeviceEvent {
  final CodeSentType codeSentType;

  SendingTokenRegistrationDeviceEvent({
    required this.codeSentType,
  });

  @override
  List<Object?> get props => [codeSentType];

  @override
  bool? get stringify => true;
}

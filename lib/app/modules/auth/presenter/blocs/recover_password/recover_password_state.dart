// ignore_for_file: constant_identifier_names

part of 'recover_password_bloc.dart';

enum RecoverPasswordStage {
  CPF,
  SELECT,
  CODE,
  // FACE_CHECK,
  CREATE_NEW_PASSWORD,
  FINISHED,
}

class RecoverPasswordState extends Equatable {
  final RecoverPasswordStage stage;
  final bool changePage;
  final String email;
  final String phone;
  final String cpf;
  final String id;

  const RecoverPasswordState({
    required this.stage,
    this.changePage = false,
    this.email = '',
    this.phone = '',
    this.cpf = '',
    this.id = '',
  });

  @override
  List<Object> get props => [stage];
}

final class RecoverPasswordInitial extends RecoverPasswordState {
  const RecoverPasswordInitial({
    super.stage = RecoverPasswordStage.CPF,
    super.changePage = false,
  });
}

final class RecoverPasswordLoading extends RecoverPasswordState {
  const RecoverPasswordLoading({super.stage = RecoverPasswordStage.CPF});
}

final class GetEmailPhoneRecoverySuccess extends RecoverPasswordState {
  const GetEmailPhoneRecoverySuccess({
    required super.email,
    required super.phone,
    required super.stage,
    required super.cpf,
    required super.id,
    super.changePage = false,
  });
}

final class SendCodeSuccess extends RecoverPasswordState {
  const SendCodeSuccess(
      {super.email,
      super.phone,
      super.id,
      super.cpf,
      required this.type,
      required super.stage,
      super.changePage = false});

  final RecoveryType type;
}

final class SendCodeError extends RecoverPasswordState {
  final ErrorResponse error;
  const SendCodeError(this.error, {super.stage = RecoverPasswordStage.CPF});
}

final class CheckCodeSuccess extends RecoverPasswordState {
  const CheckCodeSuccess(
      {required super.stage, super.changePage = false, required super.id});
}

final class CreateNewPasswordSuccess extends RecoverPasswordState {
  const CreateNewPasswordSuccess(
      {required super.stage, super.changePage = false});
}

final class CheckFaceMatchSuccess extends RecoverPasswordState {
  const CheckFaceMatchSuccess({required super.stage, super.changePage = false});
}

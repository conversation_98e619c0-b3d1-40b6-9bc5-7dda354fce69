import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/auth/domain/usecases/login_usecase.dart';

import '../../../../../shared/errors/error_response.dart';
import '../../../data/models/user_recovery_password_data.dart';

part 'recover_password_event.dart';
part 'recover_password_state.dart';

class RecoverPasswordBloc
    extends Bloc<RecoverPasswordEvent, RecoverPasswordState> {
  final ILoginUsecase _usecase;

  String cpf = '';

  UserRecoveryPasswordData? userRecoveryResponse;
  String? token;

  RecoverPasswordBloc(this._usecase) : super(const RecoverPasswordInitial()) {
    on<RecoverPasswordEvent>((event, emit) async {
      /// Send recovery email
      if (event is InitRecoveryEvent) {
        try {
          emit(const RecoverPasswordLoading());
          cpf = event.cpf;
          final result = await _usecase.recoveryPassword(cpf: event.cpf);

          cpf = event.cpf;
          userRecoveryResponse = result;

          emit(GetEmailPhoneRecoverySuccess(
            email: result.email,
            phone: result.phone,
            changePage: true,
            cpf: cpf,
            id: result.id,
            stage: RecoverPasswordStage.SELECT,
          ));
        } on ErrorResponse catch (e) {
          emit(SendCodeError(e));
        }
      }

      /// Send recovery code
      if (event is SendRecoveryCodeEvent) {
        final type = event.type;

        try {
          emit(const RecoverPasswordLoading());
          final userId = await _usecase.getCodeToRecoveryPassword(
            cpf: cpf,
            sms: RecoveryType.sms == type,
            email: RecoveryType.email == type,
          );
          await Future.delayed(const Duration(seconds: 2));
          emit(SendCodeSuccess(
            type: event.type,
            phone: userRecoveryResponse!.phone,
            email: userRecoveryResponse!.email,
            id: userId,
            cpf: cpf,
            changePage: true,
            stage: RecoverPasswordStage.CODE,
          ));
        } on ErrorResponse catch (e) {
          emit(SendCodeError(e));
        }
      }

      /// Resend recovery code
      if (event is ReSendRecoveryCodeEvent) {
        final type = event.type;
        try {
          emit(const RecoverPasswordLoading());
          final userId = await _usecase.getCodeToRecoveryPassword(
            cpf: cpf,
            sms: RecoveryType.sms == type,
            email: RecoveryType.email == type,
          );
          await Future.delayed(const Duration(seconds: 2));
          emit(SendCodeSuccess(
            type: event.type,
            phone: userRecoveryResponse!.phone,
            email: userRecoveryResponse!.email,
            cpf: cpf,
            id: userId,
            changePage: true,
            stage: RecoverPasswordStage.CODE,
          ));
        } on ErrorResponse catch (e) {
          emit(SendCodeError(e));
        }
      }

      /// Check code sent by email
      if (event is CheckCodeEvent) {
        try {
          emit(const RecoverPasswordLoading());
          final result = await _usecase.sendCodeToRecoveryPassword(
              code: event.code, userId: userRecoveryResponse!.id);
          if (result == false) {
            emit(SendCodeError(ErrorResponse(
              message: 'Código inválido. Verifique os dados e tente novamente.',
            )));
            return;
          } else {
            token = event.code;
            emit(CheckCodeSuccess(
              stage: RecoverPasswordStage.CREATE_NEW_PASSWORD,
              id: userRecoveryResponse!.id,
              changePage: true,
            ));
          }
        } on ErrorResponse catch (e) {
          emit(SendCodeError(e));
        }
      }

      /// Check face match with user
      /// Temporariamente desabilitado
      if (event is CheckFaceMatchEvent) {
        try {
          emit(const RecoverPasswordLoading());
          //final result = await _usecase.checkFaceMatch(imgBase64: event.imgBase64);
          await Future.delayed(const Duration(seconds: 2));
          emit(const CheckFaceMatchSuccess(
              stage: RecoverPasswordStage.CREATE_NEW_PASSWORD,
              changePage: true));
        } on ErrorResponse catch (e) {
          emit(SendCodeError(e));
        }
      }

      /// Create new password
      if (event is CreateNewPasswordEvent) {
        try {
          emit(const RecoverPasswordLoading());
          final result = await _usecase.createNewPassword(
              newPassword: event.password,
              cpf: cpf,
              id: userRecoveryResponse!.id);

          emit(const CreateNewPasswordSuccess(
              stage: RecoverPasswordStage.FINISHED, changePage: true));
        } on ErrorResponse catch (e) {
          emit(SendCodeError(e));
        }
      }
    });
  }
}

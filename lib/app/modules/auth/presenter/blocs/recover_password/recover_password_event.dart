// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'recover_password_bloc.dart';

enum RecoveryType { email, sms }

sealed class RecoverPasswordEvent extends Equatable {
  const RecoverPasswordEvent();

  @override
  List<Object> get props => [];
}

class InitRecoveryEvent extends RecoverPasswordEvent {
  const InitRecoveryEvent({required this.cpf});

  final String cpf;
}

class SendRecoveryCodeEvent extends RecoverPasswordEvent {
  const SendRecoveryCodeEvent({
    required this.type,
  });

  final RecoveryType type;
}

class ReSendRecoveryCodeEvent extends RecoverPasswordEvent {
  const ReSendRecoveryCodeEvent({
    required this.type,
  });

  final RecoveryType type;
}

class CheckCodeEvent extends RecoverPasswordEvent {
  const CheckCodeEvent({
    required this.code,
  });
  final String code;
}

class CreateNewPasswordEvent extends RecoverPasswordEvent {
  const CreateNewPasswordEvent({
    required this.password,
  });
  final String password;
}

class CheckFaceMatchEvent extends RecoverPasswordEvent {
  const CheckFaceMatchEvent(this.imgBase64);
  final String imgBase64;
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/item_card_text.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import 'package:siclosbank/app/shared/utils/calls_and_messages_service.dart';
import 'package:siclosbank/app/shared/utils/my_behavior.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../../shared/presenter/view/components/others/card_buttons_support_widget.dart';

class UnauthorizedDevicePage extends StatelessWidget {
  const UnauthorizedDevicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _UnauthorizedDeviceView(
        key: key,
      ),
    );
  }
}

class _UnauthorizedDeviceView extends StatefulWidget {
  const _UnauthorizedDeviceView({super.key});

  @override
  State<_UnauthorizedDeviceView> createState() => __UnauthorizedDeviceView();
}

class __UnauthorizedDeviceView extends State<_UnauthorizedDeviceView> {
  // var _service = locator.get<CallsAndMessagesService>();

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Scaffold(
      appBar: const AppBarApp(
        showBack: false,
        showLine: false,
      ),
      body: Container(
        child: ScrollConfiguration(
          behavior: MyBehavior(),
          child: CustomScrollView(
            slivers: <Widget>[
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.only(left: 16, right: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Text(
                        I18n.of(context)!.voce_precisa_autorizar_dispositivo,
                        style: textTheme.displayLarge,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        I18n.of(context)!
                            .voce_precisa_autorizar_dispositivo_msg,
                        style: textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        I18n.of(context)!.utilize_nossos_canais,
                        style: textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 57),
                      CardButtonsSupport(
                        clickEmail: () {
                          _clickEmail(context);
                        },
                      ),
                    ],
                  ),
                ),
              ),
              SliverFillRemaining(
                hasScrollBody: false,
                fillOverscroll: false,
                child: Container(
                  padding: EdgeInsetsResponsive.only(bottom: 16, top: 16),
                  alignment: Alignment.bottomCenter,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      InkWell(
                        onTap: () {
                          pop(context);
                        },
                        child: Container(
                          padding: const EdgeInsets.only(
                              top: 8, bottom: 8, left: 16, right: 16),
                          child: Text(
                            I18n.of(context)!.back_login_screen,
                            style: textTheme.bodyLarge,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  _callWhatsApp(BuildContext context) async {
    var whatsappUrl =
        "whatsapp://send?phone=${AppSession.getInstance().numberWhatsApp}";
    try {
      bool isRun = await launchUrlString(whatsappUrl);
      if (!isRun) {
        _showSnackWpp(context);
      }
    } catch (error) {
      if (error is PlatformException &&
          error.message != null &&
          error.message!.startsWith("No Activity found")) {
        _showSnackWpp(context);
      }
    }
  }

  _showSnackWpp(BuildContext buildContext) {
    SnackBarApp.showSnack(
      context: buildContext,
      message: I18n.of(buildContext)!.whatsapp_nao_instalado,
      success: false,
    );
  }

  _clickEmail(BuildContext buildContext) async {
    try {
      CallsAndMessagesService.sendEmailSuporte();
    } catch (error) {}
  }

  _clickTelefone() {
    // _service.call(Constants.TELEFONE_EMCASH);
  }
}

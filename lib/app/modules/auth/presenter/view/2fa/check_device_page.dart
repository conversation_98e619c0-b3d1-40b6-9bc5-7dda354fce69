import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/check_device/check_device_bloc.dart';
import 'package:siclosbank/app/shared/themes/styles/colors_app.dart';

import '../../../../../shared/data/models/authorize_device_response.dart';
import '../../../../../shared/navigation/named_routes.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../blocs/login/login_bloc.dart';

class CheckDevicePage extends StatelessWidget {
  const CheckDevicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CheckDeviceBloc>(
      create: (context) => Modular.get<CheckDeviceBloc>(),
      child: _CheckDeviceView(
        key: key,
      ),
    );
  }
}

class _CheckDeviceView extends StatefulWidget {
  const _CheckDeviceView({super.key});

  @override
  State<_CheckDeviceView> createState() => __CheckDeviceView();
}

class __CheckDeviceView extends State<_CheckDeviceView> {
  double _percent = 0.0;

  AuthorizeDeviceResponse? authorizeDeviceResponse;

  @override
  void initState() {
    final state = Modular.args.data?['args'] as LoginCheckDevice?;
    authorizeDeviceResponse = state?.authorizeDeviceResponse;
    BlocProvider.of<CheckDeviceBloc>(context)
        .add(SetCheckDeviceResponse(state!.checkDevicesType));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<CheckDeviceBloc, CheckDeviceState>(
        listener: (context, state) async {
          setState(() {
            _percent = state.percentLoading;
          });

          if (state.status == CheckDeviceStatus.AUTORIZADO ||
              state.status == CheckDeviceStatus.NAO_AUTORIZADO ||
              state.status == CheckDeviceStatus.CADASTRAR_AUTORIZACAO) {
            await Future.delayed(const Duration(seconds: 1));
            CheckDeviceReturn returnResponse =
                state.status == CheckDeviceStatus.AUTORIZADO
                    ? CheckDeviceReturn.AUTORIZADO
                    : state.status == CheckDeviceStatus.CADASTRAR_AUTORIZACAO
                        ? CheckDeviceReturn.CADASTRAR_DISPOSITIVO
                        : CheckDeviceReturn.NAO_AUTORIZADO;
            // pop(returnResponse);
            if (returnResponse == CheckDeviceReturn.AUTORIZADO) {
            } else if (returnResponse ==
                CheckDeviceReturn.CADASTRAR_DISPOSITIVO) {
              await push(Routes.loginAuthorizeDevice,
                      args: authorizeDeviceResponse)
                  .then((result) {
                pop(result);
              });
            } else if (returnResponse == CheckDeviceReturn.NAO_AUTORIZADO) {
              pushReplacement(Routes.loginUnauthorizedDevicePage);
            }
          }
        },
        child: BlocBuilder<CheckDeviceBloc, CheckDeviceState>(
          builder: (context, state) {
            return Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(4)),
                  child: LinearPercentIndicator(
                    lineHeight: 16,
                    padding: const EdgeInsets.all(0),
                    percent: _percent,
                    // backgroundColor: ColorsApp.verde[100],
                    linearStrokeCap: LinearStrokeCap.butt,
                    // progressColor: ColorsApp.verde[500],
                    animateFromLastPercent: true,
                    animation: true,
                    linearGradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: ColorsApp.gradientColors,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

enum CheckDeviceReturn {
  AUTORIZADO,
  NAO_AUTORIZADO,
  CADASTRAR_DISPOSITIVO,
  AGUARDE
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/authorize_device/authorize_device_bloc.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/authorize_device/authorize_device_event.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/authorize_device/authorize_device_state.dart';

import 'package:siclosbank/app/modules/pin/presenter/view/check_pin_page.dart';
import 'package:siclosbank/app/shared/errors/error_response.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/Item_channels.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/pages/code_sent/code_sent_page.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';
import '../../../../../shared/navigation/named_routes.dart';

class AuthorizeDevicePage extends StatelessWidget {
  const AuthorizeDevicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AuthorizeDeviceBloc>(
      create: (context) => Modular.get<AuthorizeDeviceBloc>(),
      child: _AuthorizeDeviceView(
        key: key,
      ),
    );
  }
}

class _AuthorizeDeviceView extends StatefulWidget {
  const _AuthorizeDeviceView({super.key});

  @override
  State<_AuthorizeDeviceView> createState() => __AuthorizeDeviceViewState();
}

class __AuthorizeDeviceViewState extends State<_AuthorizeDeviceView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Utils.setScreeenResponsive(context: context);
    var textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: const AppBarApp(
        showBack: false,
        showLine: false,
      ),
      body: BlocListener<AuthorizeDeviceBloc, AuthorizeDeviceState>(
        listener: (context, state) async {
          if (state.error != null) {
            // if (state.errorPinError != null && state.errorPinError!) {
            //   _openVerificationScreen(
            //       isEmail: state.codeSentType == CodeSentType.EMAIL,
            //       errorResponse: state.error is ErrorResponse
            //           ? (state.error as ErrorResponse)
            //           : ErrorResponse(
            //               error: state.error.toString(),
            //               message: state.error.toString(),
            //             ));
            //   return;
            // }

            DialogUtils.showSnackError(context, state.error!);
          }
          if (state.isSend != null && state.isSend!) {
            final status = await push(state.codeSentType == CodeSentType.SMS
                ? Routes.codeSendSms
                : Routes.codeSendEmail);

            if (status != null && status) {
              pop(status);
            }
          }
        },
        child: BlocBuilder<AuthorizeDeviceBloc, AuthorizeDeviceState>(
          builder: (context, state) {
            if (state.progress != null && state.progress!) {
              return _buildProgress();
            } else {
              return _buildBody(state);
            }
          },
        ),
      ),
    );
  }

  _buildBody(AuthorizeDeviceState state) {
    var textTheme = Theme.of(context).textTheme;
    return CustomScrollView(
      physics: const ClampingScrollPhysics(),
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            margin: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  I18n.of(context)!.authorize_device_title,
                  style: textTheme.displayLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  I18n.of(context)!.authorize_device_msg,
                  style: textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  I18n.of(context)!.autorizar_dispositivo_canais,
                  style: textTheme.bodyMedium,
                ),
                const SizedBox(height: 90),
                ItemChannels(
                  title: I18n.of(context)!.sms.toUpperCase(),
                  communicationChannel:
                      "${state.authorizeDeviceResponse?.phone}",
                  onTap: () {
                    _openVerificationScreen(isEmail: false);
                  },
                  isFirst: true,
                ),
                ItemChannels(
                  title: I18n.of(context)!.email,
                  communicationChannel:
                      "${state.authorizeDeviceResponse?.email}",
                  onTap: () {
                    _openVerificationScreen(isEmail: true);
                  },
                  isLast: true,
                ),
              ],
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          child: Container(
            padding: const EdgeInsets.only(top: 8, bottom: 8, left: 20),
            alignment: Alignment.bottomCenter,
            child: TextButton(
              onPressed: () {
                pop();
              },
              child: Text(
                I18n.of(context)!.back_login_screen,
              ),
            ),
          ),
        )
      ],
    );
  }

  _buildProgress() {
    return Center(
      child: Utils.circularProgressButton(),
    );
  }

  _openVerificationScreen({required bool isEmail}) async {
    await CheckPinPage.showSheet(
      context,
      () {
        BlocProvider.of<AuthorizeDeviceBloc>(context)
            .add(SendingTokenRegistrationDeviceEvent(
          codeSentType: isEmail ? CodeSentType.EMAIL : CodeSentType.SMS,
        ));
      },
    );
  }
}

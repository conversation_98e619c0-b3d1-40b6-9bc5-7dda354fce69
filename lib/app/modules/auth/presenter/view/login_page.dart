import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/login/login_bloc.dart';
import 'package:siclosbank/app/shared/config/environment.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/icon_button_help.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';
import 'package:siclosbank/app/shared/utils/auth/local_auth.dart';

import '../../../../../localization/generated/i18n.dart';
import '../../../../shared/config/flavor.dart';
import '../../../../shared/constants/constants.dart';
import '../../../../shared/errors/error_response.dart';
import '../../../../shared/errors/errors.dart';
import '../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../shared/presenter/view/responsive_widgets/responsive_widgets.dart';
import '../../../../shared/themes/styles/colors_app.dart';
import '../../../../shared/utils/fields_utils.dart';
import '../../../../shared/utils/utils.dart';
import '../../../../shared/utils/validator_utils.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<LoginBloc>(
      create: (context) => Modular.get<LoginBloc>(),
      child: _LoginView(key: key),
    );
  }
}

class _LoginView extends StatefulWidget {
  const _LoginView({super.key});

  @override
  State<_LoginView> createState() => __LoginViewState();
}

class __LoginViewState extends State<_LoginView> {
  final TextEditingController _cpfController = MaskedTextController(
    mask: '000.000.000-00',
  );
  final TextEditingController _passwordController = TextEditingController();
  bool _enableButton = false;
  final _formKey = GlobalKey<FormState>();
  bool _showDeviceSuccess = false;

  @override
  void initState() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);

    super.initState();
    _initListeners();
    _verifyLoginDigital();
    _setMock();
    // WidgetsBinding.instance.addObserver(this);
    // onWidgetDidBuild(() {
    //   if (mounted) {
    //     _verifyLoginCadastro();
    //   }
    // });
    // _verifyLoginDigital();
  }

  void onWidgetDidBuild(Function callback) =>
      WidgetsBinding.instance.addPostFrameCallback((_) {
        callback();
      });

  @override
  void dispose() {
    _cpfController.dispose();
    _passwordController.dispose();
    // WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  _initListeners() {
    _cpfController.addListener(() {
      if (_cpfController.text.isNotEmpty &&
          _passwordController.text.isNotEmpty) {
        setState(() {
          _enableButton = true;
        });
      } else {
        setState(() {
          _enableButton = false;
        });
      }
    });

    _passwordController.addListener(() {
      if (_cpfController.text.isNotEmpty &&
          _passwordController.text.isNotEmpty) {
        setState(() {
          _enableButton = true;
        });
      } else {
        setState(() {
          _enableButton = false;
        });
      }
    });
  }

  _verifyLoginDigital() async {
    BlocProvider.of<LoginBloc>(context).add(IsEnableIdDigitalLogin());
    await Future.delayed(const Duration(milliseconds: 50));

    final pageState = BlocProvider.of<LoginBloc>(context).state as LoginInitial;

    if (pageState.isEnableIdDigitalLogin) {
      await _loginSeguro();
    }
  }

  _loginSeguro() async {
    LocalAuthUtils localAuthUtils = LocalAuthUtils();
    await localAuthUtils
        .auth(
          isCheck: true,
          showAlert: (texto) {
            SnackBarApp.showSnack(
              context: context,
              message: texto,
              success: false,
              info: true,
            );
          },
        )
        .then((result) {
          if (result != null && result) {
            print("REALIZAR LOGIN");
            BlocProvider.of<LoginBloc>(context).add(LoginDigitalEvent());
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 29, 29, 29),
      body: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) {
          if (state is LoginSuccess) {
            navigate(Routes.layoutHome);
          }

          if (state is LoginCheckDevice) {
            _callCheckDevice(state);
          }
          if (state is LoginRegisterStageState) {
            pushReplacement(Routes.signUpRegister, args: state.user);
          }

          if (state is LoginCheckCodeSuccess) {
            if (state.success) {
              setState(() {
                _showDeviceSuccess = true;
              });
            }
          }
        },
        builder: (context, state) {
          var loading = false;
          ErrorResponse? error;

          if (state is LoginLoading) {
            loading = true;
          }

          if (state is LoginError) {
            error = state.error;
            if (error is TokenInvalid) {
              error = error.copyWith(
                message: I18n.of(context)!.cpf_senha_incorretos,
              );
            }
          }
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Container(
                  width: constraints.maxWidth,
                  height: constraints.maxHeight,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      fit: BoxFit.cover,
                      alignment: Alignment.topCenter,
                      image: CachedNetworkImageProvider(
                        'https://cdn.siclos.net/background_tela_login.png',
                      ),
                    ),
                  ),
                  child: CustomScrollView(
                    slivers: <Widget>[
                      SliverToBoxAdapter(
                        child: AppBarApp(
                          title: I18n.of(context)!.entrar.toUpperCase(),
                          showBackLogin: true,
                          clickBack: () {
                            pop();
                          },
                          actions: const <Widget>[
                            IconButtonHelp(
                              colorIcon: ColorsApp.whiteForegroundLogin,
                            ),
                          ],
                          brightnessBackground: Brightness.dark,
                          colorBackground: Colors.transparent,
                          showElevation: false,
                          widgetBackColor: ColorsApp.whiteForegroundLogin,
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Container(
                          padding: const EdgeInsets.only(
                            left: 16,
                            right: 16,
                            top: 24,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: <Widget>[
                              Text(
                                I18n.of(context)!.entrar_msg,
                                style: Theme.of(context).textTheme.bodyMedium!
                                    .copyWith(
                                      color: ColorsApp.whiteForegroundLogin,
                                    ),
                              ),
                              // const SizedBox(height: 76),
                            ],
                          ),
                        ),
                      ),
                      SliverFillRemaining(
                        hasScrollBody: false,
                        fillOverscroll: false,
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          alignment: Alignment.bottomCenter,
                          child: Form(
                            key: _formKey,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: <Widget>[
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 32,
                                    vertical: 16,
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl:
                                        'https://cdn.siclos.net/frame_tela_login.png',
                                    errorWidget: (context, url, error) {
                                      return Image.asset(
                                        'assets/images/frame_tela_login.png',
                                        height: constraints.maxHeight * 0.3,
                                        alignment: Alignment.center,
                                      );
                                    },
                                  ),
                                ),
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    TextFormFieldApp(
                                      controller: _cpfController,
                                      label: I18n.of(context)!.cpf,
                                      textInputType: TextInputType.number,
                                      onChanged: (text) {
                                        if (text.length == 14) {
                                          _formKey.currentState?.validate();
                                        }
                                      },
                                      validator: (text) {
                                        return ValidatorUtils.validateCpf(
                                          context: context,
                                          value: text,
                                        );
                                      },
                                      isPrefixLabel: true,
                                      highLight: true,
                                      onFieldSubmitted: (p0) {
                                        FocusScope.of(context).nextFocus();
                                      },
                                    ),
                                    const SizedBox(height: 16),
                                    TextFormFieldApp(
                                      controller: _passwordController,
                                      label: I18n.of(context)!.senha,
                                      typePassword: true,
                                      isPrefixLabel: true,
                                      highLight: true,
                                    ),
                                    AlertBanner(
                                      isShow:
                                          error != null && !_showDeviceSuccess,
                                      error: error,
                                      typeAlertBanner: TypeAlertBanner.error,
                                      message: null,
                                    ),
                                    AlertBanner(
                                      isShow: _showDeviceSuccess,
                                      error: null,
                                      message: I18n.of(
                                        context,
                                      )!.dipositivo_vinculado_sucesso,
                                      typeAlertBanner: TypeAlertBanner.success,
                                    ),
                                    const SizedBox(height: 28),
                                    InkWell(
                                      onTap: () {
                                        push(Routes.loginRecoveryPassword);
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.only(
                                          left: 16,
                                          right: 16,
                                          top: 4,
                                          bottom: 4,
                                        ),
                                        child: Text(
                                          I18n.of(context)!.esqueci_minha_senha,
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelLarge!
                                              .copyWith(
                                                color: ColorsApp
                                                    .whiteForegroundLogin,
                                              ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Visibility(
                                  // visible: !Constants.BUILD_LOJA,
                                  visible: !Flavor.isProduction,
                                  child: Container(
                                    alignment: Alignment.center,
                                    margin: EdgeInsetsResponsive.only(
                                      top: 16,
                                      bottom: 16,
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: <Widget>[
                                        TextResponsive(
                                          "Url:",
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge!
                                              .copyWith(
                                                color: ColorsApp
                                                    .whiteForegroundLogin
                                                    .withOpacity(0.7),
                                              ),
                                        ),
                                        SizedBoxResponsive(width: 8),
                                        TextResponsive(
                                          Environment.baseUrl,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium!
                                              .copyWith(
                                                color: ColorsApp
                                                    .whiteForegroundLogin
                                                    .withOpacity(0.7),
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 28),
                                  child: ButtonApp(
                                    text: I18n.of(context)!.continuar,
                                    enabled: !loading && _enableButton,
                                    onPress: _clickLogin,
                                    progress: loading
                                        ? Utils.circularProgressButton(size: 20)
                                        : null,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  _clickLogin() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      FocusScope.of(context).unfocus();
      // state.userToTransfer
      BlocProvider.of<LoginBloc>(context).add(
        SendLoginEvent(
          cpf: FieldsUtils.removeCharacters(_cpfController.text),
          password: _passwordController.text,
        ),
      );
    }
  }

  _setMock() {
    if (Constants.mock) {
      // _cpfController.text = "44544789885";
      // _cpfController.text = "78930871534";
      // _cpfController.text = "05407914581";
      // _cpfController.text = "01276241550";
      // _cpfController.text = "75933284572";
      // _passwordController.text = "Le2494Wink.";

      // _cpfController.text = "86250185569";
      // _cpfController.text = "06628417514";
      _cpfController.text = "07323918570";
      _passwordController.text = "A234\$6";

      // _cpfController.text = "863.964.235-45";
      // _cpfController.text = "215.758.050-65";
      // _passwordController.text = "Wtf123@";
      _enableButton = true;
    }
  }

  // _fluxoContinuarCadastro(CadastroContaResponse cadastroContaResponse) {
  //   if (cadastroContaResponse.estagioAnaliseDocumentacao) {
  //     pushOnePage(
  //         context,
  //         AnaliseCadastroProvider(
  //           userId: cadastroContaResponse.id,
  //         ));
  //   } else if (cadastroContaResponse.termoCompromissoEstagio) {
  //     pushOnePage(
  //         context,
  //         BemVindoCadastroView(
  //           login: cadastroContaResponse.cpf!,
  //           userId: cadastroContaResponse.id,
  //         ));
  //   } else if (cadastroContaResponse.estagioDocumentoNegado) {
  //     pushOnePage(context, const IntroView());
  //     pushSlide(context, const CadastroNaoAprovadoView());
  //   } else if (cadastroContaResponse.documentosPendentesEstagio) {
  //     pushSlide(
  //       context,
  //       AnaliseCadastroProvider(
  //         userId: cadastroContaResponse.id,
  //       ),
  //     );
  //   } else {
  //     pushSlide(
  //         context,
  //         CadastroPfProvider(
  //           cpf: cadastroContaResponse.cpf!,
  //           cadastroContaResponse: cadastroContaResponse,
  //         ));
  //   }
  // }

  // _verifyLoginCadastro() {
  //   var loginCadastro = AppSession.getInstance().loginCadastro;
  //   if (loginCadastro != null && loginCadastro.isNotEmpty) {
  //     _cpfController.text = loginCadastro;
  //     AppSession.getInstance().loginCadastro = "";
  //     BlocProvider.of<LoginBloc>(context)
  //         .add(LoginCadastroSnackEvent(login: loginCadastro));
  //   }
  // }

  _callCheckDevice(LoginCheckDevice state) async {
    var returnView = await push(Routes.loginCheckDevice, args: state);

    BlocProvider.of<LoginBloc>(
      context,
    ).add(CheckCodeResult(returnView == true));
  }
}

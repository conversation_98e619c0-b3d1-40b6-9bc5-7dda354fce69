import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:extended_masked_text/extended_masked_text.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../shared/utils/fields_utils.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../../../../shared/utils/validator_utils.dart';
import '../../../blocs/recover_password/recover_password_bloc.dart';

class RecoveryPasswordCpfView extends StatefulWidget {
  const RecoveryPasswordCpfView({super.key});

  @override
  State<RecoveryPasswordCpfView> createState() =>
      _RecoveryPasswordCpfViewState();
}

class _RecoveryPasswordCpfViewState extends State<RecoveryPasswordCpfView> {
  final TextEditingController _cpfController =
      MaskedTextController(mask: '000.000.000-00');
  bool _enable = false;
  final FocusNode _cpfFocus = FocusNode();
  final _key = GlobalKey<FormState>();

  @override
  initState() {
    super.initState();
    _initListeners();
  }

  _initListeners() {
    _cpfController.addListener(() {
      if (_cpfController.text.isNotEmpty) {
        setState(() {
          _enable = true;
        });
      } else {
        setState(() {
          _enable = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocConsumer<RecoverPasswordBloc, RecoverPasswordState>(
      listener: (context, state) {},
      builder: (context, state) {
        return _buildBody(context, state);
      },
    ));
  }

  CustomScrollView _buildBody(context, RecoverPasswordState state) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.only(left: 16, top: 32, right: 16),
            child: Form(
              key: _key,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Text(
                    I18n.of(context)!.esqueci_senha_msg,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 67),
                  TextFormFieldApp(
                    label: I18n.of(context)!.cpf,
                    controller: _cpfController,
                    validator: (text) {
                      return ValidatorUtils.validarCPFCNPJ(context, text);
                    },
                    textInputType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    focusNode: _cpfFocus,
                    enable: state is! RecoverPasswordLoading,
                  ),
                  const SizedBox(height: 16),
                  AlertBanner(
                    isShow: state is SendCodeError,
                    typeAlertBanner: TypeAlertBanner.error,
                    // message: I18n.of(context)!.recuperar_senha_enviada,
                    error: state is SendCodeError ? state.error : null,
                  ),
                ],
              ),
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: false,
          child: Container(
            alignment: Alignment.bottomCenter,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ButtonApp(
                    text: I18n.of(context)!.recuperar_senha,
                    enabled: _enable,
                    progress: state is RecoverPasswordLoading
                        ? Utils.circularProgressButton(size: 20)
                        : null,
                    onPress: () => _clickRecuperar(context)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _clickRecuperar(context) {
    if (_key.currentState!.validate()) {
      FocusScope.of(context).unfocus();

      BlocProvider.of<RecoverPasswordBloc>(context).add(InitRecoveryEvent(
        cpf: FieldsUtils.removeCharacters(_cpfController.text),
      ));
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/recover_password/recover_password_bloc.dart';

import 'package:siclosbank/app/shared/presenter/view/components/others/alert_banner.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/validator_password.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/text_form_field_app.dart';
import '../../../../../../shared/constants/constants.dart';
import '../../../../../../shared/utils/utils.dart';

class RecoveryNewPasswordPage extends StatelessWidget {
  const RecoveryNewPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return _CreatePasswordView();
  }
}

class _CreatePasswordView extends StatefulWidget {
  const _CreatePasswordView({
    super.key,
  });

  @override
  State<_CreatePasswordView> createState() => _CreatePasswordViewState();
}

class _CreatePasswordViewState extends State<_CreatePasswordView> {
  final formKey = GlobalKey<FormState>();
  final newPass = TextEditingController();
  final confirmNewPass = TextEditingController();
  bool newPassIsOk = false;
  bool _enable = false;
  final _scrollController = ScrollController();

  final _focusNewPass = FocusNode();
  final _focusConfirmPass = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNewPass.requestFocus();
    _verificaEnable();
    // _setMock();
    _focusNovaSenhaListener();
    // final bloc = Modular.get<CheckCollaboratorBloc>();
    // print(bloc.response);
  }

  _focusNovaSenhaListener() {
    _focusNewPass.addListener(() {
      _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
    });
  }

  _verificaEnable() {
    newPass.addListener(_onListenerTexts);
    confirmNewPass.addListener(_onListenerTexts);
  }

  _onListenerTexts() {
    if (newPass.text.isNotEmpty &&
        confirmNewPass.text.isNotEmpty &&
        newPass.text == confirmNewPass.text &&
        newPassIsOk) {
      _enable = true;
    } else {
      _enable = false;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocBuilder<RecoverPasswordBloc, RecoverPasswordState>(
      builder: (context, state) {
        return buildBody(state);
      },
    ));
  }

  Widget buildBody(RecoverPasswordState state) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Form(
            key: formKey,
            // autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Padding(
              padding: const EdgeInsets.only(
                  left: 16, top: 32, right: 16, bottom: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    const I18n().crie_uma_senha,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(
                    height: 40,
                  ),
                  TextFormFieldApp(
                    typePassword: true,
                    label: const I18n().nova_senha,
                    controller: newPass,
                    focusNode: _focusNewPass,
                    textInputAction: TextInputAction.next,
                    // validator: (value) {
                    //   if (!_enable) {
                    //     return const I18n().senha_invalida;
                    //   }
                    //   return null;
                    // },
                    onFieldSubmitted: (String text) {
                      if (text.isNotEmpty) {
                        _focusConfirmPass.requestFocus();
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormFieldApp(
                    label: I18n.of(context)!.confirme_nova_senha,
                    controller: confirmNewPass,
                    textInputAction: TextInputAction.done,
                    focusNode: _focusConfirmPass,
                    typePassword: true,
                    validator: (value) {
                      if (value != newPass.text) {
                        return "As senhas precisam ser iguais";
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  ValidatorPasswordWidget(
                    passwordController: newPass,
                    onPasswordOk: (bool status) {
                      newPassIsOk = status;
                      setState(() {});
                    },
                  ),
                  // AlertBanner(
                  //   isShow: newPass.text.isNotEmpty &&
                  //       confirmNewPass.text.isNotEmpty &&
                  //       newPass.text != confirmNewPass.text &&
                  //       newPassIsOk,
                  //   typeAlertBanner: TypeAlertBanner.error,
                  //   message: I18n.of(context)!.password_not_match,
                  // )
                ],
              ),
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: false,
          child: Container(
            padding: const EdgeInsets.all(16),
            alignment: Alignment.bottomCenter,
            child: ButtonApp(
              enabled: _enable,
              width: MediaQuery.of(context).size.width,
              height: 50,
              border: 0,
              text: const I18n().continuar,
              progress: state is RecoverPasswordLoading
                  ? Utils.circularProgressButton(size: 20)
                  : null,
              onPress: () {
                FocusScope.of(context).unfocus();
                if (formKey.currentState!.validate()) {
                  BlocProvider.of<RecoverPasswordBloc>(context).add(
                    CreateNewPasswordEvent(
                      password: newPass.text,
                    ),
                  );
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  _setMock() {
    if (!kReleaseMode && Constants.mock) {
      newPass.text = "A234\$6";
    }
  }
}

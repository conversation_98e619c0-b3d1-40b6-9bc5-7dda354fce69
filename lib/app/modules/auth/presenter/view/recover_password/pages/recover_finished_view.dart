import 'package:flutter/material.dart';

import 'package:siclosbank/app/shared/navigation/navigator_app.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';

class RecoverFinishedView extends StatelessWidget {
  const RecoverFinishedView({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        body: SafeArea(
          child: _bodyContainer(context),
        ),
      ),
    );
  }

  // _bodyProgress() {
  //   return Center(
  //     child: Utils.circularProgressButton(),
  //   );
  // }

  Widget _bodyContainer(context) {
    var textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  I18n.of(context)!.pronto,
                  style: textTheme.displayLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  I18n.of(context)!.senha_redefinida_sucesso,
                  style: textTheme.bodyMedium,
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              alignment: Alignment.bottomCenter,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ButtonApp(
                    text: I18n.of(context)!.fazer_login,
                    onPress: _clickSair,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _clickSair() async {
    pop();
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/Item_channels.dart';
import '../../../blocs/recover_password/recover_password_bloc.dart';

class RecoverySelectCodeView extends StatefulWidget {
  const RecoverySelectCodeView({super.key});

  @override
  State<RecoverySelectCodeView> createState() => _RecoverySelectCodeViewState();
}

class _RecoverySelectCodeViewState extends State<RecoverySelectCodeView> {
  String email = '';
  String phone = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<RecoverPasswordBloc, RecoverPasswordState>(
        listener: (context, state) {},
        builder: (context, state) {
          return _buildBody(context, state);
        },
      ),
    );
  }

  CustomScrollView _buildBody(context, RecoverPasswordState state) {
    if (state.email.isNotEmpty) {
      email = state.email;
    }
    if (state.phone.isNotEmpty) {
      phone = state.phone;
    }

    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.only(left: 16, top: 32, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  I18n.of(context)!.escolha_verificacao_msg,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 67),
              ],
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          child: Container(
            padding:
                const EdgeInsets.only(top: 8, bottom: 8, left: 16, right: 16),
            alignment: Alignment.bottomCenter,
            child: Column(
              children: [
                ItemChannels(
                  title: I18n.of(context)!.sms.toUpperCase(),
                  communicationChannel: phone,
                  onTap: () {
                    BlocProvider.of<RecoverPasswordBloc>(context)
                        .add(SendRecoveryCodeEvent(
                      type: RecoveryType.sms,
                      // cpf: state.cpf,
                    ));
                  },
                  isFirst: true,
                ),
                ItemChannels(
                  title: I18n.of(context)!.email,
                  communicationChannel: email,
                  onTap: () {
                    BlocProvider.of<RecoverPasswordBloc>(context)
                        .add(SendRecoveryCodeEvent(
                      type: RecoveryType.email,
                      // cpf: state.cpf,
                    ));
                  },
                  isLast: true,
                ),
                const SizedBox(height: 40),
                Visibility(
                    visible: state is RecoverPasswordLoading,
                    child: Utils.circularProgressButton()),
              ],
            ),
            // TextButton(
            //   onPressed: () {
            //     pop();
            //   },
            //   child: Text(
            //     I18n.of(context)!.voltar,
            //   ),
            // ),
          ),
        )
      ],
    );
  }
}

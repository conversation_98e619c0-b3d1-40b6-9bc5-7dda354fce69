import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../../../../../../localization/generated/i18n.dart';
import '../../../../../../shared/presenter/view/components/others/alert_banner.dart';
import '../../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../../shared/presenter/view/components/others/button_reenviar_codigo.dart';
import '../../../../../../shared/presenter/view/components/others/pin_code_custom.dart';
import '../../../../../../shared/themes/styles/colors_app.dart';
import '../../../../../../shared/utils/utils.dart';
import '../../../blocs/recover_password/recover_password_bloc.dart';
import '../not_recognize_this_number.dart';

class RecoverCodePage extends StatefulWidget {
  const RecoverCodePage({super.key});

  @override
  State<RecoverCodePage> createState() => _RecoverCodePageState();
}

class _RecoverCodePageState extends State<RecoverCodePage> {
  bool enableButtomContinue = false;
  late TextEditingController codePinController;
  late StreamController<ErrorAnimationType> errorController;
  late CountdownController controller;
  bool enableButtomResend = false;
  bool hasError = false;
  String email = '******';
  String phone = '******';
  RecoveryType? type;

  final _focusCode = FocusNode();

  @override
  void initState() {
    codePinController = TextEditingController();
    super.initState();
    _iniciaContador();
    _focusCode.requestFocus();
  }

  void _iniciaContador() {
    errorController = StreamController<ErrorAnimationType>();
    controller = CountdownController(autoStart: true);
  }

  @override
  void dispose() {
    errorController.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<RecoverPasswordBloc, RecoverPasswordState>(
        listener: (context, state) {},
        builder: (context, state) {
          if (state is SendCodeSuccess) {
            email = state.email;
            phone = state.phone;
            type = state.type;
          }
          return _buildBody(state);
        },
      ),
    );
  }

  _buildBody(RecoverPasswordState state) {
    if (type == null) {
      return Container();
    }
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.only(left: 16, top: 32, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  type == RecoveryType.email
                      ? I18n.of(context)!.send_email_code(email)
                      : I18n.of(context)!.send_sms_code(phone),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 67),
                Container(
                  margin: const EdgeInsets.only(
                      left: 32, right: 32, top: 64, bottom: 0),
                  child: PinCodeAppCustom(
                    autoFocus: true,
                    focusNode: _focusCode,
                    autoDisposeControllers: false,
                    controller: codePinController,
                    onChanged: (value) {
                      log(codePinController.text);
                      setState(() {
                        enableButtomContinue = (value.length == 6);
                        hasError = false;
                      });
                    },
                    length: 6,
                    obsecureText: false,
                    animationType: AnimationType.fade,
                    textStyle: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(fontSize: 24),
                    animationDuration: const Duration(milliseconds: 300),
                    pinTheme: PinTheme(
                      activeColor: hasError ? Colors.red : ColorsApp.cinza[500],
                      shape: PinCodeFieldShape.underline,
                      inactiveColor: ColorsApp.cinza[500],
                      selectedColor: ColorsApp.cinza[500],
                      fieldHeight: 50,
                      fieldWidth: 32,
                    ),
                    backgroundColor: Colors.transparent,
                    textInputType: TextInputType.number,
                    errorAnimationController: errorController,
                  ),
                ),
                Container(
                  alignment: Alignment.topCenter,
                  child: AlertBanner(
                    isShow: state is SendCodeError,
                    typeAlertBanner: TypeAlertBanner.error,
                    message:
                        state is SendCodeError ? state.error.message : null,
                    error: state is SendCodeError ? state.error : null,
                  ),
                ),
                const SizedBox(height: 16),
                state is RecoverPasswordLoading
                    ? SizedBox(
                        child: Center(
                            child: Utils.circularProgressButton(size: 20)),
                      )
                    : !enableButtomResend
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                const I18n().reenviar_codigo_sms_timer,
                                style: Theme.of(context).textTheme.labelLarge,
                              ),
                              const SizedBox(width: 4),
                              Countdown(
                                controller: controller,
                                seconds: 60,
                                build: (_, double time) {
                                  return Text(
                                    time == 60
                                        ? '1:00'
                                        : '0:${time.round().toString().padLeft(2, '0')}',
                                    style:
                                        Theme.of(context).textTheme.labelLarge,
                                  );
                                },
                                interval: const Duration(seconds: 1),
                                onFinished: () {
                                  // controller.restart();
                                  setState(() {
                                    enableButtomResend = true;
                                  });
                                },
                              ),
                            ],
                          )
                        : ButtonResendCode(
                            onTap: () {
                              BlocProvider.of<RecoverPasswordBloc>(context)
                                  .add(ReSendRecoveryCodeEvent(
                                type: type!,
                              ));
                              codePinController.clear();
                              controller.restart();
                              _iniciaContador();
                              setState(() {
                                enableButtomResend = false;
                              });
                            },
                          )
              ],
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: false,
          child: Container(
            alignment: Alignment.bottomCenter,
            padding:
                const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 40),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Visibility(
                  visible: true,
                  child: TextButton(
                    style: const ButtonStyle(
                        foregroundColor:
                            WidgetStatePropertyAll<Color>(ColorsApp.cinza)),
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => NotRecognizeThisNumber(
                                  isPhone: type == RecoveryType.sms)));
                    },
                    child: Text(
                      I18n.of(context)!
                          .nao_reconheco_este_numero(type == RecoveryType.sms),
                    ),
                  ),
                ),
                ButtonApp(
                  text: I18n.of(context)!.continuar,
                  enabled: enableButtomContinue,
                  progress: state is RecoverPasswordLoading
                      ? Utils.circularProgressButton(size: 20)
                      : null,
                  onPress: () {
                    log(codePinController.text);
                    BlocProvider.of<RecoverPasswordBloc>(context)
                        .add(CheckCodeEvent(
                      code: codePinController.text,
                    ));
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

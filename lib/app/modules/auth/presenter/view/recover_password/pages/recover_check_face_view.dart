import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/localization/generated/i18n.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/recover_password/recover_password_bloc.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/button_app.dart';
import 'package:siclosbank/app/shared/utils/utils.dart';

import '../error_check_face_page.dart';

class RecoverCheckFaceView extends StatelessWidget {
  const RecoverCheckFaceView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<RecoverPasswordBloc, RecoverPasswordState>(
        builder: (context, state) {
          return Column(
            children: [
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: const Text('Check FaceMatch'),
                ),
              ),
              Container(
                alignment: Alignment.bottomCenter,
                padding: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
                child: ButtonApp(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  border: 0,
                  text: I18n.of(context)!.erro,
                  onPress: () async {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const ErrorCheckFacePage()));
                  },
                ),
              ),
              Container(
                alignment: Alignment.bottomCenter,
                padding: const EdgeInsets.only(bottom: 40, left: 16, right: 16),
                child: ButtonApp(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  border: 0,
                  progress: state is RecoverPasswordLoading
                      ? Utils.circularProgressButton()
                      : null,
                  text: I18n.of(context)!.continuar,
                  onPress: () async {
                    // Modular.get<RecoverPasswordBloc>()
                    BlocProvider.of<RecoverPasswordBloc>(context)
                        .add(const CheckFaceMatchEvent('imagBase64'));
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../shared/utils/utils.dart';

class NotRecognizeThisNumber extends StatelessWidget {
  NotRecognizeThisNumber({super.key, this.isPhone = true});

  bool isPhone;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarApp(
        title: I18n.of(context)!.esqueci_minha_senha.toUpperCase(),
        actions: [Utils.buttonIcHelp(context)],
      ),
      body: Safe<PERSON>rea(
        child: _bodyContainer(context),
      ),
    );
  }

  Widget _bodyContainer(context) {
    var textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const SizedBox(height: 16),
                Text(
                  I18n.of(context)!
                      .nao_reconheco_este_numero_email_msg(isPhone),
                  style: textTheme.bodyMedium,
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              alignment: Alignment.bottomCenter,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ButtonApp(
                    text: I18n.of(context)!.voltar,
                    onPress: pop,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

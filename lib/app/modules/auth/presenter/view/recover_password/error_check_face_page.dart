import 'package:flutter/material.dart';
import 'package:siclosbank/app/shared/navigation/navigator_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/app_bar_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/presenter/view/components/others/button_app.dart';
import '../../../../../shared/utils/utils.dart';

class ErrorCheckFacePage extends StatelessWidget {
  const ErrorCheckFacePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: _bodyContainer(context),
      ),
    );
  }

  Widget _bodyContainer(context) {
    var textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        slivers: <Widget>[
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  I18n.of(context)!.erro,
                  style: textTheme.displayLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  I18n.of(context)!.error_check_face,
                  style: textTheme.bodyMedium,
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Container(
              alignment: Alignment.bottomCenter,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ButtonApp(
                    text: I18n.of(context)!.voltar,
                    onPress: pop,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

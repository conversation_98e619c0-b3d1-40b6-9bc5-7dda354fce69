import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/recover_password/pages/recover_check_face_view.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/recover_password/pages/recover_code_view.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/recover_password/pages/recover_finished_view.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/recover_password/pages/recover_new_password_view.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/dialog_utils.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/keep_alive_page.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/modal_sheets_app.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/sheet_alert_confirm.dart';
import 'package:siclosbank/app/shared/presenter/view/components/others/snack_bar_app.dart';

import '../../../../../../localization/generated/i18n.dart';
import '../../../../../shared/errors/errors.dart';
import '../../../../../shared/navigation/navigator_app.dart';
import '../../../../../shared/presenter/view/components/others/app_bar_app.dart';
import '../../../../../shared/utils/utils.dart';
import '../../blocs/recover_password/recover_password_bloc.dart';
import 'const_page_index.dart';
import 'pages/recovery_cpf_view.dart';
import 'pages/recovery_select_code_view.dart';

class RecoverPasswordHomePage extends StatelessWidget {
  const RecoverPasswordHomePage({super.key, this.currentPage = 0});
  final int currentPage;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (_) => Modular.get<RecoverPasswordBloc>(),
        child: _RecoverPasswordView(currentPage: currentPage));
  }
}

class _RecoverPasswordView extends StatefulWidget {
  const _RecoverPasswordView({super.key, this.currentPage = 0});
  final int currentPage;

  @override
  State<_RecoverPasswordView> createState() => _RecoverPasswordViewState();
}

class _RecoverPasswordViewState extends State<_RecoverPasswordView> {
  late int _currentPage;
  // int _previousPage = HomePageIndex.HOME;
  late PageController pageController;
  late List<Widget> pages;

  @override
  void initState() {
    _currentPage = widget.currentPage;
    pageController = PageController(
      initialPage: _currentPage,
      keepPage: true,
    );
    pages = [
      const KeepAlivePage(child: RecoveryPasswordCpfView()),
      const KeepAlivePage(child: RecoverySelectCodeView()),
      const KeepAlivePage(child: RecoverCodePage()),
      const RecoveryNewPasswordPage(),
      const RecoverFinishedView(),
    ];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _currentPage == pages.length - 1
          ? null
          : AppBarApp(
              title: I18n.of(context)!.esqueci_minha_senha.toUpperCase(),
              clickBack: _clickBack,
              actions: <Widget>[
                Utils.buttonIcHelp(context),
              ],
            ),
      body: BlocConsumer<RecoverPasswordBloc, RecoverPasswordState>(
        listener: (context, state) {
          // if (state is SendCodeSuccess) {
          //   _changePage(1);
          // }
          if (state is SendCodeError &&
              _currentPage != 0 &&
              _currentPage != RecoveryPageIndex.CODE) {
            if (state.error is ErrorForbidden || state.error is TokenInvalid) {
              SnackBarApp.showSnack(
                context: context,
                message:
                    'Solicitação negada. Necessário validacao de código de verificação.',
                success: false,
              );
            } else {
              DialogUtils.showSnackError(context, state.error);
            }
          }

          if (state.changePage) {
            if (state.stage == RecoverPasswordStage.SELECT) {
              _changePage(RecoveryPageIndex.SELECT);
            }
            if (state.stage == RecoverPasswordStage.CODE) {
              _changePage(RecoveryPageIndex.CODE);
            }
            // if (state.stage == RecoverPasswordStage.FACE_CHECK) {
            //   ModalSheetsApp.showDicasSelfie(context, onPress: () {
            //     _changePage(RecoveryPageIndex.FACE_CHECK);
            //   });
            // }
            if (state.stage == RecoverPasswordStage.CREATE_NEW_PASSWORD) {
              _changePage(RecoveryPageIndex.NEW_PASSWORD);
            }
            if (state.stage == RecoverPasswordStage.FINISHED) {
              _changePage(RecoveryPageIndex.FINISHED);
            }
          }
        },
        builder: (context, state) {
          return PageView(
            // allowImplicitScrolling: false,
            physics: const NeverScrollableScrollPhysics(),
            controller: pageController,
            children: pages,
          );
        },
      ),
    );
  }

  // _clickPagina(int index) {
  //   _changePage(index);
  // }

  _backPage() {
    setState(() {
      _currentPage--;
      _movePage();
    });
  }

  _changePage(int page) {
    setState(() {
      _currentPage = page;
      _movePage();
    });
  }

  _movePage() {
    pageController.animateToPage(_currentPage,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
  }

  // _onPageControl(int page) {

  //   // _previousPage = page;
  // }

  _clickBack() {
    FocusScope.of(context).unfocus();
    if (_currentPage == RecoveryPageIndex.CPF ||
        _currentPage == RecoveryPageIndex.FINISHED ||
        _currentPage == RecoveryPageIndex.NEW_PASSWORD) {
      pop();
    } else {
      _backPage();
    }
    // if (_currentPage == RecoveryPageIndex.CODE) {
    //   _backPage();
    // } else {
    //    else {
    //     SheetAlertConfirm.showSheet(
    //       context,
    //       title: I18n.of(context)!.confirmacao,
    //       message: I18n.of(context)!.deseja_cancelar,
    //       textPositive: I18n.of(context)!.sim,
    //       textNegative: I18n.of(context)!.nao,
    //       clickPositive: () {
    //         pop();
    //       },
    //     );
    //   }
    // }

    // else if (currentPage == EstagioCadastroPF.DADOS_PESSOAIS) {
    //   _backPage();
    // } else {
    //   BlocProvider.of<CadastroPfBloc>(context).add(VoltarEstagioPfEvent());
    // }
  }
}

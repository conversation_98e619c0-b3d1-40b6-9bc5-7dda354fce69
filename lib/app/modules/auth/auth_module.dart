import 'package:flutter_modular/flutter_modular.dart';
import 'package:siclosbank/app/modules/auth/data/database/auth_database_implements.dart';
import 'package:siclosbank/app/modules/auth/data/database/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_datasource_impl.dart';
import 'package:siclosbank/app/modules/auth/data/repository/auth_repository_impl.dart';
import 'package:siclosbank/app/modules/auth/domain/repositories/i_auth_repository.dart';

import 'package:siclosbank/app/modules/auth/domain/usecases/login_usecase.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/authorize_device/authorize_device_bloc.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/2fa/check_device/check_device_bloc.dart';

import 'package:siclosbank/app/modules/auth/presenter/blocs/login/login_bloc.dart';
import 'package:siclosbank/app/modules/auth/presenter/blocs/recover_password/recover_password_bloc.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/2fa/authorize_device_page.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/2fa/check_device_page.dart';

import 'package:siclosbank/app/modules/auth/presenter/view/2fa/unauthorized_device_page.dart';
import 'package:siclosbank/app/modules/auth/presenter/view/login_page.dart';
import 'package:siclosbank/app/modules/pin/pin_module.dart';

import 'package:siclosbank/app/app_module.dart';
import 'package:siclosbank/app/shared/navigation/named_routes.dart';

import 'presenter/view/recover_password/recovery_home_page.dart';

class AuthModule extends Module {
  @override
  void binds(Injector i) {
    i.addLazySingleton<IAuthDatabase>(AuthDatabaseImplements.new);
    i.addLazySingleton<IAuthDatasource>(AuthDatasourceImpl.new);
    i.addLazySingleton<IAuthRepository>(AuthRepositoryImpl.new);
    i.addLazySingleton<ILoginUsecase>(LoginUsecase.new);

    i.add(LoginBloc.new);
    i.add(RecoverPasswordBloc.new);
    i.add(AuthorizeDeviceBloc.new);
    i.add(CheckDeviceBloc.new);

    super.binds(i);
  }

  @override
  List<Module> get imports => [
        AppModule(),
        PinModule(),
      ];

  @override
  void routes(r) {
    r.child('/', child: (_) => const LoginPage());
    r.child(Routes.authorizeDevice, child: (_) => const AuthorizeDevicePage());
    r.child(Routes.checkDevice, child: (_) => const CheckDevicePage());

    r.child(Routes.unauthorizedDevicePage,
        child: (_) => const UnauthorizedDevicePage());
    r.child(Routes.recoveryPassword,
        child: (_) => const RecoverPasswordHomePage());
  }
}

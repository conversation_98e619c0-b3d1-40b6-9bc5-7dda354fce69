import 'package:siclosbank/app/modules/auth/data/database/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/models/login_request.dart';
import 'package:siclosbank/app/modules/auth/data/models/user_recovery_password_data.dart';
import 'package:siclosbank/app/modules/auth/domain/repositories/i_auth_repository.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';
import 'package:siclosbank/app/shared/utils/crypto_utils.dart';

import '../../../../shared/data/models/token_response.dart';

class AuthRepositoryImpl implements IAuthRepository {
  AuthRepositoryImpl(this._datasource, this._database);
  final IAuthDatasource _datasource;
  final IAuthDatabase _database;

  @override
  Future<dynamic> login(LoginRequest loginRequest) async {
    try {
      ApiResponse response = await _datasource.login(loginRequest.toMap());

      if (response.statusCode == 200) {
        TokenResponse token = TokenResponse.fromMap(response.data);
        return Future.value(token);
      } else if (response.statusCode == 202) {
        AuthorizeDeviceResponse authorizeDevice =
            AuthorizeDeviceResponse.fromJson(response.data);
        return Future.value(authorizeDevice);
      } else {
        return await ServerErrorHandling.handleError(response.data);
      }
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<UserRecoveryPasswordData> recoveryPassword(
      {required String cpf}) async {
    try {
      final result = await _datasource.recoverPassword(cpf);
      if (result.statusCode != 200) {
        throw Exception(result);
      }
      return UserRecoveryPasswordData.fromJson(result.data);
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<bool> createNewPassword({
    required String cpf,
    required String newPassword,
    required String id,
  }) async {
    try {
      final request = {'password': newPassword, 'cpf': cpf};

      final result = await _datasource.resetPassword(request: request, id: id);
      // if (result.statusCode != 200) {
      //   throw Exception(result);
      // }
      return result.statusCode == 200;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<String> getCodeToRecoveryPassword({
    required String cpf,
    required bool sms,
    required bool email,
  }) async {
    try {
      final request = {'cpf': cpf, 'email': email, 'sms': sms};

      final result = await _datasource.getCodeToRecoveryPassword(request);

      // if (result.statusCode != 200) {
      //   throw Exception(result);
      // }

      return result.data;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future checkCodeToRecoveryPassword(
      {required String code, required String userId}) async {
    try {
      final request = {'id': userId, 'code': code};
      final result = await _datasource.sendCodeToRecoveryPassword(request);

      // if (result.statusCode != 200) {
      //   throw Exception(result);
      // }

      return result.data;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  // @override
  // Future<bool> sendCheckDeviceCode({
  //   required String userId,
  //   required bool isEmail,
  // }) async {
  //   try {
  //     final result = await _datasource.sendCheckDeviceCode(
  //         userId: userId, isEmail: isEmail);
  //     return result.data;
  //   } catch (error) {
  //     return await ServerErrorHandling.handleError(error);
  //   }
  // }

  // @override
  // Future checkCodeRegisterDevice(
  //     {required String userId, required String code}) async {
  //   try {
  //     final result = await _datasource.checkCodeValidateDevice(
  //       userId: userId,
  //       code: code,
  //     );
  //     return result.data;
  //   } catch (error) {
  //     return await ServerErrorHandling.handleError(error);
  //   }
  // }

  @override
  Future<bool> setLoginuser({required String login}) async {
    try {
      final String loginEncript = await CryptoUtils.encode(login);

      await _database.setLogin(loginEncript);
      return true;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<bool> setPasswordUser({required String password}) async {
    try {
      final String passwordEncript = await CryptoUtils.encode(password);
      await _database.setpassword(passwordEncript);
      return true;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<String> getLoginuser() async {
    try {
      final String? loginEncript = await _database.getLogin();
      if (loginEncript == null) {
        throw Exception(['Value not found']);
      }
      final String login = await CryptoUtils.decode(loginEncript);
      return login;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }

  @override
  Future<String> getPasswordUser() async {
    try {
      final String? passwordEncript = await _database.getpassword();
      if (passwordEncript == null) {
        throw Exception(['Value not found']);
      }
      final String password = await CryptoUtils.decode(passwordEncript);
      return password;
    } catch (error) {
      return await ServerErrorHandling.handleError(error);
    }
  }
}

import 'package:siclosbank/app/shared/data/client/api_response.dart';

abstract class IAuthDatasource {
  Future<ApiResponse> login(Map<String, dynamic> loginRequest);

  Future<ApiResponse> recoverPassword(String cpf);

  Future<ApiResponse> getCodeToRecoveryPassword(Map<String, dynamic> request);

  Future<ApiResponse> sendCodeToRecoveryPassword(Map<String, dynamic> request);

  Future<ApiResponse> resetPassword(
      {required Map<String, dynamic> request, required String id});
}

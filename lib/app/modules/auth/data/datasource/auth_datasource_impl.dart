import 'package:siclosbank/app/modules/auth/data/datasource/auth_datasource.dart';
import 'package:siclosbank/app/modules/auth/data/datasource/auth_endpoints.dart';
import 'package:siclosbank/app/app_controller.dart';
import 'package:siclosbank/app/shared/data/client/api_response.dart';
import 'package:siclosbank/app/shared/data/client/client.dart';

import '../../../../shared/constants/constants.dart';

class AuthDatasourceImpl implements IAuthDatasource {
  final IClient _client;
  AuthDatasourceImpl(this._client);

  @override
  Future<ApiResponse> login(Map<String, dynamic> loginRequest) async {
    var result = await _client.fetch(
      method: 'POST',
      path: AuthEndPoints.login,
      data: loginRequest,
    );

    return result;
  }

  @override
  Future<void> logout() async {
    await _client.fetch(
      method: 'POST',
      path: AuthEndPoints.logout,
    );
    return;
  }

  @override
  Future<ApiResponse> recoverPassword(String cpf) async {
    final result = await _client.fetch(
      method: 'GET',
      path: AuthEndPoints.recoverPassword(cpf),
    );

    return result;
  }

  @override
  Future<ApiResponse> resetPassword({
    required Map<String, dynamic> request,
    required String id,
  }) async {
    final header = {
      Headers.AUTH: AppSession.getInstance().getAuth(isLogged: false)
    };
    final result = await _client.fetch(
        method: 'PUT',
        path: AuthEndPoints.resetPassword(id),
        data: request,
        headers: header);
    return result;
  }

  @override
  Future<ApiResponse> getCodeToRecoveryPassword(
      Map<String, dynamic> request) async {
    final result = await _client.fetch(
      method: 'POST',
      path: AuthEndPoints.getCodeToRecoveryPassword,
      data: request,
    );

    return result;
  }

  @override
  Future<ApiResponse> sendCodeToRecoveryPassword(
      Map<String, dynamic> request) async {
    final result = await _client.fetch(
      method: 'POST',
      path: AuthEndPoints.verifyForgotpassword,
      data: request,
    );

    return result;
  }
}

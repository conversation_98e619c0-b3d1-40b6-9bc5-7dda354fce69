import 'package:siclosbank/app/modules/auth/data/database/auth_datasource.dart';
import 'package:siclosbank/app/shared/constants/constants.dart';
import 'package:siclosbank/app/shared/data/database/storage_adapter.dart';

class AuthDatabaseImplements implements IAuthDatabase {
  final IStorageAdapter storage;

  AuthDatabaseImplements({required this.storage});

  @override
  Future<void> setLogin(String loginRequest) async {
    await storage.insert(LoginDigital.LAST_LOGIN, loginRequest);
  }

  @override
  Future<void> setpassword(String loginRequest) async {
    await storage.insert(LoginDigital.LAST_SENHA, loginRequest);
  }

  @override
  Future<String?> getLogin() async {
    return await storage.get(LoginDigital.LAST_LOGIN);
  }

  @override
  Future<String?> getpassword() async {
    return await storage.get(LoginDigital.LAST_SENHA);
  }
}

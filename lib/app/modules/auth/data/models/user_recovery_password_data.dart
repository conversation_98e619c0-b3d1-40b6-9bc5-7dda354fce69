// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:siclosbank/app/modules/auth/presenter/utils/oculting_user_data.dart';

class UserRecoveryPasswordData extends Equatable {
  final String id;
  final String email;
  final String phone;

  UserRecoveryPasswordData(
      {required this.id, required this.email, required this.phone});

  factory UserRecoveryPasswordData.fromJson(Map<String, dynamic> json) {
    final email = getUserEmailCodify(json['email']);
    final phone = getUserPhoneCodify(json['sms']);

    return UserRecoveryPasswordData(
      id: json['id'] ?? '',
      email: email,
      phone: phone,
    );
  }

  @override
  List<Object> get props => [id, email, phone];
}

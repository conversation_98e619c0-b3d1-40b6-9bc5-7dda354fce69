// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class LoginRequest {
  String login;
  String password;
  String tokenFCM;

  LoginRequest({
    required this.login,
    required this.password,
    required this.tokenFCM,
  });

  LoginRequest copyWith({
    String? login,
    String? password,
    String? tokenFCM,
  }) {
    return LoginRequest(
      login: login ?? this.login,
      password: password ?? this.password,
      tokenFCM: tokenFCM ?? this.tokenFCM,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'login': login,
      'password': password,
      'tokenFCM': tokenFCM,
    };
  }

  factory LoginRequest.fromMap(Map<String, dynamic> map) {
    return LoginRequest(
      login: map['login'] as String,
      password: map['password'] as String,
      tokenFCM: map['tokenFCM'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory LoginRequest.fromJson(String source) =>
      LoginRequest.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() =>
      'LoginRequest(login: $login, password: $password, token: $tokenFCM)';

  @override
  bool operator ==(covariant LoginRequest other) {
    if (identical(this, other)) return true;

    return other.login == login &&
        other.password == password &&
        other.tokenFCM == tokenFCM;
  }

  @override
  int get hashCode => login.hashCode ^ password.hashCode ^ tokenFCM.hashCode;
}

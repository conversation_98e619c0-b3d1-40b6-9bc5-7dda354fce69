import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'authorize_response.g.dart';

@JsonSerializable()
class AuthorizeResponse {
  String? id;
  String? email;
  String? phone;
  bool? pin;

  AuthorizeResponse({this.id, this.email, this.phone, this.pin});

  factory AuthorizeResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthorizeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthorizeResponseToJson(this);

  @override
  String toString() {
    return json.encode(toJson());
  }
}

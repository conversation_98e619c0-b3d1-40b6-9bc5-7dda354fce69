import 'package:siclosbank/app/modules/auth/data/models/user_recovery_password_data.dart';

import '../../data/models/login_request.dart';

abstract class IAuthRepository {
  Future<dynamic> login(LoginRequest loginRequest);

  Future<UserRecoveryPasswordData> recoveryPassword({
    required String cpf,
  });

  Future<String> getCodeToRecoveryPassword({
    required String cpf,
    required bool sms,
    required bool email,
  });

  Future checkCodeToRecoveryPassword({
    required String code,
    required String userId,
  });

  Future<bool> createNewPassword(
      {required String newPassword, required String cpf, required String id});

  // Future<bool> sendCheckDeviceCode({
  //   required String userId,
  //   required bool isEmail,
  // });

  // Future checkCodeRegisterDevice({
  //   required String userId,
  //   required String code,
  // });

  Future<bool> setLoginuser({
    required String login,
  });

  Future<bool> setPasswordUser({
    required String password,
  });

  Future<String> getLoginuser();

  Future<String> getPasswordUser();
}

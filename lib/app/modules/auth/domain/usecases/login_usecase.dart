import 'package:siclosbank/app/modules/auth/data/models/login_request.dart';
import 'package:siclosbank/app/modules/auth/data/models/user_recovery_password_data.dart';
import 'package:siclosbank/app/modules/auth/domain/repositories/i_auth_repository.dart';
import 'package:siclosbank/app/shared/data/models/authorize_device_response.dart';
import 'package:siclosbank/app/shared/data/models/token_response.dart';
import 'package:siclosbank/app/shared/errors/server_error_handling.dart';

import '../../../../app_controller.dart';

abstract class ILoginUsecase {
  Future<dynamic> login(LoginRequest loginRequest);

  Future<UserRecoveryPasswordData> recoveryPassword({required String cpf});

  Future<String> getCodeToRecoveryPassword({
    required String cpf,
    required bool sms,
    required bool email,
  });

  Future sendCodeToRecoveryPassword({
    required String code,
    required String userId,
  });

  Future<bool> createNewPassword(
      {required String cpf, required String newPassword, required String id});

  Future<String> getLogin();

  Future<String> getPassword();

  Future<String> setLogin(String login);

  Future<String> setPassword(String password);
}

class LoginUsecase implements ILoginUsecase {
  final IAuthRepository _repository;

  LoginUsecase(this._repository);
  @override
  Future<dynamic> login(LoginRequest loginRequest) async {
    var result = await _repository.login(loginRequest);

    try {
      if (loginRequest.login.length == 14) {
        AppSession.getInstance().userPF = false;
      } else {
        AppSession.getInstance().userPF = true;
      }

      if (result is TokenResponse) {
        AppSession.getInstance().setAuthToken(result);
        if (result.user?.cpf != null) {
          AppSession.getInstance().user = result.user;
        }
      } else if (result is AuthorizeDeviceResponse) {
        AppSession.getInstance().setAuthorizeDeviceResponse(result);
      }

      return result;
    } catch (e) {
      print(e);

      rethrow;
    }
  }

  @override
  Future<UserRecoveryPasswordData> recoveryPassword(
      {required String cpf}) async {
    try {
      return await _repository.recoveryPassword(cpf: cpf);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> createNewPassword(
      {required String cpf,
      required String newPassword,
      required String id}) async {
    try {
      return await _repository.createNewPassword(
          newPassword: newPassword, cpf: cpf, id: id);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> getCodeToRecoveryPassword(
      {required String cpf, required bool sms, required bool email}) async {
    try {
      return await _repository.getCodeToRecoveryPassword(
          cpf: cpf, sms: sms, email: email);
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future sendCodeToRecoveryPassword({
    required String code,
    required String userId,
  }) async {
    try {
      final result = await _repository.checkCodeToRecoveryPassword(
          code: code, userId: userId);

      if (result != null) {
        AppSession.getInstance().setNotLoggedToken(result);
      }

      return result != null;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> getLogin() async {
    try {
      final result = await _repository.getLoginuser();

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> getPassword() async {
    try {
      final result = await _repository.getPasswordUser();

      return result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> setLogin(String login) async {
    try {
      final result = await _repository.setLoginuser(login: login);

      if (result) {
        return "";
      }
      throw result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }

  @override
  Future<String> setPassword(String password) async {
    try {
      final result = await _repository.setPasswordUser(password: password);
      if (result) {
        return "";
      }
      throw result;
    } catch (e) {
      return await ServerErrorHandling.handleError(e);
    }
  }
}

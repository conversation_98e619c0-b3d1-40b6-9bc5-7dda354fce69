import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:newrelic_mobile/newrelic_navigation_observer.dart';
import 'package:siclosbank/app/shared/presenter/bloc/banks/banks_bloc.dart';
import 'package:siclosbank/app/shared/themes/theme.dart';

import '../localization/generated/i18n.dart';
import 'shared/presenter/bloc/balance/balance_bloc.dart';
import 'shared/presenter/bloc/fast_credit/fast_credit_bloc.dart';

class AppWidget extends StatelessWidget {
  const AppWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Modular.setInitialRoute(Routes.managementPage);s
    Modular.setObservers([NewRelicNavigationObserver()]);

    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => Modular.get<BalanceBloc>()),
        BlocProvider(create: (context) => Modular.get<FastCreditBloc>()),
        Bloc<PERSON>rovider(create: (context) => Modular.get<BanksBloc>()),
      ],
      child: MaterialApp.router(
        routerConfig: Modular.routerConfig,
        title: 'SiclosBank',
        theme: defaultTheme,
        debugShowCheckedModeBanner: false,
        localizationsDelegates: const [
          I18n.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: I18n.delegate.supportedLocales,
        localeResolutionCallback: I18n.delegate.resolution(
          fallback: const Locale("pt", "pt-BR"),
        ),
      ),
    );
  }
}

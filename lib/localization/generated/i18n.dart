import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes
// ignore_for_file: unnecessary_brace_in_string_interps

//WARNING: This file is automatically generated. DO NOT EDIT, all your changes would be lost.

typedef LocaleChangeCallback = void Function(Locale locale);

class I18n implements WidgetsLocalizations {
  const I18n();
  static Locale? _locale;
  static bool _shouldReload = false;

  static set locale(Locale newLocale) {
    _shouldReload = true;
    I18n._locale = newLocale;
  }

  static const GeneratedLocalizationsDelegate delegate =
      GeneratedLocalizationsDelegate();

  /// function to be invoked when changing the language
  static LocaleChangeCallback? onLocaleChanged;

  static I18n? of(BuildContext context) =>
      Localizations.of<I18n>(context, WidgetsLocalizations);

  @override
  TextDirection get textDirection => TextDirection.ltr;

  /// "Olá,"
  String get ola => "Olá,";

  /// "CPF"
  String get cpf => "CPF";

  /// "Abrir conta"
  String get abrir_conta => "Abrir conta";

  /// "Já tenho uma conta"
  String get ja_tenho_uma_conta => "Já tenho uma conta";

  /// "Continuar"
  String get continuar => "Continuar";

  /// "Vamos começar, qual é o seu CPF?"
  String get title_what_is_your_cpf => "Vamos começar, qual é o seu CPF?";

  /// "CADASTRAR"
  String get cadastrar => "CADASTRAR";

  /// "Cadastrar"
  String get register => "Cadastrar";

  /// "DADOS BÁSICOS"
  String get dados_basicos => "DADOS BÁSICOS";

  /// "Sobre você"
  String get sobre_voce => "Sobre você";

  /// "Nome completo"
  String get nome_completo => "Nome completo";

  /// "Digite um nome completo"
  String get nome_completo_erro => "Digite um nome completo";

  /// "Campo obrigatório"
  String get campo_obrigatorio => "Campo obrigatório";

  /// "Campos obrigatórios"
  String get campo_obrigatorio_rh =>
      "Campos obrigatórios, por favor entre em contato com o RH.";

  /// "Nome de usuário"
  String get nome_de_usuario => "Nome de usuário";

  /// "Data de nascimento"
  String get data_nascimento => "Data de nascimento";

  /// "Data de expedicao"
  String get data_expedicao => "Data de expedição";

  /// "Orgão expedidor"
  String get orgao_expedidor => "Orgão expedidor";

  /// "Idade inferior a 18 anos de idade"
  String get idade_inferior_18 => "Idade inferior a 18 anos de idade";

  /// "Nome da mãe"
  String get nome_mae => "Nome da mãe";

  /// "Estado civil"
  String get marital_status => "Estado civil";

  /// "Nacionalidade"
  String get nacionality => "Nacionalidade";

  /// "E-mail"
  String get email => "E-mail";

  /// "RG"
  String get rg => "RG";

  /// "RNE"
  String get rne => "RNE";

  /// "Informe um e-mail válido"
  String get email_invalido => "Informe um e-mail válido";

  /// "Telefone celular"
  String get telefone_celular => "Telefone celular";

  /// "Informe um número de telefone válido"
  String get telefone_celular_invalido =>
      "Informe um número de telefone válido";

  /// "Não reconheço este ${isPhone ? "número de celular" : "email"}"
  String nao_reconheco_este_numero([bool isPhone = true]) =>
      "Não reconheço este ${isPhone ? "número de celular" : "email"}";

  /// "Se você não reconhece o ${isPhone ? "número de celular" : "email"} exibido, entre em contato com nosso suporte para verificar sua conta."
  String nao_reconheco_este_numero_email_msg([bool isPhone = true]) =>
      "Se você não reconhece o ${isPhone ? "número de celular" : "email"} exibido, entre em contato com nosso suporte para verificar sua conta.";

  /// "Informar nome social"
  String get informar_nome_social => "Informar nome social";

  /// "Nome social"
  String get nome_social => "Nome social";
  String get nome_social_alterado_sucesso => "Nome social alterado com sucesso";
  String get nome_social_alterado_erro => "Nome social alterado com erro";

  /// "CONFIRMAÇÃO SMS"
  String get confirmacao_sms => "CONFIRMAÇÃO SMS";

  /// "sms"
  String get sms => "sms";

  /// "Confirme seu número inserindo o código que enviamos"
  String get descricao_confirmacao_sms =>
      "Confirme seu número inserindo o código que enviamos";

  /// "Confirme seu $value inserindo o código que enviamos"
  String confirmation_phone_email_message(String value) =>
      "Confirme seu $value inserindo o código que enviamos";

  /// "Descricao"
  String get descricao => "Descrição";

  /// "Descricao (opcional)"
  String get descricao_opcional => "Descrição (opcional)";

  /// "Adicionar descricao"
  String get adicionar_descricao => "Adicionar descrição";

  /// "Reenviar código em"
  String get reenviar_codigo_sms_timer => "Reenviar código em";

  /// "Reenviar código"
  String get reenviar_codigo_sms => "Reenviar código";

  /// "Data inválida"
  String get data_invalida => "Data inválida";

  /// "Data maior que 30 dias"
  String get dataMaiorQue30Dias => "Data maior que 30 dias";

  /// "SEGURANÇA"
  String get seguranca => "SEGURANÇA";

  /// "Crie uma senha"
  String get crie_uma_senha => "Crie uma senha";

  /// "Senha"
  String get senha => "Senha";

  /// "6 Caracteres"
  String get tamanho_senha => "6 Caracteres";

  /// "Senhas divergentes"
  String get password_not_match => "Senhas divergentes";

  /// "Senhas iguais"
  String get password_match => "Senhas iguais";

  /// "Número"
  String get numero => "Número";

  /// "Letra maiúscula"
  String get letra_maiuscula => "Letra maiúscula";

  /// "Caractere especial"
  String get caractere_especial => "Caractere especial";

  /// "obscureText não pode ser adicionado junto com o suffixIcon"
  String get validacao_obscure_text =>
      "obscureText não pode ser adicionado junto com o suffixIcon";

  /// "ENDEREÇO"
  String get endereco => "ENDEREÇO";

  /// "Confirme se seu endereco obtido do RH está correto e atualizado."
  String get verifique_endereco =>
      "Confirme se seu endereco obtido do RH está correto e atualizado.";

  /// "Se o endereço estiver incorreto, procure o RH para correção. Você poderá prosseguir com o cadastro no dia seguinte, após a correção."
  String get verifique_endereco_msg =>
      "Se o endereço estiver incorreto, procure o RH para correção. Você poderá prosseguir no dia seguinte, após a correção.";

  /// "Prossiga com a solicitação apenas se o endereço estiver correto. Para alterá-lo, entre em contato com o RH. Após a alteração, o endereço será atualizado no app em até 24 horas."
  String get verifique_endereco_msg2 =>
      "Prossiga com a solicitação apenas se o endereço estiver correto. Para alterá-lo, entre em contato com o RH. Após a alteração, o endereço será atualizado no app em até 24 horas.";

  /// "Senha inválida"
  String get senha_invalida => "Senha inválida";

  /// "Senha incorreta"
  String get senha_incorreta => "Senha incorreta.";

  /// "Senha atual incorreta. Verifique e tente novamente"
  String get senha_atual_incorreta =>
      "Senha atual incorreta. Verifique e tente novamente.";

  /// "Insira seu CEP"
  String get titulo_cep => "Insira seu CEP";

  /// "CEP"
  String get cep => "CEP";

  /// "Está tudo certo?"
  String get tuod_certo => "Está tudo certo?";

  /// "Nº"
  String get numero_abreviado => "Nº";

  /// "Rua/Avenida"
  String get rua_avenida => "Rua/Avenida";

  /// "Bairro"
  String get bairro => "Bairro";

  /// "Complemento"
  String get complemento => "Complemento";

  /// "Cidade"
  String get cidade => "Cidade";

  /// "Estado"
  String get estado => "Estado";

  /// "Vamos verificar sua identidade"
  String get verificao_identidade => "Vamos verificar sua identidade";

  /// "Você será redirecionado para um ambiente seguro onde poderá enviar a documentação necessária."
  String get voce_sera_redirecionado =>
      "Você será redirecionado para um ambiente seguro onde poderá enviar a documentação necessária.";

  /// "Auto-retrato"
  String get auto_retrato => "Auto-retrato";

  /// "RG/CNH Frente"
  String get rg_cnh_frete => "RG/CNH Frente";

  /// "RG/CNH Verso"
  String get rg_cnh_verso => "RG/CNH Verso";

  /// "Enviar para análise"
  String get enviar_para_analise => "Enviar para análise";

  /// "Hora da foto!"
  String get hora_da_foto => "Hora da foto!";

  /// "\u2022 Retire óculos ou acessórios"
  String get dica1_auto_retrato => "\u2022 Retire óculos ou acessórios";

  /// "\u2022 Escolha um local bem iluminado"
  String get dica2_auto_retrato => "\u2022 Escolha um local bem iluminado";

  /// "\u2022 Evite expressões faciais"
  String get dica3_auto_retrato => "\u2022 Evite expressões faciais";

  /// "\u2022 Deixe seu rosto centralizado"
  String get dica4_auto_retrato => "\u2022 Deixe seu rosto centralizado";

  /// "Segure o smartphone na altura dos olhos e encaixe seu rosto na marcação."
  String get dica_posicionamento_auto_retrato =>
      "Segure o smartphone na altura dos olhos e encaixe seu rosto na marcação.";

  /// "Centralize ${lado} da CNH no retângulo"
  String dica_posicionamento_cnh(String lado) =>
      "Centralize ${lado} da CNH no retângulo";

  /// "Com a CNH dobrada, fotografe ${lado} do documento"
  String dica_posicionamento_cnh_topo(String lado) =>
      "Com a CNH dobrada, fotografe ${lado} do documento";

  /// "Centralize ${lado} da identidade no retângulo"
  String dica_posicionamento_rg(String lado) =>
      "Centralize ${lado} da identidade no retângulo";

  /// "Fotografe ${lado} do documento"
  String dica_posicionamento_rg_topo(String lado) =>
      "Fotografe ${lado} do documento";

  /// "Fotografe ${lado} do documento"
  String dica_posicionamento_comprovante_topo(String lado) =>
      "Fotografe ${lado} do documento";

  /// "Centralize seu comprovante no retângulo"
  String get dica_posicionamento_comprovante =>
      "Centralize seu comprovante no retângulo";

  /// "A foto ficou boa?"
  String get titulo_validacao_auto_retrato => "A foto ficou boa?";

  /// "Sim, usar esta foto"
  String get utilizar_foto => "Sim, usar esta foto";

  /// "Não, tentar novamente"
  String get nao_utilizar_foto => "Não, tentar novamente";

  /// "Qual documento você irá fotografar?"
  String get titulo_selecionar_documento =>
      "Qual documento você irá fotografar?";

  /// "CNH - Carteira de motorista"
  String get carteira_de_motorista => "CNH - Carteira de motorista";

  /// "RG - Carteira de identidade"
  String get carteira_de_identidade => "RG - Carteira de identidade";

  /// "Documentação em falta"
  String get documentacao_em_falta => "Documentação em falta";

  /// "Analisamos o seu cadastro e vamos precisar dos seguintes itens:"
  String get documentacao_em_falta_descricao =>
      "Analisamos o seu cadastro e vamos precisar dos seguintes itens:";

  /// "Notificações"
  String get notificacoes => "Notificações";

  /// "Buscar Serviços"
  String get search_services => "Buscar Serviços";

  /// "Nenhuma notificação ainda."
  String get notificacao_empty => "Nenhuma notificação ainda.";

  /// "Autorize este dispositivo"
  String get authorize_device_title => "Autorize este dispositivo";

  /// "Este dispositivo não está vinculado a esta conta. Isso pode ocorrer se você trocou de aparelho, por exemplo."
  String get authorize_device_msg =>
      "Este dispositivo não está vinculado a esta conta. Isso pode ocorrer se você trocou de aparelho, por exemplo.";

  /// "Utilize um de nossos canais para receber o código de segurança."
  String get autorizar_dispositivo_canais =>
      "Utilize um de nossos canais para receber o código de segurança.";

  /// "E-mail enviado!"
  String get email_enviado => "E-mail enviado!";

  /// "SMS enviado!"
  String get sms_enviado => "SMS enviado!";

  /// "Digite o código recebido em\n${comunicacao}"
  String digiteCodigoRecebido(String comunicacao) =>
      "Digite o código recebido em\n${comunicacao}";

  /// "Dispositivo vinculado com sucesso."
  String get dipositivo_vinculado_sucesso =>
      "Dispositivo vinculado com sucesso.";

  /// "Recarga de telefone"
  String get recarga_de_telefone => "Recarga de telefone";

  /// "Insira o número do telefone e operadora que deseja recarregar"
  String get insira_os_dados_do_telefone =>
      "Insira o número do telefone e operadora que deseja recarregar";

  /// "Preencher com meu número"
  String get preencher_com_meu_numero => "Preencher com meu número";

  /// "Operadora"
  String get operadora => "Operadora";

  /// "DDD"
  String get ddd => "DDD";

  /// "Outro valor"
  String get outro_valor => "Outro valor";

  /// "Valor da recarga"
  String get valor_recarga => "Valor da recarga";

  /// "Saldo atual: ${saldo}"
  String saldoAtualConta(String saldo) => "Saldo atual: ${saldo}";

  /// "Recarga\n${operadora}"
  String recargaOperadora(String operadora) => "Recarga\n${operadora}";

  /// "Valor a ser pago ${valor}"
  String valorSerPago(String valor) => "Valor a ser pago ${valor}";

  /// "Você não tem saldo suficiente"
  String get saldo_insuficiente => "Você não tem saldo suficiente";

  /// "É necessário transferir o valor para a sua carteira antes de efetivar a compra"
  String get saldo_insuficiente_recarga =>
      "É necessário transferir o valor para a sua carteira antes de efetivar a compra";

  /// "Ir para carteira"
  String get ir_carteira => "Ir para carteira";

  /// "Comprovante"
  String get comprovante => "Comprovante";

  /// "Comprovante de "
  String get comprovanteDe => "Comprovante de ";

  /// "Comprovante de TED"
  String get comprovanteTED => "Comprovante de TED";

  /// "Comprovante de transferência entre contas"
  String get comprovanteTransferenciaEntreContas =>
      "Comprovante de transferência entre contas";

  /// "Comprovante de pagamento de boleto bancário"
  String get comprovantePagBoletoBancario =>
      "Comprovante de pagamento de boleto bancário";

  /// "Tipo"
  String get tipo => "Tipo";

  /// "Tipo de transacao"
  String get tipo_transacao => "Tipo de transação";

  /// "Valor"
  String get valor => "Valor";

  /// "Data"
  String get data => "Data";

  /// "Código de transação"
  String get codigo_transacao => "Código de transação";

  /// "Código de transação"
  String get transacao_concluida => "Transação concluída";

  /// "Estabelecimento"
  String get estabelecimento => "Estabelecimento";

  /// "EmCash"
  String get emcash => "EmCash";

  /// "nsu"
  String get nsu => "nsu";

  /// "Recarga realizada com sucesso!"
  String get recarga_realizada_sucesso => "Recarga realizada com sucesso!";

  /// "Recarga de celular pré-pago"
  String get recarga_celular => "Recarga de celular pré-pago";

  /// "Finalizar"
  String get finalizar => "Finalizar";

  /// "Comprovante de residência"
  String get comprovante_residencia => "Comprovante de residência";

  /// "Enviar comprovante"
  String get enviar_comprovante => "Enviar comprovante";

  /// "Escolha como deseja enviar a sua documentação."
  String get escolha_enviar_documentacao =>
      "Escolha como deseja enviar a sua documentação.";

  /// "Foto"
  String get foto => "Foto";

  /// "Os empréstimos estarão disponíveis assim que você completar 6 meses de trabalho na empresa."
  String get emprestimo_simulacao_mensagem_6_meses =>
      "Os empréstimos estarão disponíveis assim que você completar 6 meses de trabalho na empresa.";

  /// "Conheça o empréstimo consignado com a praticidade e melhores taxas do mercado."
  String get emprestimo_simulacao_mensagem =>
      "Conheça o empréstimo consignado com a praticidade e melhores taxas do mercado.";

  /// "Simulação"
  String get simulacao => "Simulação";

  /// "Primeiramente, precisamos que preencha os seguintes dados:"
  String get emprestimo_simulacao_renda_mensagem =>
      "Primeiramente, precisamos que preencha os seguintes dados:";

  /// "Salário base"
  String get salario_base => "Salário base";

  /// "Salário líquido"
  String get salario_liquido => "Salário líquido";

  /// "Remuneração variável"
  String get remuneracao_variavel => "Remuneração variável";

  /// "Qual o valor do empréstimo que você deseja?"
  String get emprestimo_simulacao_valor_mensagem =>
      "Qual o valor do empréstimo que você deseja?";

  String get seu_limite_emprestimo_disponivel => "Limite máximo disponível: ";

  /// "Você deseja pagar seu empréstimo em quantas parcelas?"
  String get emprestimo_simulacao_parcelas_mensagem =>
      "Você deseja pagar seu empréstimo em quantas parcelas?";

  /// "Taxa"
  String get taxa => "Taxa";

  /// "Valor da parcela"
  String get valor_parcela => "Valor da parcela";

  /// "Valor da parcela"
  String get valor_credito => "Valor do crédito";

  /// "Total a ser pago"
  String get total_a_ser_pago => "Total a ser pago";

  /// "*O valor máximo de parcela é de acordo com o salário informado."
  String get valor_maximo_de_parcela_msg =>
      "*O valor máximo de parcela é de acordo com o salário informado.";

  /// "Pedido"
  String get pedido => "Pedido";

  /// "Parcelas"
  String get parcelas => "Parcelas";

  /// "Valor liberado"
  String get valor_liberado => "Valor liberado";

  /// "Valor de parcelas"
  String get valor_parcelas => "Valor de parcelas";

  /// "Taxa de juros estimada"
  String get taxa_juros_estimada => "Taxa de juros estimada";

  /// "Confirmação"
  String get confirmacao => "Confirmação";

  /// "Confirmar pedido"
  String get confirmar_pedido => "Confirmar pedido";

  /// "Envie sua documentação"
  String get envie_sua_documentacao => "Envie sua documentação";

  /// "Precisamos de seus documentos para realizar a análise."
  String get precisamos_de_seus_documentacao =>
      "Precisamos de seus documentos para realizar a análise.";

  /// "Pedido enviado!"
  String get pedido_enviado => "Pedido enviado!";

  /// "Nossa equipe vai analisar o seu pedido."
  String get emprestimo_simulacao_enviada_mensagem =>
      "Nossa equipe vai analisar o seu pedido.";

  /// "Infelizmente no momento você não possui vínculo empregatício com nenhuma empresa. Entre em contato com o suporte para mais informações."
  String get emprestimo_simulacao_sem_vinculo =>
      "Infelizmente no momento você não possui vínculo empregatício com nenhuma empresa. Entre em contato com o suporte para mais informações.";

  /// "Estamos analisando sua conta de crédito. \nEnquanto isso, não é possivel realizar empréstimos."
  String get credit_account_in_analysys =>
      "Estamos analisando sua conta de crédito. \nEnquanto isso, não é possivel realizar empréstimos.";

  /// "Conta de crédito não aprovada.\n\nInfelizmente, sua conta de crédito não foi aprovada e, por isso, você não pode solicitar empréstimos no momento."
  String get credit_account_not_approved =>
      "Conta de crédito não aprovada.\n\nInfelizmente, sua conta de crédito não foi aprovada e, por isso, você não pode solicitar empréstimos no momento.";

  /// "Depositar"
  String get depositar => "Depositar";

  /// "Deposite de qualquer banco para a sua conta."
  String get depositar_mensagem =>
      "Deposite de qualquer banco para a sua conta.";

  /// "Deposite via um boleto"
  String get deposite_via_um_boleto => "Deposite via um boleto";

  /// "Boleto bancário"
  String get boleto_bancario => "Boleto bancário";

  /// "Titular"
  String get titular => "Titular";

  /// "Banco"
  String get banco => "Banco";

  /// "Banco Central"
  String get banco_central => "Banco Central";

  /// "Acessar Banco Central"
  String get acessar_banco_central => "Acessar Banco Central";

  /// "Entre em contato com a central de atendimento do seu aplicativo. Caso sua reclamação não seja solucionada, você poderá registrar sua reclamação no site do Banco Central do Brasil."
  String get banco_central_message =>
      "Entre em contato com a central de atendimento do seu aplicativo. Caso sua reclamação não seja solucionada, você poderá registrar sua reclamação no site do Banco Central do Brasil.";

  /// "Agência"
  String get agencia => "Agência";

  /// "Conta corrente"
  String get conta_corrente => "Conta corrente";

  /// "Texto copiado!"
  String get texto_copiado => "Texto copiado!";

  /// "Qual valor você deseja depositar?"
  String get qual_valor_deseja_depositar => "Qual valor você deseja depositar?";

  /// "Disponíveis ${qdedisp} de ${qdelimite} depósitos mensais isentos"
  String qde_disponivel_depositos(String qdedisp, String qdelimite) =>
      "Disponíveis ${qdedisp} de ${qdelimite} depósitos mensais isentos";

  /// "O valor mínimo é de ${valor}"
  String valor_minimo_deposito(String valor) => "O valor mínimo é de ${valor}";

  /// "Será cobrado uma tarifa de ${emissao} para esta emissão e de ${compensacao} para compensação"
  String msgTaxaBoletoEmissaoCompensacao(String emissao, String compensacao) =>
      "Será cobrado uma tarifa de ${emissao} para esta emissão e de ${compensacao} para compensação";

  /// "Será cobrado uma tarifa de ${valor} para esta emissão"
  String msgTaxaBoletoEmissao(String valor) =>
      "Será cobrado uma tarifa de ${valor} para esta emissão";

  /// "Será cobrado uma tarifa de ${valor} para compensação"
  String msgTaxaBoletoCompensacao(String valor) =>
      "Será cobrado uma tarifa de ${valor} para compensação";

  /// "Boleto para depósito"
  String get boleto_para_deposito => "Boleto para depósito";

  /// "Erro ao fazer download, tente novamente mais tarde."
  String get erro_ao_fazer_download =>
      "Erro ao fazer download, tente novamente mais tarde.";

  /// "Favor verificar as permissões do aplicativo."
  String get permissoes_aplicativo =>
      "Favor verificar as permissões do aplicativo.";

  /// "Tarifa emissão/compensação"
  String get tarifa_compensacao => "Tarifa emissão/compensação";

  /// "Transferência bancária"
  String get transferencia_bancaria => "Transferência bancária";

  /// "Qual valor você deseja transferir?"
  String get qual_valor_deseja_transeferir =>
      "Qual valor você deseja transferir?";

  /// "Faça uma transferência por TED e envie uma foto do comprovante. O dinheiro será creditado após ser identificado na nossa conta"
  String get msg_tela_deposito_transferencia =>
      "Faça uma transferência por TED e envie uma foto do comprovante. O dinheiro será creditado após ser identificado na nossa conta";

  /// "Enviar foto do comprovante"
  String get enviar_foto_comprevante => "Enviar foto do comprovante";

  /// "Cancelar transferência"
  String get cancelar_tranferencia => "Cancelar transferência";

  /// "Aviso de transferência enviado"
  String get aviso_transferencia_enviado => "Aviso de transferência enviado";

  /// "Quando identificarmos o pagamento em nossa conta, o seu saldo será atualizado."
  String get msg_deposito_tranferencia_dialog =>
      "Quando identificarmos o pagamento em nossa conta, o seu saldo será atualizado.";

  /// "CNPJ"
  String get cnpj => "CNPJ";

  /// "Já existe um aviso de transferência realizado, você pode efetivá-lo ou cancelá-lo."
  String get ja_existe_transferencia =>
      "Já existe um aviso de transferência realizado, você pode efetivá-lo ou cancelá-lo.";

  /// "Estamos processando a última transferência realizada."
  String get ja_existe_envio_comprovante =>
      "Estamos processando a última transferência realizada.";

  /// "Valor máximo de parcela\nde acordo com salário: ${valor}"
  String valorParcelaMaximo(String valor) =>
      "Valor máximo de parcela\nde acordo com salário: ${valor}";

  /// "Digite CPF ou busque um dos seus contatos"
  String get busque_seus_contatos =>
      "Digite CPF para nova transferência ou busque um dos seus contatos";

  /// "Nome ou CPF"
  String get nome_ou_cpf => "Nome ou CPF";

  /// "Nome ou CPF/CNPJ"
  String get nome_ou_cpf_cnpj => "Nome ou CPF/CPNJ";

  /// "Nenhum contato encontrado em sua agenda."
  String get nenhum_usuario_encontrado_agenda =>
      "Nenhum contato encontrado em sua agenda.";

  /// "Contato"
  String get contato => "Contato";

  /// "Fale conosco"
  String get contact_us => "Fale conosco";

  /// "Contatos"
  String get contatos => "Contatos";

  /// "Frequentes"
  String get frequentes => "Frequentes";

  /// "Carteira Digital"
  String get carteira_digital => "Carteira Digital";

  /// "Outro banco"
  String get outro_banco => "Outro banco";

  /// "Editar"
  String get editar => "Editar";

  /// "Siclos"
  String get siclos => "Siclos";

  /// "AG ${agencia} | ${conta}"
  String agenciaContaCorrenteFormat(String agencia, String conta) =>
      "AG ${agencia} | ${conta}";

  /// "Remover ${nomeUsuario} \nde sua lista de contatos?"
  String excluirContato(String nomeUsuario) =>
      "Remover ${nomeUsuario} \nde sua lista de contatos?";

  /// "Remover a conta do contato ${nomeUsuario}?"
  String excluirConta(String nomeUsuario) =>
      "Remover a conta do contato ${nomeUsuario}?";

  /// "Transferir para"
  String get transferir_para => "Transferir para";

  /// "Instituição Bancária"
  String get instituicao_bancaria => "Instituição Bancária";

  /// "Instituição"
  String get instituicao => "Instituição";

  /// "Selecione uma instituição bancária"
  String get instituicao_bancaria_error => "Selecione uma instituição bancária";

  /// "Agência (sem o dígito verificador)"
  String get agencia_sem_digito => "Agência (sem o dígito verificador)";

  /// "Agência não pode ser vazia."
  String get erro_agencia => "Agência não pode ser vazia.";

  /// "Erro!"
  String get erro => "Erro!";

  /// "Você não pode remover o celular atual"
  String get erro_remove_device1 => "Você não pode remover o celular atual";

  /// "Não conseguimos validar sua identidade. \nPor favor, tente novamente ou entre em contato com o suporte para continuar o processo de recuperação de senha.""
  String get error_check_face =>
      "Não conseguimos validar sua identidade. \nPor favor, tente novamente ou entre em contato com o suporte para continuar o processo de recuperação de senha.";

  /// "Conta  (com o dígito verificador)"
  String get conta_com_digito => "Conta  (com o dígito verificador)";

  /// "Conta não pode ser vazio."
  String get erro_conta => "Conta não pode ser vazio.";

  /// "Tipo de conta"
  String get tipo_conta => "Tipo de conta";

  /// "Adicionar à lista de contatos"
  String get adicionar_lista_contato => "Adicionar à lista de contatos";

  /// "Conta Poupança"
  String get conta_poupanca => "Conta Poupança";

  /// "Com a carteira digital Siclos você pode realizar transferências somente para contas de mesma titularidade ou para outras carteiras digitais Siclos."
  String get msg_sheet_error_transferencia_digital =>
      "Com a carteira digital Siclos você pode realizar transferências somente para contas de mesma titularidade ou para outras carteiras digitais Siclos.";

  /// "Entendido"
  String get entendido => "Entendido";

  /// "Valor da transferência inferior ao mínimo de R\$0,50"
  String get erro_valor_transferencia =>
      "Valor da transferência inferior ao mínimo de R\$0,50";

  /// "Transferir"
  String get transferir => "Transferir";

  /// "Disponíveis ${disponivel} de ${total} envios mensais isentos"
  String isencaoTarifaTransferir(String disponivel, String total) =>
      "Disponíveis ${disponivel} de ${total} envios mensais isentos";

  /// "Será cobrado uma tarifa de ${valor} para este envio"
  String msgInformeRendimentos(String email) =>
      "Será enviado um e-mail com os informes de rendimentos da conta para ${email}";

  /// "Será cobrado uma tarifa de ${valor} para este envio"
  String msgTaxaTransferencia(String valor) =>
      "Será cobrado uma tarifa de ${valor} para este envio";

  /// "Confirmar"
  String get confirmar => "Confirmar";

  /// "Transferir de"
  String get transferir_de => "Transferir de";

  /// "AG ${agencia}"
  String agenciaUsuario(String agencia) => "AG ${agencia}";

  /// "${conta}"
  String contaUsuario(String conta) => conta;

  /// "Transferência em processamento"
  String get processamento_de_transferencia => "Transferência em processamento";

  /// "Confirmação de transferência"
  String get confirmacao_de_transferencia => "Confirmação de transferência";

  /// "Confirmação de transferência"
  String get confirmar_transferencia => "Confirmar transferência";

  /// "Comprovante de transferência"
  String get comprovante_transferencia => "Comprovante de transferência";

  /// "Realizar outra transferência"
  String get realizar_outra_transferencia => "Realizar outra transferência";

  /// "Comprovante\nde transferência"
  String get comprovante_transferencia_quebra =>
      "Comprovante\nde transferência";

  /// "Transferido de"
  String get transferido_de => "Transferido de";

  /// "Titularidade"
  String get titularidade => "Titularidade";

  /// "Sua transferência está em processamento, assim que confirmada enviaremos uma notificação e o comprovante ficará disponível no extrato."
  String get transferencia_status_detalhe =>
      "Sua transferência está em processamento, assim que confirmada enviaremos uma notificação e o comprovante ficará disponível no extrato.";

  /// "Editar conta"
  String get editar_conta => "Editar conta";

  /// "Salvar alterações"
  String get salvar_alteracoes => "Salvar alterações";

  /// "Salvar"
  String get salvar => "Salvar";

  /// "Lista de contatos atualizada com sucesso"
  String get lista_contatos_atualizada =>
      "Lista de contatos atualizada com sucesso";

  String get contatos_adicionado =>
      "Conta bancária adicionada com sucesso ao contato";

  /// "CPF/CNPJ"
  String get cpf_cnpj => "CPF/CNPJ";

  /// "Não podemos continuar com seu cadastro!"
  String get nao_podemos_continuar_com_cadastro =>
      "Não podemos continuar com seu cadastro!";

  /// "Cadastro exclusivo para funcionários."
  String get cadastro_exclusivo_para_funcionarios =>
      "Cadastro exclusivo para funcionários.";

  /// "Acessos Exclusivos"
  String get acessos_exclusivos => "Acessos Exclusivos";

  /// "Aqui você pode acessar o seu\nHolerite (demonstrativo de\npagamento) e seu Informe de\nRendimentos para IRPF.\nEm breve mais novidades!"
  String get messagem_card_gestao =>
      "Aqui você pode acessar o seu\nHolerite (demonstrativo de\npagamento) e seu Informe de\nRendimentos para IRPF.\nEm breve mais novidades!";

  /// "O holerite do mês vigente fica disponível para download a partir do dia 08 daquele mês."
  String get mensagem_vigencia_holerite =>
      "O holerite do mês vigente fica disponível para download a partir do dia 08 daquele mês.";

  /// "Não habilitado"
  String get nao_habilitado => "Não habilitado";

  /// "Infelizmente no momento você não possui vinculo empregatício com nenhuma empresa. Entre em contato com o suporte para mais informações."
  String get texto_usuario_sem_viculo_empresa =>
      "Infelizmente no momento você não possui vinculo empregatício com nenhuma empresa. Entre em contato com o suporte para mais informações.";

  /// "Faixa de renda mensal"
  String get faixa_renda_mensal => "Faixa de renda mensal";

  /// "Ocupação"
  String get occupation => "Ocupação";

  /// "Nível de exposição política"
  String get nivel_exposicao_politica => "Nível de exposição política";

  /// "Infelizmente a simulação não pode ser iniciada."
  String get simulacao_nao_pode_ser_iniciada =>
      "Infelizmente a simulação não pode ser iniciada.";

  /// "Os dados financeiros consultados se encontram indisponíveis no momento. Entre em contato com o nosso suporte."
  String get simulacao_nao_pode_ser_iniciada_explicacao =>
      "Os dados financeiros consultados se encontram indisponíveis no momento. Entre em contato com o nosso suporte.";

  /// "Erro simulação"
  String get erro_simulacao => "Erro simulação";

  /// "Gift Cards"
  String get gift_cards => "Gift Cards Digitais";

  /// "Gift Card"
  String get gift_card => "Gift Card Digital";

  /// "Você está comprando um Gift Card Digital"
  String get voce_comprando_gift_card_digital =>
      "Você está adquirindo um Gift Card Digital";

  /// "Termos e condições"
  String get termos_e_condicoes => "Termos e condições";

  /// "Google play"
  String get google_play => "Google play";

  /// "Comprar"
  String get comprar => "Comprar";

  /// "Código pra uso"
  String get codigo_pra_uso => "Código pra uso";

  /// "Copiar código"
  String get copiar_codigo => "Copiar código";

  /// "Código de transação"
  String get codigo_de_transacao => "Código de transação";

  /// "Entradas"
  String get entradas => "Entradas";

  /// "Saídas"
  String get saidas => "Saídas";

  /// "Resumo de meses"
  String get resumo_de_meses => "Resumo de meses";

  /// "minhas finanças"
  String get minhas_financas => "minhas finanças";

  /// "Você não tem movimentações desta(s) categoria(s) neste período."
  String get voce_ainda_nao_teve_movimentacao_no_mes =>
      "Você não tem movimentações desta(s) categoria(s) neste período.";

  /// "O período atual pode sofrer alterações."
  String get periodo_atual_pode_sofrer_alteracoes =>
      "O período atual pode sofrer alterações.";

  /// "Adiantar pagamento"
  String get adiantar_pagamento => "Adiantar pagamento";

  /// "Parcela ${parcela}/${total}"
  String parcelaItemTotal(String parcela, String total) =>
      "Parcela ${parcela}/${total}";

  /// "Valor original"
  String get valor_original => "Valor original";

  /// "Total adiantamento"
  String get total_adiantamento => "Total adiantamento";

  /// "${qde} parcela selecionada"
  String qde_parcela_selecionada(String qde) => "${qde} parcela selecionada";

  /// "${qde} parcelas selecionadas"
  String qde_parcelas_selecionadas(String qde) =>
      "${qde} parcelas selecionadas";

  /// "Escolher forma de pagamento"
  String get escolher_forma_pagamento => "Escolher forma de pagamento";

  /// "Adiantamento de parcela"
  String get adiantamento_de_parcela => "Adiantamento de parcela";

  /// "Valor a ser pago"
  String get valor_a_ser_pago => "Valor a ser pago";

  /// "Valor a receber (opcional)"
  String get valor_a_receber_opcional => "Valor a receber (opcional)";

  /// "O valor a receber será definido por quem for pagar o QR Code."
  String get valor_a_receber_0_message =>
      "O valor a receber será definido por quem for pagar o QR Code.";

  /// "Valor a receber"
  String get valor_a_receber => "Valor a receber";

  /// "Adiantamento de parcela de empréstimo"
  String get adiantamento_parcela_emprestimo =>
      "Adiantamento de parcela de empréstimo";

  /// "Parcela ${numero}/${total} — ${valor}"
  String parcelaAdiantamentoComprovante(
    String numero,
    String total,
    String valor,
  ) => "Parcela ${numero}/${total} — ${valor}";

  /// "Boleto para adiantamento"
  String get boleto_para_adiantamento => "Boleto para adiantamento";

  /// "Parcelas pagas por boleto podem demorar até 3 dias úteis para serem compensadas."
  String get mensagem_compensacao_boleto_adiantamento_parcela =>
      "Parcelas pagas por boleto podem demorar até 3 dias úteis para serem compensadas.";

  /// "Valor original das parcelas"
  String get valor_original_das_parcelas => "Valor original das parcelas";

  /// "Valor total pago"
  String get valor_total_pago => "Valor total pago";

  /// "Valor pago"
  String get valor_pago => "Valor pago";

  /// "Valor pago"
  String get valor_recebido => "Valor recebido";

  /// "Nome social"
  String get nome_social_dialog => "Nome social";

  /// "Nome social é aquele pelo qual travestis ou transexuais optam por serem chamados, em contraste com o nome civil registrado que não reflete sua identidade de gênero."
  String get nome_sicial_dialog_msg =>
      "Nome social é aquele pelo qual travestis ou transexuais optam por serem chamados, em contraste com o nome civil registrado que não reflete sua identidade de gênero.";

  /// "CPF inválido"
  String get cpf_invalido => "CPF inválido";

  /// "CPF ou senha incorretos"
  String get cpf_senha_incorretos => "CPF ou senha incorretos";

  /// "Nome inválido"
  String get nome_invalido => "Nome inválido";

  /// "Cartões"
  String get cartoes => "Cartões";

  /// "Investimentos"
  String get investimentos => "Investimentos";

  /// "Investir"
  String get investir => "Investir";

  /// "Info"
  String get info => "Info";

  /// "Parece que você ainda não tem um cartão virtual Siclos.\n\nCom ele, você tem tranquilidade na hora de realizar compras online. Peça já o seu!"
  String get mensagem_sem_cartao_virtual =>
      "Parece que você ainda não tem um cartão virtual Siclos.\n\nCom ele, você tem tranquilidade na hora de realizar compras online. Peça já o seu!";

  /// "Ah não!"
  String get ah_nao => "Ah não!";

  /// "Parece que você não está apto a solicitar um cartão virtual.\n\nEssa funcionalidade está disponível apenas para usuários que possuem conta corrente."
  String get mensagem_voce_nao_tem_cartao =>
      "Parece que você não está apto a solicitar um cartão virtual.\n\nEssa funcionalidade está disponível apenas para usuários que possuem conta corrente.";

  /// "Você colhe o que\nplanta!"
  String get voce_colhe_o_que_planta => "Você colhe o que\nplanta!";

  /// "a"
  String get a => "a";

  /// "em parceria com a"
  String get em_parceria_com_a => "em parceria com a";

  /// "trouxeram para você uma nova forma de investir com segurança."
  String get trouxeram_para_voce_uma_nova_forma =>
      "trouxeram para você uma nova forma de investir com segurança.";

  /// "Funcionalidade ainda\n não disponível"
  String get funcionalidade_ainda_nao_disponivel =>
      "Funcionalidade ainda\n não disponível";

  /// "Em breve vamos disponibilizar essa e outras mais funcionalidades."
  String get em_breve_vamos_disponibilizar =>
      "Em breve vamos disponibilizar essa e outras mais funcionalidades.";

  /// "Prazo"
  String get prazo => "Prazo";

  /// "Rentabilidade"
  String get rentabilidade => "Rentabilidade";

  /// "Risco"
  String get risco_text => "Risco";

  /// "menos risco"
  String get menos_risco => "menos risco";

  /// "mais risco"
  String get mais_risco => "mais risco";

  /// "Limpar"
  String get limpar => "Limpar";

  /// "Filtrar"
  String get filtrar => "Filtrar";

  /// "Ver"
  String get ver => "Ver";

  /// "Oportunidades indicadas"
  String get oportunidades_indicadas => "Oportunidades indicadas";

  /// "Todas as oportunidades"
  String get todas_oportunidades => "Todas as oportunidades";

  /// "Oportunidades"
  String get oportunidades => "Oportunidades";

  /// "A—E"
  String get AE => "A—E";

  /// "Descubra seu perfil de investimento e otimize seus resultados!"
  String get mensagem_perfil_investimentos =>
      "Descubra seu perfil de investimento e otimize seus resultados!";

  /// "CDI + ${taxa}% a.a"
  String taxaPosFixadaAno(String taxa) => "CDI + ${taxa}% a.a";

  /// "${qtde} ativos"
  String contratosAtivos(String qtde) => "${qtde} ativos";

  /// "${qtde} finalizados"
  String contratosFinalizados(String qtde) => "${qtde} finalizados";

  /// "${qtde} na carteira"
  String contratosCarteira(String qtde) => "${qtde} na carteira";

  /// "1 Contrato ativo"
  String get um_contrato_ativo_home => "1 Contrato ativo";

  /// "1 ativo"
  String get um_contrato_ativo => "1 ativo";

  /// "1 finalizado"
  String get um_contrato_finalizado => "1 finalizado";

  /// "1 na carteira"
  String get um_contrato_carteira => "1 na carteira";

  /// "Contratos ativos"
  String get contratos_ativos => "Contratos ativos";

  /// "Contratos finalizados"
  String get contratos_finalizados => "Contratos finalizados";

  /// "Contratos na carteira"
  String get contratos_na_carteira => "Contratos na carteira";

  /// "Menor\nrisco"
  String get riscoA1 => "Menor\nrisco";

  /// "Menor\nrisco"
  String get riscoA2 => "Menor\nrisco";

  /// "Menor\nrisco"
  String get riscoA3 => "Menor\nrisco";

  /// "Risco\nbaixo"
  String get riscoB1 => "Risco\nbaixo";

  /// "Risco\nmoderado"
  String get riscoC1 => "Risco\nmoderado";

  /// "Risco\nalto"
  String get riscoD1 => "Risco\nalto";

  /// "Maior\nrisco"
  String get riscoE3 => "Maior\nrisco";

  /// "Último dia"
  String get expiraEmUmDias => "Último dia";

  /// "${dias} Dias"
  String expiraEmDias(String dias) => "${dias} Dias";

  /// "${dias} Dia"
  String expiraEm1Dia(String dias) => "${dias} Dia";

  /// "${rentabilidade}% a.a."
  String rentabilidadeAoAno(String rentabilidade) => "${rentabilidade}% a.a.";

  /// "${cdi}% CDI"
  String porcentagemCdi(String cdi) => "${cdi}% CDI";

  /// "Ver detalhes"
  String get ver_detalhes => "Ver detalhes";

  /// "Número de contrato"
  String get numero_de_contrato => "Número de contrato";

  /// "Data de expiração da proposta"
  String get data_de_expiracao_da_proposta => "Data de expiração da proposta";

  /// "Investimento"
  String get investimento => "Investimento";

  /// "Lucro"
  String get lucro => "Lucro";

  /// "Recebimento aproximado"
  String get recebimento_aproximado => "Recebimento aproximado";

  /// "1ª parcela"
  String get primeira_parcela => "1ª parcela";

  /// "${qtde} meses"
  String qtdeMeses(num qtde) => "${qtde} mês";

  /// "${qtde} mes"
  String qtdeMes(num qtde) => "${qtde} mês";

  /// "${valor} todo dia ${dia}"
  String valorRecebimentoMes(String valor, String dia) =>
      "${valor} todo dia ${dia}";

  /// "Rating de riscos"
  String get entenda_riscos => "Rating de riscos";

  /// "Criamos uma escala para te ajudar a escolher o investimento ideal."
  String get entenda_riscos_msg =>
      "Criamos uma escala para te ajudar a escolher o investimento ideal.";

  /// "risco e retorno\nmenor"
  String get risco_retorno_menor => "risco e retorno\nmenor";

  /// "risco maior\ne retorno melhor"
  String get risco_retorno_melhor => "risco maior\ne retorno melhor";

  /// "O score de crédito está associado ao risco de inadimplência histórica por perfil. A média de inadimplência é de x% para este perfil"
  String get msg_explicacao_risco =>
      "O score de crédito está associado ao risco de inadimplência histórica por perfil. A média de inadimplência é de x% para este perfil";

  /// "Atenção ao horário"
  String get atencao_ao_horario => "Atenção ao horário";

  /// "Consignados"
  String get consignados => "Consignados";

  /// "Consignado"
  String get consignado => "Consignado";

  /// "Rápido"
  String get rapido => "Rápido";

  /// "Antecipação de Recebíveis"
  String get antecipacao_recebiveis => "Antecipação de Recebíveis";

  /// "Com garantias"
  String get com_garantias => "Com garantias";

  /// "Com garantia"
  String get com_garantia => "Com garantia";

  /// "Ver empréstimo"
  String get ver_emprestimo => "Ver empréstimo";

  /// "Ver empréstimos"
  String get ver_emprestimos => "Ver empréstimos";

  /// "Parcelas pagas"
  String get parcelas_pagas => "Parcelas pagas";

  /// "Parcelas não encontradas ou não disponíveis ainda."
  String get parcelas_nao_encontradas_ou_nao_disponiveis =>
      "Parcelas não encontradas ou não disponíveis ainda.";

  /// "Buscar novo investimento"
  String get buscar_novo_investimento => "Buscar novo investimento";

  /// "Buscar novos investimentos"
  String get buscar_novos_investimentos => "Buscar novos investimentos";

  /// "Você não possui contratos\nativos."
  String get nao_possui_contratos_ativos =>
      "Você não possui contratos\nativos.";

  /// "Você não possui contratos\nfinalizados."
  String get nao_possui_contratos_finalizados =>
      "Você não possui contratos\nfinalizados.";

  /// "Transações de valores acima de"
  String get msg_limite_investimento_1 => "Transações de valores acima de";

  /// "não podem ser"
  String get msg_limite_investimento_2 => "não podem ser";

  /// "realizadas entre "
  String get msg_limite_investimento_3 => "realizadas entre ";

  /// " e "
  String get e => " e ";

  /// "Tente no próximo horário disponível."
  String get msg_limite_investimento_4 =>
      "Tente no próximo horário disponível.";

  /// "Exclusivamente"
  String get exclusivamente => "Exclusivamente:";

  /// "segunda-sexta"
  String get segunda_sexta => "Segunda-feira a sexta-feira ";

  /// "entre"
  String get entre => "entre ";

  /// "horaInicio"
  String get horaInicio => "06:30h ";

  /// "horaFim"
  String get horaFim => "17:00h ";

  /// "horario brasilia"
  String get horario_brasilia => "(horário de Brasília).";

  /// "Prosseguir com aplicação?"
  String get prossessuir_aplicacao => "Prosseguir com aplicação?";

  /// "Ao prosseguir você confirma que deseja fazer este investimento."
  String get prossessuir_aplicacao_msg =>
      "Ao prosseguir você confirma que deseja fazer este investimento.";

  /// "É necessário transferir o valor para a sua carteira antes de efetivar o investimento"
  String get saldo_insuficiente_msg =>
      "É necessário transferir o valor para a sua carteira antes de efetivar o investimento";

  /// "Antes de prosseguir, faça a leitura dos seguintes termos:"
  String get antes_prosseguir =>
      "Antes de prosseguir, faça a leitura dos seguintes termos:";

  /// "Endosso"
  String get endoso_ccb => "Endosso";

  /// "Termo de investidor"
  String get termo_de_investidor => "Termo de investidor";

  /// "Confirmo que li e concordo com todos os documentos listados."
  String get confirmo_li_documentos =>
      "Confirmo que li e concordo com todos os documentos listados.";

  /// "Termos"
  String get termos => "Termos";

  /// "É necessário transferir o valor para a sua conta antes de efetivar o investimento"
  String get saldo_insuficiente_msg_investimento =>
      "É necessário transferir o valor para a sua conta antes de efetivar o investimento";

  /// "Ver investidos"
  String get ver_investidos => "Ver investidos";

  /// "Empréstimos Consignados"
  String get emprestimos_consignados => "Empréstimos Consignados";

  /// "Nome do tomador"
  String get nome_do_tomador => "Nome do tomador";

  /// "Detalhes do investimento"
  String get detalhes_do_investimento => "Detalhes do investimento";

  /// "Visualização de Endosso"
  String get visualizar_endosso => "Visualização de Endosso";

  /// "O Endosso é um documento que representa a operação de crédito realizada com os dados envolvidos."
  String get visualizar_endosso_msg =>
      "O Endosso é um documento que representa a operação de crédito realizada com os dados envolvidos.";

  /// "Abrir"
  String get abrir => "Abrir";

  /// "Endosso_${id}.pdf"
  String nomeArquivoEndosso(String id) => "Endosso_${id}.pdf";

  /// "Erro ao fazer download do arquivo"
  String get erro_download_arquivo => "Erro ao fazer download do arquivo";

  /// "Contrato"
  String get contrato => "Contrato";

  /// "Retorno"
  String get retorno => "Retorno";

  /// "Recebido"
  String get recebido => "Recebido";

  /// "Ganho Líquido"
  String get ganho_liquido => "Ganho Líquido";

  /// "A receber"
  String get a_receber => "A receber";

  /// "Detalhes da Parcela"
  String get detalhes_da_parcela => "Detalhes da Parcela";

  /// "Juros do contrato"
  String get juros_do_contrato => "Juros do contrato";

  /// "Desconto IRRF"
  String get desconto_irrf => "Desconto IRRF";

  /// "Desconto por antecipação"
  String get desconto_por_antecipacao => "Desconto por antecipação";

  /// "Pagamento recisão"
  String get pagamento_recisao => "Pagamento recisão";

  /// "Total recebido"
  String get total_recebido => "Total recebido";

  /// "Período"
  String get periodo => "Período";

  /// "Mês atual"
  String get mes_atual => "Mês atual";

  /// "Mês anterior"
  String get mes_anterior => "Mês anterior";

  /// "Últimos 3 meses"
  String get ultimos_3_meses => "Últimos 3 meses";

  /// "Ano atual"
  String get ano_atual => "Ano atual";

  /// "Ano anterior"
  String get ano_anterior => "Ano anterior";

  /// "Aplicar"
  String get aplicar => "Aplicar";

  /// "Principal ${percent}%"
  String valorPrincipalInvestimento(String percent) => "Principal ${percent}%";

  /// "Juros ${percent}%"
  String valorJurosInvestimento(String percent) => "Juros ${percent}%";

  /// "Rent. estimada"
  String get rentabilidade_estimada => "Rent. estimada";

  /// "Recebidos"
  String get recebidos => "Recebidos";

  /// "Principal"
  String get principal => "Principal";

  /// "${rentabilidade}% período"
  String rentabilidadePeriodo(String rentabilidade) =>
      "${rentabilidade}% período";

  /// "Rentabilidade estimada"
  String get rentabilidade_estimada_dialog => "Rentabilidade estimada";

  /// "Cálculo de taxa de retorno interno sobre um investimento com base em uma série especificada de fluxos de caixa."
  String get rentabilidade_estimada_dialog_msg =>
      "Cálculo de taxa de retorno interno sobre um investimento com base em uma série especificada de fluxos de caixa.";

  /// "Cálculo da rentabilidade com base nos juros sobre o principal do período filtrado."
  String get rentabilidade_dialog_msg =>
      "Cálculo da rentabilidade com base nos juros sobre o principal do período filtrado.";

  /// "Inadimplentes"
  String get inadimplentes => "Inadimplentes";

  /// "${valor} (${percent}%)"
  String valorDinheiroPercent(String valor, String percent) =>
      "${valor} (${percent}%)";

  /// "Inadimplência"
  String get inadimplencia => "Inadimplência";

  /// "Para todas situações de inadimplência, temos um time próprio que realizará o trabalho de cobrança em etapas pré-definidas de acionamentos.\n\nAtuamos desde o contato preventivo até acionamentos extrajudiciais e protestos."
  String get inadimplencia_msg =>
      "Para todas situações de inadimplência, temos um time próprio que realizará o trabalho de cobrança em etapas pré-definidas de acionamentos.\n\nAtuamos desde o contato preventivo até acionamentos extrajudiciais e protestos.";

  /// "Score"
  String get score => "Score";

  /// "${porcentagem}% inad."
  String inadimplenciaTooltip(String porcentagem) => "${porcentagem}% inad.";

  /// "Total Investido"
  String get total_investido => "Total Investido";

  /// "Taxa de Retorno"
  String get taxa_de_retorno => "Taxa de Retorno";

  /// "Ren. Estimada"
  String get ren_estimada => "Ren. Estimada";

  /// "Total"
  String get total => "Total";

  /// "Cálculo de taxa de retorno interno sobre um investimento com base em uma série especificada de fluxos de caixa."
  String get visualizar_taxa_retorno_msg =>
      "Cálculo de taxa de retorno interno sobre um investimento com base em uma série especificada de fluxos de caixa.";

  /// "Rentabilidade estimada"
  String get rentabilidade_estimada_title => "Rentabilidade estimada";

  /// "Cálculo da rentabilidade com base nos juros sobre o principal do período filtrado."
  String get rentabilidade_estimada_msg =>
      "Cálculo da rentabilidade com base nos juros sobre o principal do período filtrado.";

  /// "No momento, nenhuma\noportunidade está disponível.\nVolte em breve!"
  String get mesagem_nenhuma_oportunidade_disponivel =>
      "No momento, nenhuma\noportunidade está disponível.\nVolte em breve!";

  /// "Vamos entender o seu perfil de investidor."
  String get vamos_entender_perfil_investidor =>
      "Vamos entender o seu perfil de investidor.";

  /// "Responda à algumas perguntas para entendermos melhor quais os melhores investimentos para você."
  String get vamos_entender_perfil_investidor_msg =>
      "Responda à algumas perguntas para entendermos melhor quais os melhores investimentos para você.";

  /// "Fique tranquilo, suas informações estão seguras com a gente."
  String get fique_tranquilo_informacoes =>
      "Fique tranquilo, suas informações estão seguras com a gente.";

  /// "Entender o seu perfil de investidor é fundamental para tomar decisões corretas  e garantir melhores resultados."
  String get entender_questionario_investidor =>
      "Entender o seu perfil de investidor é fundamental para tomar decisões corretas  e garantir melhores resultados.";

  /// "Responder"
  String get responder => "Responder";

  /// "Descubra seu perfil de investidor!"
  String get descubra_perfil_investidor => "Descubra seu perfil de investidor!";

  /// "Refazer questionário"
  String get refazer_questionario => "Refazer questionário";

  /// "Arrojado"
  String get arrojado => "Arrojado";

  /// "Moderado"
  String get moderado => "Moderado";

  /// "Conservador"
  String get conservador => "Conservador";

  /// "Seu perfil é arrojado."
  String get seu_perfil_arrojado => "Seu perfil é arrojado.";

  /// "Você tem experiência com investimentos e está disposto a fazer as escolhas certas para usufruir dos benefícios de correr maiores riscos."
  String get seu_perfil_arrojado_msg_1 =>
      "Você tem experiência com investimentos e está disposto a fazer as escolhas certas para usufruir dos benefícios de correr maiores riscos.";

  /// "Diversifique sua carteira e invista em todos os níveis de riscos."
  String get seu_perfil_arrojado_msg_2 =>
      "Diversifique sua carteira e invista em todos os níveis de riscos.";

  /// "Seu perfil é moderado."
  String get seu_perfil_moderado => "Seu perfil é moderado.";

  /// "Você tem experiência suficiente para aumentar sua rentabilidade diversificando entre diversos níveis de riscos."
  String get seu_perfil_moderado_msg_1 =>
      "Você tem experiência suficiente para aumentar sua rentabilidade diversificando entre diversos níveis de riscos.";

  /// "Invista em pessoas com score entre <b>A+</b> e <b>C</b>."
  String get seu_perfil_moderado_msg_2 =>
      "Invista em pessoas com score entre <b>A+</b> e <b>C</b>.";

  /// "Seu perfil é conservador."
  String get seu_perfil_conservador => "Seu perfil é conservador.";

  /// "Me parece que você tem experiência com investimentos de baixo risco e rentabilidade"
  String get seu_perfil_conservador_msg_1 =>
      "Me parece que você tem experiência com investimentos de baixo risco e rentabilidade";

  /// "Invista em pessoas com score entre <b>A+</b> e <b>B</b>."
  String get seu_perfil_conservador_msg_2 =>
      "Invista em pessoas com score entre <b>A+</b> e <b>B</b>.";

  /// "Perfil de investidor"
  String get perfil_investidor => "Perfil de investidor";

  /// "Responder depois"
  String get responder_depois => "Responder depois";

  /// "Seu perfil é"
  String get seu_perfil_e => "Seu perfil é";

  /// "arrojado."
  String get arrojado_perfil => "arrojado.";

  /// "moderado."
  String get moderado_perfil => "moderado.";

  /// "conservador."
  String get conservador_perfil => "conservador.";

  /// "Ver oportunidades de investimento!"
  String get ver_oportunidades_mercado => "Ver oportunidades de investimento!";

  /// "Você pode escolher mais de uma opção."
  String get escolher_mais_opcao => "Você pode escolher mais de uma opção.";

  /// "Quer otimizar seus resultados para encontrar os melhores investimentos para o seu bolso? Faça o teste agora e não perca tempo! "
  String get questionario_perfil_investidor_intro_message =>
      "Quer otimizar seus resultados para encontrar os melhores investimentos para o seu bolso? Faça o teste agora e não perca tempo! ";

  /// "Fazer o teste agora"
  String get fazer_o_teste_agora => "Fazer o teste agora";

  /// "Powered by"
  String get powered_by => "Powered by";

  /// "Total a receber"
  String get total_a_receber => "Total a receber";

  /// "Usuário não autorizado para nova transferência"
  String get errors4000 => "Usuário não autorizado para nova transferência";

  /// "Conta bancária inexistente"
  String get errors4003 => "Conta bancária inexistente";

  /// "Sua carteira está desabilitada"
  String get errors4005 => "Sua carteira está desabilitada";

  /// "Valor do boleto menor que o permitido"
  String get errors4006 => "Valor do boleto menor que o permitido";

  /// "Valor do boleto maior que o permitido"
  String get errors4007 => "Valor do boleto maior que o permitido";

  /// "Limite de emissão de boletos diários já atingido"
  String get errors4008 => "Limite de emissão de boletos diários já atingido";

  /// "Valor limite de emissão de boletos diários já atingido"
  String get errors4009 =>
      "Valor limite de emissão de boletos diários já atingido";

  /// "Data de emissão de boletos com data incorreta"
  String get errors4010 => "Data de emissão de boletos com data incorreta";

  /// "Boleto não encontrado"
  String get errors4011 => "Boleto não encontrado";

  /// "Transação não encontrada"
  String get errors4012 => "Transação não encontrada";

  /// "Tipo de documento inválido"
  String get errors4035 => "Tipo de documento inválido";

  /// "Tipo de arquivo inválido"
  String get errors4036 => "Tipo de arquivo inválido";

  /// "Estatus de arquivo inválido"
  String get errors4037 => "Estatus de arquivo inválido";

  /// "Tamanho do arquivo excedido"
  String get errors4038 => "Tamanho do arquivo excedido";

  /// "Status do usuário inválido para operação"
  String get errors4039 => "Status do usuário inválido para operação";

  /// "O salário do usuário não pode ser inferior ao salário mínimo previsto em CLT"
  String get errors4040 =>
      "O salário do usuário não pode ser inferior ao salário mínimo previsto em CLT";

  /// "Investimento restrito a esse usuário"
  String get errors4041 => "Investimento restrito a esse usuário";

  /// "Empréstimo indisponível no mercado"
  String get errors4042 => "Empréstimo indisponível no mercado";

  /// "Status de investimento incorreto"
  String get errors4043 => "Status de investimento incorreto";

  /// "Carteira do investidor inválida "
  String get errors4044 => "Carteira do investidor inválida ";

  /// "Saldo insuficiente para investimento"
  String get errors4045 => "Saldo insuficiente para investimento";

  /// "Valores de investimentos devem ser maiores que 0"
  String get errors4046 => "Valores de investimentos devem ser maiores que 0";

  /// "Erro no cálculo de taxas"
  String get errors4047 => "Erro no cálculo de taxas";

  /// "Empréstimo não encontrado"
  String get errors4048 => "Empréstimo não encontrado";

  /// "Outra proposta de empréstimo está em andamento"
  String get errors4049 => "Outra proposta de empréstimo está em andamento";

  /// "Empréstimo negado por política interna"
  String get errors4050 => "Empréstimo negado por política interna";

  /// "Empréstimo não pertence ao usuário"
  String get errors4051 => "Empréstimo não pertence ao usuário";

  /// "Há mais de um empréstimo em andamento ou nenhum foi registrado"
  String get errors4052 =>
      "Há mais de um empréstimo em andamento ou nenhum foi registrado";

  /// "Não há funcionário cadastrado"
  String get errors4053 => "Não há funcionário cadastrado";

  /// "Faltam documentos para o empréstimo"
  String get errors4054 => "Faltam documentos para o empréstimo";

  /// "Documentos do usuário não encontrado"
  String get errors4055 => "Documentos do usuário não encontrado";

  /// "Não há documentos vinculados ao usuário"
  String get errors4056 => "Não há documentos vinculados ao usuário";

  /// "Status de empréstimo incorreto "
  String get errors4057 => "Status de empréstimo incorreto ";

  /// "Valor do empréstimo inferior ao permitido"
  String get errors4058 => "Valor do empréstimo inferior ao permitido";

  /// "Valor do empréstimo superior ao permitido"
  String get errors4059 => "Valor do empréstimo superior ao permitido";

  /// "Valor financiado inferior ao permitido"
  String get errors4060 => "Valor financiado inferior ao permitido";

  /// "Valor financiado inferior ao permitido"
  String get errors4061 => "Valor financiado inferior ao permitido";

  /// "Taxas inferiores as permitidas"
  String get errors4062 => "Taxas inferiores as permitidas";

  /// "Período de carência inválida"
  String get errors4063 => "Período de carência inválida";

  /// "Valor do investimento inferior ao permitido"
  String get errors4064 => "Valor do investimento inferior ao permitido";

  /// "Valor das parcelas de investimento inferior a permitida"
  String get errors4065 =>
      "Valor das parcelas de investimento inferior a permitida";

  /// "Valor rentabilizado inferior ao permitido"
  String get errors4066 => "Valor rentabilizado inferior ao permitido";

  /// "Rentabilidade inferior a permitida"
  String get errors4067 => "Rentabilidade inferior a permitida";

  /// "Rentabilidade líquida inferior a permitida"
  String get errors4068 => "Rentabilidade líquida inferior a permitida";

  /// "CDI inferior ao permitido"
  String get errors4069 => "CDI inferior ao permitido";

  /// "Valor das parcelas de investimento inferior ao limite"
  String get errors4070 =>
      "Valor das parcelas de investimento inferior ao limite";

  /// "Valor das parcelas de investimento superior ao limite"
  String get errors4071 =>
      "Valor das parcelas de investimento superior ao limite";

  /// "Parcela inexistente"
  String get errors4072 => "Parcela inexistente";

  /// "Número da parcela inferior a permitida"
  String get errors4073 => "Número da parcela inferior a permitida";

  /// "Valor total pago inferior ao permitido"
  String get errors4074 => "Valor total pago inferior ao permitido";

  /// "Tempo de empresa inferior ao permitido pela política da siclosapp"
  String get errors4075 =>
      "Tempo de empresa inferior ao permitido pela política da siclosapp";

  /// "Erro na paginação"
  String get errors4076 => "Erro na paginação";

  /// "Limite de registros por página excedido"
  String get errors4077 => "Limite de registros por página excedido";

  /// "Saldo insuficiente para pagamento"
  String get errors4078 => "Saldo insuficiente para pagamento";

  /// "Não é permitido transações com a própria carteira"
  String get errors4079 => "Não é permitido transações com a própria carteira";

  /// "Status da carteira de origem inativa"
  String get errors4080 => "Status da carteira de origem inativa";

  /// "Status da carteira de destino inativa"
  String get errors4081 => "Status da carteira de destino inativa";

  /// "Saldo insuficiente para realizar a transferência"
  String get errors4082 => "Saldo insuficiente para realizar a transferência";

  /// "Valor da transferência inferior a permitida"
  String get errors4083 => "Valor da transferência inferior a permitida";

  /// "Valor da transferência superior a permitida"
  String get errors4084 => "Valor da transferência superior a permitida";

  /// "Saldo insuficiente para resgate"
  String get errors4085 => "Saldo insuficiente para resgate";

  /// "Tipo de banco inválido"
  String get errors4086 => "Tipo de banco inválido";

  /// "Conta bancária não pertence ao usuário"
  String get errors4087 => "Conta bancária não pertence ao usuário";

  /// "Expediente bancário encerrado"
  String get errors4088 => "Expediente bancário encerrado";

  /// "Filtro de TEDs não existentes"
  String get errors4089 => "Filtro de TEDs não existentes";

  /// "Filtro de transferências não existentes"
  String get errors4090 => "Filtro de transferências não existentes";

  /// "Conta bancária inválida"
  String get errors4091 => "Conta bancária inválida";

  /// "Conta bancária não existente"
  String get errors4092 => "Conta bancária não existente";

  /// "Código de verificação desvinculado ao usuário"
  String get errors4093 => "Código de verificação desvinculado ao usuário";

  /// "Já existe outra conta para resgate"
  String get errors4094 => "Já existe outra conta para resgate";

  /// "Documentos de aprovação não encontrados"
  String get errors4095 => "Documentos de aprovação não encontrados";

  /// "Não é permitido voltar desse estágio"
  String get errors4096 => "Não é permitido voltar desse estágio";

  /// "E-mail já confirmado"
  String get errors4097 => "E-mail já confirmado";

  /// "Usuário em outro estágio"
  String get errors4098 => "Usuário em outro estágio";

  /// "Usuário não é uma pessoa física"
  String get errors4099 => "Usuário não é uma pessoa física";

  /// "Usuário não é uma pessoa juridica"
  String get errors4100 => "Usuário não é uma pessoa juridica";

  /// "Senha atual incorreta"
  String get errors4101 => "Senha atual incorreta";

  /// "Nova senha deve ser diferente de anteriores"
  String get errors4102 => "Nova senha deve ser diferente de anteriores";

  /// "Usuário ou senha incorretos"
  String get errors4104 => "Usuário ou senha incorretos";

  /// "CPF filtrado não existe"
  String get errors4105 => "CPF filtrado não existe";

  /// "CPF e CNPJ filtado não existe"
  String get errors4106 => "CPF e CNPJ filtado não existe";

  /// "Nickname filtrado não existe"
  String get errors4107 => "Nickname filtrado não existe";

  /// "Carteira filtrada não existe"
  String get errors4108 => "Carteira filtrada não existe";

  /// "Número de tentativa máxima excedida"
  String get errors4109 => "Número de tentativa máxima excedida";

  /// "Carteira filtrada não existe"
  String get errors4110 => "Carteira filtrada não existe";

  /// "Transação Boleto não encontrada"
  String get errors4111 => "Transação Boleto não encontrada";

  /// "Registro de boleto não encontrado"
  String get errors4112 => "Registro de boleto não encontrado";

  /// "Proposta não encontrada"
  String get errors4113 => "Proposta não encontrada";

  /// "Proposta não encontrada"
  String get errors4114 => "Proposta não encontrada";

  /// "Aceite de termo não encontrado"
  String get errors4115 => "Aceite de termo não encontrado";

  /// "Recusa de termo não encontrada"
  String get errors4116 => "Recusa de termo não encontrada";

  /// "Documentos do empréstimo não encontrados"
  String get errors4117 => "Documentos do empréstimo não encontrados";

  /// "Termo de compromisso pendente não encontrado"
  String get errors4118 => "Termo de compromisso pendente não encontrado";

  /// "Documentos não encontrados"
  String get errors4119 => "Documentos não encontrados";

  /// "Documentos não encontrados"
  String get errors4120 => "Documentos não encontrados";

  /// "Usuário já existente"
  String get errors4126 => "Usuário já existente";

  /// "Código de verificação incorreto"
  String get errors4132 => "Código de verificação incorreto";

  /// "Confirmação de e-mail inválida"
  String get errors4135 => "Confirmação de e-mail inválida";

  /// "Oportunidade já aceita por outro investidor"
  String get errors4142 => "Oportunidade já aceita por outro investidor";

  /// "Não é permitido investir em você mesmo"
  String get errors4145 => "Não é permitido investir em você mesmo";

  /// "Usuário PJ já existente"
  String get errors4148 => "Usuário PJ já existente";

  /// "Há outra TED depósito pendente"
  String get errors4200 => "Há outra TED depósito pendente";

  /// "Registro de TED depósito duplicados ou inexistentes"
  String get errors4201 =>
      "Registro de TED depósito duplicados ou inexistentes";

  /// "TED depósito já foi submetida anteriormente"
  String get errors4202 => "TED depósito já foi submetida anteriormente";

  /// "Valor inferior a R\$ 10,00"
  String get errors4203 => "Valor inferior a R\$ 10,00";

  /// "Limite de TED diárias atingido"
  String get errors4204 => "Limite de TED diárias atingido";

  /// "Data de fundação não pode ser em datas futuras"
  String get errors4206 => "Data de fundação não pode ser em datas futuras";

  /// "Idade inferior a 18 anos"
  String get errors4208 => "Idade inferior a 18 anos";

  /// "O valor é inferior ao permitido"
  String get errors4212 => "O valor é inferior ao permitido";

  /// "A parcela é superior ao permitido para seu salário. Altere o valor ou prazo do pedido"
  String get errors4219 =>
      "A parcela é superior ao permitido para seu salário. Altere o valor ou prazo do pedido";

  /// "Ocorreu um erro, entre em contato com nosso suporte. Obrigado!"
  String get errorsGeneric =>
      "Ocorreu um erro, entre em contato com nosso suporte. Obrigado!";

  /// "Ocorreu um erro interno, entre em contato com nosso suporte. Obrigado!"
  String get errorsGenericIntern =>
      "Ocorreu um erro interno, entre em contato com nosso suporte. Obrigado!";

  /// "Requisição não permitida. Favor entrar em contato com nosso suporte."
  String get errorsForbidden =>
      "Requisição não permitida. Favor entrar em contato com nosso suporte.";

  /// "Cancelar"
  String get cancelar => "Cancelar";

  /// "Autenticar"
  String get autenticar => "Autenticar";

  /// "Facilite seu acesso ao aplicativo"
  String get facilite_seu_acesso => "Facilite seu acesso ao aplicativo";

  /// "Utilize sua digital para continuar"
  String get por_favor_login_digital => "Utilize sua digital para continuar";

  /// "Utilize Face ID para continuar"
  String get por_favor_login_digital_face_id =>
      "Utilize Face ID para continuar";

  /// "Ação bloqueada devido a muitas tentativas. Isso ocorre após 5 tentativas malsucedidas e dura 30 segundos."
  String get bloqueio_tentativar_login_digital =>
      "Ação bloqueada devido a muitas tentativas. Isso ocorre após 5 tentativas malsucedidas e dura 30 segundos.";

  /// "digital"
  String get digital => "digital";

  /// "Sim"
  String get sim => "Sim";

  /// "Não"
  String get nao => "Não";

  /// "Código copiado."
  String get codigo_barra_copiado => "Código copiado.";

  /// "Código de verificação inválido. Verifique e tente novamente"
  String get codigo_invalido =>
      "Código de verificação inválido. Verifique e tente novamente.";

  /// "Código de verificação enviado novamente"
  String get codigo_enviado_novamente =>
      "Código de verificação enviado novamente";

  /// ["Solteiro(a)", "Casado(a)", "Separado(a)", "Divorciado(a)", "Viúvo(a)"]
  List<String> get listEstadoCivil => [
    "Solteiro(a)",
    "Casado(a)",
    "Separado(a)",
    "Divorciado(a)",
    "Viúvo(a)",
  ];

  /// ["solteiro", "casado", "separado", "divorciado", "viuvo"]
  List<String> get listEnumEstadoCivil => [
    "solteiro",
    "casado",
    "separado",
    "divorciado",
    "viuvo",
  ];

  /// ["Inferior a 1 mil", "De 1 mil a 2 mil", "De 2 mil a 3 mil", "De 3 mil a 5 mil", "De 3 mil a 5 mil"]
  List<String> get listFaixaSalarial => [
    "Inferior a 1 mil",
    "De 1 mil a 2 mil",
    "De 2 mil a 3 mil",
    "De 3 mil a 5 mil",
    "De 3 mil a 5 mil",
  ];

  /// ["Administrador Iniciativa Privada", "Administrador Público", "Advogado", "Agricultor", "Agrônomo"]
  List<String> get listOcupacao => [
    "Administrador Iniciativa Privada",
    "Administrador Público",
    "Advogado",
    "Agricultor",
    "Agrônomo",
  ];

  /// ["Não sou e não possuo vínculo com pessoa exposta politicamente", "Sou uma pessoa exposta politicamente", "Tenho vínculo com pessoa exposta politicamente"]
  List<String> get listExposicaoPolitica => [
    "Não sou e não possuo vínculo com pessoa exposta politicamente",
    "Sou uma pessoa exposta politicamente",
    "Tenho vínculo com pessoa exposta politicamente",
  ];

  /// "Hoje"
  String get hoje => "Hoje";

  /// "Ontem"
  String get ontem => "Ontem";

  /// "Enviar documento"
  String get enviar_documento => "Enviar documento";

  /// "Enviar documentos"
  String get enviar_documentos => "Enviar documentos";

  /// "Arquivo no Telefone"
  String get arquivo_telefone => "Arquivo no Telefone";

  /// "Tirar Foto"
  String get tirar_foto => "Tirar Foto";

  /// "Aguarde..."
  String get loading => "Aguarde...";

  /// "Atenção!"
  String get atencao => "Atenção!";

  /// "Tivemos um problema. Por favor tente mais tarde."
  String get error_time_out =>
      "Tivemos um problema. Por favor tente mais tarde.";

  String get alert_change_address =>
      "Por favor entre em contato com o RH para correção do endereço.";

  String get warning_change_address =>
      "Este é o seu endereço cadastrado. Para alterá-lo, entre em contato com o RH. Após a alteração o endereço será atualizado no app em até 24hrs.";

  /// "Ops! Parece que você está sem conexão com a internet."
  String get error_no_internet =>
      "Ops! Parece que você está sem conexão com a internet.";

  /// "OK"
  String get ok => "OK";

  /// "Fechar"
  String get fechar => "Fechar";

  /// "Selecione um arquivo válido. Precisamos de uma imagem ou PDF."
  String get erro_arquivo_upload =>
      "Selecione um arquivo válido. Precisamos de uma imagem ou PDF.";

  String get imagem_nao_encontrada => "Imagem não encontrada!";

  /// "A sessão expirou"
  String get eita_sessao_expirada => "A sessão expirou";

  /// "Por motivos de segurança, faça login\nnovamente"
  String get sessao_expirada_msg =>
      "Por motivos de segurança, faça login\nnovamente";

  /// "Fazer login"
  String get fazer_login => "Fazer login";

  /// "Documentação"
  String get documentacao => "Documentação";

  /// "Entrar"
  String get entrar => "Entrar";

  /// "Entre com o CPF e sua senha cadastrada"
  String get entrar_msg => "Entre com o CPF e sua senha cadastrada";

  /// "Esqueci minha senha"
  String get esqueci_minha_senha => "Esqueci minha senha";

  /// "Digite um CPF valido"
  String get cpf_error => "Digite um CPF valido";

  /// "Digite um CNPJ valido"
  String get cnpj_error => "Digite um CNPJ valido";

  /// "CPF/CNPJ não valido"
  String get cpf_cnpj_invalido => "CPF/CNPJ não valido";

  /// "Campo não pode ser vazio"
  String get campo_nao_pode_ser_vazio => "Campo não pode ser vazio";

  /// "Digite um email válido"
  String get email_error => "Digite um email válido";

  /// "Digite um valor valido"
  String get error_valor_vazio_emprestimo => "Digite um valor valido";

  /// "Sistema em manutenção"
  String get sistema_manutencao => "Sistema em manutenção";

  /// "Sistema em manutenção"
  String get informe_rendimentos => "Informe de rendimentos";

  /// "Digite seu CPF para verificarmos sua identidade. \nEm seguida, enviaremos um código de verificação no e-mail ou por SMS para o número de celular cadastrado."
  String get esqueci_senha_msg =>
      "Digite seu CPF para verificarmos sua identidade. \nEm seguida, enviaremos um código de verificação no e-mail ou por SMS para o número de celular cadastrado.";

  /// "Para redefinir sua senha, escolha como deseja receber o código de verificação. Você pode optar por recebê-lo por e-mail ou SMS."
  String get escolha_verificacao_msg =>
      "Para redefinir sua senha, escolha como deseja receber o código de verificação. Você pode optar por recebê-lo por e-mail ou SMS.";

  /// "Recuperar senha"
  String get recuperar_senha => "Recuperar senha";

  /// "Email enviado com instruções para cadastro de uma nova senha."
  String get recuperar_senha_enviada =>
      "Email enviado com instruções para cadastro de uma nova senha.";

  /// "Pronto!"
  String get pronto => "Pronto!";

  /// "Recebemos suas informações, agora aguarde enquanto a nossa equipe analisará seu cadastro."
  String get recebemos_informacoes_cadastro =>
      "Recebemos suas informações, agora aguarde enquanto a nossa equipe analisará seu cadastro.";

  /// "Sair"
  String get sair => "Sair";

  /// "Termos de uso"
  String get termos_de_uso => "Termos de uso";

  /// "Pendente de assinatura de contrato"
  String get pendent_signature => "Pendente de assinatura de contrato";

  /// "Assinatura efetuada com sucesso"
  String get assinatura_efetuada_sucesso => "Assinatura efetuada com sucesso!";

  /// "Assinar contrato"
  String get sign_term => "Assinar contrato";

  /// "Confirmo que li e aceito os termos"
  String get confirmo_que_li => "Confirmo que li e aceito os termos";

  /// "Aceitar"
  String get aceitar => "Aceitar";

  /// "Recusar"
  String get recusar => "Recusar";

  /// "Bem vindo!"
  String get bem_vindo => "Bem vindo!";

  /// "Seu cadastro foi aprovado,\nAgora você faz parte da Siclos!"
  String get cadastro_aprovado =>
      "Seu cadastro foi aprovado,\nAgora você faz parte da Siclos!";

  /// "Sair da conta"
  String get sair_conta => "Sair da conta";

  /// "Tem certeza que deseja sair?"
  String get certeza_sair => "Tem certeza que deseja sair?";

  /// "Política de privacidade"
  String get politica_privacidade => "Política de privacidade";

  /// "Recusar termo de compromisso?"
  String get recusar_termo_de_compromisso => "Recusar termo de compromisso?";

  /// "Caso não aceite os termos seu cadastro será finalizado e o usuário apagado."
  String get msg_recusar_termo_de_comprimisso =>
      "Caso não aceite os termos seu cadastro será finalizado e o usuário apagado.";

  /// "Sim, cancelar"
  String get sim_cancelar => "Sim, cancelar";

  /// "Deseja cancelar?"
  String get deseja_cancelar => "Deseja cancelar?";

  /// "Deseja voltar?"
  String get deseja_voltar => "Deseja voltar?";

  /// "Ao voltar desta etapa, seu empréstimo será cancelado."
  String get ao_voltar_cancelar =>
      "Ao voltar desta etapa, seu empréstimo será cancelado.";

  /// "A solicitação do seu emprestimo já foi iniciada. E está pendente de assinatura do termo."
  String get ao_voltar_terms =>
      "A solicitação do seu emprestimo já foi iniciada. E está pendente de assinatura do termo.";

  /// "Cancelar cadastro"
  String get cancelar_cadastro => "Cancelar cadastro";

  /// "Voltar"
  String get voltar => "Voltar";

  /// "Sua conta foi criada com sucesso, faça login para começar."
  String get cadastro_criado_login =>
      "Sua conta foi criada com sucesso, faça login para começar.";

  /// "Infelizmente seu cadastro não foi aprovado."
  String get cadastro_nao_aprovado =>
      "Infelizmente seu cadastro não foi aprovado.";

  /// "Cadastro já realizado"
  String get already_registered => "Cadastro já realizado";

  ///"Sua conta foi criada com sucesso. \nFaça login para começar"
  String get account_created_success_login =>
      "Sua conta foi criada com sucesso. \nFaça login para começar.";

  /// "Sentimos muito, mas sua documentação\nnão foi aprovada."
  String get cadastro_nao_aprovado_msg =>
      "Sentimos muito, mas sua documentação\nnão foi aprovada.";

  /// "Carteira"
  String get carteira => "Carteira";

  /// "Empréstimo"
  String get emprestimo => "Empréstimo";

  /// "Empréstimo Convencional"
  String get emprestimo_convencional => "Empréstimo Convencional";

  /// "Home"
  String get home => "Home";

  /// "Gestão"
  String get gestao => "Gestão";

  /// "Perfil"
  String get perfil => "Perfil";

  /// "Saldo em conta"
  String get saldo_disponivel => "Saldo disponível";

  /// "Saldo disponível + Crédito rápido"
  String get saldo_mais_credito_rapido => "Saldo disponível + Crédito";

  /// "Crédito rápido"
  String get credito_rapido => "Crédito rápido";

  ///Saiba mais!
  String get saiba_mais => "Saiba mais!";

  /// "Ao utilizar o Crédito Rápido, você concorda com os termos e condições e o respectivo desconto na folha de pagamento. A solicitação desse recurso será encaminhada para análise e assim que aprovada, o valor ficará disponível para uso em sua conta Siclos. Com valor pré-fixado e pagamento em uma parcela e você poderá solicitar novamente, todo mês, assim que o empréstimo anterior seja quitado. Caso você já tenha um empréstimo consignado ativo você não poderá usar esta modalidade.";
  String get msg_info_credito_rapido =>
      "Ao utilizar o Crédito Rápido, você concorda com os termos e condições e o respectivo desconto na folha de pagamento. A solicitação desse recurso será encaminhada para análise e assim que aprovada, o valor ficará disponível para uso em sua conta Siclos.";

  /// "Crédito indisponível ou insuficiente devido à outra linha de crédito já contratada ou não atente a política de crédito vigente. "
  String get msg_info_credito_rapido_indisponivel =>
      "Crédito indisponível ou insuficiente devido à outra linha de crédito já contratada ou não atente a política de crédito vigente.";

  /// "Ver extrato"
  String get ver_extrato => "Ver extrato";

  /// "Finanças e gestão"
  String get financas_gestao => "Finanças e gestão";

  /// "Pensando em você"
  String get pensando_voce => "Pensando em você";

  /// "Empréstimos"
  String get emprestimos => "Empréstimos";

  /// "Transferências"
  String get transferencias => "Transferências";

  /// "Pagamento"
  String get pagamento => "Pagamento";

  /// "Recarga"
  String get recarga => "Recarga";

  /// "Depósito"
  String get deposito => "Depósito";

  /// "Extrato"
  String get extrato => "Extrato";

  /// "Crédito\nconsignado\nSiclos"
  String get credito_consignado => "Crédito\nconsignado\nSiclos";

  /// "Veja mais"
  String get veja_mais => "Veja mais";

  /// "Dicas"
  String get dicas => "Dicas";

  /// "Ajustes"
  String get ajustes => "Ajustes";

  /// "Tarifas"
  String get tarifas => "Tarifas";

  /// "Dispositivos"
  String get dispositivos => "Dispositivos";

  /// "Login com digital"
  String get login_digital => "Login com digital";

  /// "Termos de uso da plataforma"
  String get termos_uso => "Termos de uso da plataforma";

  /// "Ajuda"
  String get ajuda => "Ajuda";

  /// "Alterar e-mail"
  String get alterar_email => "Alterar e-mail";

  /// "Alterar chave"
  String get alterar_chave => "Alterar chave";

  /// "Alterar nome exibido"
  String get alterar_display_name => "Alterar nome exibido";

  /// "Alterar senha"
  String get alterar_senha => "Alterar senha";

  /// "Alterar PIN"
  String get altear_pin => "Alterar PIN";

  /// "Cadastrar PIN"
  String get cadastrar_pin => "Cadastrar PIN";

  /// "Alterar endereço"
  String get alterar_endereco => "Alterar endereço";

  /// "Tire suas dúvidas aqui, entre em contato com a gente por email ou chat"
  String get ajuda_msg =>
      "Tire suas dúvidas aqui, entre em contato com a gente por email ou chat";

  /// "Dúvidas frequentes"
  String get duvidas_frequentes => "Dúvidas frequentes";

  /// "Qual é a sua dúvida?"
  String get what_your_question => "Qual é a sua dúvida?";

  /// "Ultimas transações"
  String get last_transactions => "Últimas transações";

  /// "Cadastrar PIN"
  String get cadastro_pin => "Cadastrar PIN";

  String get esqueci_meu_pin => "Esqueci meu PIN";

  /// "O PIN é um código pessoal para realizar transações de forma segura. Evite datas de nascimento e números de telefone."
  String get cadastro_pin_msg =>
      "O PIN é um código pessoal para realizar transações de forma segura. Evite datas de nascimento e números de telefone.";

  /// "Crie um PIN de 4 números"
  String get digite_pin_4 => "Crie um PIN de 4 números";

  /// "Digite novamente para confirmar"
  String get digite_novamente_pin => "Digite novamente para confirmar";

  /// "As sequências digitadas são diferentes, digite novamente o PIN para cadastro."
  String get pin_nao_confere =>
      "As sequências digitadas são diferentes, digite novamente o PIN para cadastro.";

  /// "Digite a "
  String get nova_senha_cartao_1 => "Digite a ";

  /// "nova "
  String get nova => "nova ";

  /// "senha do cartão"
  String get nova_senha_cartao_2 => "senha do cartão";

  /// "Digite a senha do cartão"
  String get digite_senha_cartao => "Digite a senha do cartão";

  /// "Código de 4 dígitos usado para pagamentos no cartão."
  String get digite_senha_cartao_msg =>
      "Código de 4 dígitos usado para pagamentos no cartão.";

  /// "PIN"
  String get pin => "PIN";

  /// "O PIN é a sua senha de 4 dígitos cadastrada anteriormente."
  String get digite_seu_pin_text =>
      "PIN é uma senha de 4 digitos que você definiu anteriomente.";

  /// "Após ${qtdeTentativas} tentativas sua conta será bloqueada"
  String tentativasPinBloqueio(String qtdeTentativas) =>
      "PIN incorreto, após ${qtdeTentativas} tentativas sua conta será bloqueada";

  /// "Informe o e-mail que deseja utilizar."
  String get digite_email_alterar => "Informe o e-mail que deseja utilizar.";

  /// "Informe o nome que deseja ser exibido."
  String get digite_nome_exibido_alterar =>
      "Informe o nome que deseja ser exibido.";

  /// "Confirme sua senha"
  String get confirme_sua_senha => "Confirme sua senha";

  /// "Para confirmar sua alteração de e-mail para ${email}\n\nLembre-se que sua senha contém no mínimo 6 caracteres, e com pelo menos uma letra maiúscula, um número e um caractere especial"
  String confirmarAlterarParaEmail(String email) =>
      "Para confirmar sua alteração de e-mail para ${email}\n\nLembre-se que sua senha contém no mínimo 6 caracteres, e com pelo menos uma letra maiúscula, um número e um caractere especial";

  /// "E-mail alterado com sucesso."
  String get email_alterado_com_sucesso => "E-mail alterado com sucesso.";

  /// "Nome de exibição alterado com sucesso."
  String get nome_exibicao_alterado_com_sucesso =>
      "Nome de exibição alterado com sucesso.";

  /// "Alterar PIN"
  String get alterar_pin => "Alterar PIN";

  /// "PIN é uma senha de 4 digitos que você definiu anteriomente. Digitie o PIN atual."
  String get alterar_pin_msg =>
      "PIN é uma senha de 4 digitos que você definiu anteriomente. Digitie o PIN atual.";

  /// "PIN Atual"
  String get digite_pin_atual => "PIN Atual";

  /// "Novo PIN"
  String get digite_novo_pin => "Novo PIN";

  /// "Insira o novo PIN de 4 dígitos que deseja usar:",
  String get digite_novo_pin_msg =>
      'Insira o novo PIN de 4 dígitos que deseja usar:';

  /// "Sua conta foi bloqueada"
  String get conta_bloqueada_title => "Sua conta foi bloqueada";

  /// "Por segurança, bloqueamos a sua conta temporariamente."
  String get conta_bloqueada_msg =>
      "Por segurança, bloqueamos a sua conta temporariamente.";

  /// "Utilize um de nossos canais para solicitar a autorização do aparelho:"
  String get conta_bloqueada_msg_2 =>
      "Utilize um de nossos canais para solicitar a autorização do aparelho:";

  /// "Voltar para o login"
  String get back_login_screen => "Voltar para o login";

  /// "Ops! Sistema indisponível no momento."
  String get ops_sistema_fora_title => "Ops! Sistema indisponível no momento.";

  /// "Estamos preparando melhorias para você. Por favor, tente novamente mais tarde."
  String get ops_sistema_fora_msg_1 =>
      "Estamos preparando melhorias para você. Por favor, tente novamente mais tarde.";

  /// "Se necessário, utilize um de nossos canais para solicitar ajuda:"
  String get ops_sistema_fora_msg_2 =>
      "Se necessário, utilize um de nossos canais para solicitar ajuda:";

  /// "Pin alterado com sucesso."
  String get pin_alterado_sucesso => "Pin alterado com sucesso.";

  /// "Agência:"
  String get agencia_ => "Agência:";

  /// "Conta Corrente:"
  String get conta_corrente_ => "Conta Corrente:";

  /// "Banco:"
  String get banco_ => "Banco:";

  /// "Usuário:"
  String get usuario_ => "Usuário:";

  /// "Telefone:"
  String get telefone_ => "Telefone:";

  /// "CPF:"
  String get cpf_ => "CPF:";

  /// "Valores cobrados pelos nossos serviços"
  String get valores_cobrados_servicos =>
      "Valores cobrados pelos nossos serviços";

  /// "Anuidade"
  String get anuidade => "Anuidade";

  /// "Pacote de serviços de Conta Corrente e Carteira Digital"
  String get anuidade_text =>
      "Pacote de serviços de Conta Corrente e Carteira Digital";

  /// "TED entre contas Siclos By EmCash"
  String get ted_entre_contas_siclos => "TED entre contas Siclos By EmCash";

  /// "TED para outros bancos"
  String get ted_outros_bancos => "TED para outros bancos";

  /// "*Isenção em ${qtde} envios mensais"
  String tedOutrosBancosTaxa(String qtde) =>
      "*Isenção em ${qtde} envios mensais";

  /// "/envio"
  String get por_envio => "/envio";

  /// "Emissão de boleto"
  String get emissao_boleto => "Emissão de boleto";

  /// "*Isenção em ${qtde} depósitos mensais"
  String emissaoBoletoTaxa(String qtde) =>
      "*Isenção em ${qtde} depósitos mensais";

  /// "Compensação de boleto"
  String get compensacao_boleto => "Compensação de boleto";

  /// "*Isenção em ${qtde} depósitos mensais"
  String compensacaoBoletoTaxa(String qtde) =>
      "*Isenção em ${qtde} depósitos mensais";

  /// "/emissão"
  String get por_emissao => "/emissão";

  /// "Confime sua senha atual e depois digite e repita sua nova senha."
  String get confirme_sua_senha_atual =>
      "Confime sua senha atual e depois digite e repita sua nova senha.";

  /// "Digite sua senha para confirmar a alteração de e-mail para."
  String get digite_sua_senha_confirmar_alteracao_email =>
      "Digite sua senha para confirmar a alteração de e-mail para ";

  /// "Senha atual"
  String get senha_atual => "Senha atual";

  /// "Nova senha"
  String get nova_senha => "Nova senha";

  /// "Confirme nova senha"
  String get confirme_nova_senha => "Confirme nova senha";

  /// "Senha alterada com sucesso."
  String get senha_alterada_sucesso => "Senha alterada com sucesso.";

  /// "Sua senha foi redefinida com sucesso!"
  String get senha_redefinida_sucesso =>
      "Sua senha foi redefinida com sucesso!";

  /// "Concedido"
  String get concedido => "Concedido";

  /// "Expirado"
  String get expirado => "Expirado";

  /// "Pedido não autorizado"
  String get pedido_nao_autorizado => "Pedido não autorizado";

  /// "Empréstimo concedido"
  String get emprestimo_concedido_dialog => "Empréstimo concedido";

  /// "Parabéns! O empréstimo já disponível para saque"
  String get emprestimo_concedido_dialog_msg =>
      "Parabéns! O empréstimo já disponível para saque";

  /// "O empréstimo expirou"
  String get emprestimo_expirado => "O empréstimo expirou";

  /// "Infelizmente não foi possível encontrar um investidor a tempo para efetivarmos o empréstimo. Você pode tentar um novo empréstimo sempre que quiser."
  String get emprestimo_expirado_msg =>
      "Infelizmente não foi possível encontrar um investidor a tempo para efetivarmos o empréstimo. Você pode tentar um novo empréstimo sempre que quiser.";

  /// "Não foi possível prosseguir"
  String get nao_foi_possivel_prosseguir => "Não foi possível prosseguir";

  /// "Lamentamos por isso, mas seu empréstimo não foi autorizado."
  String get nao_foi_possivel_prosseguir_msg =>
      "Lamentamos por isso, mas seu empréstimo não foi autorizado.";

  /// "Minha solicitação"
  String get minha_solicitacao => "Minha solicitação";

  /// "Em análise"
  String get em_analise => "Em análise";

  /// "Contratado"
  String get contratado => "Contratado";

  /// "Valor da solicitação"
  String get valor_solicitado => "Valor da solicitação";

  /// "Valor mensal"
  String get valor_mensal => "Valor mensal";

  /// "Nova proposta"
  String get nova_proposta => "Nova proposta";

  /// "Proposta pré aprovada"
  String get proposta_pre_aprovada => "Proposta pré aprovada";

  /// "Aguardando investidor"
  String get aguardando_investidor => "Aguardando investidor";

  /// "Aguardando fundo"
  String get aguardando_fundo => "Aguardando fundo";

  /// "Falta pouco. Agora é só aguardar, em breve o valor estará disponível na sua conta."
  String get aguardando_fundo_msg =>
      "Falta pouco. Agora é só aguardar, em breve o valor estará disponível na sua conta.";

  /// "Estamos analisando o documento enviado."
  String get em_analise_msg => "Estamos analisando o documento enviado.";

  /// "Analisamos o seu perfil e temos uma  nova proposta para você."
  String get nova_proposta_msg =>
      "Analisamos o seu perfil e temos uma  nova proposta para você.";

  /// "Analisamos a sua documentação e temos uma  proposta pré-aprovada."
  String get proposta_pre_aprovada_msg =>
      "Analisamos a sua documentação e temos uma  proposta pré-aprovada.";

  /// "É somente aguardar que sua proposta seja aceita por um investidor."
  String get aguardando_investidor_msg =>
      "É somente aguardar que sua proposta seja aceita por um investidor.";

  /// "Valor de parcelas"
  String get valor_de_parcelas => "Valor de parcelas";

  /// "${taxa}% a.m"
  String taxaJuros(String taxa) => "${taxa}% a.m";

  /// "Primeiro vencimento"
  String get primeiro_vencimento => "Primeiro vencimento";

  /// "Detalhe de empréstimo"
  String get detalhe_emprestimo => "Detalhe de empréstimo";

  /// "Taxa de juros"
  String get taxas_de_juros => "Taxa de juros";

  /// "Tipo de empréstimo"
  String get tipo_emprestimo => "Tipo de empréstimo";

  /// "Desconto de rescisão"
  String get desconto_recisao => "Desconto de rescisão";

  /// "Pago com andiantamento"
  String get pago_com_adiantamento => "Pago com andiantamento";

  /// "A parcela foi descontada por antecipação. Isso pode ocorrer devido a um período de férias ou por um afastamento de trabalho.\nEm casa de dúvidas, consulte o RH de sua empresa."
  String get pago_com_adiantamento_msg =>
      "A parcela foi descontada por antecipação. Isso pode ocorrer devido a um período de férias ou por um afastamento de trabalho.\nEm casa de dúvidas, consulte o RH de sua empresa.";

  /// "Número de contrato: ${numero}"
  String numeroDeContrato(String numero) => "Número de contrato: ${numero}";

  /// "Pago"
  String get pago => "Pago";

  /// "Atrasado"
  String get atrasado => "Atrasado";

  /// "À  vencer"
  String get a_vencer => "À  vencer";

  /// "Pendente"
  String get pendente => "Pendente";

  /// "Finalizados"
  String get finalizados => "Finalizados";

  /// "Temos uma contra proposta"
  String get temos_contra_proposta => "Temos uma contra proposta";

  /// "Ver nova proposta"
  String get ver_nova_proposta => "Ver nova proposta";

  /// "Tem certeza?"
  String get tem_certeza => "Tem certeza?";

  /// "Ao cancelar seu pedido toda sua documentação será desconsiderada. Para realizar uma nova solicitação você deverá repetir o processo."
  String get msg_cancelar_emprestimo =>
      "Ao cancelar seu pedido toda sua documentação será desconsiderada. Para realizar uma nova solicitação você deverá repetir o processo.";

  /// "Cancelar pedido"
  String get cancelar_pedido => "Cancelar pedido";

  /// "Pedido expirado"
  String get pedido_expirado => "Pedido expirado";

  /// "Documentos pendentes"
  String get documentos_pendentes => "Documentos pendentes";

  /// "Adicionar documentos"
  String get adicionar_documentos => "Adicionar documentos";

  /// "Temos uma nova proposta para você."
  String get temos_nova_proposta => "Temos uma nova proposta para você.";

  /// "Após a análise, percebemos que ainda falta o seguinte documento:"
  String get msg_documentos_pendentes =>
      "Após a análise, percebemos que ainda falta o seguinte documento:";

  /// "Último contra-cheque"
  String get ultimo_contra_cheque => "Último contra-cheque";

  /// "Penúltimo contra-cheque"
  String get penunltimo_contra_cheque => "Penúltimo contra-cheque";

  /// "Antepenúltimo contra-cheque"
  String get antipenultimo_contra_cheque => "Antepenúltimo contra-cheque";

  /// "Enviar para análise"
  String get enviar_analise => "Enviar para análise";

  /// "Falta documentação"
  String get falta_documentacao => "Falta documentação";

  /// "Ver proposta"
  String get ver_proposta => "Ver proposta";

  /// "Boleto bancário"
  String get boleto_bancaria => "Boleto bancário";

  /// "Como deseja pagar?"
  String get como_deseja_fazer_pagamento => "Como deseja pagar?";

  /// "Saldo em conta"
  String get saldo_em_conta => "Saldo em conta";

  /// "Saldo Insuficiente"
  String get saldo_insuficiente_text => "Saldo Insuficiente";

  /// "Você não possui saldo suficiente para adiantar este valor. Selecione parcelas suficientes para pagar com o saldo disponível."
  String get saldo_insuficinente_adiantar_parcela =>
      "Você não possui saldo suficiente para adiantar este valor. Selecione parcelas suficientes para pagar com o saldo disponível.";

  /// "Saldo atual"
  String get saldo_atual => "Saldo atual";

  /// "Pagar parcela"
  String get pagar_parcela => "Pagar parcela";

  /// "Pagamento de parcela"
  String get pagamento_parcela => "Pagamento de parcela";

  /// "Deseja continuar e realizar o pagamento da parcela ${parcelas} do contrato nº ${numContrato}"
  String pagamentoParcelaMsg(String parcelas, String numContrato) =>
      "Deseja continuar e realizar o pagamento da parcela ${parcelas} do contrato nº ${numContrato}";

  /// "Prosseguir"
  String get prosseguir => "Prosseguir";

  /// "Detalhe de parcela"
  String get detalhe_parcela => "Detalhe de parcela";

  /// "Ir para minha conta"
  String get ir_para_minha_conta => "Ir para minha conta";

  /// "É necessário transferir o valor para a sua conta antes de efetivar a solicitação"
  String get e_necessario_transferir_valor =>
      "É necessário transferir o valor para a sua conta antes de efetivar a solicitação";

  /// "Valores"
  String get valores => "Valores";

  /// "Multa + mora"
  String get multa_mora => "Multa + mora";

  /// "Pagamento rescisão"
  String get pagamento_rescisao => "Pagamento rescisão";

  /// "Desconto adiantamento"
  String get desconto_adiantamento => "Desconto adiantamento";

  /// "Total parcela"
  String get total_parcela => "Total parcela";

  /// "Parcela"
  String get parcela => "Parcela";

  /// "Pago Adiantado"
  String get pago_adiantado => "Pago Adiantado";

  /// "Processando"
  String get processado => "Processando";

  /// "Vencido"
  String get vencido => "Vencido";

  /// "Pago com Atraso"
  String get pago_com_atraso => "Pago com Atraso";

  /// "Parcela ${numParcela}/${qtdeTotal}"
  String contagemParcelas(String numParcela, String qtdeTotal) =>
      "Parcela ${numParcela}/${qtdeTotal}";

  /// "PARCELA ${numParcela}/${qtdeTotal}"
  String contagemParcelasInvetimento(String numParcela, String qtdeTotal) =>
      "PARCELA ${numParcela}/${qtdeTotal}";

  /// "A parcela foi paga de forma antecipada e pode ter alterações no valor recebido. Isso pode acontecer por adiantamento feito pelo tomador ou adversidades em seu vínculo empregatício."
  String get msg_parcela_paga_antecipada =>
      "A parcela foi paga de forma antecipada e pode ter alterações no valor recebido. Isso pode acontecer por adiantamento feito pelo tomador ou adversidades em seu vínculo empregatício.";

  /// "Vencimento"
  String get vencimento => "Vencimento";

  /// "Fatura NF"
  String get fatura_nf => "Fatura NF";

  /// "Seu boleto foi gerado!"
  String get boleto_depositado => "Seu boleto foi gerado!";

  /// "Uma cópia foi enviada para ${email}"
  String umaCopiaEmail(String email) => "Uma cópia foi enviada para ${email}";

  /// "Boleto_${id}.pdf"
  String nomeArquivoBoleto(String id) => "Boleto_${id}.pdf";

  /// "Pagar"
  String get pagar => "Pagar";

  /// "Devolver"
  String get devolver => "Devolver";

  /// "Devolução de Pix"
  String get devolucao_pix => "Devolução de Pix";

  /// "Solicitar novo empréstimo"
  String get solicitar_novo_emprestimo => "Solicitar novo empréstimo";

  /// "Pagamento de parcela com saldo"
  String get pagamento_parcela_saldo => "Pagamento de parcela com saldo";

  /// "Pagamento parcela ${numeroParcela}/${totalParcela}"
  String pagamentoParcela(String numeroParcela, String totalParcela) =>
      "Pagamento parcela ${numeroParcela}/${totalParcela}";

  /// "Pago por"
  String get pago_por => "Pago por";

  /// "em ${data} às ${horas}"
  String dataTransferencia(String data, String horas) =>
      "em ${data} às ${horas}";

  /// "Para"
  String get para => "Para";

  /// "Devolução para"
  String get devolucao_para => "Devolução para";

  /// "Devolvido por"
  String get devolucao_por => "Devolvido por";

  /// "Valor total"
  String get valor_total => "Valor total";

  /// "Nº contrato"
  String get numero_contrato => "Nº contrato";

  /// "Autenticação"
  String get autenticacao => "Autenticação";

  /// "Autenticação da transação"
  String get autenticacaoDaTransacao => "Autenticação da transação";

  /// "Boleto"
  String get boleto => "Boleto";

  /// "Área Pix"
  String get area_pix => "Área Pix";

  /// "Pix"
  String get pix => "Pix";

  /// "Pix com chave"
  String get pix_with_key => "Pix com chave";

  /// "Pix Copia e Cola"
  String get pix_copy_paste => "Pix Copia e Cola";

  /// "Ler QR code"
  String get read_qr_code => "Ler QR code";

  /// "Criar QR code"
  String get create_qr_code => "Criar QR Code";

  /// "Cobrar com Pix"
  String get charge_with_pix => "Cobrar com Pix";

  /// "Minhas chaves"
  String get my_keys => "Minhas chaves";

  /// "n de 5 chaves"
  String n_de_5_chaves(int n) => "$n de 5 chaves";

  /// "Nova chave"
  String get new_key => "Nova chave";

  /// "Cadastrar nova chave"
  String get register_new_key => "Cadastrar nova chave";

  /// "Compartilhar chave"
  String get share_key => "Compartilhar chave";

  /// "Compartilhar QR Code"
  String get share_qrcode => "Compartilhar QR Code";

  /// "Caso você não informe o valor, ele será definido por quem for pagar o QR Code."
  String get if_not_inform_value_pix_qrcode_message =>
      "Caso você não informe o valor, ele será definido por quem for pagar o QR Code.";

  /// "Compartilhar PIX copia e cola"
  String get share_copy_past => "Compartilhar PIX copia e cola";

  /// "Excluir chave"
  String get delete_key => "Excluir chave";

  /// "Você quer mesmo excluir essa chave?"
  String get confirm_delete_key => "Você quer mesmo excluir essa chave?";

  /// "Chave apagada com sucesso!"
  String get success_delete_key => "Chave apagada com sucesso!";

  /// "$claim cancelada com sucesso!"
  String success_cancel_claim(String claim) => "$claim cancelada com sucesso!";

  /// "$claim confirmada com sucesso!"
  String success_confirm_claim(String claim) =>
      "$claim confirmada com sucesso!";

  /// "Você não possui chaves cadastradas."
  String get no_keys_found => "Você não possui chaves cadastradas.";

  /// "Crie uma chave pix para receber transferências pix facilmente."
  String get register_new_key_pix_message =>
      "Crie uma chave pix para receber transferências pix facilmente.";

  /// "Cadastre sua chave pix"
  String get register_new_key_pix => "Cadastre sua chave pix";

  ///'Quem usa Pix pode saber que você tem uma chave cadastrada por telefone ou e-mail, mas sem ter acesso aos seus dados. Ao te pagar, a pessoa verá seu nome completo e alguns dígitos do seu CPF.'
  String get register_new_key_alert_txt =>
      'Quem usa Pix pode saber que você tem uma chave cadastrada por telefone ou e-mail, mas sem ter acesso aos seus dados. Ao te pagar, a pessoa verá seu nome completo e alguns dígitos do seu CPF.';

  /// "Chave registrada!"
  String get registered_key => "Chave registrada!";

  /// "Pronto! Você já pode receber Pix de um jeito muito fácil, basta informar sua chave."
  String get registered_key_message =>
      "Pronto! Você já pode receber Pix de um jeito muito fácil, basta informar sua chave.";

  /// "Não foi possível cadastrar a chave!"
  String get error_registered_key => "Não foi possível cadastrar a chave!";

  /// "Não foi possível finalizar a operação pois tivemos um problema técnico. Tente novamente em alguns minutos."
  String get error_registered_key_message =>
      "Não foi possível finalizar a operação pois tivemos um problema técnico. Tente novamente em alguns minutos.";

  /// Esse e-mail já foi registrado
  String error_key_already_registered_title(String keyType) =>
      'Esse $keyType já foi registrado';

  /// Tente registrar outro $keyType como chave Pix.
  String error_key_already_registered_message(String keyType) =>
      'Tente registrar outro $keyType como chave Pix.';

  /// Confirme a mudança
  String get confirm_change => 'Confirme a mudança';

  /// 'Esse $keyType já foi adicionada por você como chave em outra conta. Para usá-la no SiclosBank iremos iniciar uma portabilidade'
  String error_key_used_other_account_message(String keyType) =>
      'Esse $keyType já foi adicionada por você como chave em outra conta. Para usá-la no SiclosBank iremos iniciar uma portabilidade';

  /// Iniciar portabilidade
  String get start_portability => 'Iniciar portabilidade';

  /// Portabilidade iniciada
  String get started_portability => 'Portabilidade iniciada';

  /// "Acesse sua conta BCO DE ORIGEM DA CHAVE e confirme o pedido de portabilidade da chave até dd/mm/aaaa"
  String started_portability_message({
    required String originBank,
    required String date,
  }) =>
      "Acesse sua conta $originBank e confirme o pedido de portabilidade da chave até $date";

  /// 'Iniciar reivindicação'
  String get start_claim => 'Iniciar reivindicação';

  /// Reivindicação iniciada
  String get started_claim => 'Reivindicação iniciada';

  /// 'Esse $keyType já foi registrada por outra pessoa. Para usá-la na Siclos, você precisará solicitar a reivindicação.'
  String error_key_used_other_user_message(String keyType) =>
      'Esse $keyType já foi registrada por outra pessoa. Para usá-la na Siclos, você precisará solicitar a reivindicação.';

  ///"Acesse sua conta BCO DE ORIGEM DA CHAVE e confirme o pedido de portabilidade da chave até dd/mm/aaaa"
  String get started_claim_message =>
      "Agora, para usar essa chave como chave na Siclos você deve esperar até 7 dias para a reivindicação ser confirmada.";

  ///"Você pediu para tirar essa chave da Siclos e levar para outra conta. Confirme esse pedido até dd/mm/aaaa."
  String requested_portability_message(String date) =>
      "Você pediu para tirar essa chave da Siclos e levar para outra conta. Confirme esse pedido até $date.";

  ///"Para continuar usando a sua chave, confirme que quer mantê-la até 10/06/2025. Se não, basta liberar a chave para a outra pessoa"
  String requested_claim_message(String date) =>
      "Para continuar usando a sua chave, confirme que quer mantê-la até $date. Se não, basta liberar a chave para a outra pessoa";

  /// "Você quer usar essa chave em outra conta?"
  String get requested_portability_title =>
      "Você quer usar essa chave em outra conta?";

  /// "Atenção: Outra pessoa quer registrar essa chave"
  String get requested_claim_title =>
      "Atenção: Outra pessoa quer registrar essa chave";

  /// "Chave em reivindicação"
  String get request_claimer_title => "Chave em reivindicação";

  ///"Agora precisamos esperar a liberação da chave até o dia 10/06/2025. Vamos te avisar quando o processo estiver finalizado."
  String request_claimer_message(String date) =>
      "Agora precisamos esperar a liberação da chave até o dia $date. Vamos te avisar quando o processo estiver finalizado.";

  /// "Manter chave na Siclos"
  String get keep_key_here => "Manter chave na Siclos";

  /// "Usar chave em outra conta"
  String get confirm_requested_portability => "Usar chave em outra conta";

  /// "Liberar chave para outra pessoa"
  String get confirm_requested_claim => "Liberar chave para outra pessoa";

  /// "Cancelar reivindicação"
  String get cancel_claim => "Cancelar reivindicação";

  /// "Cancelar portabilidade"
  String get cancel_portability => "Cancelar portabilidade";

  /// "Meus limites Pix"
  String get my_limits_pix => "Meus limites Pix";

  /// "Dúvidas"
  String get questions => "Dúvidas";

  /// "Realize seus serviços com o Pix!"
  String get title_onboarding_pix_1 => "Realize seus serviços com o Pix!";

  /// "Simples e rápido!"
  String get title_onboarding_pix_2 => "Simples e rápido!";

  /// "Chave PIX e comprovante."
  String get title_onboarding_pix_3 => "Chave PIX e comprovante.";

  /// "Com o Pagamento Instantâneo - PIX, você realiza pagamentos e recebimentos dos seus serviços por meio de transferências digitais e QR Code. \nMais facilidade e segurança  para o seu negócio!"
  String get description_onboarding_pix_1 =>
      "Com o Pagamento Instantâneo - PIX, você realiza pagamentos e recebimentos dos seus serviços por meio de transferências digitais e QR Code. \n\nMais facilidade e segurança  para o seu negócio!";

  /// "Nos pagamentos, você pode escolher entre QR Code, “Pix Copia e Cola” ou usar a Chave PIX. Para recebimentos, você pode criar o seu QRCode e realizar transferências ou usar para recarregar seu saldo no aplicativo."
  String get description_onboarding_pix_2 =>
      "Nos pagamentos, você pode escolher entre QR Code, “Pix Copia e Cola” ou usar a Chave PIX. Para recebimentos, você pode criar o seu QRCode e realizar transferências ou usar para recarregar seu saldo no aplicativo.";

  /// "A Chave Pix são dados como CPF/CNPJ, e-mail, celular ou uma chave aleatória, que servem para comprovar sua conta.\nTodos os serviços realizados com o Pix também possuem comprovantes, que você pode enviar ou imprimir para seu cliente."
  String get description_onboarding_pix_3 =>
      "A Chave Pix são dados como CPF/CNPJ, e-mail, celular ou uma chave aleatória, que servem para comprovar sua conta.\nTodos os serviços realizados com o Pix também possuem comprovantes, que você pode enviar ou imprimir para seu cliente.";

  /// "Comece a receber usando o Pix!"
  String get title_onboarding_keys_1 => "Comece a receber usando o Pix!";

  /// "Cadastre as suas chaves."
  String get title_onboarding_keys_2 => "Cadastre as suas chaves.";

  /// "Descomplique seus recebimentos."
  String get title_onboarding_keys_3 => "Descomplique seus recebimentos.";

  /// "O registro da chave é necessário para receber um Pix de forma prática.\n\nCom ela, não é necessário digitar inúmeros dados para identificar a conta, basta informar a chave registrada que pode ser número de celular, CPF ou e-mail."
  String get description_onboarding_keys_1 =>
      "O registro da chave é necessário para receber um Pix de forma prática.\n\nCom ela, não é necessário digitar inúmeros dados para identificar a conta, basta informar a chave registrada que pode ser número de celular, CPF ou e-mail.";

  /// "Cada chave poderá ser vinculada apenas a uma única conta, sendo possível realizar o cadastro de até 5 chaves."
  String get description_onboarding_keys_2 =>
      "Cada chave poderá ser vinculada apenas a uma única conta, sendo possível realizar o cadastro de até 5 chaves.";

  /// "Com a chave cadastrada, é só escolher a de sua preferência e encaminhar para quem você desejar para receber seu dinheiro de maneira simples, rápida e instantânea!"
  String get description_onboarding_keys_3 =>
      "Com a chave cadastrada, é só escolher a de sua preferência e encaminhar para quem você desejar para receber seu dinheiro de maneira simples, rápida e instantânea!";

  /// "Quanto você deseja devolver?"
  String get quanto_deseja_devolver => "Quanto você deseja devolver?";

  /// "Você pode devolver ate"
  String get voce_pode_devolver_ate => "Você pode devolver ate ";

  /// "Boleto de pagamento de parcela"
  String get boleto_pagamento_parcela => "Boleto de pagamento de parcela";

  /// "Código da transação"
  String get codigo_da_transacao => "Código da transação";

  /// "ID/Transação"
  String get id_transacao => "ID/Transação";

  /// "ID/Transação original"
  String get id_transacao_originial => "ID/Transação original";

  /// "End-to-End"
  String get endToEnd => "End-to-End";

  /// "End-to-End/Transação original"
  String get endToEnd_transacao_originial => "End-to-End/Transação original";

  /// "Salvar boleto em PDF"
  String get salvar_boleto_pdf => "Salvar boleto em PDF";

  /// "Conheça o empréstimo consignado com a praticidade e melhores taxas do mercado."
  String get conheca_emprestimo_consignado =>
      "Conheça o empréstimo consignado com a praticidade e melhores taxas do mercado.";

  /// "Você tem o limite pré-aprovado de acordo com o seu salário e tempo de serviço na empresa e em alguns dias fica disponível na sua conta. O valor é descontado todo mês na folha e você somente poderá pedir novamente, quando terminar de pagar."
  String get como_funciona_emprestimo_consignado =>
      "Você tem o limite pré-aprovado de acordo com o seu salário e tempo de serviço na empresa e em alguns dias fica disponível na sua conta. O valor é descontado todo mês na folha e você somente poderá pedir novamente, quando terminar de pagar.";

  /// "Simular"
  String get simular => "Simular";

  /// "No caso de desligamento da empresa de acordo com a Lei 10.820/2003 é autorizado reter até 30% do valor da rescisão para pagamento do empréstimo consignado."
  String get text_pagamento_rescisao_part_1 =>
      "No caso de desligamento da empresa de acordo com a Lei 10.820/2003 é autorizado reter até 30% do valor da rescisão para pagamento do empréstimo consignado.";

  /// "O valor retido é abatido do slado devedor antecipado e o empréstimo é refinanciado."
  String get text_pagamento_rescisao_part_2 =>
      "O valor retido é abatido do slado devedor antecipado e o empréstimo é refinanciado.";

  /// "Os empréstimos estarão disponíveis assim\nque você completar 6 meses de trabalho\nna empresa."
  String get usuario_6_meses_empresa =>
      "Os empréstimos estarão disponíveis assim\nque você completar 6 meses de trabalho\nna empresa.";

  /// "Os empréstimos estarão disponíveis assim\nque você completar 3 meses de trabalho\nna empresa."
  String get usuario_3_meses_empresa =>
      "Os empréstimos estarão disponíveis assim\nque você completar 3 meses de trabalho\nna empresa.";

  /// "Estamos aqui para ajudar! Entre em contato conosco através dos canais de atendimento abaixo."

  String get msg_ajuda =>
      "Estamos aqui para ajudar! Entre em contato conosco através dos canais de atendimento abaixo.";

  /// "Telefone (seg/sex: 8h às 18h30 e sáb: 8h às 14h)"
  String get segunda_a_terca =>
      "Telefone (seg/sex: 8h às 18h30 e sáb: 8h às 14h)";

  /// "Ops! Você precisa autorizar este dispostivo."
  String get voce_precisa_autorizar_dispositivo =>
      "Ops! Você precisa autorizar este dispostivo.";

  /// "Este dispostivo não está vinculado a esta conta. Isso pode ocorrer se você trocou de aparelho, por exemplo."
  String get voce_precisa_autorizar_dispositivo_msg =>
      "Este dispostivo não está vinculado a esta conta. Isso pode ocorrer se você trocou de aparelho, por exemplo.";

  /// "Utilize um de nossos canais para solicitar a autorização do aparelho:"
  String get utilize_nossos_canais =>
      "Utilize um de nossos canais para solicitar a autorização do aparelho:";

  /// "Parece que você não tem o aplicativo Email instalado."
  String get email_nao_instalado =>
      "Parece que você não tem o aplicativo Email instalado.";

  /// "O valor mínimo é ${valor}."
  String errorValorMinimoEmprestimo(String valor) =>
      "O valor mínimo é ${valor}.";

  /// "O valor máximo é ${valor}."
  String errorValorMaximoEmprestimo(String valor) =>
      "O valor máximo é ${valor}.";

  /// "Data de validade vazia"
  String get data_vencimento_vazia => "Data de validade vazia";

  /// "Faça a leitura do código através da câmera ou digite os números."
  String get faca_leitura_cod_barras =>
      "Faça a leitura do código através da câmera ou digite os números.";

  /// "Pagar boleto"
  String get pagar_boleto => "Pagar boleto";

  /// "Pocisione o código de barras no centro"
  String get posicione_celular => "Posicione o código de barras no centro";

  /// "Digitar código de barras"
  String get digitar_cod_barras => "Digitar código de barras";

  /// "Boleto não identificado"
  String get boleto_nao_identificado => "Boleto não identificado";

  /// "Tentar novamente"
  String get tentar_novamente => "Tentar novamente";

  /// "Valor do boleto"
  String get valor_boleto => "Valor do boleto";

  /// "Pagando hoje"
  String get pagando_hoje => "Pagando hoje";

  /// "Data de vencimento"
  String get data_vencimento => "Data de vencimento";

  /// "Código do Boleto"
  String get cod_boleto => "Código do Boleto";

  /// "Multa/Mora"
  String get multa => "Multa/Mora";

  /// "Juros"
  String get juros => "Juros";

  /// "Descontos"
  String get descontos => "Descontos";

  /// "Mora"
  String get mora => "Mora";

  /// "Data de vencimento não pode ser menor do que data de hoje."
  String get data_vencimento_menor_hoje =>
      "Data de vencimento não pode ser menor do que data de hoje.";

  /// "Valor do documento"
  String get valor_documento => "Valor do documento";

  /// "Valor de multa pago"
  String get valor_multa_pago => "Valor de multa pago";

  /// "Valor de desconto"
  String get valor_desconto => "Valor de desconto";

  /// "Comprovante Boleto"
  String get comprovante_boleto => "Comprovante Boleto";

  /// "Valor total a ser pago"
  String get valor_total_a_ser_pago => "Valor total a ser pago";

  /// "Dispositivo removido com sucesso."
  String get dispositivo_removido_sucesso =>
      "Dispositivo removido com sucesso.";

  /// "Dispositivo"
  String get dispositivo => "Dispositivo";

  /// "Dispositivos autorizados a acessar esta conta"
  String get dispositivos_autorizados =>
      "Dispositivos autorizados a acessar esta conta";

  /// "Deseja remover dispositivo?"
  String get deseja_remover_dispositivo => "Deseja remover dispositivo?";

  /// "O dispositivo será removido da lista de autorizados."
  String get deseja_remover_dispositivo_msg =>
      "O dispositivo será removido da lista de autorizados.";

  /// "Remover"
  String get remover => "Remover";

  /// "Digite o código recebido em\n${email}"
  String digiteCodigoRecebidoRemocao(String email) =>
      "Digite o código recebido em\n${email}";

  /// "Você pode habilitar o login por digital no menu de configurações do aplicativo."
  String get msg_habilitar_digital_depois =>
      "Você pode habilitar o login por digital no menu de configurações do aplicativo.";

  /// "Você pode habilitar o login por Face ID no menu de configurações do aplicativo."
  String get msg_habilitar_digital_depois_face_id =>
      "Você pode habilitar o login por Face ID no menu de configurações do aplicativo.";

  /// "Digital habilitada com sucesso."
  String get msg_habilitar_sucesso => "Digital habilitada com sucesso.";

  /// "Face ID habilitado com sucesso."
  String get msg_habilitar_sucesso_face_id => "Face ID habilitado com sucesso.";

  /// "Habilitar login com digital?"
  String get habilitar_login_digital => "Habilitar login com digital?";

  /// "Habilitar login com Face ID?"
  String get habilitar_login_face_id => "Habilitar login com Face ID?";

  /// "Com o login com digital o acesso a sua conta será mais rápido e seguro."
  String get login_digital_seguro =>
      "Com o login com digital o acesso a sua conta será mais rápido e seguro.";

  /// "Com o login com o Face ID o acesso a sua conta será mais rápido e seguro."
  String get login_face_id_seguro =>
      "Com o login com o Face ID o acesso a sua conta será mais rápido e seguro.";

  /// "Habilitar login digital"
  String get habilitar_login_digital_btn => "Habilitar login digital";

  /// "Habilitar Face ID"
  String get habilitar_login_face_id_btn => "Habilitar Face ID";

  /// "Desabilitar digital"
  String get desabilitar_digital => "Desabilitar digital";

  /// "Desabilitar Face ID"
  String get desabilitar_face_id => "Desabilitar Face ID";

  /// "O login com a digital não será solicitado e será necessário logar com CPF/CNPJ e senha."
  String get desabilitar_login_digital_msg =>
      "O login com a digital não será solicitado e será necessário logar com CPF/CNPJ e senha.";

  /// "O login com Face ID não será solicitado e será necessário logar com CPF/CNPJ e senha."
  String get desabilitar_login_digital_msg_face_id =>
      "O login com Face ID não será solicitado e será necessário logar com CPF/CNPJ e senha.";

  /// "Desabilitar login com digital?"
  String get desabilitar_login_digital => "Desabilitar login com digital?";

  /// "Desabilitar login com Face ID?"
  String get desabilitar_login_digital_face_id =>
      "Desabilitar login com Face ID?";

  /// "Habilitar login com digital"
  String get habilitar_login_com_digital_menu => "Habilitar login com digital";

  /// "Habilitar login com Face ID"
  String get habilitar_login_com_digital_menu_face_id =>
      "Habilitar login com Face ID";

  /// "Desabilitar login com digital"
  String get desabilitar_login_digital_menu => "Desabilitar login com digital";

  /// "Desabilitar login com Face ID"
  String get desabilitar_login_digital_menu_face_id =>
      "Desabilitar login com Face ID";

  /// "Solicitar cartão virtual"
  String get solicitar_cartao_virtual => "Solicitar cartão virtual";

  /// "Visualizar dados"
  String get visualizar_dados_cartao => "Visualizar dados";

  /// "Desbloquear"
  String get desbloquear => "Desbloquear";

  /// "Bloquear"
  String get bloquear => "Bloquear";

  /// "Excluir"
  String get excluir => "Excluir";

  /// "Número do cartão"
  String get numero_cartao => "Número do cartão";

  /// "Cod.\nSegurança"
  String get cod_seguranca => "Cod.\nSegurança";

  /// "Valido\naté"
  String get validade => "Valido\naté";

  /// "Cartão virtual"
  String get cartao_virtual => "Cartão virtual";

  /// "Cartão"
  String get cartao => "Cartão";

  /// "Cartão Virtual"
  String get cartao_virtual_primeira_letra_maiuscula => "Cartão Virtual";

  /// "Empresa:"
  String get empresa_ => "Empresa:";

  /// "Ocultar dados"
  String get ocultar_dados_cartao => "Ocultar dados";

  /// "Nome"
  String get nome => "Nome";

  /// "Nome de exibicao"
  String get nome_exibido => "Nome de exibição";

  /// "Bloquear seu cartão\ntemporariamente?"
  String get bloquear_temporariamente =>
      "Bloquear seu cartão\ntemporariamente?";

  /// "Cartão bloqueado"
  String get cartao_bloqueado => "Cartão bloqueado";

  /// "Excluir seu cartão virtual?"
  String get apagar_cartao_virtal => "Excluir seu cartão virtual?";

  /// "É possível criar um cartão posteriormente. Lembre-se de atualizar os dados nos serviços e aplicativos caso crie um novo cartão."
  String get apagar_cartao_virtual_msg =>
      "É possível criar um cartão posteriormente. Lembre-se de atualizar os dados nos serviços e aplicativos caso crie um novo cartão.";

  /// "Excluir cartão virtual"
  String get apagar_cartao_vitual_btn => "Excluir cartão virtual";

  /// "Detalhes"
  String get detalhes => "Detalhes";

  /// "Valor original da parcela"
  String get valor_original_parcela => "Valor original da parcela";

  /// "Finalizado"
  String get finalizado => "Finalizado";

  /// "Cancelado"
  String get cancelado => "Cancelado";

  /// "Nenhuma transação a mostrar"
  String get nenhum_transacao => "Nenhuma transação a mostrar";

  /// "Tarifa"
  String get tarifa => "Tarifa";

  /// "Pacote de serviços de Conta Corrente e Carteira Digital "
  String get pacote_de_servicos =>
      "Pacote de serviços de Conta Corrente e Carteira Digital";

  /// "TED entre contas Siclos"
  String get TED_entre_contas_Siclos => "TED entre contas Siclos";

  /// "TED para outros bancos"
  String get TED_para_outros_bancos => "TED para outros bancos";

  /// "*Isenção em 2 envios mensais*Isenção em 2 envios mensais"
  String get isencao => "*Isenção em 2 envios mensais";

  /// "TED"
  String get ted => "TED";

  /// "TED Interno"
  String get ted_interno => "TED Interno";

  /// "Resgate"
  String get resgate => "Resgate";

  /// "Depósito (Boleto)"
  String get deposito_boleto_extrato => "Depósito (Boleto)";

  /// "Antecipação"
  String get antecipacao => "Antecipação";

  /// "Rescisão"
  String get recisao => "Rescisão";

  /// "Investimento"
  String get investimento_text => "Investimento";

  /// "Taxa de parcela"
  String get tec_taxa_diferida => "Taxa de parcela";

  /// "Taxa iniciais"
  String get tec_taxa_cabeca => "Taxa iniciais";

  /// "ted transferencia"
  String get ted_transferencia => " TED (para outros bancos)";

  /// "tec transferencia"
  String get tec_transferencia => "TEC (para uma conta Siclos)";

  /// "Emprest. Parcelas"
  String get emp_parcelas => "Emprest. Parcelas";

  /// "Transferência"
  String get transferencia => "Transferência";

  /// "Depósito dos consignados"
  String get deposito_dos_consiguinado => "Depósito dos consignados";

  /// "Concedido!"
  String get concedido_esclamacao => "Concedido!";

  /// "Parcela de empréstimo"
  String get tec_emprestimo_parcela => "Parcela de empréstimo";

  /// "Pagamento de conta"
  String get pagamento_conta => "Pagamento de conta";

  /// "Deposito por TED"
  String get ted_deposito => "Deposito por TED";

  /// "Empréstimo de ${nome}"
  String emprestimoDe(String nome) => "Empréstimo de ${nome}";

  /// "Parcelas Recebidas (${qtde})"
  String parcelasRecebidas(String qtde) => "Parcelas Recebidas (${qtde})";

  /// "Parcela Recebida (1)"
  String get parcela_recebida_parcelas => "Parcela Recebida (1)";

  /// "Investimento realizado (${nome})"
  String investimentoRealizado(String nome) =>
      "Investimento realizado (${nome})";

  /// "Novo Siclos ® APP"
  String get titulo_card_1 => "Novo Siclos ® APP";

  /// "Tudo num só lugar, fácil e\nrápido como você nunca viu!\nNavegue e veja todas as\nfuncionalidades."
  String get msg_card_1 =>
      "Tudo num só lugar, fácil e\nrápido como você nunca viu!\nNavegue e veja todas as\nfuncionalidades.";

  /// "Novo Consignado"
  String get titulo_card_2 => "Novo Crédito Rápido";

  /// "Um novo empréstimo, rápido, fácil e você pode renovar todo mês."
  String get msg_card_2 =>
      "Um novo empréstimo, \nrápido, fácil e você pode \nrenovar todo mês. \n\nSaiba mais...";

  /// "Cartão Virtual"
  String get titulo_card_3 => "Cartão Virtual";

  /// "Faça compras pela internet,\nsempre na função "
  String get msg_card_3_1 => "Faça compras pela internet,\nsempre na função ";

  /// "CRÉDITO, "
  String get msg_card_3_2 => "CRÉDITO, ";

  /// "e\no valor será debitado\ndiretamente na sua conta!"
  String get msg_card_3_3 =>
      "e\no valor será debitado\ndiretamente na sua conta!";

  /// "Novo Chat!"
  String get titulo_card_4 => "Novo Chat!";

  /// "Temos uma equipe pronta\npara te atender sempre que\nprecisar. Acesse aqui e tire todas\nas suas dúvidas com nossa\nequipe."
  String get msg_card_4 =>
      "Temos uma equipe pronta\npara te atender sempre que\nprecisar. Acesse aqui e tire todas\nas suas dúvidas com nossa\nequipe.";

  /// "Você errou o PIN três vezes, por segurança, \nbloqueamos a sua conta temporariamente."
  String get conta_bloqueada_pin_msg =>
      "Você errou o PIN três vezes, por segurança, \nbloqueamos a sua conta temporariamente.";

  /// "Enviamos um e-mail para realizar o desbloqueio."
  String get enviamos_email_desbloqueio =>
      "Enviamos um e-mail para realizar o desbloqueio.";

  /// "Digite o código enviado para $email. \nLembre-se de verificar a caixa de spam."
  String send_email_code(String email) =>
      "Digite o código enviado para $email. \nLembre-se de verificar a caixa de spam.";

  /// "Digite o código enviado para $email. \nLembre-se de verificar a caixa de spam."
  String send_sms_code(String phone) =>
      "Digite o código recebido por SMS em $phone.";

  /// "Gestão Financeira"
  String get gestao_financeira => "Gestão Financeira";

  /// "Gestão profissional"
  String get gestao_profissional => "Gestão profissional";

  /// "IRPF 2022"
  String get irpf_2022 => "IRPF 2022";

  /// "Gere seu demonstrativo"
  String get gere_demonstrativo => "Gere seu demonstrativo";

  /// "Imposto de renda"
  String get imposto_renda => "Imposto de renda";

  /// "Holerite"
  String get holerite => "Holerite";

  /// "Horas profissionais"
  String get horas_profissionais => "Horas profissionais";

  /// "Férias"
  String get ferias => "Férias";

  /// "Deseja realizar o download do imposto de renda do ano de ${ano}, ano referência ${anoReferencia}?"
  String desejaDownloadImpostoRenda(String ano, String anoReferencia) =>
      "Deseja realizar o download do imposto de renda do ano de ${ano}, ano referência ${anoReferencia}?";

  /// "Para abrir o seu imposto de renda, é necessário digitar os 6 primeiros números do seu CPF."
  String get senha_cpf_irpf =>
      "Para abrir o seu imposto de renda, é necessário digitar os 6 primeiros números do seu CPF.";

  /// "Para abrir o seu holerite, é necessário digitar os 6 primeiros números do seu CPF."
  String get abrir_holerite =>
      "Para abrir o seu holerite, é necessário digitar os 6 primeiros números do seu CPF.";

  /// "Para abrir o seu holerite, é necessário digitar os 6 primeiros números do seu CPF."
  String get abrir_ferias =>
      "Para abrir o seu recibo de férias, é necessário digitar os 6 primeiros números do seu CPF.";

  /// "Sim, fazer download"
  String get sim_fazer_download => "Sim, fazer download";

  /// "Holerites disponíveis"
  String get holerite_disponiveis => "Holerites disponíveis";

  /// "**** **** ****  ${finalCartao}"
  String ultimoDigitosCartao(String finalCartao) =>
      "**** **** ****  ${finalCartao}";

  /// "WhatsApp (seg/sex: 8h às 18h30 e sáb: 8h às 14h)"
  String get whatsapp_horario =>
      "WhatsApp (seg/sex: 8h às 18h30 e sáb: 8h às 14h)";

  /// "${valor}% período"
  String porcentagemPeriodo(String valor) => "${valor}% período";

  /// "Funcionário(a) há 1 ano"
  String get trabalha_um_ano => "Funcionário(a) há 1 ano";

  /// "Funcionário(a) há ${qtde} anos"
  String trabalhaVariosAnos(String qtde) => "Funcionário(a) há ${qtde} anos";

  /// "Funcionário(a) há 1 mês"
  String get trabalha_um_mes => "Funcionário(a) há 1 mês";

  /// "Funcionário(a) há ${qtde} meses"
  String trabalhaVariosMeses(String qtde) => "Funcionário(a) há ${qtde} meses";

  /// "Rentabilidade de ${percent}% CDI"
  String rentabilidadeCdi(String percent) => "Rentabilidade de ${percent}% CDI";

  /// "Investimento de baixíssimo risco"
  String get investimento_a => "Investimento de baixíssimo risco";

  /// "Investimento de baixo risco"
  String get investimento_b => "Investimento de baixo risco";

  /// "Investimento de risco moderado"
  String get investimento_c => "Investimento de risco moderado";

  /// "Investimento de maior risco"
  String get investimento_d_e => "Investimento de maior risco";

  /// "Empresa"
  String get empresa => "Empresa";

  /// "${nomeEmpresa} (${qtde} funcionários)"
  String nomeEmpresaQtdeFuncionario(String nomeEmpresa, String qtde) =>
      "${nomeEmpresa} (${qtde} funcionários)";

  /// "${qtdeAnos} de atividade"
  String anosDeAtividade(String qtdeAnos) => "${qtdeAnos} de atividade";

  /// "1 ano"
  String get um_ano => "1 ano";

  /// "1 mês"
  String get um_mes => "1 mês";

  /// "${qtde} anos"
  String anos(String qtde) => "${qtde} anos";

  /// "${qtde} meses"
  String meses(String qtde) => "${qtde} meses";

  /// "Alterar imagem de perfil"
  String get alterar_imagem_perfil => "Alterar imagem de perfil";

  /// "Selecione a imagem que deseja utilizar como foto de perfil"
  String get selecione_imagem =>
      "Selecione a imagem que deseja utilizar como foto de perfil";

  /// "Imagem de perfil alterada com sucesso"
  String get imagem_alterada_sucesso => "Imagem de perfil alterada com sucesso";

  /// "Editar imagem"
  String get editar_imagem => "Editar imagem";

  /// "Galeria de fotos"
  String get galeria_fotos => "Galeria de fotos";

  /// "Câmera"
  String get camera => "Câmera";

  /// "Ajuste sua foto"
  String get ajuste_foto => "Ajuste sua foto";

  /// "O score de crédito está associado ao risco de inadimplência histórica por perfil, além de levar em consideração diversos outros fatores de consulta de crédito do tomador. Quanto mais o risco está próximo de A+, menor a chance de inadimplência."
  String get frase_score =>
      "O score de crédito está associado ao risco de inadimplência histórica por perfil, além de levar em consideração diversos outros fatores de consulta de crédito do tomador. Quanto mais o risco está próximo de A+, menor a chance de inadimplência.";

  /// "Renda mensal"
  String get renda_mensal => "Renda mensal";

  /// "Parece que você não tem o aplicativo WhatsApp instalado."
  String get whatsapp_nao_instalado =>
      "Parece que você não tem o aplicativo WhatsApp instalado.";

  String get atualizar_app => "Atualizar aplicativo";

  String get fechar_app => "Fechar aplicativo";

  String get simulacao_em_andamento_pendente_documentos =>
      "Você já possui uma simulação de empréstimo em andamento, pendente do envio de documentação. Para continuar envie a documentação solicitada, ou volte e cancele a simulação em andamento.";

  String get msg_error_limite =>
      "Ocorreu um erro ao obter a política de crédito no momento. Tente novamente mais tarde.";

  String get nome_exibicao_invalido =>
      'O nome de exibição deve conter apenas letras e espaços';
  String get nome_exibicao_espacos_consecutivos =>
      'Não são permitidos múltiplos espaços consecutivos';
  String get nome_exibicao_alterado_sucesso =>
      'Nome de exibição alterado com sucesso';
  String get nome_exibicao_erro =>
      'Ocorreu um erro ao alterar o nome de exibição. Tente novamente mais tarde';

  @override
  // TODO: implement reorderItemDown
  String get reorderItemDown => 'Mover para baixo';

  @override
  // TODO: implement reorderItemLeft
  String get reorderItemLeft => 'Mover para esquerda';

  @override
  // TODO: implement reorderItemRight
  String get reorderItemRight => 'Mover para direita';

  @override
  // TODO: implement reorderItemToEnd
  String get reorderItemToEnd => 'Mover para o final';

  @override
  // TODO: implement reorderItemToStart
  String get reorderItemToStart => 'Mover para o início';

  @override
  // TODO: implement reorderItemUp
  String get reorderItemUp => 'Mover para cima';

  @override
  // TODO: implement copyButtonLabel
  String get copyButtonLabel => 'Copiar';

  @override
  // TODO: implement cutButtonLabel
  String get cutButtonLabel => 'Recortar';

  @override
  // TODO: implement lookUpButtonLabel
  String get lookUpButtonLabel => 'Pesquisar';

  @override
  // TODO: implement pasteButtonLabel
  String get pasteButtonLabel => 'Colar';

  @override
  // TODO: implement searchWebButtonLabel
  String get searchWebButtonLabel => 'Pesquisar na web';

  @override
  // TODO: implement selectAllButtonLabel
  String get selectAllButtonLabel => 'Selecionar tudo';

  @override
  // TODO: implement shareButtonLabel
  String get shareButtonLabel => 'Compartilhar';
}

class _I18n_pt_BR extends I18n {
  const _I18n_pt_BR();

  @override
  TextDirection get textDirection => TextDirection.ltr;
}

class GeneratedLocalizationsDelegate
    extends LocalizationsDelegate<WidgetsLocalizations> {
  const GeneratedLocalizationsDelegate();
  List<Locale> get supportedLocales {
    return const <Locale>[Locale("pt", "BR")];
  }

  LocaleResolutionCallback resolution({Locale? fallback}) {
    return (Locale? locale, Iterable<Locale> supported) {
      if (isSupported(locale!)) {
        return locale;
      }
      final Locale fallbackLocale = fallback ?? supported.first;
      return fallbackLocale;
    };
  }

  @override
  Future<WidgetsLocalizations> load(Locale locale) {
    I18n._locale ??= locale;
    I18n._shouldReload = false;
    final String lang = I18n._locale != null ? I18n._locale.toString() : "";
    final String languageCode = I18n._locale != null
        ? I18n._locale!.languageCode
        : "";
    if ("pt_BR" == lang) {
      return SynchronousFuture<WidgetsLocalizations>(const _I18n_pt_BR());
    } else if ("pt" == languageCode) {
      return SynchronousFuture<WidgetsLocalizations>(const _I18n_pt_BR());
    }

    return SynchronousFuture<WidgetsLocalizations>(const I18n());
  }

  @override
  bool isSupported(Locale locale) {
    for (var i = 0; i < supportedLocales.length; i++) {
      final l = supportedLocales[i];
      if (l.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }

  @override
  bool shouldReload(GeneratedLocalizationsDelegate old) => I18n._shouldReload;
}

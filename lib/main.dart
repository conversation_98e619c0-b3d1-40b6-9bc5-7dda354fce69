import 'dart:async';
import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_modular/flutter_modular.dart';

import 'app/app_module.dart';
import 'app/app_widget.dart';
import 'app/shared/config/environment.dart';
import 'app/shared/data/datasource/new_relic/new_relic.dart';
import 'app/shared/notifications/firebase_notifications.dart';
import 'firebase_options.dart';

void main() {
  kIsWeb ? launchAppWeb() : launchApp();
}

launchAppWeb() async {
  await initConfig();
  runApp(ModularApp(module: AppModule(), child: const AppWidget()));
}

launchApp() async {
  final newRelic = NewRelicService();
  runZonedGuarded(() async {
    await initConfig();
    await newRelic.start();
    runApp(ModularApp(module: AppModule(), child: const AppWidget()));
  }, (Object error, StackTrace stackTrace) {
    if (kDebugMode) {
      log('Error in main: $error', stackTrace: stackTrace);
    } else {
      newRelic.recordError(error, stackTrace);
    }
  });
}

Future<void> initConfig() async {
  WidgetsFlutterBinding.ensureInitialized();
  const flavor = String.fromEnvironment('FLAVOR'); // ex: dev, stag, prod
  await Environment.initialize(flavor);

  if (!kIsWeb) {
    await Firebase.initializeApp(options: AppFirebaseOptions.options);
    FirebaseNotifications.getPermissions();
  }
}

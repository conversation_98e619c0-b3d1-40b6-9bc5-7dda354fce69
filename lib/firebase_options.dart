import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:siclosbank/app/shared/config/flavor.dart';

class AppFirebaseOptions {
  static FirebaseOptions get options {
    final isProd = Flavor.isProduction;
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return isProd ? androidProd : androidStag;
      case TargetPlatform.iOS:
        return isProd ? iosProd : iosStag;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions androidStag = FirebaseOptions(
    apiKey: 'AIzaSyAKSKjTvtddY93zEYNB7Bvwwks-tcpSgP4',
    appId: '1:************:android:ea5f341bff6a19db34dee2',
    messagingSenderId: '************',
    projectId: 'siclosbank [homolog]',
    storageBucket: 'siclosbank.firebasestorage.app',
  );

  static const FirebaseOptions iosStag = FirebaseOptions(
    apiKey: 'AIzaSyAE6DFg54rnmCgLeXJrJynX0-pk0-nAP98',
    appId: '1:************:ios:1a89fc3e73886fd834dee2',
    messagingSenderId: '************',
    projectId: 'siclosbank',
    storageBucket: 'siclosbank.firebasestorage.app',
    iosBundleId: 'com.siclos.siclosbank',
    // iosBundleId: 'com.siclos.siclosbank-homolog',
  );

  static const FirebaseOptions androidProd = FirebaseOptions(
    apiKey: 'AIzaSyAHzXpN7vkTSNSqdHArIe0lVPaqgbwIijY',
    appId: '1:************:android:edc9c177ece2194bc4de66',
    messagingSenderId: '************',
    projectId: 'siclos-bank-prod',
    storageBucket: 'siclos-bank-prod.firebasestorage.app',
  );

  static const FirebaseOptions iosProd = FirebaseOptions(
    apiKey: 'AIzaSyDwjRU2dlPuMDGRSJTpTR6_ocX4w5AfXbk',
    appId: '1:************:ios:e13413bf9c0cd55fc4de66',
    messagingSenderId: '************',
    projectId: 'siclos-bank-prod',
    storageBucket: 'siclos-bank-prod.firebasestorage.app',
    iosBundleId: 'com.siclos.siclosbank',
  );
}

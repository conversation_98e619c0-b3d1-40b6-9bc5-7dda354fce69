<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Simple widget usage</title>
  <script src="https://cdn-public-library.clicksign.com/embedded/embedded.min-2.0.0.js"></script>
</head>

<body>
  <!-- <input id='request_signature_id' />
    <input type='button' value='Load' onclick='run()'/> -->
  <div id='container' style='height: 600px'></div>

  <script type='text/javascript'>

    var widget;
    var request_signature_id;
    function run() {
      // Obtém a string completa da URL
      var urlParams = new URLSearchParams(window.location.search);
      // Acessa o valor da query 'signature_id'
      request_signature_id = urlParams.get('signature_id');
      var value = urlParams.get('prod');
      var isProd = value == 'true' ? true : false;
      console.log(`signature_id: ${request_signature_id}`, );
      if (widget) { widget.unmount(); }
      // Carrega o widget embedded com o id do signatário
      widget = new Clicksign(request_signature_id);
      // Define o endpoint https://sandbox.clicksign.com ou https://app.clicksign.com
      widget.endpoint = isProd ? 'https://app.clicksign.com' : 'https://sandbox.clicksign.com';
      // Define a URL de origem (parametro necessário para utilizar através de WebView)
      widget.origin = '*'; // <endereço do seu site>
      console.log('widget', widget);
      widget.mount('container');

      // Callback que será disparado quando o documento for carregado
      widget.on('loaded', function (event) { console.log('loaded!'); });
      // Callback que será disparado quando o documento for assinado
      widget.on('signed', function (event) {
        console.log('signed!');
        widget.unmount();
        window.ON_SIGNED_SUCCESS.postMessage("Usuário autenticado com sucesso!");
      });
      /* Callback que será disparado nas etapas de informar dados do signatário
      e token, retornando a altura (height) dessas páginas para ajuste do container
      onde o iframe se encontra. */
      widget.on('resized', function (event) {
        console.log('resized!');
        document.getElementById('container').style.height = event.data.height + 'px';
      });

    };

    document.addEventListener('DOMContentLoaded', function () {
      run();
    });

  </script>
</body>

</html>